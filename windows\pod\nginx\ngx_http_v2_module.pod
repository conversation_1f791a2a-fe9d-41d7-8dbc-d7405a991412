=encoding utf-8

=head1 NAME

ngx_http_v2_module - Module ngx_http_v2_module




=head1



The C<ngx_http_v2_module> module (1.9.5) provides
support for
L<HTTPE<sol>2|https://datatracker.ietf.org/doc/html/rfc7540>.





This module is not built by default, it should be enabled with
the C<--with-http_v2_module>
configuration parameter.




=head1 Known Issues



Before version 1.9.14,
buffering of a client request body could not be disabled
regardless of
L<ngx_http_proxy_module>,
L<ngx_http_fastcgi_module>,
L<ngx_http_uwsgi_module>, and
L<ngx_http_scgi_module>
directive values.





Before version 1.19.1,
the L<ngx_http_core_module> mechanism
was not used to control closing HTTPE<sol>2 connections.




=head1 Example Configuration




    
    server {
        listen 443 ssl;
    
        http2 on;
    
        ssl_certificate server.crt;
        ssl_certificate_key server.key;
    }


Note that accepting HTTPE<sol>2 connections over TLS requires
the “Application-Layer Protocol Negotiation” (ALPN) TLS extension
support, which is available since
L<OpenSSL|http://www.openssl.org> version 1.0.2.





Also note that if the
L<ngx_http_ssl_module> directive
is set to the value “C<on>”,
the L<ciphers|ngx_http_ssl_module>
should be configured to comply with
L<RFC 9113, Appendix A|https://datatracker.ietf.org/doc/html/rfc9113#appendix-A>
black list and supported by clients.




=head1 Directives

=head2 http2


B<syntax:> http2 I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>



This directive appeared in version 1.25.1.





Enables
the L<HTTPE<sol>2|https://datatracker.ietf.org/doc/html/rfc9113>
protocol.







=head2 http2_body_preread_size


B<syntax:> http2_body_preread_size I<I<C<size>>>


B<default:> I<64k>


B<context:> I<http>


B<context:> I<server>



This directive appeared in version 1.11.0.





Sets the I<C<size>> of the buffer per each request
in which the request body may be saved
before it is started to be processed.







=head2 http2_chunk_size


B<syntax:> http2_chunk_size I<I<C<size>>>


B<default:> I<8k>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets the maximum size of chunks
into which the response body is sliced.
A too low value results in higher overhead.
A too high value impairs prioritization due to
L<HOL blocking|http://en.wikipedia.org/wiki/Head-of-line_blocking>.







=head2 http2_idle_timeout


B<syntax:> http2_idle_timeout I<I<C<time>>>


B<default:> I<3m>


B<context:> I<http>


B<context:> I<server>






B<NOTE>

This directive is obsolete since version 1.19.7.
The L<ngx_http_core_module>
directive should be used instead.






Sets the timeout of inactivity after which the connection is closed.







=head2 http2_max_concurrent_pushes


B<syntax:> http2_max_concurrent_pushes I<I<C<number>>>


B<default:> I<10>


B<context:> I<http>


B<context:> I<server>



This directive appeared in version 1.13.9.






B<NOTE>

This directive is obsolete since version 1.25.1.






Limits the maximum number of concurrent
push requests in a connection.







=head2 http2_max_concurrent_streams


B<syntax:> http2_max_concurrent_streams I<I<C<number>>>


B<default:> I<128>


B<context:> I<http>


B<context:> I<server>





Sets the maximum number of concurrent HTTPE<sol>2 streams
in a connection.







=head2 http2_max_field_size


B<syntax:> http2_max_field_size I<I<C<size>>>


B<default:> I<4k>


B<context:> I<http>


B<context:> I<server>






B<NOTE>

This directive is obsolete since version 1.19.7.
The L<ngx_http_core_module>
directive should be used instead.






Limits the maximum size of
an L<HPACK|https://datatracker.ietf.org/doc/html/rfc7541>-compressed
request header field.
The limit applies equally to both name and value.
Note that if Huffman encoding is applied,
the actual size of decompressed name and value strings may be larger.
For most requests, the default limit should be enough.







=head2 http2_max_header_size


B<syntax:> http2_max_header_size I<I<C<size>>>


B<default:> I<16k>


B<context:> I<http>


B<context:> I<server>






B<NOTE>

This directive is obsolete since version 1.19.7.
The L<ngx_http_core_module>
directive should be used instead.






Limits the maximum size of the entire request header list after
L<HPACK|https://datatracker.ietf.org/doc/html/rfc7541> decompression.
For most requests, the default limit should be enough.







=head2 http2_max_requests


B<syntax:> http2_max_requests I<I<C<number>>>


B<default:> I<1000>


B<context:> I<http>


B<context:> I<server>



This directive appeared in version 1.11.6.






B<NOTE>

This directive is obsolete since version 1.19.7.
The L<ngx_http_core_module>
directive should be used instead.






Sets the maximum number of requests (including
push requests) that can be served
through one HTTPE<sol>2 connection,
after which the next client request will lead to connection closing
and the need of establishing a new connection.





Closing connections periodically is necessary to free
per-connection memory allocations.
Therefore, using too high maximum number of requests
could result in excessive memory usage and not recommended.







=head2 http2_push


B<syntax:> http2_push I<I<C<uri>> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.13.9.






B<NOTE>

This directive is obsolete since version 1.25.1.






Pre-emptively sends
(L<pushes|https://datatracker.ietf.org/doc/html/rfc9113#section-8.4>)
a request to the specified I<C<uri>>
along with the response to the original request.
Only relative URIs with absolute path will be processed,
for example:

    
    http2_push /static/css/main.css;


The I<C<uri>> value can contain variables.





Several C<http2_push> directives
can be specified on the same configuration level.
The C<off> parameter cancels the effect
of the C<http2_push> directives
inherited from the previous configuration level.







=head2 http2_push_preload


B<syntax:> http2_push_preload I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.13.9.






B<NOTE>

This directive is obsolete since version 1.25.1.






Enables automatic conversion of
L<preload
links|https://www.w3.org/TR/preload/#server-push-http-2>
specified in the C<Link> response header fields into
L<push|https://datatracker.ietf.org/doc/html/rfc9113#section-8.4>
requests.







=head2 http2_recv_buffer_size


B<syntax:> http2_recv_buffer_size I<I<C<size>>>


B<default:> I<256k>


B<context:> I<http>





Sets the size of the per
L<worker|ngx_core_module>
input buffer.







=head2 http2_recv_timeout


B<syntax:> http2_recv_timeout I<I<C<time>>>


B<default:> I<30s>


B<context:> I<http>


B<context:> I<server>






B<NOTE>

This directive is obsolete since version 1.19.7.
The L<ngx_http_core_module>
directive should be used instead.






Sets the timeout for expecting more data from the client,
after which the connection is closed.







=head1 Embedded Variables



The C<ngx_http_v2_module> module
supports the following embedded variables:

=over



=item C<$http2>




negotiated protocol identifier:
“C<h2>” for HTTPE<sol>2 over TLS,
“C<h2c>” for HTTPE<sol>2 over cleartext TCP,
or an empty string otherwise.




=back






