=encoding utf-8

=head1 NAME

ngx_http_scgi_module - Module ngx_http_scgi_module




=head1



The C<ngx_http_scgi_module> module allows passing
requests to an SCGI server.




=head1 Example Configuration




    
    location / {
        include   scgi_params;
        scgi_pass localhost:9000;
    }






=head1 Directives

=head2 scgi_bind


B<syntax:> scgi_bind I<
    I<C<address>>
    [C<transparent>] E<verbar>
    C<off>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Makes outgoing connections to an SCGI server originate
from the specified local IP address with an optional port (1.11.2).
Parameter value can contain variables (1.3.12).
The special value C<off> (1.3.12) cancels the effect
of the C<scgi_bind> directive
inherited from the previous configuration level, which allows the
system to auto-assign the local IP address and port.





The C<transparent> parameter (1.11.0) allows
outgoing connections to an SCGI server originate
from a non-local IP address,
for example, from a real IP address of a client:

    
    scgi_bind $remote_addr transparent;


In order for this parameter to work,
it is usually necessary to run nginx worker processes with the
L<superuser|ngx_core_module> privileges.
On Linux it is not required (1.13.8) as if
the C<transparent> parameter is specified, worker processes
inherit the C<CAP_NET_RAW> capability from the master process.
It is also necessary to configure kernel routing table
to intercept network traffic from the SCGI server.







=head2 scgi_buffer_size


B<syntax:> scgi_buffer_size I<I<C<size>>>


B<default:> I<4kE<verbar>8k>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets the I<C<size>> of the buffer used for reading the first part
of the response received from the SCGI server.
This part usually contains a small response header.
By default, the buffer size is equal to one memory page.
This is either 4K or 8K, depending on a platform.
It can be made smaller, however.







=head2 scgi_buffering


B<syntax:> scgi_buffering I<C<on> E<verbar> C<off>>


B<default:> I<on>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Enables or disables buffering of responses from the SCGI server.





When buffering is enabled, nginx receives a response from the SCGI server
as soon as possible, saving it into the buffers set by the
L</scgi_buffer_size> and L</scgi_buffers> directives.
If the whole response does not fit into memory, a part of it can be saved
to a temporary file on the disk.
Writing to temporary files is controlled by the
L</scgi_max_temp_file_size> and
L</scgi_temp_file_write_size> directives.





When buffering is disabled, the response is passed to a client synchronously,
immediately as it is received.
nginx will not try to read the whole response from the SCGI server.
The maximum size of the data that nginx can receive from the server
at a time is set by the L</scgi_buffer_size> directive.





Buffering can also be enabled or disabled by passing
“C<yes>” or “C<no>” in the
C<X-Accel-Buffering> response header field.
This capability can be disabled using the
L</scgi_ignore_headers> directive.







=head2 scgi_buffers


B<syntax:> scgi_buffers I<I<C<number>> I<C<size>>>


B<default:> I<8 4kE<verbar>8k>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets the I<C<number>> and I<C<size>> of the
buffers used for reading a response from the SCGI server,
for a single connection.
By default, the buffer size is equal to one memory page.
This is either 4K or 8K, depending on a platform.







=head2 scgi_busy_buffers_size


B<syntax:> scgi_busy_buffers_size I<I<C<size>>>


B<default:> I<8kE<verbar>16k>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





When buffering of responses from the SCGI
server is enabled, limits the total I<C<size>> of buffers that
can be busy sending a response to the client while the response is not
yet fully read.
In the meantime, the rest of the buffers can be used for reading the response
and, if needed, buffering part of the response to a temporary file.
By default, I<C<size>> is limited by the size of two buffers set by the
L</scgi_buffer_size> and L</scgi_buffers> directives.







=head2 scgi_cache


B<syntax:> scgi_cache I<I<C<zone>> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines a shared memory zone used for caching.
The same zone can be used in several places.
Parameter value can contain variables (1.7.9).
The C<off> parameter disables caching inherited
from the previous configuration level.







=head2 scgi_cache_background_update


B<syntax:> scgi_cache_background_update I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.11.10.





Allows starting a background subrequest
to update an expired cache item,
while a stale cached response is returned to the client.
Note that it is necessary to
allow
the usage of a stale cached response when it is being updated.







=head2 scgi_cache_bypass


B<syntax:> scgi_cache_bypass I<I<C<string>> ...>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines conditions under which the response will not be taken from a cache.
If at least one value of the string parameters is not empty and is not
equal to “0” then the response will not be taken from the cache:

    
    scgi_cache_bypass $cookie_nocache $arg_nocache$arg_comment;
    scgi_cache_bypass $http_pragma    $http_authorization;


Can be used along with the L</scgi_no_cache> directive.







=head2 scgi_cache_key


B<syntax:> scgi_cache_key I<I<C<string>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines a key for caching, for example

    
    scgi_cache_key localhost:9000$request_uri;









=head2 scgi_cache_lock


B<syntax:> scgi_cache_lock I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.1.12.





When enabled, only one request at a time will be allowed to populate
a new cache element identified according to the L</scgi_cache_key>
directive by passing a request to an SCGI server.
Other requests of the same cache element will either wait
for a response to appear in the cache or the cache lock for
this element to be released, up to the time set by the
L</scgi_cache_lock_timeout> directive.







=head2 scgi_cache_lock_age


B<syntax:> scgi_cache_lock_age I<I<C<time>>>


B<default:> I<5s>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.7.8.





If the last request passed to the SCGI server
for populating a new cache element
has not completed for the specified I<C<time>>,
one more request may be passed to the SCGI server.







=head2 scgi_cache_lock_timeout


B<syntax:> scgi_cache_lock_timeout I<I<C<time>>>


B<default:> I<5s>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.1.12.





Sets a timeout for L</scgi_cache_lock>.
When the I<C<time>> expires,
the request will be passed to the SCGI server,
however, the response will not be cached.

B<NOTE>

Before 1.7.8, the response could be cached.








=head2 scgi_cache_max_range_offset


B<syntax:> scgi_cache_max_range_offset I<I<C<number>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.11.6.





Sets an offset in bytes for byte-range requests.
If the range is beyond the offset,
the range request will be passed to the SCGI server
and the response will not be cached.







=head2 scgi_cache_methods


B<syntax:> scgi_cache_methods I<
    C<GET> E<verbar>
    C<HEAD> E<verbar>
    C<POST>
    ...>


B<default:> I<GET HEAD>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





If the client request method is listed in this directive then
the response will be cached.
“C<GET>” and “C<HEAD>” methods are always
added to the list, though it is recommended to specify them explicitly.
See also the L</scgi_no_cache> directive.







=head2 scgi_cache_min_uses


B<syntax:> scgi_cache_min_uses I<I<C<number>>>


B<default:> I<1>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets the I<C<number>> of requests after which the response
will be cached.







=head2 scgi_cache_path


B<syntax:> scgi_cache_path I<
    I<C<path>>
    [C<levels>=I<C<levels>>]
    [C<use_temp_path>=C<on>E<verbar>C<off>]
    C<keys_zone>=I<C<name>>:I<C<size>>
    [C<inactive>=I<C<time>>]
    [C<max_size>=I<C<size>>]
    [C<min_free>=I<C<size>>]
    [C<manager_files>=I<C<number>>]
    [C<manager_sleep>=I<C<time>>]
    [C<manager_threshold>=I<C<time>>]
    [C<loader_files>=I<C<number>>]
    [C<loader_sleep>=I<C<time>>]
    [C<loader_threshold>=I<C<time>>]
    [C<purger>=C<on>E<verbar>C<off>]
    [C<purger_files>=I<C<number>>]
    [C<purger_sleep>=I<C<time>>]
    [C<purger_threshold>=I<C<time>>]>



B<context:> I<http>





Sets the path and other parameters of a cache.
Cache data are stored in files.
The file name in a cache is a result of
applying the MD5 function to the
cache key.
The C<levels> parameter defines hierarchy levels of a cache:
from 1 to 3, each level accepts values 1 or 2.
For example, in the following configuration

    
    scgi_cache_path /data/nginx/cache levels=1:2 keys_zone=one:10m;


file names in a cache will look like this:

    
    /data/nginx/cache/<emphasis>c</emphasis>/<emphasis>29</emphasis>/b7f54b2df7773722d382f4809d650<emphasis>29c</emphasis>







A cached response is first written to a temporary file,
and then the file is renamed.
Starting from version 0.8.9, temporary files and the cache can be put on
different file systems.
However, be aware that in this case a file is copied
across two file systems instead of the cheap renaming operation.
It is thus recommended that for any given location both cache and a directory
holding temporary files
are put on the same file system.
A directory for temporary files is set based on
the C<use_temp_path> parameter (1.7.10).
If this parameter is omitted or set to the value C<on>,
the directory set by the L</scgi_temp_path> directive
for the given location will be used.
If the value is set to C<off>,
temporary files will be put directly in the cache directory.





In addition, all active keys and information about data are stored
in a shared memory zone, whose I<C<name>> and I<C<size>>
are configured by the C<keys_zone> parameter.
One megabyte zone can store about 8 thousand keys.

B<NOTE>

As part of
commercial subscription,
the shared memory zone also stores extended
cache L<information|ngx_http_api_module>,
thus, it is required to specify a larger zone size for the same number of keys.
For example,
one megabyte zone can store about 4 thousand keys.






Cached data that are not accessed during the time specified by the
C<inactive> parameter get removed from the cache
regardless of their freshness.
By default, C<inactive> is set to 10 minutes.





The special “cache manager” process monitors the maximum cache size set
by the C<max_size> parameter,
and the minimum amount of free space set
by the C<min_free> (1.19.1) parameter
on the file system with cache.
When the size is exceeded or there is not enough free space,
it removes the least recently used data.
The data is removed in iterations configured by
C<manager_files>,
C<manager_threshold>, and
C<manager_sleep> parameters (1.11.5).
During one iteration no more than C<manager_files> items
are deleted (by default, 100).
The duration of one iteration is limited by the
C<manager_threshold> parameter (by default, 200 milliseconds).
Between iterations, a pause configured by the C<manager_sleep>
parameter (by default, 50 milliseconds) is made.





A minute after the start the special “cache loader” process is activated.
It loads information about previously cached data stored on file system
into a cache zone.
The loading is also done in iterations.
During one iteration no more than C<loader_files> items
are loaded (by default, 100).
Besides, the duration of one iteration is limited by the
C<loader_threshold> parameter (by default, 200 milliseconds).
Between iterations, a pause configured by the C<loader_sleep>
parameter (by default, 50 milliseconds) is made.





Additionally,
the following parameters are available as part of our
commercial subscription:






=over



=item 
C<purger>=C<on>E<verbar>C<off>





Instructs whether cache entries that match a
wildcard key
will be removed from the disk by the cache purger (1.7.12).
Setting the parameter to C<on>
(default is C<off>)
will activate the “cache purger” process that
permanently iterates through all cache entries
and deletes the entries that match the wildcard key.



=item 
C<purger_files>=I<C<number>>





Sets the number of items that will be scanned during one iteration (1.7.12).
By default, C<purger_files> is set to 10.



=item 
C<purger_threshold>=I<C<number>>





Sets the duration of one iteration (1.7.12).
By default, C<purger_threshold> is set to 50 milliseconds.



=item 
C<purger_sleep>=I<C<number>>





Sets a pause between iterations (1.7.12).
By default, C<purger_sleep> is set to 50 milliseconds.




=back








B<NOTE>

In versions 1.7.3, 1.7.7, and 1.11.10 cache header format has been changed.
Previously cached responses will be considered invalid
after upgrading to a newer nginx version.








=head2 scgi_cache_purge


B<syntax:> scgi_cache_purge I<string ...>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.5.7.





Defines conditions under which the request will be considered a cache
purge request.
If at least one value of the string parameters is not empty and is not equal
to “0” then the cache entry with a corresponding
cache key is removed.
The result of successful operation is indicated by returning
the C<204> (C<No Content>) response.





If the cache key of a purge request ends
with an asterisk (“C<*>”), all cache entries matching the
wildcard key will be removed from the cache.
However, these entries will remain on the disk until they are deleted
for either inactivity,
or processed by the cache purger (1.7.12),
or a client attempts to access them.





Example configuration:

    
    scgi_cache_path /data/nginx/cache keys_zone=cache_zone:10m;
    
    map $request_method $purge_method {
        PURGE   1;
        default 0;
    }
    
    server {
        ...
        location / {
            scgi_pass        backend;
            scgi_cache       cache_zone;
            scgi_cache_key   $uri;
            scgi_cache_purge $purge_method;
        }
    }



B<NOTE>

This functionality is available as part of our
commercial subscription.








=head2 scgi_cache_revalidate


B<syntax:> scgi_cache_revalidate I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.5.7.





Enables revalidation of expired cache items using conditional requests with
the C<If-Modified-Since> and C<If-None-Match>
header fields.







=head2 scgi_cache_use_stale


B<syntax:> scgi_cache_use_stale I<
    C<error> E<verbar>
    C<timeout> E<verbar>
    C<invalid_header> E<verbar>
    C<updating> E<verbar>
    C<http_500> E<verbar>
    C<http_503> E<verbar>
    C<http_403> E<verbar>
    C<http_404> E<verbar>
    C<http_429> E<verbar>
    C<off>
    ...>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Determines in which cases a stale cached response can be used
when an error occurs during communication with the SCGI server.
The directive’s parameters match the parameters of the
L</scgi_next_upstream> directive.





The C<error> parameter also permits
using a stale cached response if an SCGI server to process a request
cannot be selected.





Additionally, the C<updating> parameter permits
using a stale cached response if it is currently being updated.
This allows minimizing the number of accesses to SCGI servers
when updating cached data.





Using a stale cached response
can also be enabled directly in the response header
for a specified number of seconds after the response became stale (1.11.10).
This has lower priority than using the directive parameters.

=over




=item *

The
“L<stale-while-revalidate|https://datatracker.ietf.org/doc/html/rfc5861#section-3>”
extension of the C<Cache-Control> header field permits
using a stale cached response if it is currently being updated.



=item *

The
“L<stale-if-error|https://datatracker.ietf.org/doc/html/rfc5861#section-4>”
extension of the C<Cache-Control> header field permits
using a stale cached response in case of an error.



=back







To minimize the number of accesses to SCGI servers when
populating a new cache element, the L</scgi_cache_lock>
directive can be used.







=head2 scgi_cache_valid


B<syntax:> scgi_cache_valid I<[I<C<code>> ...] I<C<time>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets caching time for different response codes.
For example, the following directives

    
    scgi_cache_valid 200 302 10m;
    scgi_cache_valid 404      1m;


set 10 minutes of caching for responses with codes 200 and 302
and 1 minute for responses with code 404.





If only caching I<C<time>> is specified

    
    scgi_cache_valid 5m;


then only 200, 301, and 302 responses are cached.





In addition, the C<any> parameter can be specified
to cache any responses:

    
    scgi_cache_valid 200 302 10m;
    scgi_cache_valid 301      1h;
    scgi_cache_valid any      1m;







Parameters of caching can also be set directly
in the response header.
This has higher priority than setting of caching time using the directive.

=over




=item *

The C<X-Accel-Expires> header field sets caching time of a
response in seconds.
The zero value disables caching for a response.
If the value starts with the C<@> prefix, it sets an absolute
time in seconds since Epoch, up to which the response may be cached.



=item *

If the header does not include the C<X-Accel-Expires> field,
parameters of caching may be set in the header fields
C<Expires> or C<Cache-Control>.



=item *

If the header includes the C<Set-Cookie> field, such a
response will not be cached.



=item *

If the header includes the C<Vary> field
with the special value “C<*>”, such a
response will not be cached (1.7.7).
If the header includes the C<Vary> field
with another value, such a response will be cached
taking into account the corresponding request header fields (1.7.7).



=back


Processing of one or more of these response header fields can be disabled
using the L</scgi_ignore_headers> directive.







=head2 scgi_connect_timeout


B<syntax:> scgi_connect_timeout I<I<C<time>>>


B<default:> I<60s>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines a timeout for establishing a connection with an SCGI server.
It should be noted that this timeout cannot usually exceed 75 seconds.







=head2 scgi_force_ranges


B<syntax:> scgi_force_ranges I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.7.7.





Enables byte-range support
for both cached and uncached responses from the SCGI server
regardless of the C<Accept-Ranges> field in these responses.







=head2 scgi_hide_header


B<syntax:> scgi_hide_header I<I<C<field>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





By default,
nginx does not pass the header fields C<Status> and
C<X-Accel-...> from the response of an SCGI
server to a client.
The C<scgi_hide_header> directive sets additional fields
that will not be passed.
If, on the contrary, the passing of fields needs to be permitted,
the L</scgi_pass_header> directive can be used.







=head2 scgi_ignore_client_abort


B<syntax:> scgi_ignore_client_abort I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Determines whether the connection with an SCGI server should be
closed when a client closes the connection without waiting
for a response.







=head2 scgi_ignore_headers


B<syntax:> scgi_ignore_headers I<I<C<field>> ...>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Disables processing of certain response header fields from the SCGI server.
The following fields can be ignored: C<X-Accel-Redirect>,
C<X-Accel-Expires>, C<X-Accel-Limit-Rate> (1.1.6),
C<X-Accel-Buffering> (1.1.6),
C<X-Accel-Charset> (1.1.6), C<Expires>,
C<Cache-Control>, C<Set-Cookie> (0.8.44),
and C<Vary> (1.7.7).





If not disabled, processing of these header fields has the following
effect:

=over




=item *

C<X-Accel-Expires>, C<Expires>,
C<Cache-Control>, C<Set-Cookie>,
and C<Vary>
set the parameters of response caching;



=item *

C<X-Accel-Redirect> performs an
L<internal
redirect|ngx_http_core_module> to the specified URI;



=item *

C<X-Accel-Limit-Rate> sets the
L<rate
limit|ngx_http_core_module> for transmission of a response to a client;



=item *

C<X-Accel-Buffering> enables or disables
buffering of a response;



=item *

C<X-Accel-Charset> sets the desired
L<ngx_http_charset_module>
of a response.



=back









=head2 scgi_intercept_errors


B<syntax:> scgi_intercept_errors I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Determines whether an SCGI server responses with codes greater than or equal
to 300 should be passed to a client
or be intercepted and redirected to nginx for processing
with the L<ngx_http_core_module> directive.







=head2 scgi_limit_rate


B<syntax:> scgi_limit_rate I<I<C<rate>>>


B<default:> I<0>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.7.7.





Limits the speed of reading the response from the SCGI server.
The I<C<rate>> is specified in bytes per second.
The zero value disables rate limiting.
The limit is set per a request, and so if nginx simultaneously opens
two connections to the SCGI server,
the overall rate will be twice as much as the specified limit.
The limitation works only if
buffering of responses from the SCGI
server is enabled.
Parameter value can contain variables (1.27.0).







=head2 scgi_max_temp_file_size


B<syntax:> scgi_max_temp_file_size I<I<C<size>>>


B<default:> I<1024m>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





When buffering of responses from the SCGI
server is enabled, and the whole response does not fit into the buffers
set by the L</scgi_buffer_size> and L</scgi_buffers>
directives, a part of the response can be saved to a temporary file.
This directive sets the maximum I<C<size>> of the temporary file.
The size of data written to the temporary file at a time is set
by the L</scgi_temp_file_write_size> directive.





The zero value disables buffering of responses to temporary files.






B<NOTE>

This restriction does not apply to responses
that will be cached
or stored on disk.








=head2 scgi_next_upstream


B<syntax:> scgi_next_upstream I<
    C<error> E<verbar>
    C<timeout> E<verbar>
    C<invalid_header> E<verbar>
    C<http_500> E<verbar>
    C<http_503> E<verbar>
    C<http_403> E<verbar>
    C<http_404> E<verbar>
    C<http_429> E<verbar>
    C<non_idempotent> E<verbar>
    C<off>
    ...>


B<default:> I<error timeout>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Specifies in which cases a request should be passed to the next server:

=over



=item C<error>



an error occurred while establishing a connection with the
server, passing a request to it, or reading the response header;


=item C<timeout>



a timeout has occurred while establishing a connection with the
server, passing a request to it, or reading the response header;


=item C<invalid_header>



a server returned an empty or invalid response;


=item C<http_500>



a server returned a response with the code 500;


=item C<http_503>



a server returned a response with the code 503;


=item C<http_403>



a server returned a response with the code 403;


=item C<http_404>



a server returned a response with the code 404;


=item C<http_429>



a server returned a response with the code 429 (1.11.13);


=item C<non_idempotent>



normally, requests with a
L<non-idempotent|https://datatracker.ietf.org/doc/html/rfc7231#section-4.2.2>
method
(C<POST>, C<LOCK>, C<PATCH>)
are not passed to the next server
if a request has been sent to an upstream server (1.9.13);
enabling this option explicitly allows retrying such requests;



=item C<off>



disables passing a request to the next server.



=back







One should bear in mind that passing a request to the next server is
only possible if nothing has been sent to a client yet.
That is, if an error or timeout occurs in the middle of the
transferring of a response, fixing this is impossible.





The directive also defines what is considered an
L<unsuccessful
attempt|ngx_http_upstream_module> of communication with a server.
The cases of C<error>, C<timeout> and
C<invalid_header> are always considered unsuccessful attempts,
even if they are not specified in the directive.
The cases of C<http_500>, C<http_503>,
and C<http_429> are
considered unsuccessful attempts only if they are specified in the directive.
The cases of C<http_403> and C<http_404>
are never considered unsuccessful attempts.





Passing a request to the next server can be limited by
the number of tries
and by time.







=head2 scgi_next_upstream_timeout


B<syntax:> scgi_next_upstream_timeout I<I<C<time>>>


B<default:> I<0>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.7.5.





Limits the time during which a request can be passed to the
next server.
The C<0> value turns off this limitation.







=head2 scgi_next_upstream_tries


B<syntax:> scgi_next_upstream_tries I<I<C<number>>>


B<default:> I<0>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.7.5.





Limits the number of possible tries for passing a request to the
next server.
The C<0> value turns off this limitation.







=head2 scgi_no_cache


B<syntax:> scgi_no_cache I<I<C<string>> ...>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines conditions under which the response will not be saved to a cache.
If at least one value of the string parameters is not empty and is not
equal to “0” then the response will not be saved:

    
    scgi_no_cache $cookie_nocache $arg_nocache$arg_comment;
    scgi_no_cache $http_pragma    $http_authorization;


Can be used along with the L</scgi_cache_bypass> directive.







=head2 scgi_param


B<syntax:> scgi_param I<
    I<C<parameter>> I<C<value>>
    [C<if_not_empty>]>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets a I<C<parameter>> that should be passed to the SCGI server.
The I<C<value>> can contain text, variables, and their combination.
These directives are inherited from the previous configuration level
if and only if there are no C<scgi_param> directives
defined on the current level.





Standard
L<CGI
environment variables|https://datatracker.ietf.org/doc/html/rfc3875#section-4.1>
should be provided as SCGI headers, see the F<scgi_params> file
provided in the distribution:

    
    location / {
        include scgi_params;
        ...
    }







If the directive is specified with C<if_not_empty> (1.1.11) then
such a parameter will be passed to the server only if its value is not empty:

    
    scgi_param HTTPS $https if_not_empty;









=head2 scgi_pass


B<syntax:> scgi_pass I<I<C<address>>>



B<context:> I<location>


B<context:> I<if in location>





Sets the address of an SCGI server.
The address can be specified as a domain name or IP address,
and a port:

    
    scgi_pass localhost:9000;


or as a UNIX-domain socket path:

    
    scgi_pass unix:/tmp/scgi.socket;







If a domain name resolves to several addresses, all of them will be
used in a round-robin fashion.
In addition, an address can be specified as a
L<server group|ngx_http_upstream_module>.





Parameter value can contain variables.
In this case, if an address is specified as a domain name,
the name is searched among the described
L<server groups|ngx_http_upstream_module>,
and, if not found, is determined using a
L<ngx_http_core_module>.







=head2 scgi_pass_header


B<syntax:> scgi_pass_header I<I<C<field>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Permits passing otherwise disabled header
fields from an SCGI server to a client.







=head2 scgi_pass_request_body


B<syntax:> scgi_pass_request_body I<C<on> E<verbar> C<off>>


B<default:> I<on>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Indicates whether the original request body is passed
to the SCGI server.
See also the L</scgi_pass_request_headers> directive.







=head2 scgi_pass_request_headers


B<syntax:> scgi_pass_request_headers I<C<on> E<verbar> C<off>>


B<default:> I<on>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Indicates whether the header fields of the original request are passed
to the SCGI server.
See also the L</scgi_pass_request_body> directive.







=head2 scgi_read_timeout


B<syntax:> scgi_read_timeout I<I<C<time>>>


B<default:> I<60s>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines a timeout for reading a response from the SCGI server.
The timeout is set only between two successive read operations,
not for the transmission of the whole response.
If the SCGI server does not transmit anything within this time,
the connection is closed.







=head2 scgi_request_buffering


B<syntax:> scgi_request_buffering I<C<on> E<verbar> C<off>>


B<default:> I<on>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.7.11.





Enables or disables buffering of a client request body.





When buffering is enabled, the entire request body is
L<read|ngx_http_core_module>
from the client before sending the request to an SCGI server.





When buffering is disabled, the request body is sent to the SCGI server
immediately as it is received.
In this case, the request cannot be passed to the
next server
if nginx already started sending the request body.





When HTTPE<sol>1.1 chunked transfer encoding is used
to send the original request body,
the request body will be buffered regardless of the directive value.







=head2 scgi_send_timeout


B<syntax:> scgi_send_timeout I<I<C<time>>>


B<default:> I<60s>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets a timeout for transmitting a request to the SCGI server.
The timeout is set only between two successive write operations,
not for the transmission of the whole request.
If the SCGI server does not receive anything within this time,
the connection is closed.







=head2 scgi_socket_keepalive


B<syntax:> scgi_socket_keepalive I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.15.6.





Configures the “TCP keepalive” behavior
for outgoing connections to an SCGI server.
By default, the operating system’s settings are in effect for the socket.
If the directive is set to the value “C<on>”, the
C<SO_KEEPALIVE> socket option is turned on for the socket.







=head2 scgi_store


B<syntax:> scgi_store I<
    C<on> E<verbar>
    C<off> E<verbar>
    I<C<string>>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Enables saving of files to a disk.
The C<on> parameter saves files with paths
corresponding to the directives
L<ngx_http_core_module> or
L<ngx_http_core_module>.
The C<off> parameter disables saving of files.
In addition, the file name can be set explicitly using the
I<C<string>> with variables:

    
    scgi_store /data/www$original_uri;







The modification time of files is set according to the received
C<Last-Modified> response header field.
The response is first written to a temporary file,
and then the file is renamed.
Starting from version 0.8.9, temporary files and the persistent store
can be put on different file systems.
However, be aware that in this case a file is copied
across two file systems instead of the cheap renaming operation.
It is thus recommended that for any given location both saved files and a
directory holding temporary files, set by the L</scgi_temp_path>
directive, are put on the same file system.





This directive can be used to create local copies of static unchangeable
files, e.g.:

    
    location /images/ {
        root              /data/www;
        error_page        404 = /fetch$uri;
    }
    
    location /fetch/ {
        internal;
    
        scgi_pass         backend:9000;
        ...
    
        scgi_store        on;
        scgi_store_access user:rw group:rw all:r;
        scgi_temp_path    /data/temp;
    
        alias             /data/www/;
    }









=head2 scgi_store_access


B<syntax:> scgi_store_access I<I<C<users>>:I<C<permissions>> ...>


B<default:> I<user:rw>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets access permissions for newly created files and directories, e.g.:

    
    scgi_store_access user:rw group:rw all:r;







If any C<group> or C<all> access permissions
are specified then C<user> permissions may be omitted:

    
    scgi_store_access group:rw all:r;









=head2 scgi_temp_file_write_size


B<syntax:> scgi_temp_file_write_size I<I<C<size>>>


B<default:> I<8kE<verbar>16k>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Limits the I<C<size>> of data written to a temporary file
at a time, when buffering of responses from the SCGI server
to temporary files is enabled.
By default, I<C<size>> is limited by two buffers set by the
L</scgi_buffer_size> and L</scgi_buffers> directives.
The maximum size of a temporary file is set by the
L</scgi_max_temp_file_size> directive.







=head2 scgi_temp_path


B<syntax:> scgi_temp_path I<
    I<C<path>>
    [I<C<level1>>
    [I<C<level2>>
    [I<C<level3>>]]]>


B<default:> I<scgi_temp>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines a directory for storing temporary files
with data received from SCGI servers.
Up to three-level subdirectory hierarchy can be used underneath the specified
directory.
For example, in the following configuration

    
    scgi_temp_path /spool/nginx/scgi_temp 1 2;


a temporary file might look like this:

    
    /spool/nginx/scgi_temp/<emphasis>7</emphasis>/<emphasis>45</emphasis>/00000123<emphasis>457</emphasis>







See also the C<use_temp_path> parameter of the
L</scgi_cache_path> directive.







