统一使用新的`lib/redisclient.lua`工具包。

具体用法参考[TestRedisClientGet.lua](../src/controller/TestRedisClientGet.lua)、[TestRedisClientSet.lua](../src/controller/TestRedisClientSet.lua)、[TestRedisClientMisc.lua](../src/controller/TestRedisClientMisc.lua)。

启动服务后打开[rule-engine.http](../test/rule-engine.http)访问相关`http`接口即可。

# 读取

```lua

local redis = require("lib/redisclient")

-- 读取单个键
local value, geterr = redis.get('key')

-- 读取多个键（数组形式）
local values, mgeterr = redis.mget({ 'key1', 'key2', 'key3' })

```

# 读取 zstd 压缩数据（自动解压缩）

```lua

local redis = require("lib/redisclient")

-- 读取单个键
local value, geterr = redis.zstd_get('key')

-- 读取多个键（数组形式）
local values, mgeterr = redis.zstd_mget({ 'key1', 'key2', 'key3' })

```

# 写入

```lua

local redis = require("lib/redisclient")

-- 写入单个键（不有效期）
local ok, err = redis.set('key', 'value')

-- 写入单个键（同时设置有效期）
local set_ok, seterr = redis.set('key', 'value', 100)

-- 写入多个键（数组形式）
local mset_ok, mseterr = redis.mset({ { key = 'key1', value = 'value1', expire_seconds = 100 }, { key = 'key2', value = 'value2' } })

```

# 写入 zstd 压缩数据（自动压缩）

```lua

local redis = require("lib/redisclient")

-- 写入单个键（不有效期）
local ok, err = redis.zstd_set('key', 'value')

-- 写入单个键
local set_ok, seterr = redis.zstd_set('key', 'value', 100)

-- 写入多个键（数组形式）
local mset_ok, mseterr = redis.zstd_mset({ { key = 'key1', value = 'value1', expire_seconds = 100 }, { key = 'key2', value = 'value2' } })

```

# 删除

```lua

local redis = require("lib/redisclient")

-- 删除单个键
local ok, err = redis.del('key')

-- 删除多个键（数组形式）
local _, dels_err = redis.del({ 'key1', 'key2', 'key3' })

```

# 获取指定模式的 key 列表

```lua

local redis = require("lib/redisclient")

-- 获取指定模式的 key 列表
local keys, err = redis.keys('pattern:*')

```
