=encoding utf-8


=head1 Name

lua-cjson - Fast JSON encoding/parsing


=head1 Description

This fork of L<mpxE<sol>lua-cjson|https://github.com/mpx/lua-cjson> is included in
the L<OpenResty|https://openresty.org/> bundle and includes a few bugfixes and
improvements, especially to facilitate the encoding of empty tables as JSON Arrays.

Please refer to the L<lua-cjson documentation|https://kyne.au/~mark/software/lua-cjson.php>
for standard usage, this README only provides informations regarding this fork's additions.

See L<`mpxE<sol>master..openrestyE<sol>master`|https://github.com/mpx/lua-cjson/compare/master...openresty:master>
for the complete history of changes.




=head1 Additions


=head2 encode_empty_table_as_object

B<syntax:> C<cjson.encode_empty_table_as_object(true|false|"on"|"off")>

Change the default behavior when encoding an empty Lua table.

By default, empty Lua tables are encoded as empty JSON Objects (C<{}>). If this is set to false,
empty Lua tables will be encoded as empty JSON Arrays instead (C<[]>).

This method either accepts a boolean or a string (C<"on">, C<"off">).




=head2 empty_array

B<syntax:> C<cjson.empty_array>

A lightuserdata, similar to C<cjson.null>, which will be encoded as an empty JSON Array by
C<cjson.encode()>.

For example, since C<encode_empty_table_as_object> is C<true> by default:


    local cjson = require "cjson"
    
    local json = cjson.encode({
        foo = "bar",
        some_object = {},
        some_array = cjson.empty_array
    })

This will generate:


    {
        "foo": "bar",
        "some_object": {},
        "some_array": []
    }




=head2 array_mt

B<syntax:> C<setmetatable({}, cjson.array_mt)>

When lua-cjson encodes a table with this metatable, it will systematically
encode it as a JSON Array. The resulting, encoded Array will contain the array
part of the table, and will be of the same length as the C<#> operator on that
table. Holes in the table will be encoded with the C<null> JSON value.

Example:


    local t = { "hello", "world" }
    setmetatable(t, cjson.array_mt)
    cjson.encode(t) -- ["hello","world"]

Or:


    local t = {}
    t[1] = "one"
    t[2] = "two"
    t[4] = "three"
    t.foo = "bar"
    setmetatable(t, cjson.array_mt)
    cjson.encode(t) -- ["one","two",null,"three"]

This value was introduced in the C<2.1.0.5> release of this module.




=head2 empty_array_mt

B<syntax:> C<setmetatable({}, cjson.empty_array_mt)>

A metatable which can "tag" a table as a JSON Array in case it is empty (that is, if the
table has no elements, C<cjson.encode()> will encode it as an empty JSON Array).

Instead of:


    local function serialize(arr)
        if #arr < 1 then
            arr = cjson.empty_array
        end
    
        return cjson.encode({some_array = arr})
    end

This is more concise:


    local function serialize(arr)
        setmetatable(arr, cjson.empty_array_mt)
    
        return cjson.encode({some_array = arr})
    end

Both will generate:


    {
        "some_array": []
    }




=head2 encode_number_precision

B<syntax:> C<cjson.encode_number_precision(precision)>

This fork allows encoding of numbers with a C<precision> up to 16 decimals (vs. 14 in mpx/lua-cjson).




=head2 encode_escape_forward_slash

B<syntax:> C<cjson.encode_escape_forward_slash(enabled)>

B<default:> true

If enabled, forward slash '/' will be encoded as '\\/'.

If disabled, forward slash '/' will be encoded as '/' (no escape is applied).




=head2 encode_skip_unsupported_value_types

B<syntax:> C<cjson.encode_skip_unsupported_value_types(enabled)>

B<default:> false

If enabled, cjson will not throw exception when there are unsupported types
in the Lua table.

For example:


    local ffi = require "ffi"
    local cjson = require "cjson"
    cjson.encode_skip_unsupported_value_types(true)
    local t = {key = "val"}
    
    t.cdata = ffi.new("char[?]", 100)
    print(cjson.encode(t))

This will generate:


    {"key":"val"}




=head2 decode_array_with_array_mt

B<syntax:> C<cjson.decode_array_with_array_mt(enabled)>

B<default:> false

If enabled, JSON Arrays decoded by C<cjson.decode> will result in Lua
tables with the L<`array_mt`> metatable. This can ensure a 1-to-1
relationship between arrays upon multiple encoding/decoding of your
JSON data with this module.

If disabled, JSON Arrays will be decoded to plain Lua tables, without
the C<array_mt> metatable.

The C<enabled> argument is a boolean.

Example:


    local cjson = require "cjson"
    
    -- default behavior
    local my_json = [[{"my_array":[]}]]
    local t = cjson.decode(my_json)
    cjson.encode(t) -- {"my_array":{}} back to an object
    
    -- now, if this behavior is enabled
    cjson.decode_array_with_array_mt(true)
    
    local my_json = [[{"my_array":[]}]]
    local t = cjson.decode(my_json)
    cjson.encode(t) -- {"my_array":[]} properly re-encoded as an array


