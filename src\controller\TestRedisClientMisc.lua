local rc = require('lib/redisclient')
local json = require('cjson')

--[[
接口请求体格式为：
{
    "method": "keys|del", // 执行的操作，二选一
    "pattern": "string", // keys 操作的 pattern
    "keys": ["string", "string"] // 多个 key，针对 del 操作
}
]]

local function get_keys(pattern)
    local keys, err = rc.keys(pattern)
    if err then
        ngx.say('{"code":1,"msg":"keys 操作失败：' .. err .. '"}')
        return
    end
    ngx.say(json.encode(keys))
end

local function del(keys)
    local ok, err = rc.del(keys)
    if err ~= nil then
        ngx.say('{"code":1,"msg":"del 操作失败：' .. (err or "未知错误") .. '"}')
        return
    end
    ngx.say('{"code":0,"msg":"ok"}')
end

local function main()
    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    if data == nil then
        ngx.say('{"code":1,"msg":"请求体不能为空"}')
        return
    end

    local req = json.decode(data)

    if req.method == "keys" then
        get_keys(req.pattern)
    elseif req.method == 'del' then
        del(req.keys)
    else
        ngx.say('{"code":1,"msg":"不支持的操作"}')
    end
end

main()
