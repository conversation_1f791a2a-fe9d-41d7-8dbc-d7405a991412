=encoding utf-8

=head1 NAME

ngx_stream_proxy_protocol_vendor_module - Module ngx_stream_proxy_protocol_vendor_module




=head1



The C<ngx_stream_proxy_protocol_vendor_module> module (1.23.3)
allows obtaining additional information about a connection in
cloud platforms from application-specific TLVs of the
L<PROXY
protocol|http://www.haproxy.org/download/1.8/doc/proxy-protocol.txt>
header.





Supported cloud platforms:

=over




=item *

Amazon Web Services



=item *

Google Cloud Platform



=item *

Microsoft Azure



=back







The PROXY protocol must be previously enabled by setting the
C<proxy_protocol> parameter
in the L<ngx_stream_core_module> directive.






B<NOTE>

This module is available as part of our
commercial subscription.





=head1 Example Configuration




    
    server {
        listen 12345 proxy_protocol;
        return $proxy_protocol_tlv_gcp_conn_id;
    }






=head1 Embedded Variables




=over



=item C<$proxy_protocol_tlv_aws_vpce_id>




TLV value from the PROXY Protocol header representing the
L<ID
of AWS VPC endpoint|https://docs.aws.amazon.com/elasticloadbalancing/latest/network/load-balancer-target-groups.html#proxy-protocol>



=item C<$proxy_protocol_tlv_azure_pel_id>




TLV value from the PROXY Protocol header representing the
L<LinkID
of Azure private endpoint|https://learn.microsoft.com/en-us/azure/private-link/private-link-service-overview#getting-connection-information-using-tcp-proxy-v2>



=item C<$proxy_protocol_tlv_gcp_conn_id>




TLV value from the PROXY Protocol header representing
L<Google Cloud PSC
connection ID|https://cloud.google.com/vpc/docs/configure-private-service-connect-producer#proxy-protocol>




=back






