local rc = require('lib/redisclient')
local json = require('cjson')

--[[
-- 接口请求体格式为：
{
    "method": "set|mset|zstd_set|zstd_mset", // 执行的操作，四选一
    "single": { // 单个对象，针对 set 和 zstd_set 操作
        "key": "string",
        "value": "string",
        "expire_seconds": 0
    },
    "multiple": [ // 多个对象，针对 mset 和 zstd_mset 操作
        {
            "key": "string",
            "value": "string",
            "expire_seconds": 0
        },
        {
            "key": "string",
            "value": "string"
        }
    ]
}
-- 其中 key 和 value 必须不为空，而 expire_seconds 可以为空
]]

local function set(def)
    local _, err = rc.set(def.key, def.value, def.expire_seconds)
    if err then
        ngx.say('{"code":1,"msg":"set 操作失败：' .. err .. '"}')
        return
    end
    ngx.say('{"code":0,"msg":"ok"}')
end

local function mset(array)
    local _, err = rc.mset(array)
    if err then
        ngx.say('{"code":1,"msg":"mset 操作失败：' .. err .. '"}')
        return
    end
    ngx.say('{"code":0,"msg":"ok"}')
end

local function zstd_set(def)
    local _, err = rc.zstd_set(def.key, def.value, def.expire_seconds)
    if err then
        ngx.say('{"code":1,"msg":"zstd_set 操作失败：' .. err .. '"}')
        return
    end
    ngx.say('{"code":0,"msg":"ok"}')
end

local function zstd_mset(array)
    local _, err = rc.zstd_mset(array)
    if err then
        ngx.say('{"code":1,"msg":"zstd_mset 操作失败：' .. err .. '"}')
        return
    end
    ngx.say('{"code":0,"msg":"ok"}')
end

local function main()
    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    if data == nil then
        ngx.say('{"code":1,"msg":"请求体不能为空"}')
        return
    end

    local req = json.decode(data)

    if req.method == "set" then
        set(req.single)
    elseif req.method == "mset" then
        mset(req.multiple)
    elseif req.method == "zstd_set" then
        zstd_set(req.single)
    elseif req.method == "zstd_mset" then
        zstd_mset(req.multiple)
    else
        ngx.say('{"code":1,"msg":"不支持的操作"}')
    end
end

main()
