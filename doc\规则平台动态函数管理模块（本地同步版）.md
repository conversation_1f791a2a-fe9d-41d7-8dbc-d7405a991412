# 规则平台动态函数管理模块（本地同步版）

## 1. 概述

本文档是规则平台技术方案的重要组成部分，详细描述了规则引擎中的动态函数管理模块设计方案。该模块作为规则平台的核心功能组件，主要负责在OpenResty多worker环境下实现函数的动态加载、更新和同步管理。主要特性包括：

- 函数的动态注册与热更新
- 多worker进程间函数状态同步
- 基于Redis的分布式函数管理
- 支持K8s多Pod环境下的函数一致性
- 函数按需更新与版本控制
- 本地文件缓存提升性能

## 2. 系统架构

### 2.1 核心组件

```mermaid
graph TD
    A[规则管理服务<br/>SpringBoot] -->|存储| B[MySQL]
    A -->|同步| C[Redis集群]
    C -->|定时同步| D[OpenResty Pod1]
    C -->|定时同步| E[OpenResty Pod2]
    C -->|定时同步| F[OpenResty PodN]
    
    subgraph Pod1
        D -->|写入| G[本地目录]
        G -->|require| H[Worker1]
        G -->|require| I[Worker2]
        G -->|require| J[WorkerN]
    end
    
    subgraph Pod2
        E -->|写入| K[本地目录]
        K -->|require| L[Worker1]
        K -->|require| M[Worker2]
        K -->|require| N[WorkerN]
    end
    
    subgraph PodN
        F -->|写入| O[本地目录]
        O -->|require| P[Worker1]
        O -->|require| Q[Worker2]
        O -->|require| R[WorkerN]
    end
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bfb,stroke:#333,stroke-width:2px
    style D fill:#fbb,stroke:#333,stroke-width:2px
    style E fill:#fbb,stroke:#333,stroke-width:2px
    style F fill:#fbb,stroke:#333,stroke-width:2px
```

1. **规则管理服务（SpringBoot）**
   - 负责函数的注册和更新
   - 管理函数的生命周期
   - 提供RESTful API接口
   - 管理MySQL和Redis数据同步

2. **数据存储**
   - MySQL：主数据存储
   - Redis：多服务间数据共享缓存
   - 本地文件：函数脚本缓存

3. **本地同步器**
   - 定时从Redis同步函数到本地
   - 管理本地文件版本
   - 处理文件写入和更新

4. **更新检查机制**
   - 基于定时器检查更新状态
   - 通过Redis实现跨Pod同步
   - 本地文件变更检测

### 2.2 数据流

```
[规则管理服务] -> [MySQL存储] -> [Redis同步] -> [定时同步] -> [本地文件] -> [require加载]
```

## 3. 详细设计

### 3.1 函数命名规范

- 动态函数统一使用前缀：`dyn_`
- 示例：`dyn_user_func`, `dyn_order_func`

### 3.2 规则管理服务实现

#### 3.2.1 核心功能

1. **函数管理接口**
   - **注册函数**
     - 接口路径：`POST /api/functions/register`
     - 请求参数：
       - 函数名称（必须以`dyn_`开头）
       - 函数代码
     - 功能说明：
       - 验证函数名格式
       - 将函数代码存储到MySQL
       - 同步到Redis缓存
       - 更新函数最后修改时间
     - 返回结果：
       - 成功：HTTP 200
       - 失败：HTTP 400（参数错误）或 HTTP 500（服务器错误）

   - **查询函数**
     - 接口路径：`GET /api/functions/{name}`
     - 请求参数：
       - 函数名称
     - 功能说明：
       - 优先从Redis获取函数代码
       - Redis未命中则从MySQL获取并更新Redis
     - 返回结果：
       - 成功：HTTP 200，返回函数代码
       - 失败：HTTP 404（函数不存在）或 HTTP 500（服务器错误）

2. **Redis存储结构**
   - **函数代码存储**
     ```java
     // Redis Hash结构
     // Key: dynamic_functions
     // Field: 函数名称（如：dyn_user_func）
     // Value: 函数代码（Lua代码字符串）
     // 示例：
     // HSET dynamic_functions dyn_user_func "local M = {} function M.getUser(id) return {id = id} end return M"
     ```
     - 特点：
       - 使用Hash结构存储所有函数代码
       - 函数名作为field，便于快速查找
       - 函数代码作为value，直接存储Lua代码
       - 不设置过期时间，保持持久化

   - **更新信息存储**
     ```java
     // Redis Hash结构
     // Key: function_updates
     // Field: 函数名称（如：dyn_user_func）
     // Value: JSON格式的更新信息
     // 示例：
     // {
     //   "function": "dyn_user_func",
     //   "version": 1,
     //   "timestamp": 1234567890,
     //   "status": 1
     // }
     ```
     - 特点：
       - 使用Hash结构存储所有函数的更新信息
       - 包含版本号和更新时间戳
       - 用于函数更新检查和同步

### 3.3 本地同步器实现

```lua
-- localSync.lua
local ngx = ngx
local redis = require "lib.redisclient"
local cjson = require "cjson"
local lfs = require "lfs"
local lrucache = require "resty.lrucache"

local M = {}

-- 配置
local CONFIG = {
    base_dir = (os.getenv("TEMP") or "C:\\tmp") .. package.config:sub(1,1) .. "dyn_funcs" .. package.config:sub(1,1),
    sync_interval = 300,  -- 5分钟同步一次
    cache_size = 10000,    -- LRU缓存大小
}

-- 初始化LRU缓存
-- 注意：这个cache是每个worker进程独立的
local function init_cache()
    local cache, err = lrucache.new(CONFIG.cache_size)
    if not cache then
        return nil, "failed to create cache: " .. (err or "unknown")
    end
    return cache
end

-- 创建缓存实例
local md5_cache = init_cache()

-- 计算字符串的MD5
local function md5(str)
    local ngx_md5 = ngx.md5
    return ngx_md5(str)
end

-- 确保目录存在
local function ensure_dir(path)
    local attr = lfs.attributes(path)
    if not attr then
        lfs.mkdir(path)
    end
end

-- 从Redis拉取所有函数
local function fetch_all_funcs()
    -- 使用 redisclient 的 hgetall 方法获取所有函数
    local funcs = {}
    local res, err = redis.hgetall("dynamic_functions")
    if err then
        ngx.log(ngx.ERR, "获取函数列表失败: ", err)
        return {}
    end
    
    -- 解析返回的结果
    if type(res) == "table" then
        for k, v in pairs(res) do
            funcs[k] = v
        end
    end
    
    return funcs
end

-- 写入本地文件（原子写入）
local function write_func_file(name, code)
    local file_path = CONFIG.base_dir .. name .. ".lua"
    local tmp_path = file_path .. ".tmp"
    local f = io.open(tmp_path, "w+")
    if not f then
        ngx.log(ngx.ERR, "open file failed: ", tmp_path)
        return false
    end
    f:write(code)
    f:close()
    
    -- 更新成功后，更新MD5缓存
    local code_md5 = md5(code)
    if md5_cache then
        md5_cache:set(file_path, code_md5)  -- 不设置TTL
    end
    
    os.rename(tmp_path, file_path)  -- 原子替换
    return true
end

-- 检查文件内容是否变更（高效版本）
local function is_file_changed(file_path, new_content)
    if not md5_cache then
        md5_cache = init_cache()  -- 尝试重新初始化缓存
        if not md5_cache then
            ngx.log(ngx.ERR, "failed to initialize cache")
            return true  -- 缓存不可用时，认为文件已变更
        end
    end

    -- 1. 首先比较文件大小
    local attr = lfs.attributes(file_path)
    if not attr then 
        return true 
    end
    
    -- 2. 比较新内容的MD5与缓存的MD5
    local new_md5 = md5(new_content)
    local cached_md5 = md5_cache:get(file_path)
    
    if cached_md5 and cached_md5 == new_md5 then
        return false  -- 内容未变化
    end
    
    -- 3. 如果缓存未命中，读取文件计算MD5
    local f = io.open(file_path, "r")
    if not f then 
        return true 
    end
    local old_content = f:read("*a")
    f:close()
    
    local old_md5 = md5(old_content)
    md5_cache:set(file_path, old_md5)  -- 不设置TTL
    
    return old_md5 ~= new_md5
end

-- 定时同步任务
local function sync_funcs()
    ensure_dir(CONFIG.base_dir)
    local funcs = fetch_all_funcs()
    
    for name, code in pairs(funcs) do
        local file_path = CONFIG.base_dir .. name .. ".lua"
        if is_file_changed(file_path, code) then
            if write_func_file(name, code) then
                package.loaded[name] = nil  -- 只在内容变更时清理缓存
                ngx.log(ngx.INFO, "函数已更新: ", name)
            end
        end
    end
end

function M.install()
    -- 确保目录存在
    ensure_dir(CONFIG.base_dir)
    
    -- 设置package.path
    package.path = CONFIG.base_dir .. "?.lua;" .. package.path
    
    -- 初始化LRU缓存
    if not md5_cache then
        local err
        md5_cache, err = init_cache()
        if not md5_cache then
            ngx.log(ngx.ERR, "failed to initialize cache: ", err)
        end
    end
    
    -- 启动定时同步
    local ok, err = ngx.timer.every(CONFIG.sync_interval, sync_funcs)
    if not ok then
        ngx.log(ngx.ERR, "failed to start sync_funcs timer: ", err)
    end
end

return M
```

### 3.4 初始化配置

```nginx
http {
    lua_package_path "/path/to/lua/lib/?.lua;;";  # 确保能找到 lua-resty-lrucache

    init_worker_by_lua_block {
        require("localSync").install()
    }
}
```

## 4. 性能考虑

1. **缓存策略**
   - 函数代码缓存在本地文件系统
   - Redis作为中间缓存
   - 定时检查更新状态
   - 只在内容变更时清理package.loaded

2. **数据同步**
   - MySQL主从复制
   - Redis集群部署
   - 本地文件原子写入
   - 合理的同步间隔

## 5. 注意事项

1. **数据一致性**
   - MySQL和Redis数据同步
   - 本地文件版本管理
   - 原子文件写入
   - 异常回滚机制

2. **高可用**
   - MySQL主从配置
   - Redis集群部署
   - 本地文件备份
   - 服务降级策略

## 6. 扩展性考虑

1. **函数版本管理**
   - 支持多版本共存
   - 版本回滚机制
   - 版本切换通知

2. **权限控制**
   - 函数注册权限控制
   - 更新权限控制
   - 数据访问权限控制 