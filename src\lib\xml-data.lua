-- XML数据访问模块
local redis = require "lib.redisclient"

local _M = {
    -- 模块版本
    _version = '0.1.0'
}

-- 获取固定的XML文本
-- @return xml_content XML内容
function _M.get_fixed_xml()
    return [[<Root>
	<Parts>
		<Part isGroup="False" type="ProductionParamModel" modelTypeId="1" productId="3FO3OG6YJEJP" prodCatId="688" name="平板门" textureId="3FO458RB7OAE" textureName="景入桑榆：ML635" roomId="24" id="3B485142-91F3-451E-AA81-704C417F5C08" parentId="" thickness="" W="50" D="250" H="2099" module="平板门">
			<Space X="-906.54724" Y="2182.7551" Z="0.0" CX="-881.54724" CY="1907.7551" CZ="1020.0" RX="0.0" RY="0.0" RZ="0.0" sizeX="50.0" sizeY="550.0" sizeZ="2040.0" boxSizeX="50.0" boxSizeY="550.0" boxSizeZ="2040.0"/>
			<Parameters>
				<Partmetr min="100.0" max="1940.0" displayName="按弹器" name="ATQ" value="0"/>
				<Partmetr min="" max="" displayName="偏移" name="offset" value="0,0,0"/>
			</Parameters>
			<ParamPlankPath/>
			<Part isGroup="False" type="ProductionParamModel" modelTypeId="1" productId="3FO3OEW68IED" prodCatId="713" name="18mm板" textureId="3FO3U6PVQWG5" textureName="杏子灰（优化）" roomId="24" id="1B787AA2-2C4A-472F-A8DE-F43B46B16680" parentId="3B485142-91F3-451E-AA81-704C417F5C08" thickness="" W="18" D="532" H="2040" module="门板">
				<Space X="32.0" Y="0.0" Z="0.0" CX="16.0" CY="9.0" CZ="0.0" RX="0.0" RY="0.0" RZ="0.0" sizeX="18.0" sizeY="532.0" sizeZ="2040.0" boxSizeX="18.0" boxSizeY="532.0" boxSizeZ="2040.0"/>
				<Parameters>
					<Partmetr min="" max="" displayName="铺贴方式" name="texturePavingStyle" value="0"/>
				</Parameters>
				<ParamPlankPath/>
			</Part>
		</Part>
	</Parts>	
</Root>]]
end

-- 根据缓存key获取XML内容
-- @param cache_key 缓存key
-- @return xml_content XML内容，如果不存在则返回nil
-- @return err 错误信息
function _M.get_xml_from_cache(cache_key)
    if not cache_key then
        return nil, "缓存key不能为空"
    end

    -- 从Redis获取XML数据
    local xml_content, err = redis.zstd_get(cache_key)

    if err then
        return nil, "获取XML缓存[" .. cache_key .. "]失败，" .. err
    end

    if not xml_content then
        return nil, "XML缓存[" .. cache_key .. "]不存在"
    end

    return xml_content, nil
end

-- 保存XML到缓存
-- @param cache_key 缓存key
-- @param xml_content XML内容
-- @return ok 是否保存成功
-- @return err 错误信息	
function _M.save_xml_to_cache(cache_key, xml_content)
    if not cache_key then
        return false, "缓存key不能为空"
    end	

    -- 保存XML到缓存
    local ok, err = redis.zstd_set(cache_key, xml_content)
    if not ok then
        return false, "保存XML缓存[" .. cache_key .. "]失败，" .. err
    end

    return true, nil
end



-- 模块版本信息
function _M.version()
    return _M._version
end

return _M 