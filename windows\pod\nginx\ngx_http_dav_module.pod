=encoding utf-8

=head1 NAME

ngx_http_dav_module - Module ngx_http_dav_module




=head1



The C<ngx_http_dav_module> module is intended for file
management automation via the WebDAV protocol.
The module processes HTTP and WebDAV
methods PUT, DELETE, MKCOL, COPY, and MOVE.





This module is not built by default, it should be enabled with the
C<--with-http_dav_module>
configuration parameter.






B<NOTE>

WebDAV clients that require additional WebDAV methods to operate
will not work with this module.





=head1 Example Configuration




    
    location / {
        root                  /data/www;
    
        client_body_temp_path /data/client_temp;
    
        dav_methods PUT DELETE MKCOL COPY MOVE;
    
        create_full_put_path  on;
        dav_access            group:rw  all:r;
    
        limit_except GET {
            allow 192.168.1.0/32;
            deny  all;
        }
    }






=head1 Directives

=head2 create_full_put_path


B<syntax:> create_full_put_path I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





The WebDAV specification only allows creating files in already
existing directories.
This directive allows creating all needed intermediate directories.







=head2 dav_access


B<syntax:> dav_access I<I<C<users>>:I<C<permissions>> ...>


B<default:> I<user:rw>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets access permissions for newly created files and directories, e.g.:

    
    dav_access user:rw group:rw all:r;







If any C<group> or C<all> access permissions
are specified then C<user> permissions may be omitted:

    
    dav_access group:rw all:r;









=head2 dav_methods


B<syntax:> dav_methods I<
    C<off> E<verbar> I<C<method>> ...>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Allows the specified HTTP and WebDAV methods.
The parameter C<off> denies all methods processed
by this module.
The following methods are supported:
C<PUT>, C<DELETE>, C<MKCOL>,
C<COPY>, and C<MOVE>.





A file uploaded with the PUT method is first written to a temporary file,
and then the file is renamed.
Starting from version 0.8.9, temporary files and the persistent store
can be put on different file systems.
However, be aware that in this case a file is copied
across two file systems instead of the cheap renaming operation.
It is thus recommended that for any given location both saved files and a
directory holding temporary files, set by the
L<ngx_http_core_module>
directive, are put on the same file system.





When creating a file with the PUT method, it is possible to specify
the modification date by passing it in the C<Date>
header field.







=head2 min_delete_depth


B<syntax:> min_delete_depth I<I<C<number>>>


B<default:> I<0>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Allows the DELETE method to remove files provided that
the number of elements in a request path is not less than the specified
number.
For example, the directive

    
    min_delete_depth 4;


allows removing files on requests

    
    /users/00/00/name
    /users/00/00/name/pic.jpg
    /users/00/00/page.html


and denies the removal of

    
    /users/00/00









