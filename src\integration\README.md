# Integration Modules

This directory contains modules for integrating with third-party systems.

- `canonical_model/`: Defines the NH-AI standard data models.
- `adapters/`: Contains adapters for fetching and parsing data from specific third-party systems.
- `transformers/`: Contains transformers for converting third-party data to the NH-AI canonical model.
- `examples/`: Contains example code illustrating the integration process.
