-- 计算生产参数 - 模块：平板门，厂商：平板门、平板抽面，参数：MKBQ

local _M = {}

function _M.dowork(root, target, context)
    -- 获取父节点的MKBQ属性值
    local mkbq = target:parent().MKBQ
    
    -- 检查属性是否存在
    if not mkbq then
        return nil, "从父节点获取不到[MKBQ]属性值"
    end
    
    -- 根据MKBQ的值返回对应结果
    if mkbq == "3" then
        return 1  -- 平板门 
    elseif mkbq == "4" then
        return 2  -- 平板抽面
    else
        return nil, "MKBQ的值不是3（平板门）或4（平板抽面），当前值：" .. tostring(mkbq)
    end
end

return _M