# 规则平台动态函数管理模块

## 1. 概述

本文档是规则平台技术方案的重要组成部分，详细描述了规则引擎中的动态抽象函数管理模块设计方案。该模块作为规则平台的核心功能组件，主要负责在OpenResty多worker环境下实现抽象函数的动态加载、更新和同步管理。主要特性包括：

- 抽象函数的动态注册与热更新
- 多worker进程间函数状态同步
- 基于Redis的分布式函数管理
- 支持K8s多Pod环境下的函数一致性
- 函数按需更新与版本控制

## 2. 系统架构

### 2.1 核心组件

```mermaid
graph TD
    A[规则管理服务<br/>SpringBoot] -->|存储| B[MySQL]
    A -->|同步| C[Redis集群]
    C -->|定时检查| D[OpenResty Pod1]
    C -->|定时检查| E[OpenResty Pod2]
    C -->|定时检查| F[OpenResty PodN]
    
    subgraph Pod1
        D -->|加载| G[Worker1]
        D -->|加载| H[Worker2]
        D -->|加载| I[WorkerN]
    end
    
    subgraph Pod2
        E -->|加载| J[Worker1]
        E -->|加载| K[Worker2]
        E -->|加载| L[WorkerN]
    end
    
    subgraph PodN
        F -->|加载| M[Worker1]
        F -->|加载| N[Worker2]
        F -->|加载| O[WorkerN]
    end
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bfb,stroke:#333,stroke-width:2px
    style D fill:#fbb,stroke:#333,stroke-width:2px
    style E fill:#fbb,stroke:#333,stroke-width:2px
    style F fill:#fbb,stroke:#333,stroke-width:2px
```

1. **规则管理服务（SpringBoot）**
   - 负责模块的注册和更新
   - 管理模块的生命周期
   - 提供RESTful API接口
   - 管理MySQL和Redis数据同步

2. **数据存储**
   - MySQL：主数据存储
   - Redis：多服务间数据共享缓存

3. **模块加载器**
   - 自定义Lua模块加载器
   - 支持从Redis加载模块

4. **更新检查机制**
   - 基于定时器检查更新状态
   - 通过Redis实现跨Pod同步

### 2.2 数据流

```
[规则管理服务] -> [MySQL存储] -> [Redis同步] -> [定时检查] -> [加载新模块]
```

## 3. 详细设计

### 3.1 模块命名规范

- 动态模块统一使用前缀：`dyn_`
- 示例：`dyn_user_module`, `dyn_order_module`

### 3.2 规则管理服务实现

#### 3.2.1 核心功能

1. **模块管理接口**
   - **注册模块**
     - 接口路径：`POST /api/modules/register`
     - 请求参数：
       - 模块名称（必须以`dyn_`开头）
       - 模块代码
     - 功能说明：
       - 验证模块名格式
       - 将模块代码存储到MySQL
       - 同步到Redis缓存
       - 更新模块最后修改时间
     - 返回结果：
       - 成功：HTTP 200
       - 失败：HTTP 400（参数错误）或 HTTP 500（服务器错误）

   - **查询模块**
     - 接口路径：`GET /api/modules/{name}`
     - 请求参数：
       - 模块名称
     - 功能说明：
       - 优先从Redis获取模块代码
       - Redis未命中则从MySQL获取并更新Redis
     - 返回结果：
       - 成功：HTTP 200，返回模块代码
       - 失败：HTTP 404（模块不存在）或 HTTP 500（服务器错误）

2. **Redis存储结构**
   - **模块代码存储**
     ```java
     // Redis Hash结构
     // Key: dynamic_modules
     // Field: 模块名称（如：dyn_user_module）
     // Value: 模块代码（Lua代码字符串）
     // 示例：
     // HSET dynamic_modules dyn_user_module "local M = {} function M.getUser(id) return {id = id} end return M"
     ```
     - 特点：
       - 使用Hash结构存储所有模块代码
       - 模块名作为field，便于快速查找
       - 模块代码作为value，直接存储Lua代码
       - 不设置过期时间，保持持久化

   - **更新信息存储**
     ```java
     // Redis Hash结构
     // Key: module_updates
     // Field: 模块名称（如：dyn_user_module）
     // Value: JSON格式的更新信息
     // 示例：
     // {
     //   "module": "dyn_user_module",
     //   "version": 1,
     //   "timestamp": 1234567890,
     //   "status": 1
     // }
     ```
     - 特点：
       - 使用Hash结构存储所有模块的更新信息
       - 包含版本号和更新时间戳
       - 用于模块更新检查和同步

### 3.3 模块加载器实现

```lua
-- moduleLoader.lua
local ngx = ngx
local redis = require "lib.redisclient"
local cjson = require "cjson"

local M = {}

-- 存储每个worker最后检查时间
local last_check_times = {}

-- 检查模块更新
local function check_updates()
    -- 获取所有模块的更新信息
    local updates, err = redis.hgetall("module_updates")
    if err then
        ngx.log(ngx.ERR, "获取更新信息失败: ", err)
        return
    end
    
    -- 检查每个模块是否需要更新
    for moduleName, updateInfo in pairs(updates) do
        local info = cjson.decode(updateInfo)
        local last_check = last_check_times[moduleName] or 0
        
        if info.timestamp > last_check then
            -- 更新检查时间
            last_check_times[moduleName] = info.timestamp
            -- 清除模块缓存
            package.loaded[moduleName] = nil
        end
    end
end

function M.install()
    -- 启动定时检查
    ngx.timer.every(5, check_updates)  -- 每5秒检查一次
    
    -- 安装模块加载器
    local loaders = package.searchers or package.loaders
    table.insert(loaders, 1, function(moduleName)
        -- 只处理动态模块
        if not moduleName:match("^dyn_") then
            return "\n\tnot a dynamic module"
        end
        
        -- 加载模块
        local moduleCode, err = redis.hget("dynamic_modules", moduleName)
        if err then
            return "\n\tRedis获取模块失败: " .. err
        end
        
        if moduleCode then
            return load(moduleCode, moduleName)
        end
        
        return "\n\tno dynamic module '" .. moduleName .. "'"
    end)
end

return M
```

### 3.4 初始化配置

```nginx
http {
    init_worker_by_lua_block {
        require("moduleLoader").install()
    }
}
```

## 4. 性能考虑

1. **缓存策略**
   - 模块代码编译后缓存在每个worker中
   - Redis缓存模块代码
   - 定时检查更新状态

2. **数据同步**
   - MySQL主从复制
   - Redis集群部署
   - 合理的同步策略

## 5. 注意事项

1. **数据一致性**
   - MySQL和Redis数据同步
   - 事务处理
   - 异常回滚

2. **高可用**
   - MySQL主从配置
   - Redis集群部署
   - 服务降级策略

## 6. 扩展性考虑

1. **模块版本管理**
   - 支持多版本共存
   - 版本回滚机制
   - 版本切换通知

2. **权限控制**
   - 模块注册权限控制
   - 更新权限控制
   - 数据访问权限控制 