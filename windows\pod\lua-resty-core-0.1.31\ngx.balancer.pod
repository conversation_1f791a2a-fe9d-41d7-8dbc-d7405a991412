=encoding utf-8


=head1 Name

ngx.balancer - Lua API for defining dynamic upstream balancers in Lua


=head1 Status

This Lua module is production ready.


=head1 Synopsis


=head2 http subsystem


    http {
        upstream backend {
            server *******;   # just an invalid address as a place holder
    
            balancer_by_lua_block {
                local balancer = require "ngx.balancer"
    
                -- well, usually we calculate the peer's host and port
                -- according to some balancing policies instead of using
                -- hard-coded values like below
                local host = "*********"
                local port = 8080
    
                local ok, err = balancer.set_current_peer(host, port)
                if not ok then
                    ngx.log(ngx.ERR, "failed to set the current peer: ", err)
                    return ngx.exit(500)
                end
            }
    
            keepalive 10;  # connection pool
        }
    
        server {
            # this is the real entry point
            listen 80;
    
            location / {
                # make use of the upstream named "backend" defined above:
                proxy_pass http://backend/fake;
            }
        }
    
        server {
            # this server is just for mocking up a backend peer here...
            listen *********:8080;
    
            location = /fake {
                echo "this is the fake backend peer...";
            }
        }
    }




=head2 stream subsystem


    stream {
        upstream backend {
            server *******:1234;   # just an invalid address as a place holder
    
            balancer_by_lua_block {
                local balancer = require "ngx.balancer"
    
                -- well, usually we calculate the peer's host and port
                -- according to some balancing policies instead of using
                -- hard-coded values like below
                local host = "*********"
                local port = 8080
    
                local ok, err = balancer.set_current_peer(host, port)
                if not ok then
                    ngx.log(ngx.ERR, "failed to set the current peer: ", err)
                    return ngx.exit(ngx.ERROR)
                end
            }
        }
    
        server {
            # this is the real entry point
            listen 10000;
    
            # make use of the upstream named "backend" defined above:
            proxy_pass backend;
        }
    
        server {
            # this server is just for mocking up a backend peer here...
            listen *********:8080;
    
            echo "this is the fake backend peer...";
        }
    }




=head1 Description

This Lua module provides API functions to allow defining highly dynamic NGINX load balancers for
any existing nginx upstream modules like L<ngx_http_proxy_module|http://nginx.org/en/docs/http/ngx_http_proxy_module.html>,
L<ngx_http_fastcgi_module|http://nginx.org/en/docs/http/ngx_http_fastcgi_module.html> and
L<ngx_stream_proxy_module|https://nginx.org/en/docs/stream/ngx_stream_proxy_module.html>.

It allows you to dynamically select a backend peer to connect to (or retry) on a per-request
basis from a list of backend peers which may also be dynamic.




=head1 Methods

All the methods of this module are static (or module-level). That is, you do not need an object (or instance)
to call these methods.




=head2 set_current_peer

B<syntax:> I<ok, err = balancer.set_current_peer(host, port, host?)>

B<context:> I<balancer_by_luaE<42>>

Sets the peer address (host and port) for the current backend query (which may be a retry).

Domain names in C<host> do not make sense. You need to use OpenResty libraries like
L<lua-resty-dns|https://github.com/openresty/lua-resty-dns> to obtain IP address(es) from
all the domain names before entering the C<balancer_by_lua*> handler (for example,
you can perform DNS lookups in an earlier phase like L<access_by_lua*|https://github.com/openresty/lua-nginx-module#access_by_lua>
and pass the results to the C<balancer_by_lua*> handler via L<ngx.ctx|https://github.com/openresty/lua-nginx-module#ngxctx>.

C<host> can be set to a string value or nil. If you set C<host> to C<nil>, this function will use the host set by directive C<proxy_ssl_name>.
You should not specify C<host> and C<proxy_ssl_name> at the same time.

This directive should be used on the toplevel scope of your C<nginx.conf>.

In case of an error, this function returns C<nil> and a string describing the error.




=head2 bind_to_local_addr

B<syntax:> I<ok, err = balancer.bind_to_local_addr(addr)>

B<context:> I<balancer_by_luaE<42>>

Makes outgoing connections to a proxied server originate from the specified local IP address with an optional port.

C<addr> is a string value of the IP address with optional port. For example: 127.0.0.1, 127.0.0.1:12345.

In case of an error, this function returns C<nil> and a string describing the error.




=head2 enable_keepalive

B<syntax:> C<ok, err = balancer.enable_keepalive(idle_timeout?, max_requests?)>

B<context:> I<balancer_by_luaE<42>>

Instructs the current upstream connection to be kept-alive once the current
request succeeds. The connection will be inserted in the pool specified by the
C<pool> option of the L<set_current_peer> function (if
unspecified, the default pool name will be C<< "<host>:<port>" >>).

The keepalive capabilities offered via this function are similar to that of the
L<keepalive|http://nginx.org/en/docs/http/ngx_http_upstream_module.html#keepalive>
directive of the ngx_http_upstream_module, with more dynamic capabilities
addressing a wide range of use-cases.

The first optional argument C<idle_timeout> may be a number used to specify the
maximum amount of time the connection may remain unused in the pool. The value
is to be specified in seconds, with floating point numbers allowing for
millisecond precision. If omitted, the default value is C<60> (60 seconds).
When the idle timeout threshold is reached and the connection hasn't been
reused, it will be closed. A value of C<0> will keep the connection in the pool
indefinitely (it may still be eventually closed by the remote peer).
This argument is identical to the
L<keepalive_timeout|http://nginx.org/en/docs/http/ngx_http_upstream_module.html#keepalive_timeout>
directive of the ngx_http_upstream_module, but can be set dynamically for each
upstream connection.

The second optional argument C<max_requests> may be a number used to specify the
amount of upstream requests a given connection should be reused for before
being closed. If omitted, the default value is C<100>.
When the connection has been reused as many times as the C<max_requests> value,
it will be closed instead of being inserted back into the connection pool. A
value of C<0> will allow for the connection to be reused for any number of
upstream requests.
This argument is identical to the
L<keepalive_requests|http://nginx.org/en/docs/http/ngx_http_upstream_module.html#keepalive_requests>
directive of the ngx_http_upstream_module, but can be set dynamically for each
upstream connection.

This function returns C<true> upon success, or C<nil> and a string describing the
error otherwise.

Below is a standard example usage:


    http {
        upstream backend {
            server *******;    # placeholder
    
            balancer_by_lua_block {
                local balancer = require "ngx.balancer"
    
                local ok, err = balancer.set_current_peer("*********", 8080)
                if not ok then
                    ngx.log(ngx.ERR, "failed to set current peer: ", err)
                    return
                end
    
                -- default pool will be "host:port"
                -- default pool_size will be 30
                ok, err = balancer.enable_keepalive()
                if not ok then
                    ngx.log(ngx.ERR, "failed to set keepalive: ", err)
                    return
                end
            }
        }
    
        server {
            listen 80;
    
            location / {
                proxy_pass http://backend/;
            }
        }
    }

A more advanced usage of this API can be made to overcome specific limitations
of NGINX's upstream keepalive pooling behavior. One of such limitations is the
lack of consideration for TLS attributes in the connection reuse logic: within
a given C<upstream {}> block, NGINX's connection reuse logic only considers the
IP and port attributes of a connection, and fails to consider the SNI
extension (among others), which could result in requests being sent over the
wrong TLS connection. NGINX's official stance on this limitation is to use
different C<upstream {}> blocks (e.g. one for each SNI), which would not only be
wasteful but also defeat the purpose of the dynamic capabilities offered by
OpenResty.

Below is an example of how to overcome this limitation and pool connections by
IP, port, and SNI:


    http {
        upstream backend {
            server *******;    # placeholder
    
            balancer_by_lua_block {
                local balancer = require "ngx.balancer"
    
                local host = "example.org"
                local ip = "*********"
                local port = 8080
    
                local ok, err = balancer.set_current_peer("*********", 8080, host)
                if not ok then
                    ngx.log(ngx.ERR, "failed to set current peer: ", err)
                    return
                end
    
                ok, err = balancer.enable_keepalive(60, 100)
                if not ok then
                    ngx.log(ngx.ERR, "failed to set keepalive: ", err)
                    return
                end
            }
        }
    
        ...
    }

Should not specify nginx keepalive with balancer_by_lua at the same time.
The following configurations are not recommended:


    http {
        upstream backend_ngx_keepalive {
            server *******;    # placeholder
    
            balancer_by_lua_block {
                local balancer = require "ngx.balancer"
                local host = "example.org"
                balancer.set_current_peer("*********", 8080, host)
                balancer.enable_keepalive(60, 100)
            }
    
            keepalive 60;
            keepalive_timeout 60s;
            keepalive_requests 100;
        }
    }

This function was first added to the C<http> subsystem in the C<v0.1.18> release
of this library. It is not yet supported in the C<stream> subsystem.




=head2 set_more_tries

B<syntax:> I<ok, err = balancer.set_more_tries(count)>

B<context:> I<balancer_by_luaE<42>>

Sets the tries performed when the current attempt (which may be a retry) fails (as determined
by directives like L<proxy_next_upstream|http://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_next_upstream>, depending on what
particular nginx uptream module you are currently using). Note that the current attempt is I<excluded> in the C<count> number set here.

Please note that, the total number of tries in a single downstream request cannot exceed the
hard limit configured by directives like L<proxy_next_upstream_tries|http://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_next_upstream_tries>,
depending on what concrete nginx upstream module you are using. When exceeding this limit,
the C<count> value will get reduced to meet the limit and the second return value will be
the string C<"reduced tries due to limit">, which is a warning, while the first return value
is still a C<true> value.




=head2 get_last_failure

B<syntax:> I<state_name, status_code = balancer.get_last_failure()>

B<context:> I<balancer_by_luaE<42>>

Retrieves the failure details about the previous failed attempt (if any) when the C<next_upstream> retrying
mechanism is in action. When there was indeed a failed previous attempt, it returned a string describing
that attempt's state name, as well as an integer describing the status code of that attempt.

Possible state names are as follows:

=over


=item *

C<"next">
Failures due to bad status codes sent from the backend server. The origin's response is same though, which means the backend connection
can still be reused for future requests.

=item *

C<"failed">
Fatal errors while communicating to the backend server (like connection timeouts, connection resets, and etc). In this case,
the backend connection must be aborted and cannot get reused.


=back

Possible status codes are those HTTP error status codes like C<502> and C<504>.

For stream module, C<status_code> will always be 0 (ngx.OK) and is provided for compatibility reasons.

When the current attempt is the first attempt for the current downstream request (which means
there is no previous attempts at all), this
method always returns a single C<nil> value.




=head2 set_timeouts

B<syntax:> C<ok, err = balancer.set_timeouts(connect_timeout, send_timeout, read_timeout)>

B<context:> I<balancer_by_luaE<42>>

Sets the upstream timeout (connect, send and read) in seconds for the current and any
subsequent backend requests (which might be a retry).

If you want to inherit the timeout value of the global C<nginx.conf> configuration (like C<proxy_connect_timeout>), then
just specify the C<nil> value for the corresponding argument (like the C<connect_timeout> argument).

Zero and negative timeout values are not allowed.

You can specify millisecond precision in the timeout values by using floating point numbers like 0.001 (which means 1ms).

B<Note:> C<send_timeout> and C<read_timeout> are controlled by the same config
L<`proxy_timeout`|https://nginx.org/en/docs/stream/ngx_stream_proxy_module.html#proxy_timeout>
for C<ngx_stream_proxy_module>. To keep API compatibility, this function will use C<max(send_timeout, read_timeout)>
as the value for setting C<proxy_timeout>.

Returns C<true> when the operation is successful; returns C<nil> and a string describing the error
otherwise.

This only affects the current downstream request. It is not a global change.

For the best performance, you should use the L<OpenResty|https://openresty.org/> bundle.

This function was first added in the C<0.1.7> version of this library.




=head2 recreate_request

B<syntax:> C<ok, err = balancer.recreate_request()>

B<context:> I<balancer_by_luaE<42>>

Recreates the request buffer for sending to the upstream server. This is useful, for example
if you want to change a request header field to the new upstream server on balancer retries.

Normally this does not work because the request buffer is created once during upstream module
initialization and won't be regenerated for subsequent retries. However you can use
C<proxy_set_header My-Header $my_header> and set the C<ngx.var.my_header> variable inside the
balancer phase. Calling C<balancer.recreate_request()> after updating a header field will
cause the request buffer to be re-generated and the C<My-Header> header will thus contain
the new value.

B<Warning:> because the request buffer has to be recreated and such allocation occurs on the
request memory pool, the old buffer has to be thrown away and will only be freed after the request
finishes. Do not call this function too often or memory leaks may be noticeable. Even so, a call
to this function should be made B<only> if you know the request buffer must be regenerated,
instead of unconditionally in each balancer retries.

This function was first added in the C<0.1.20> version of this library.




=head2 set_upstream_tls

B<syntax:> C<ok, err = balancer.set_upstream_tls(on)>

B<context:> I<balancer_by_luaE<42>>

Turn off the HTTPs or reenable the HTTPs for the upstream connection.


=over


=item *

If C<on> is C<true>, then the https protocol will be used to connect to the upstream server.

=item *

If C<on> is C<false>, then the http protocol will be used to connect to the upstream server.


=back

This function was first added in the C<0.1.29> version of this library.




=head1 Community




=head2 English Mailing List

The L<openresty-en|https://groups.google.com/group/openresty-en> mailing list is for English speakers.




=head2 Chinese Mailing List

The L<openresty|https://groups.google.com/group/openresty> mailing list is for Chinese speakers.




=head1 Bugs and Patches

Please report bugs or submit patches by


=over


=item 1.

creating a ticket on the L<GitHub Issue Tracker|https://github.com/openresty/lua-resty-core/issues>,

=item 2.

or posting to the L<OpenResty community>.


=back




=head1 Author

Yichun Zhang E<lt><EMAIL><gt> (agentzh), OpenResty Inc.




=head1 Copyright and License

This module is licensed under the BSD license.

Copyright (C) 2015-2017, by Yichun "agentzh" Zhang, OpenResty Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:


=over


=item *

Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.


=back


=over


=item *

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

the ngx_lua module: https://github.com/openresty/lua-nginx-module

=item *

the L<balancer_by_lua*|https://github.com/openresty/lua-nginx-module#balancer_by_lua_block> directive.

=item *

the L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

=item *

OpenResty: https://openresty.org


=back


