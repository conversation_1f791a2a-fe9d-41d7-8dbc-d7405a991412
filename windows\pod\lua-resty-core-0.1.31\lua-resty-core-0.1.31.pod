=encoding utf-8


=head1 Name

lua-resty-core - New FFI-based Lua API for ngx_http_lua_module and/or ngx_stream_lua_module


=head1 Status

This library is production ready.


=head1 Synopsis

This library is automatically loaded by default since OpenResty 1.15.8.1. This
behavior can be disabled via the
L<lua_load_resty_core|https://github.com/openresty/lua-nginx-module#lua_load_resty_core>
directive, but note that the use of this library is vividly recommended, as its
FFI implementation is both faster, safer, and more complete than the Lua C API
of the ngx_lua module.

If you are using an older version of OpenResty, you must load this library like
so:


        # nginx.conf
    
        http {
            # you do NOT need to configure the following line when you
            # are using the OpenResty bundle *******+.
            lua_package_path "/path/to/lua-resty-core/lib/?.lua;;";
    
            init_by_lua_block {
                require "resty.core"
                collectgarbage("collect")  -- just to collect any garbage
            }
    
            ...
        }


=head1 Description

This pure Lua library reimplements part of the L<ngx_lua|https://github.com/openresty/lua-nginx-module#readme> module's
L<Nginx API for Lua|https://github.com/openresty/lua-nginx-module#nginx-api-for-lua>
with LuaJIT FFI and installs the new FFI-based Lua API into the ngx.I< and ndk.> namespaces
used by the ngx_lua module.

In addition, this Lua library implements any significant new Lua APIs of
the L<ngx_lua|https://github.com/openresty/lua-nginx-module#readme> module
as proper Lua modules, like L<ngx.semaphore> and L<ngx.balancer>.

The FFI-based Lua API can work with LuaJIT's JIT compiler. ngx_lua's default API is based on the standard
Lua C API, which will never be JIT compiled and the user Lua code is always interpreted (slowly).

Support for the new L<ngx_stream_lua_module|https://github.com/openresty/stream-lua-nginx-module> has also begun.

This library is shipped with the OpenResty bundle by default. So you do not really need to worry about the dependencies
and requirements.




=head1 Prerequisites

B<WARNING> This library is included with every OpenResty release. You should use the bundled version
of this library in the particular OpenResty release you are using. Otherwise you may run
into serious compatibility issues.


=over


=item *

LuaJIT 2.1 (for now, it is the v2.1 git branch in the official luajit-2.0 git repository: http://luajit.org/download.html )

=item *

L<ngx_http_lua_module|https://github.com/openresty/lua-nginx-module> v0.10.25.

=item *

L<ngx_stream_lua_module|https://github.com/openresty/stream-lua-nginx-module> v0.0.13.

=item *

L<lua-resty-lrucache|https://github.com/openresty/lua-resty-lrucache>


=back




=head1 Installation

By default, LuaJIT will search Lua files in /usr/local/share/lua/5.1/.
But C<make install> will install this module to /usr/local/lib/lua.
So you may find the error like this:


    nginx: [alert] failed to load the 'resty.core' module

You can install this module with the following command to resolve the above problem.


    cd lua-resty-core
    sudo make install LUA_LIB_DIR=/usr/local/share/lua/5.1

You can also change the installation directory to any other directory you like with the LUA_LIB_DIR argument.


    cd lua-resty-core
    sudo make install LUA_LIB_DIR=/opt/nginx/lualib

After that, you need to add the above directory to the LuaJIT search direcotries with C<lua_package_path> nginx directive in the http context and stream context.


    lua_package_path "/opt/nginx/lualib/?.lua;;";




=head1 API Implemented




=head2 resty.core.hash



=over


=item *

L<ngx.md5|https://github.com/openresty/lua-nginx-module#ngxmd5>

=item *

L<ngx.md5_bin|https://github.com/openresty/lua-nginx-module#ngxmd5_bin>

=item *

L<ngx.sha1_bin|https://github.com/openresty/lua-nginx-module#ngxsha1_bin>


=back




=head2 resty.core.base64



=over


=item *

L<ngx.encode_base64|https://github.com/openresty/lua-nginx-module#ngxencode_base64>

=item *

L<ngx.decode_base64|https://github.com/openresty/lua-nginx-module#ngxdecode_base64>

=item *

L<ngx.decode_base64mime|https://github.com/openresty/lua-nginx-module#ngxdecode_base64mime>


=back




=head2 resty.core.uri



=over


=item *

L<ngx.escape_uri|https://github.com/openresty/lua-nginx-module#ngxescape_uri>

=item *

L<ngx.unescape_uri|https://github.com/openresty/lua-nginx-module#ngxunescape_uri>


=back




=head2 resty.core.regex



=over


=item *

L<ngx.re.match|https://github.com/openresty/lua-nginx-module#ngxrematch>

=item *

L<ngx.re.gmatch|https://github.com/openresty/lua-nginx-module#ngxregmatch>

=item *

L<ngx.re.find|https://github.com/openresty/lua-nginx-module#ngxrefind>

=item *

L<ngx.re.sub|https://github.com/openresty/lua-nginx-module#ngxresub>

=item *

L<ngx.re.gsub|https://github.com/openresty/lua-nginx-module#ngxregsub>


=back




=head2 resty.core.exit



=over


=item *

L<ngx.exit|https://github.com/openresty/lua-nginx-module#ngxexit>


=back




=head2 resty.core.shdict



=over


=item *

L<ngx.shared.DICT.get|https://github.com/openresty/lua-nginx-module#ngxshareddictget>

=item *

L<ngx.shared.DICT.get_stale|https://github.com/openresty/lua-nginx-module#ngxshareddictget_stale>

=item *

L<ngx.shared.DICT.incr|https://github.com/openresty/lua-nginx-module#ngxshareddictincr>

=item *

L<ngx.shared.DICT.set|https://github.com/openresty/lua-nginx-module#ngxshareddictset>

=item *

L<ngx.shared.DICT.safe_set|https://github.com/openresty/lua-nginx-module#ngxshareddictsafe_set>

=item *

L<ngx.shared.DICT.add|https://github.com/openresty/lua-nginx-module#ngxshareddictadd>

=item *

L<ngx.shared.DICT.safe_add|https://github.com/openresty/lua-nginx-module#ngxshareddictsafe_add>

=item *

L<ngx.shared.DICT.replace|https://github.com/openresty/lua-nginx-module#ngxshareddictreplace>

=item *

L<ngx.shared.DICT.delete|https://github.com/openresty/lua-nginx-module#ngxshareddictdelete>

=item *

L<ngx.shared.DICT.ttl|https://github.com/openresty/lua-nginx-module#ngxshareddictttl>

=item *

L<ngx.shared.DICT.expire|https://github.com/openresty/lua-nginx-module#ngxshareddictexpire>

=item *

L<ngx.shared.DICT.flush_all|https://github.com/openresty/lua-nginx-module#ngxshareddictflush_all>

=item *

L<ngx.shared.DICT.free_space|https://github.com/openresty/lua-nginx-module#ngxshareddictfree_space>

=item *

L<ngx.shared.DICT.capacity|https://github.com/openresty/lua-nginx-module#ngxshareddictcapacity>


=back




=head2 resty.core.var



=over


=item *

L<ngx.var.VARIABLE|https://github.com/openresty/lua-nginx-module#ngxvarvariable>


=back




=head2 resty.core.ctx



=over


=item *

L<ngx.ctx|https://github.com/openresty/lua-nginx-module#ngxctx>


=back




=head2 get_ctx_table


B<syntax:> I<ctx = resty.core.ctx.get_ctx_table(ctx?)>

Similar to L<ngx.ctx> but it accepts an optional C<ctx> argument.
It will use the C<ctx> from caller instead of creating a new table
when the C<ctx> table does not exist.

Notice: the C<ctx> table will be used in the current request's whole life cycle.
Please be very careful when you try to reuse the C<ctx> table.
You need to make sure there is no Lua code using or going to use the C<ctx> table
in the current request before you reusing the C<ctx> table in some other place.




=head2 resty.core.request



=over


=item *

L<ngx.req.get_headers|https://github.com/openresty/lua-nginx-module#ngxreqget_headers>

=item *

L<ngx.req.get_uri_args|https://github.com/openresty/lua-nginx-module#ngxreqget_uri_args>

=item *

L<ngx.req.start_time|https://github.com/openresty/lua-nginx-module#ngxreqstart_time>

=item *

L<ngx.req.get_method|https://github.com/openresty/lua-nginx-module#ngxreqget_method>

=item *

L<ngx.req.set_method|https://github.com/openresty/lua-nginx-module#ngxreqset_method>

=item *

L<ngx.req.set_header|https://github.com/openresty/lua-nginx-module#ngxreqset_header>

=item *

L<ngx.req.clear_header|https://github.com/openresty/lua-nginx-module#ngxreqclear_header>


=back




=head2 resty.core.response



=over


=item *

L<ngx.header.HEADER|https://github.com/openresty/lua-nginx-module#ngxheaderheader>


=back




=head2 resty.core.misc



=over


=item *

L<ngx.status|https://github.com/openresty/lua-nginx-module#ngxstatus>

=item *

L<ngx.is_subrequest|https://github.com/openresty/lua-nginx-module#ngxis_subrequest>

=item *

L<ngx.headers_sent|https://github.com/openresty/lua-nginx-module#ngxheaders_sent>

=item *

L<ngx.req.is_internal|https://github.com/openresty/lua-nginx-module#ngxreqis_internal>


=back




=head2 resty.core.time



=over


=item *

L<ngx.time|https://github.com/openresty/lua-nginx-module#ngxtime>

=item *

L<ngx.now|https://github.com/openresty/lua-nginx-module#ngxnow>

=item *

L<ngx.update_time|https://github.com/openresty/lua-nginx-module#ngxupdate_time>

=item *

L<ngx.localtime|https://github.com/openresty/lua-nginx-module#ngxlocaltime>

=item *

L<ngx.utctime|https://github.com/openresty/lua-nginx-module#ngxutctime>

=item *

L<ngx.cookie_time|https://github.com/openresty/lua-nginx-module#ngxcookie_time>

=item *

L<ngx.http_time|https://github.com/openresty/lua-nginx-module#ngxhttp_time>

=item *

L<ngx.parse_http_time|https://github.com/openresty/lua-nginx-module#ngxparse_http_time>

=item *

L<monotonic_msec|./lib/resty/core/time.md#monotonic_msec>

=item *

L<monotonic_time|./lib/resty/core/time.md#monotonic_time>


=back




=head2 resty.core.worker



=over


=item *

L<ngx.worker.exiting|https://github.com/openresty/lua-nginx-module#ngxworkerexiting>

=item *

L<ngx.worker.pid|https://github.com/openresty/lua-nginx-module#ngxworkerpid>

=item *

L<ngx.worker.id|https://github.com/openresty/lua-nginx-module#ngxworkerid>

=item *

L<ngx.worker.count|https://github.com/openresty/lua-nginx-module#ngxworkercount>


=back




=head2 resty.core.phase



=over


=item *

L<ngx.get_phase|https://github.com/openresty/lua-nginx-module#ngxget_phase>


=back




=head2 resty.core.ndk



=over


=item *

L<ndk.set_var|https://github.com/openresty/lua-nginx-module#ndkset_vardirective>


=back




=head2 resty.core.socket



=over


=item *

L<socket.setoption|https://github.com/openresty/lua-nginx-module#tcpsocksetoption>

=item *

L<socket.setclientcert|https://github.com/openresty/lua-nginx-module#tcpsocksetclientcert>

=item *

L<socket.sslhandshake|https://github.com/openresty/lua-nginx-module#tcpsocksslhandshake>


=back




=head2 resty.core.param



=over


=item *

L<ngx.arg|https://github.com/openresty/lua-nginx-module#ngxarg> (getter only)


=back




=head2 ngx.semaphore


This Lua module implements a semaphore API for efficient "light thread" synchronization,
which can work across different requests (but not across nginx worker processes).

See the L<documentation|./lib/ngx/semaphore.md> for this Lua module for more details.




=head2 ngx.balancer


This Lua module implements for defining dynamic upstream balancers in Lua.

See the L<documentation|./lib/ngx/balancer.md> for this Lua module for more details.




=head2 ngx.ssl


This Lua module provides a Lua API for controlling SSL certificates, private keys,
SSL protocol versions, and etc in NGINX downstream SSL handshakes.

See the L<documentation|./lib/ngx/ssl.md> for this Lua module for more details.




=head2 ngx.ssl.clienthello


This Lua module provides a Lua API for post-processing SSL client hello message
for NGINX downstream SSL connections.

See the L<documentation|./lib/ngx/ssl/clienthello.md> for this Lua module for more details.




=head2 ngx.ssl.session


This Lua module provides a Lua API for manipulating SSL session data and IDs
for NGINX downstream SSL connections.

See the L<documentation|./lib/ngx/ssl/session.md> for this Lua module for more details.




=head2 ngx.re


This Lua module provides a Lua API which implements convenience utilities for
the C<ngx.re> API.

See the L<documentation|./lib/ngx/re.md> for this Lua module for more details.




=head2 ngx.resp


This Lua module provides Lua API which could be used to handle HTTP response.

See the L<documentation|./lib/ngx/resp.md> for this Lua module for more details.




=head2 ngx.pipe


This module provides a Lua API to spawn processes and communicate with them in
a non-blocking fashion.

See the L<documentation|./lib/ngx/pipe.md> for this Lua module for more
details.

This module was first introduced in lua-resty-core v0.1.16.




=head2 ngx.process


This Lua module is used to manage the nginx process in Lua.

See the L<documentation|./lib/ngx/process.md> for this Lua module for more details.

This module was first introduced in lua-resty-core v0.1.12.




=head2 ngx.errlog


This Lua module provides Lua API to capture and manage nginx error log messages.

See the L<documentation|./lib/ngx/errlog.md> for this Lua module for more details.

This module was first introduced in lua-resty-core v0.1.12.




=head2 ngx.base64


This Lua module provides Lua API to urlsafe base64 encode/decode.

See the L<documentation|./lib/ngx/base64.md> for this Lua module for more details.

This module was first introduced in lua-resty-core v0.1.14.




=head1 Caveat

If the user Lua code is not JIT compiled, then use of this library may
lead to performance drop in interpreted mode. You will only observe
speedup when you get a good part of your user Lua code JIT compiled.




=head1 TODO


=over


=item *

Re-implement C<ngx_lua>'s cosocket API with FFI.

=item *

Re-implement C<ngx_lua>'s C<ngx.eof> and C<ngx.flush> API functions with FFI.


=back




=head1 Author

Yichun "agentzh" Zhang (章亦春) E<lt><EMAIL><gt>, OpenResty Inc.




=head1 Copyright and License

This module is licensed under the BSD license.

Copyright (C) 2013-2019, by Yichun "agentzh" Zhang, OpenResty Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:


=over


=item *

Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.


=back


=over


=item *

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

the ngx_lua module: https://github.com/openresty/lua-nginx-module#readme

=item *

LuaJIT FFI: http://luajit.org/ext_ffi.html


=back



