=encoding utf-8


=head1 Name


daemon_master_process_off


=head1



B<Q:>
Can I run nginx with "daemon off" or "master_process off" settings
in a production environment?





E<nbsp>





B<A:>
First of all, both "daemon onE<verbar>off" and "master_process onE<verbar>off" directives were
intended to be used primarily for nginx code development.





While many people use "daemon off" in production it wasn’t really meant for
that. Since version 1.0.9 it is now quite safe to run nginx in production
with "daemon off", though. Bear in mind that non-stop upgrade is not an
option with "daemon off".





In a development environment, using "master_process off", nginx can run in
the foreground without the master process and can be terminated simply
with ^C (SIGINT). This is somewhat similar to running Apache with
an 'X' command-line option. However you should NEVER run nginx in production
with "master_process off".




