target.MBKS==1 and target.CZFX==0 and target.CG==0 and target.JSLX==0
target.MBKS==1 and target.CZFX==0 and target.CG==0 and target.JSLX>0
target.MBKS==1 and target.CZFX==0 and target.CG==1 and target.JSLX==0
target.MBKS==1 and target.CZFX==0 and target.CG==1 and target.JSLX>0
target.MBKS==1 and target.CZFX==90 and target.CG==0 and target.JSLX==0
target.MBKS==1 and target.CZFX==90 and target.CG==0 and target.JSLX>0
target.MBKS==1 and target.CZFX==90 and target.CG==1 and target.JSLX==0
target.MBKS==1 and target.CZFX==90 and target.CG==1 and target.JSLX>0
/
/
/
target.PT==18 and target.DT_Pos==0 and target.JGLX==0
target.PT==18 and target.DT_Pos>0 and target.JGLX==0
target.PT==18 and target.DT_Pos==0 and target.JGLX==1 and target.CBSP==0
target.PT==18 and target.DT_Pos>0 and target.JGLX==1 and target.CBSP==0
target.PT==25 and target.DT_Pos==0 and target.JGLX==0
target.PT==25 and target.DT_Pos>0 and target.JGLX==0
target.PT==25 and target.DT_Pos>0 and target.JGLX==1 and target.CBSP==0
target.PT==18 and target.JGLX==1 and target.CBSP>0
target.PT==25 and target.JGLX==1 and target.CBSP>0
target.PT==18 and target.DT_Pos==0 and target.JGLX==0
target.PT==18 and target.DT_Pos>0 and target.JGLX==0
target.PT==18 and target.DT_Pos==0 and target.JGLX==1 and target.CBSP==0
target.PT==18 and target.DT_Pos>0 and target.JGLX==1 and target.CBSP==0
target.PT==25 and target.DT_Pos==0 and target.JGLX==0
target.PT==25 and target.DT_Pos>0 and target.JGLX==0
target.PT==25 and target.DT_Pos>0 and target.JGLX==1 and target.CBSP==0
target.PT==18 and target.JGLX==1 and target.CBSP>0
target.PT==25 and target.JGLX==1 and target.CBSP>0
target.PT==18 and target.CutOut==0 and target.JGLX==0
target.PT==25 and target.CutOut==0 and target.JGLX==0
target.PT==18 and target.CutOut==23
target.PT==18 and target.CutOut==24
target.PT==18 and target.CutOut==234
target.PT==25 and target.CutOut==23
target.PT==25 and target.CutOut==24
target.PT==25 and target.CutOut==234
target.PT==18 and target.CutOut==0 and target.JGLX==1
target.PT==25 and target.CutOut==0 and target.JGLX==1
target.PT==18 and target.CutOut==0
target.PT==18 and target.CutOut>0
target.PT==25 and target.CutOut==0
target.PT==25 and target.CutOut>0
target.PT==35 and target.CutOut==0
target.PT==35 and target.CutOut>0
target.PT==18 and target.CutOut==0
target.PT==18 and target.CutOut>0
target.PT==25 and target.CutOut==0
target.PT==25 and target.CutOut>0
target.PT==18 and target.Height>0 and target.Height<70
target.PT==25
target.PT==18 and target.Height>=70
/
/
/
/
/
/
/
target.JSLX==0 and target.JGLX==1
target.JSLX>0 and target.JGLX==1
target.CXHD==18
target.CXHD==12
target.CTGZJ==1
target.CTGZJ==2
target.CTGZJ>1
/
target.Width>=144 and target.Width<=494
target.Width>494 and target.Width<=894
target.Width>894 and target.Width<=1186
/
/
target.PT==18
target.PT==25
target.PT==18 and target.Height==18 and target.TMDGC==0
target.PT==18 and target.Height>18 and target.Height<=28 and target.TMDGC==0
target.PT==18 and target.Height>28 and target.Height<=41 and target.TMDGC==0
target.TMDGC==1

-- SQL更新语句
UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.CZFX)==0 and tonumber(target.CG)==0 and tonumber(target.JSLX)==0' 
WHERE lua_script = 'target.MBKS==1 and target.CZFX==0 and target.CG==0 and target.JSLX==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.CZFX)==0 and tonumber(target.CG)==0 and tonumber(target.JSLX)>0' 
WHERE lua_script = 'target.MBKS==1 and target.CZFX==0 and target.CG==0 and target.JSLX>0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.CZFX)==0 and tonumber(target.CG)==1 and tonumber(target.JSLX)==0' 
WHERE lua_script = 'target.MBKS==1 and target.CZFX==0 and target.CG==1 and target.JSLX==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.CZFX)==0 and tonumber(target.CG)==1 and tonumber(target.JSLX)>0' 
WHERE lua_script = 'target.MBKS==1 and target.CZFX==0 and target.CG==1 and target.JSLX>0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.CZFX)==90 and tonumber(target.CG)==0 and tonumber(target.JSLX)==0' 
WHERE lua_script = 'target.MBKS==1 and target.CZFX==90 and target.CG==0 and target.JSLX==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.CZFX)==90 and tonumber(target.CG)==0 and tonumber(target.JSLX)>0' 
WHERE lua_script = 'target.MBKS==1 and target.CZFX==90 and target.CG==0 and target.JSLX>0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.CZFX)==90 and tonumber(target.CG)==1 and tonumber(target.JSLX)==0' 
WHERE lua_script = 'target.MBKS==1 and target.CZFX==90 and target.CG==1 and target.JSLX==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.CZFX)==90 and tonumber(target.CG)==1 and tonumber(target.JSLX)>0' 
WHERE lua_script = 'target.MBKS==1 and target.CZFX==90 and target.CG==1 and target.JSLX>0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==0' 
WHERE lua_script = 'target.PT==18 and target.DT_Pos==0 and target.JGLX==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==0' 
WHERE lua_script = 'target.PT==18 and target.DT_Pos>0 and target.JGLX==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0' 
WHERE lua_script = 'target.PT==18 and target.DT_Pos==0 and target.JGLX==1 and target.CBSP==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0' 
WHERE lua_script = 'target.PT==18 and target.DT_Pos>0 and target.JGLX==1 and target.CBSP==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==0' 
WHERE lua_script = 'target.PT==25 and target.DT_Pos==0 and target.JGLX==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==0' 
WHERE lua_script = 'target.PT==25 and target.DT_Pos>0 and target.JGLX==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0' 
WHERE lua_script = 'target.PT==25 and target.DT_Pos>0 and target.JGLX==1 and target.CBSP==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)>0' 
WHERE lua_script = 'target.PT==18 and target.JGLX==1 and target.CBSP>0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)>0' 
WHERE lua_script = 'target.PT==25 and target.JGLX==1 and target.CBSP>0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==0' 
WHERE lua_script = 'target.PT==18 and target.CutOut==0 and target.JGLX==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==0' 
WHERE lua_script = 'target.PT==25 and target.CutOut==0 and target.JGLX==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==23' 
WHERE lua_script = 'target.PT==18 and target.CutOut==23' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==24' 
WHERE lua_script = 'target.PT==18 and target.CutOut==24' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==234' 
WHERE lua_script = 'target.PT==18 and target.CutOut==234' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==23' 
WHERE lua_script = 'target.PT==25 and target.CutOut==23' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==24' 
WHERE lua_script = 'target.PT==25 and target.CutOut==24' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==234' 
WHERE lua_script = 'target.PT==25 and target.CutOut==234' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==1' 
WHERE lua_script = 'target.PT==18 and target.CutOut==0 and target.JGLX==1' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==1' 
WHERE lua_script = 'target.PT==25 and target.CutOut==0 and target.JGLX==1' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==0' 
WHERE lua_script = 'target.PT==18 and target.CutOut==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)>0' 
WHERE lua_script = 'target.PT==18 and target.CutOut>0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==0' 
WHERE lua_script = 'target.PT==25 and target.CutOut==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)>0' 
WHERE lua_script = 'target.PT==25 and target.CutOut>0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==35 and tonumber(target.CutOut)==0' 
WHERE lua_script = 'target.PT==35 and target.CutOut==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==35 and tonumber(target.CutOut)>0' 
WHERE lua_script = 'target.PT==35 and target.CutOut>0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.Height)>0 and tonumber(target.Height)<70' 
WHERE lua_script = 'target.PT==18 and target.Height>0 and target.Height<70' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25' 
WHERE lua_script = 'target.PT==25' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.Height)>=70' 
WHERE lua_script = 'target.PT==18 and target.Height>=70' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.JSLX)==0 and tonumber(target.JGLX)==1' 
WHERE lua_script = 'target.JSLX==0 and target.JGLX==1' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.JSLX)>0 and tonumber(target.JGLX)==1' 
WHERE lua_script = 'target.JSLX>0 and target.JGLX==1' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.CXHD)==18' 
WHERE lua_script = 'target.CXHD==18' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.CXHD)==12' 
WHERE lua_script = 'target.CXHD==12' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.CTGZJ)==1' 
WHERE lua_script = 'target.CTGZJ==1' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.CTGZJ)==2' 
WHERE lua_script = 'target.CTGZJ==2' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.CTGZJ)>1' 
WHERE lua_script = 'target.CTGZJ>1' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.Width)>=144 and tonumber(target.Width)<=494' 
WHERE lua_script = 'target.Width>=144 and target.Width<=494' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.Width)>494 and tonumber(target.Width)<=894' 
WHERE lua_script = 'target.Width>494 and target.Width<=894' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.Width)>894 and tonumber(target.Width)<=1186' 
WHERE lua_script = 'target.Width>894 and target.Width<=1186' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18' 
WHERE lua_script = 'target.PT==18' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==25' 
WHERE lua_script = 'target.PT==25' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.Height)==18 and tonumber(target.TMDGC)==0' 
WHERE lua_script = 'target.PT==18 and target.Height==18 and target.TMDGC==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.Height)>18 and tonumber(target.Height)<=28 and tonumber(target.TMDGC)==0' 
WHERE lua_script = 'target.PT==18 and target.Height>18 and target.Height<=28 and target.TMDGC==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.Height)>28 and tonumber(target.Height)<=41 and tonumber(target.TMDGC)==0' 
WHERE lua_script = 'target.PT==18 and target.Height>28 and target.Height<=41 and target.TMDGC==0' 
AND id >= 94 AND id < 172;

UPDATE nhai_rule_version 
SET lua_script = 'tonumber(target.TMDGC)==1' 
WHERE lua_script = 'target.TMDGC==1' 
AND id >= 94 AND id < 172;