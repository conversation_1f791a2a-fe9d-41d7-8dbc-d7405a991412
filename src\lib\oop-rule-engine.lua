local re = require("lib.rule-engine")

local Engine = {}

function Engine.new(xmlString)
    local instance = {
        _t_id = re.scene_init(xmlString)
    }
    -- 这里将引擎对象放到当前进程上下文中，随请求结束销毁
    -- 这样做的话就变成强依赖 openresty
    -- 另一种方式是入口方法手动调用 ngx.ctx.engine = Engine.new(xxx) 去维护上下文
    -- ngx.ctx.engine = instance
    setmetatable(instance, { __index = Engine })
    return instance
end

function Engine.set(engineInstance)
    ngx.ctx.engine = engineInstance
end

function Engine.current()
    if ngx.ctx.engine then
        return ngx.ctx.engine
    end
    error('当前请求未主动实例化引擎对象，请调用 Engine.new() 方法实例化引擎对象')
end

function Engine:t_id()
    return self._t_id
end

function Engine:scene_clear()
    re.scene_clear(self._t_id)
    self._t_id = nil
end

--- TODO 将 GeometryObject 的实例方法都直接移植到当前类里

function Engine:aabb_cover_detect(id, direction, distance, detectIDs)
    return re.aabb_cover_detect(self._t_id, id, direction, distance, detectIDs)
end

function Engine:add_test(a, b)
    return re.add_test(a, b)
end

function Engine:scene_export()
    return re.scene_export(self._t_id)
end

function Engine:get_objects_distance(attr_key1, op1, value1, directions, distance_op, distance, attr_key2, op2, value2)
    return re.get_objects_distance(self._t_id, attr_key1, op1, value1, directions, distance_op, distance, attr_key2, op2, value2)
end

function Engine:get_objects_distance_value_array(attr_key1, op1, values1, directions, distance_op, distance, attr_key2, op2, values2)
    return re.get_objects_distance_value_array(self._t_id, attr_key1, op1, values1, directions, distance_op, distance, attr_key2, op2, values2)
end

function Engine:get_objects_overlap(objects, ignoreSameRoot)
    return re.get_objects_overlap(self._t_id, objects, ignoreSameRoot)
end

function Engine:add_test_by_id(a, b)
    return re.add_test_by_id(self._t_id, a, b)
end

function Engine:getObjectsAngle(angleObjects)
    return re.getObjectsAngle(self._t_id, angleObjects)
end

function Engine:get_door_hinge_face_by_pos(uid)
    return re.get_door_hinge_face_by_pos(self._t_id, uid)
end

function Engine:get_door_hinge_cover_info(uid, cover_type)
    return re.get_door_hinge_cover_info(self._t_id, uid, cover_type)
end

function Engine:get_internal_space(id)
    return re.get_internal_space(self._t_id, id)
end

function Engine:get_internal_space_distance(id, internalSpace)
    return re.get_internal_space_distance(self._t_id, id, internalSpace)
end

function Engine:get_door_intersect_inside_board(uuid, distance)
    return re.get_door_intersect_inside_board(self._t_id, uuid, distance)
end

function Engine:get_sceen_door_aligned_group()
    return re.get_sceen_door_aligned_group(self._t_id)
end

return Engine
