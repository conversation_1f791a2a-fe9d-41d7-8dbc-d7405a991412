-- 函数数据访问模块
local redis = require "lib.redisclient"
local json = require "cjson"
local string_util = require "lib.string-util"

local _M = {
    -- 模块版本
    _version = '0.1.1'
}

-- Redis 中函数数据的键前缀
local function_key_prefix = "rule_platform:function:"

-- 根据函数ID获取函数对象
-- @param function_id 函数ID
-- @return function_obj 函数对象，如果不存在则返回nil
-- @return err 错误信息
function _M.get_function_by_id(function_id)
    if string_util.is_empty(function_id) then
        return nil, "函数ID不能为空"
    end

    -- 构建Redis键
    local key = function_key_prefix .. function_id

    -- 从Redis获取函数数据
    local function_data, err = redis.get(key)
    if err then
        return nil, "函数[" .. function_id .. "]" .. err
    end

    if not function_data then
        return nil, "函数[" .. function_id .. "]数据不存在"
    end

    -- 解析JSON数据
    local success, function_obj = pcall(json.decode, function_data)
    if not success then
        return nil, "函数[" .. function_id .. "]数据格式错误"
    end

    return function_obj, nil
end

-- 获取所有函数对象
-- @return functions 函数对象数组，如果不存在则返回空表
-- @return err 错误信息
function _M.get_all_functions()
    local functions = {}
    local errors = {}
    local pattern = function_key_prefix .. "*"

    -- 使用keys命令获取所有匹配的键
    local keys, err = redis.keys(pattern)
    if err then
        return {}, "获取函数列表失败：" .. err
    end

    -- 遍历所有键获取函数数据
    for _, key in ipairs(keys) do
        local function_data, err = redis.get(key)
        if err then
            table.insert(errors, "获取函数数据失败：" .. err)
            goto continue
        end

        if not function_data then
            table.insert(errors, "函数数据不存在：" .. key)
            goto continue
        end

        -- 解析JSON数据
        local success, function_obj = pcall(json.decode, function_data)
        if not success then
            table.insert(errors, "函数数据格式错误：" .. key)
            goto continue
        end

        table.insert(functions, function_obj)
        ::continue::
    end

    if #errors > 0 then
        return functions, table.concat(errors, "; ")
    end

    return functions, nil
end

-- 保存函数数据到Redis
-- @param function_id 函数ID
-- @param function_data 函数数据
-- @return ok 是否保存成功
-- @return err 错误信息
function _M.save_function(function_id, function_data)
    if string_util.is_empty(function_id) then
        return false, "函数ID不能为空"
    end

    local key = function_key_prefix .. function_id
    
    local ok, err = redis.set(key, function_data)
    if not ok then
        return false, "保存函数数据失败: " .. err
    end

    return true, nil
end



-- 模块版本信息
function _M.version()
    return _M._version
end

return _M
