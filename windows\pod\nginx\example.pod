=encoding utf-8


=head1 Name


example - Example nginx configuration


=head1



An example site configuration that passes all requests to the backend except
images and requests starting with "E<sol>downloadE<sol>".

    
    user  www www;
    
    worker_processes  2;
    
    pid /var/run/nginx.pid;
    
    #                          [ debug | info | notice | warn | error | crit ]
    
    error_log  /var/log/nginx.error_log  info;
    
    events {
        worker_connections   2000;
    
        # use [ kqueue | epoll | /dev/poll | select | poll ];
        use kqueue;
    }
    
    http {
    
        include       conf/mime.types;
        default_type  application/octet-stream;
    
    
        log_format main      '$remote_addr - $remote_user [$time_local] '
                             '"$request" $status $bytes_sent '
                             '"$http_referer" "$http_user_agent" '
                             '"$gzip_ratio"';
    
        log_format download  '$remote_addr - $remote_user [$time_local] '
                             '"$request" $status $bytes_sent '
                             '"$http_referer" "$http_user_agent" '
                             '"$http_range" "$sent_http_content_range"';
    
        client_header_timeout  3m;
        client_body_timeout    3m;
        send_timeout           3m;
    <!--
        connection_pool_size         256;
    -->
        client_header_buffer_size    1k;
        large_client_header_buffers  4 4k;
    <!--
        request_pool_size            4k;
    -->
        gzip on;
        gzip_min_length  1100;
        gzip_buffers     4 8k;
        gzip_types       text/plain;
    
        output_buffers   1 32k;
        postpone_output  1460;
    
        sendfile         on;
        tcp_nopush       on;
        tcp_nodelay      on;
        send_lowat       12000;
    
        keepalive_timeout  75 20;
    
        #lingering_time     30;
        #lingering_timeout  10;
        #reset_timedout_connection  on;
    
    
        server {
            listen        one.example.com;
            server_name   one.example.com  www.one.example.com;
    
            access_log   /var/log/nginx.access_log  main;
    
            location / {
                proxy_pass         http://127.0.0.1/;
                proxy_redirect     off;
    
                proxy_set_header   Host             $host;
                proxy_set_header   X-Real-IP        $remote_addr;
                #proxy_set_header  X-Forwarded-For  $proxy_add_x_forwarded_for;
    
                client_max_body_size       10m;
                client_body_buffer_size    128k;
    
                client_body_temp_path      /var/nginx/client_body_temp;
    
                proxy_connect_timeout      70;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_send_lowat           12000;
    
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
    
                proxy_temp_path            /var/nginx/proxy_temp;
    
                charset  koi8-r;
            }
    
            error_page  404  /404.html;
    
            location = /404.html {
                root  /spool/www;
            }
    
            location /old_stuff/ {
                rewrite   ^/old_stuff/(.*)$  /new_stuff/$1  permanent;
            }
    
            location /download/ {
    
                valid_referers  none  blocked  server_names  *.example.com;
    
                if ($invalid_referer) {
                    #rewrite   ^/   http://www.example.com/;
                    return   403;
                }
    
                #rewrite_log  on;
    
                # rewrite /download/*/mp3/*.any_ext to /download/*/mp3/*.mp3
                rewrite ^/(download/.*)/mp3/(.*)\..*$
                        /$1/mp3/$2.mp3                   break;
    
                root         /spool/www;
                #autoindex    on;
                access_log   /var/log/nginx-download.access_log  download;
            }
    
            location ~* \.(jpg|jpeg|gif)$ {
                root         /spool/www;
                access_log   off;
                expires      30d;
            }
        }
    }






