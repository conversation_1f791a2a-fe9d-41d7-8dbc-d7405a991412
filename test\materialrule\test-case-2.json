{"context": {}, "expressions": [{"key": "exp_001", "ruleId": "", "expression": "target.MBKS==1 and target.CZFX==0 and target.CG==0 and target.JSLX==0", "target": {"MBKS": 1, "CZFX": 0, "CG": 0, "JSLX": 0}}, {"key": "exp_002", "ruleId": "", "expression": "target.MBKS==1 and target.CZFX==0 and target.CG==0 and target.JSLX>0", "target": {"MBKS": 1, "CZFX": 0, "CG": 0, "JSLX": 1}}, {"key": "exp_003", "ruleId": "", "expression": "target.MBKS==1 and target.CZFX==0 and target.CG==1 and target.JSLX==0", "target": {"MBKS": 1, "CZFX": 0, "CG": 1, "JSLX": 0}}, {"key": "exp_004", "ruleId": "", "expression": "target.MBKS==1 and target.CZFX==0 and target.CG==1 and target.JSLX>0", "target": {"MBKS": 1, "CZFX": 0, "CG": 1, "JSLX": 1}}, {"key": "exp_005", "ruleId": "", "expression": "target.MBKS==1 and target.CZFX==90 and target.CG==0 and target.JSLX==0", "target": {"MBKS": 1, "CZFX": 90, "CG": 0, "JSLX": 0}}, {"key": "exp_006", "ruleId": "", "expression": "target.MBKS==1 and target.CZFX==90 and target.CG==0 and target.JSLX>0", "target": {"MBKS": 1, "CZFX": 90, "CG": 0, "JSLX": 1}}, {"key": "exp_007", "ruleId": "", "expression": "target.MBKS==1 and target.CZFX==90 and target.CG==1 and target.JSLX==0", "target": {"MBKS": 1, "CZFX": 90, "CG": 1, "JSLX": 0}}, {"key": "exp_008", "ruleId": "", "expression": "target.MBKS==1 and target.CZFX==90 and target.CG==1 and target.JSLX>0", "target": {"MBKS": 1, "CZFX": 90, "CG": 1, "JSLX": 1}}, {"key": "exp_009", "ruleId": "", "expression": "target.PT==18 and target.DT_Pos==0 and target.JGLX==0", "target": {"PT": 18, "DT_Pos": 0, "JGLX": 0}}, {"key": "exp_010", "ruleId": "", "expression": "target.PT==18 and target.DT_Pos>0 and target.JGLX==0", "target": {"PT": 18, "DT_Pos": 1, "JGLX": 0}}, {"key": "exp_011", "ruleId": "", "expression": "target.PT==18 and target.DT_Pos==0 and target.JGLX==1 and target.CBSP==0", "target": {"PT": 18, "DT_Pos": 0, "JGLX": 1, "CBSP": 0}}, {"key": "exp_012", "ruleId": "", "expression": "target.PT==18 and target.DT_Pos>0 and target.JGLX==1 and target.CBSP==0", "target": {"PT": 18, "DT_Pos": 1, "JGLX": 1, "CBSP": 0}}, {"key": "exp_013", "ruleId": "", "expression": "target.PT==25 and target.DT_Pos==0 and target.JGLX==0", "target": {"PT": 25, "DT_Pos": 0, "JGLX": 0}}, {"key": "exp_014", "ruleId": "", "expression": "target.PT==25 and target.DT_Pos>0 and target.JGLX==0", "target": {"PT": 25, "DT_Pos": 1, "JGLX": 0}}, {"key": "exp_015", "ruleId": "", "expression": "target.PT==25 and target.DT_Pos>0 and target.JGLX==1 and target.CBSP==0", "target": {"PT": 25, "DT_Pos": 1, "JGLX": 1, "CBSP": 0}}, {"key": "exp_016", "ruleId": "", "expression": "target.PT==18 and target.JGLX==1 and target.CBSP>0", "target": {"PT": 18, "JGLX": 1, "CBSP": 1}}, {"key": "exp_017", "ruleId": "", "expression": "target.PT==25 and target.JGLX==1 and target.CBSP>0", "target": {"PT": 25, "JGLX": 1, "CBSP": 1}}, {"key": "exp_018", "ruleId": "", "expression": "target.PT==18 and target.CutOut==0 and target.JGLX==0", "target": {"PT": 18, "CutOut": 0, "JGLX": 0}}, {"key": "exp_019", "ruleId": "", "expression": "target.PT==25 and target.CutOut==0 and target.JGLX==0", "target": {"PT": 25, "CutOut": 0, "JGLX": 0}}, {"key": "exp_020", "ruleId": "", "expression": "target.PT==18 and target.CutOut==23", "target": {"PT": 18, "CutOut": 23}}, {"key": "exp_021", "ruleId": "", "expression": "target.PT==18 and target.CutOut==24", "target": {"PT": 18, "CutOut": 24}}, {"key": "exp_022", "ruleId": "", "expression": "target.PT==18 and target.CutOut==234", "target": {"PT": 18, "CutOut": 234}}, {"key": "exp_023", "ruleId": "", "expression": "target.PT==25 and target.CutOut==23", "target": {"PT": 25, "CutOut": 23}}, {"key": "exp_024", "ruleId": "", "expression": "target.PT==25 and target.CutOut==24", "target": {"PT": 25, "CutOut": 24}}, {"key": "exp_025", "ruleId": "", "expression": "target.PT==25 and target.CutOut==234", "target": {"PT": 25, "CutOut": 234}}, {"key": "exp_026", "ruleId": "", "expression": "target.PT==18 and target.CutOut==0 and target.JGLX==1", "target": {"PT": 18, "CutOut": 0, "JGLX": 1}}, {"key": "exp_027", "ruleId": "", "expression": "target.PT==25 and target.CutOut==0 and target.JGLX==1", "target": {"PT": 25, "CutOut": 0, "JGLX": 1}}, {"key": "exp_028", "ruleId": "", "expression": "target.PT==18 and target.CutOut==0", "target": {"PT": 18, "CutOut": 0}}, {"key": "exp_029", "ruleId": "", "expression": "target.PT==18 and target.CutOut>0", "target": {"PT": 18, "CutOut": 1}}, {"key": "exp_030", "ruleId": "", "expression": "target.PT==25 and target.CutOut==0", "target": {"PT": 25, "CutOut": 0}}, {"key": "exp_031", "ruleId": "", "expression": "target.PT==25 and target.CutOut>0", "target": {"PT": 25, "CutOut": 1}}, {"key": "exp_032", "ruleId": "", "expression": "target.PT==35 and target.CutOut==0", "target": {"PT": 35, "CutOut": 0}}, {"key": "exp_033", "ruleId": "", "expression": "target.PT==35 and target.CutOut>0", "target": {"PT": 35, "CutOut": 1}}, {"key": "exp_034", "ruleId": "", "expression": "target.PT==18 and target.Height>0 and target.Height<70", "target": {"PT": 18, "Height": 35}}, {"key": "exp_035", "ruleId": "", "expression": "target.PT==25", "target": {"PT": 25}}, {"key": "exp_036", "ruleId": "", "expression": "target.PT==18 and target.Height>=70", "target": {"PT": 18, "Height": 70}}, {"key": "exp_037", "ruleId": "", "expression": "target.JSLX==0 and target.JGLX==1", "target": {"JSLX": 0, "JGLX": 1}}, {"key": "exp_038", "ruleId": "", "expression": "target.JSLX>0 and target.JGLX==1", "target": {"JSLX": 1, "JGLX": 1}}, {"key": "exp_039", "ruleId": "", "expression": "target.CXHD==18", "target": {"CXHD": 18}}, {"key": "exp_040", "ruleId": "", "expression": "target.CXHD==12", "target": {"CXHD": 12}}, {"key": "exp_041", "ruleId": "", "expression": "target.CTGZJ==1", "target": {"CTGZJ": 1}}, {"key": "exp_042", "ruleId": "", "expression": "target.CTGZJ==2", "target": {"CTGZJ": 2}}, {"key": "exp_043", "ruleId": "", "expression": "target.CTGZJ>1", "target": {"CTGZJ": 2}}, {"key": "exp_044", "ruleId": "", "expression": "target.Width>=144 and target.Width<=494", "target": {"Width": 300}}, {"key": "exp_045", "ruleId": "", "expression": "target.Width>494 and target.Width<=894", "target": {"Width": 600}}, {"key": "exp_046", "ruleId": "", "expression": "target.Width>894 and target.Width<=1186", "target": {"Width": 1000}}, {"key": "exp_047", "ruleId": "", "expression": "target.PT==18", "target": {"PT": 18}}, {"key": "exp_048", "ruleId": "", "expression": "target.PT==25", "target": {"PT": 25}}, {"key": "exp_049", "ruleId": "", "expression": "target.PT==18 and target.Height==18 and target.TMDGC==0", "target": {"PT": 18, "Height": 18, "TMDGC": 0}}, {"key": "exp_050", "ruleId": "", "expression": "target.PT==18 and target.Height>18 and target.Height<=28 and target.TMDGC==0", "target": {"PT": 18, "Height": 25, "TMDGC": 0}}, {"key": "exp_051", "ruleId": "", "expression": "target.PT==18 and target.Height>28 and target.Height<=41 and target.TMDGC==0", "target": {"PT": 18, "Height": 35, "TMDGC": 0}}, {"key": "exp_052", "ruleId": "", "expression": "target.TMDGC==1", "target": {"TMDGC": 1}}]}