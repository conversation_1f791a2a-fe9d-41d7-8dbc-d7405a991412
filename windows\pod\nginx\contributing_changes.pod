=encoding utf-8


=head1 Name


contributing_changes - Contributing Changes


=head1 Getting Sources



L<GitHub|https://github.com> is used
to store source code.
The L<repository|https://github.com/nginx/nginx> can be cloned
with the following command:

    
    git clone https://github.com/nginx/nginx.git






=head1 Formatting Changes



Changes should be formatted according to the
L<code style|development_guide>
used by nginx.
Sometimes, there is no clear rule; in such cases
examine how existing nginx sources are formatted and mimic this style.
Changes will more likely be accepted if style corresponds to the surrounding
code.





L<Commit|https://docs.github.com/en/pull-requests/committing-changes-to-your-project/creating-and-editing-commits/about-commits>
the changes in your nginx GitHub fork.
Please ensure that the specified
L<e-mail|https://docs.github.com/en/get-started/getting-started-with-git/setting-your-username-in-git>
address and real name of the author are correct.





The commit message should have a single-line synopsis followed by verbose
description after an empty line.
It is desirable that the first line is no longer than 67 symbols,
and the remaining lines are no longer than 76 symbols.
The resulting commit can be obtained using the
C<git show> command:

    
    commit 8597218f386351d6c6cdced24af6716e19a18fc3
    Author: Filipe Da Silva <<EMAIL>>
    Date:   Thu May 9 10:54:28 2013 +0200
    
        Mail: removed surplus ngx_close_connection() call.
    
        It is already called for a peer connection a few lines above.
    
    diff --git a/src/mail/ngx_mail_auth_http_module.c b/src/mail/ngx_mail_auth_http_module.c
    index 2e9b9f24d..8094bbc5c 100644
    --- a/src/mail/ngx_mail_auth_http_module.c
    +++ b/src/mail/ngx_mail_auth_http_module.c
    @@ -699,7 +699,6 @@ ngx_mail_auth_http_process_headers(ngx_mail_session_t *s,
    
                         p = ngx_pnalloc(s->connection->pool, ctx->err.len);
                         if (p == NULL) {
    -                        ngx_close_connection(ctx->peer.connection);
                             ngx_destroy_pool(ctx->pool);
                             ngx_mail_session_internal_server_error(s);
                             return;






=head1 Before Submitting



Several points are worth to consider before submitting changes:

=over




=item *

The proposed changes should work properly on a wide range of
L<supported
platforms|index>.



=item *

Try to make it clear why the suggested change is needed, and provide a use
case, if possible.



=item *

Passing your changes through the test suite is a good way to ensure
that they do not cause a regression.
The L<repository|https://github.com/nginx/nginx-tests> with
tests can be cloned with the following command:

    
    git clone https://github.com/nginx/nginx-tests.git





=back






=head1 Submitting Changes



The proposed changes should be submitted from your fork to
L<nginx repository|https://github.com/nginx/nginx>
as a
L<pull request|https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/proposing-changes-to-your-work-with-pull-requests/creating-a-pull-request-from-a-fork>.




=head1 Website



GitHub is used to store the sources for this website.
The L<repository|https://www.github.com/nginx/nginx.org>
can be cloned with the following command:

    
    git clone https://github.com/nginx/nginx.org.git


Documentation changes should be submitted from your fork
as a pull request.




=head1 License



Submitting changes implies granting project a permission to use it under
an appropriate L<license|../../LICENSE>.




