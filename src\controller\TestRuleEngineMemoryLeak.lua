local engine = require("lib/rule-engine")

-- 主函数，处理HTTP请求
local function main()
    -- 设置响应头
    ngx.header.content_type = "application/json; charset=utf-8"

    local xml_content = [[
<Root>
    <Soft>
        <Furnitures>
            <Furniture id="87" isGroup="true" level="1" sizeX="219.68382263183594" sizeY="-111.32211303710938"
                       sizeZ="100.51317596435547" X="-1252.936767578125" Y="993.7791748046875" Z="50.256587982177734"
                       RX="0" RY="0" RZ="0">
                <Furniture id="85" productId="3FO3VXTB7AQ8" name="芝华仕-维纳斯真皮（爵士灰）沙发-单人双扶（功能款）"
                           isGroup="false" level="1" categoryId="203" categoryName="单人沙发" sizeX="1087.1300048828125"
                           sizeY="1058.219970703125" sizeZ="981.573974609375" X="-12764.8203125" Y="-9704.875"
                           Z="490.7869873046875" RX="0" RY="0" RZ="-3.1415927410125732"/>
                <Furniture id="86" productId="3FO3VXU55PFB" name="芝华仕-维纳斯真皮（浅奶咖）沙发-单人双扶（功能款）"
                           isGroup="false" level="1" categoryId="203" categoryName="单人沙发" sizeX="1087.1300048828125"
                           sizeY="1058.219970703125" sizeZ="981.573974609375" X="-11692.1455078125" Y="-9704.875"
                           Z="490.7869873046875" RX="0" RY="0" RZ="-1.5707963705062866"/>
            </Furniture>
            <Furniture id="19" productId="3FO3VXJC2YTR" name="CM20036298-雅典娜真皮沙发（橄榄茶）-左扶二+右扶二(2020)"
                       isGroup="false" level="1" categoryId="205" categoryName="多人沙发" sizeX="2820.820068359375"
                       sizeY="980.051025390625" sizeZ="800.3070068359375" X="2436.95947265625" Y="1675.7825927734375"
                       Z="400.15350341796875" RX="0" RY="0" RZ="0"/>
        </Furnitures>
        <DoorWindows>
            <DoorWindow modelType="custom" type="3" id="8262FCBC-35F6-4988-9648-25445ACAA952" productId="3FO4MMDAWNIU"
                        level="1" sizeX="1450" sizeY="240" sizeZ="2100" X="-7169.89111328125" Y="-308.43243408203125"
                        Z="1050" RX="0" RY="0" RZ="1.5707963705062866"/>
            <DoorWindow modelType="custom" type="5" id="E8990CD9-6A57-4F6C-B763-D74D8C4464ED" productId="3FO4J2ECXD6X"
                        level="1" sizeX="2044" sizeY="240" sizeZ="2172" X="-5609.40869140625" Y="-3087.640869140625"
                        Z="1086" RX="0" RY="0" RZ="3.1415927410125732"/>
            <DoorWindow modelType="model" type="1" id="1" productId="3FO4IYA3XUB9" level="1" sizeX="907.236" sizeY="240"
                        sizeZ="2127.7" X="-6422.857421875" Y="1472.3592529296875" Z="1063.8499755859375" RX="0" RY="0"
                        RZ="0"/>
        </DoorWindows>
    </Soft>
</Root>
    ]]

    -- 调用最原始的几何引擎封装代码，这里就会导致 NG 内存不断上涨并且不释放
    local id = engine.scene_init(xml_content)
    engine.scene_clear(id)

    -- 返回响应
    ngx.status = 200
    ngx.say('{ "engine": "' .. id .. '" }')
end

-- 执行主函数
main()
