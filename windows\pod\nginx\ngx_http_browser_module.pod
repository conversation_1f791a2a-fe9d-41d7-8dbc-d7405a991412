=encoding utf-8

=head1 NAME

ngx_http_browser_module - Module ngx_http_browser_module




=head1



The C<ngx_http_browser_module> module creates variables
whose values depend on the value of the C<User-Agent>
request header field:

=over



=item C<$modern_browser>




equals the value set by the L</modern_browser_value> directive,
if a browser was identified as modern;



=item C<$ancient_browser>




equals the value set by the L</ancient_browser_value> directive,
if a browser was identified as ancient;



=item C<$msie>




equals “1” if a browser was identified as MSIE of any version.




=back






=head1 Example Configuration



Choosing an index file:

    
    modern_browser_value "modern.";
    
    modern_browser msie      5.5;
    modern_browser gecko     1.0.0;
    modern_browser opera     9.0;
    modern_browser safari    413;
    modern_browser konqueror 3.0;
    
    index index.${modern_browser}html index.html;







Redirection for old browsers:

    
    modern_browser msie      5.0;
    modern_browser gecko     0.9.1;
    modern_browser opera     8.0;
    modern_browser safari    413;
    modern_browser konqueror 3.0;
    
    modern_browser unlisted;
    
    ancient_browser Links Lynx netscape4;
    
    if ($ancient_browser) {
        rewrite ^ /ancient.html;
    }






=head1 Directives

=head2 ancient_browser


B<syntax:> ancient_browser I<I<C<string>> ...>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





If any of the specified substrings is found in the C<User-Agent>
request header field, the browser will be considered ancient.
The special string “C<netscape4>” corresponds to the
regular expression “C<^MozillaE<sol>[1-4]>”.







=head2 ancient_browser_value


B<syntax:> ancient_browser_value I<I<C<string>>>


B<default:> I<1>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets a value for the C<$ancient_browser> variables.







=head2 modern_browser


B<syntax:> modern_browser I<I<C<browser>> I<C<version>>>


B<syntax:> modern_browser I<C<unlisted>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Specifies a version starting from which a browser is considered modern.
A browser can be any one of the following: C<msie>,
C<gecko> (browsers based on Mozilla),
C<opera>, C<safari>,
or C<konqueror>.





Versions can be specified in the following formats: X, X.X, X.X.X, or X.X.X.X.
The maximum values for each of the format are
4000, 4000.99, 4000.99.99, and 4000.99.99.99, respectively.





The special value C<unlisted> specifies to consider
a browser as modern if it was not listed by the
C<modern_browser> and L</ancient_browser>
directives.
Otherwise such a browser is considered ancient.
If a request does not provide the C<User-Agent> field
in the header, the browser is treated as not being listed.







=head2 modern_browser_value


B<syntax:> modern_browser_value I<I<C<string>>>


B<default:> I<1>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets a value for the C<$modern_browser> variables.







