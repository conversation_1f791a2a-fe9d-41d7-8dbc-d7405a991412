-- XML part节点核心工具模块
-- 不做参数保护 由调用者保证
-- 如果参数错误 会返回nil 并打印错误信息

local M = {}

-- 内部函数：在节点中查找指定键的值
-- @param node: XML节点
-- @param match_type: 匹配类型，"attribute"表示匹配属性，其他值表示匹配子节点
-- @param key: 要搜索的键名
-- @return: 找到的值，如果未找到返回nil
function M.find_value_in_node(node, match_type, key)
    -- 参数验证
    if not node or not match_type or not key then
        return nil, "find_value_in_node: invalid parameters"
    end

    if match_type == "attribute" then
        -- 直接返回节点的属性值
        local result_table = {}
        table.insert(result_table, node:get_attribute(key))
        return result_table
    else
        -- 搜索指定类型的子节点
        local child_nodes = node:search("./" .. match_type)
        if not child_nodes or #child_nodes == 0 then
            return nil
        end

        -- 遍历子节点查找匹配的属性值
        local result = {}
        for _, child in ipairs(child_nodes) do
            local value = child:get_attribute(key)
            if value then
                table.insert(result, value)
            end
        end

        return result
    end
end

-- 内部函数：检查节点是否匹配所有的key-value对
-- @param node: XML节点
-- @param match_type: 匹配类型
-- @param kvs_array: key-value数组
-- @return: 是否全部匹配
function M.check_node_matches(node, match_type, kvs_array)
    for _, kv in ipairs(kvs_array) do
        local found_value = M.find_value_in_node(node, match_type, kv.key)

        if not found_value then
            return false
        end

        if #found_value == 1 then
            if tostring(found_value) ~= tostring(kv.value) then
                return false
            end
        else
            for _, value in ipairs(found_value) do
                if tostring(value) ~= tostring(kv.value) then
                    return false
                end
            end
        end
    end

    return true
end

function M.get_childs_node_by_kvs(node, match_type, name, value)
    local xpath = ".//" .. match_type .. "[@" .. name .. "='" .. value .. "']"
    local matched_nodes = node:search(xpath)
    return matched_nodes, nil
end

return M