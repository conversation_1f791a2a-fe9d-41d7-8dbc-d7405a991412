local xmlua = require("xmlua")

local html = [[
<html>
  <head>
    <title>Hello</title>
  </head>
  <body>
    <p cxv='无歧义' name='中文'>中文ABC</p>
  </body>
</html>
]]

print('=========> xml字符串解析成 document 表 <=========')
local doc = xmlua.XML.parse(html)
print('type(doc)', type(doc))
print('doc:to_xml()', doc:to_xml({encoding = 'UTF-8'}))
print('\n\n')

print('=========> xpath 路径搜索 <=========')
local p = doc:search("/html/body/p")
print('#p', #p)
print('p[1]:to_xml()', p[1]:to_xml())
print('\n\n')

print('=========> 属性获取 <=========')
print('无歧义或者冲突的时候可以直接通过 dot 形式获取：p[1].cxv', p[1].cxv)
print('或者使用中括号形式获取：p[1].cxv', p[1]['cxv'])
print('name属性歧义直接变成 function 类型了：p[1].name', p[1]['name'])
print('最保险就是使用 get_attribute 方法获取：p[1]:get_attribute("name")', p[1]:get_attribute("name"))
print('\n\n')

print('=========> 获取标签内容 <=========')
print('p[1]:text()', p[1]:text())
print('p[1]:content()', p[1]:content())
print('\n\n')

print('=========> 获取标签的 xpath <=========')
print(type(p:paths()))
print('p:xpath()[1]', p:paths()[1])
print('\n\n')

print('=========> to_xml 选项支持 <=========')
print(doc:search("/html/body")[1]:to_xml({ indent = true, declaration = false, encoding = 'UTF-8' }))
