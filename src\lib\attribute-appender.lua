--[[
属性附加器
功能：将子节点的属性附加到父节点
规则：
1. 检查节点是否为Part节点（通过节点名称判断）
2. 提取Space节点的位置相关属性（X、Y、Z、absX、absY、absZ）
3. 提取Parameters/Parameter节点的name和value属性
4. 如果Parameter节点存在min和max属性，则添加name_min和name_max属性
5. 将提取的属性附加到当前节点（如果当前节点不存在该属性）

返回值：
- table: 处理后的节点
- nil, error_msg: 发生错误
]]

local _M = {}

-- 检查节点是否为Part节点
local function is_part_node(node)
    return node:name() == "Part"
end

-- 主处理函数
function _M.append_attributes(node)
    if not node then
        return nil, "节点不能为空"
    end

    -- 检查是否为Part节点
    if not is_part_node(node) then
        return nil, "节点不是Part节点"
    end

    -- 检查是否已经附加过属性
    if node:get_attribute("_attributes_appended") then
        return node
    end

    -- 获取Space节点的位置属性
    local space_nodes = node:search("./Space")
    for _, space in ipairs(space_nodes) do
        local space_attrs = { "X", "Y", "Z", "absX", "absY", "absZ" }
        for _, attr_name in ipairs(space_attrs) do
            -- 检查节点是否已存在该属性
            if not node:get_attribute(attr_name) then
                local value = space:get_attribute(attr_name)
                if value then
                    node:set_attribute(attr_name, value)
                end
            end
        end
    end

    -- 获取Parameter节点的name和value属性
    local param_nodes = node:search("./Parameters/Parameter")
    for _, param in ipairs(param_nodes) do
        local name = param:get_attribute("name")
        if name then
            -- 检查节点是否已存在该属性
            if not node:get_attribute(name) then
                local value = param:get_attribute("value")
                if value then
                    node:set_attribute(name, value)
                    
                    -- 处理min和max属性
                    local min_value = param:get_attribute("min")
                    local max_value = param:get_attribute("max")
                    
                    if min_value then
                        node:set_attribute(name .. "_min", min_value)
                    end
                    
                    if max_value then
                        node:set_attribute(name .. "_max", max_value)
                    end
                end
            end
        end
    end

    -- 标记节点已附加属性
    node:set_attribute("_attributes_appended", "true")

    return node
end

return _M
