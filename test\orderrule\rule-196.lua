-- 计算生产参数 - 模块：平板门【2门】，厂商：平板门，参数：TJBS

local _M = {}

function _M.dowork(root, target, context)
    -- 获取参数值并转换为数字
    local mbtj = tonumber(target:parent().MBTJ)

    -- 参数检查
    if not mbtj then
        return nil, "获取不到MBTJ参数值或参数值不是有效的数字"
    end

    -- 如果MBTJ为0，直接返回空字符串
    if mbtj == 0 then
        return ""
    end

    -- 查找所有MBTJ不为0的Part节点
    local nodes = {}
    local success, result = pcall(function()
        return root:search("//Part[@MBTJ!='0' and @MBTJ!='5']")
    end)

    if not success then
        return nil, "查找节点失败: " .. tostring(result)
    end

    -- 将找到的节点存入数组
    for _, node in ipairs(result) do
        table.insert(nodes, node)
    end

    -- 查找目标节点在数组中的位置
    local target_id = target:parent().id
    local index = nil
    for i, node in ipairs(nodes) do
        if node:get_attribute("id") == target_id then
            index = i
            break
        end
    end

    -- 如果找到目标节点，返回序号
    if index then
        return string.format("%d:A", index)
    end

    return ""
end

return _M