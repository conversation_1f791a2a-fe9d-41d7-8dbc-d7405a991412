local test_xml = [[
<Root><SchemeInfo name="组合柜" level="1"/><Parts><Part id="71CA4223-2EB9-4F10-9E72-90DBA0EF43E1" isGroup="true" name="组合" roomId="20"><Space sizeX="1200.0" sizeY="300.0" sizeZ="2040.0" X="-1044.8579" Y="2484.0547" Z="1020.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters/><Part id="6146A812-A101-4216-A59F-3D6A399CBB07" isGroup="false" type="ProductionParamModel" modelTypeId="1" productId="3FO3OWXEKIH3" prodCatId="719" name="靠墙立柜1格（左右）" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" roomId="20" jdProductId="51309529186" W="600.0" D="300.0" H="2040.0"><Space sizeX="600.0" sizeY="300.0" sizeZ="2040.0" boxSizeX="600.0" boxSizeY="300.0" boxSizeZ="2040.0" absX="-1644.8579" absY="2634.0547" absZ="0.0" X="-1644.8579" Y="2634.0547" Z="0.0" CX="-1344.8579" CY="2484.0547" CZ="1020.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="YHQJ" value="0" displayName="是否右后切角"/><Parameter name="SGCJXK" value="0" displayName="检修口"/><Parameter name="D" value="300" displayName="深度" min="110" max="1000"/><Parameter name="ZCBSP" value="0" displayName="左侧板上飘" min="0" max="740"/><Parameter name="SHQJ" value="0" displayName="是否上后切角"/><Parameter name="H" value="2040" displayName="高度" min="110" max="2780"/><Parameter name="MKBQ" value="1" displayName="模块标签"/><Parameter name="JXGD" value="50" displayName="脚线高度" min="45" max="405"/><Parameter name="XGCHD" value="18" displayName="下固层厚"/><Parameter name="ZCBQP" value="0" displayName="左侧板前飘" min="0" max="900"/><Parameter name="YCBSP" value="0" displayName="右侧板上飘" min="0" max="740.0"/><Parameter name="YCBQP" value="0" displayName="右侧板前飘" min="0" max="900"/><Parameter name="offGround" value="0" displayName="离地"/><Parameter name="SHQJS" value="0" displayName="上后切角深" min="100" max="200"/><Parameter name="W" value="600" displayName="宽度" min="110" max="2000"/><Parameter name="SGCHD" value="18" displayName="上固层厚"/><Parameter name="YCBSDY" value="0" displayName="右侧板上倒圆"/><Parameter name="SGCQS" value="0" displayName="上固层前缩" min="-200" max="229"/><Parameter name="YHQJS" value="0" displayName="右后切角深" min="100" max="200"/><Parameter name="BBHD" value="5" displayName="背板厚度"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="ZCBFD" value="0" displayName="左侧板分段"/><Parameter name="ZCBJG" value="0" displayName="左侧板见光"/><Parameter name="ZCBHD" value="18" displayName="左侧板厚"/><Parameter name="JXHQS" value="0" displayName="脚线盒前缩" min="0" max="229"/><Parameter name="SHQJG" value="0" displayName="上后切角高" min="100" max="1200"/><Parameter name="YHQJK" value="0" displayName="右后切角宽" min="100" max="482"/><Parameter name="ZCBSDY" value="0" displayName="左侧板上倒圆"/><Parameter name="ZHQJ" value="0" displayName="是否左后切角"/><Parameter name="XGCQS" value="0" displayName="下固层前缩" min="0" max="229"/><Parameter name="YCBJG" value="0" displayName="右侧板见光"/><Parameter name="YCBHD" value="18" displayName="右侧板厚"/><Parameter name="ZHQJK" value="0" displayName="左后切角宽" min="100" max="482"/><Parameter name="YCBFD" value="0" displayName="右侧板分段"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="HJXHS" value="22" displayName="后脚线缩进" min="0" max="244"/><Parameter name="location" value="3" displayName="摆放方式"/><Parameter name="ZHQJS" value="0" displayName="左后切角深" min="100" max="200"/><Parameter name="JXHLX" value="0" displayName="脚线盒类型"/><Parameter name="JXHHS" value="0" displayName="脚线盒后缩" min="0" max="229"/></Parameters><ParamPlankPath/><Part id="72AC00C4-A58D-4C7B-88F9-A83FF7BBBB9A" isGroup="false" parentId="6146A812-A101-4216-A59F-3D6A399CBB07" type="ProductionParamModel" modelTypeId="1" productId="3FO3NRWVVC9Y" prodCatId="713" name="左侧板" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51559043642" W="18.0" D="295.0" H="2040.0"><Space sizeX="18.0" sizeY="295.0" sizeZ="2040.0" boxSizeX="18.0" boxSizeY="295.0" boxSizeZ="2040.0" absX="-1644.8579" absY="2629.0547" absZ="0.0" X="0.0" Y="-5.0" Z="0.0" CX="-291.0" CY="-2.5" CZ="0.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="295.0" displayName="深度" min="1" max="1200"/><Parameter name="BJBQ" value="1" displayName="板件标签"/><Parameter name="SHQJ" value="0" displayName="是否上后切角"/><Parameter name="H" value="2040.0" displayName="高度" min="1" max="2800"/><Parameter name="Buttcode" value="Y_CBL_001" displayName="厂商编码"/><Parameter name="SHQJG" value="0" displayName="上后切角高" min="0" max="1940"/><Parameter name="ZCBSDY" value="0" displayName="左侧板上倒圆" min="0" max="295"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="SHQJS" value="0" displayName="上后切角深" min="0" max="195"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="18.0" displayName="宽度" min="1" max="100"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="c2faa213-4ba7-59c6-ac64-0bc418510b3e" isGroup="false" parentId="72AC00C4-A58D-4C7B-88F9-A83FF7BBBB9A" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="2040.0" sizeY="295.0" sizeZ="18.0" boxSizeX="2040.0" boxSizeY="295.0" boxSizeZ="18.0" absX="-1635.8579" absY="2334.0547" absZ="2040.0" X="9.0" Y="-295.0" Z="2040.0" CX="-0.000044822693" CY="0.0" CZ="0.0" RX="0.0" RY="90.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="textureAngleDegree" value="0.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:1,&quot;radius&quot;:&quot;0.0&quot;},{&quot;position&quot;:&quot;0.0,295.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;2040.0,295.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;2040.0,0.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="0.0" Y="295.0"/><ParamPoint X="2040.0" Y="295.0"/><ParamPoint X="2040.0" Y="0.0"/></ParamPoints><ParamLines><ParamLine type="1" clockwise="true" minorArc="true" radius="0.0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part><Part id="276E0D27-53DE-4F1D-B082-6184530A1574" isGroup="false" parentId="6146A812-A101-4216-A59F-3D6A399CBB07" type="ProductionParamModel" modelTypeId="1" productId="3FO3NS1DQFKJ" prodCatId="713" name="右侧板" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51341870624" W="18.0" D="295.0" H="2040.0"><Space sizeX="18.0" sizeY="295.0" sizeZ="2040.0" boxSizeX="18.0" boxSizeY="295.0" boxSizeZ="2040.0" absX="-1062.8579" absY="2629.0547" absZ="0.0" X="582.0" Y="-5.0" Z="0.0" CX="291.0" CY="-2.5" CZ="0.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="295.0" displayName="深度" min="1" max="1200"/><Parameter name="BJBQ" value="2" displayName="板件标签"/><Parameter name="SHQJ" value="0" displayName="是否上后切角"/><Parameter name="H" value="2040.0" displayName="高度" min="1" max="2800"/><Parameter name="Buttcode" value="Y_CBR_001" displayName="厂商编码"/><Parameter name="SHQJG" value="0" displayName="上后切角高" min="0" max="1940"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="SHQJS" value="0" displayName="上后切角深" min="0" max="195"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="18.0" displayName="宽度" min="1" max="100"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="YCBSDY" value="0" displayName="右侧板上倒圆" min="0" max="295"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="3e26c559-45af-5e1e-bf25-77e1173b7efa" isGroup="false" parentId="276E0D27-53DE-4F1D-B082-6184530A1574" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="2040.0" sizeY="295.0" sizeZ="18.0" boxSizeX="2040.0" boxSizeY="295.0" boxSizeZ="18.0" absX="-1053.8579" absY="2334.0547" absZ="2040.0" X="9.0" Y="-295.0" Z="2040.0" CX="-0.000044822693" CY="0.0" CZ="0.0" RX="0.0" RY="90.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="textureAngleDegree" value="0.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:1,&quot;radius&quot;:&quot;0.0&quot;},{&quot;position&quot;:&quot;0.0,295.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;2040.0,295.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;2040.0,0.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="0.0" Y="295.0"/><ParamPoint X="2040.0" Y="295.0"/><ParamPoint X="2040.0" Y="0.0"/></ParamPoints><ParamLines><ParamLine type="1" clockwise="true" minorArc="true" radius="0.0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part><Part id="485C7325-98A7-4303-8D9E-E571EAB38063" isGroup="false" parentId="6146A812-A101-4216-A59F-3D6A399CBB07" type="ProductionParamModel" modelTypeId="1" productId="3FO3NRW8D86S" prodCatId="713" name="上固层" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51530791050" W="564.0" D="294.0" H="18.0"><Space sizeX="564.0" sizeY="294.0" sizeZ="18.0" boxSizeX="564.0" boxSizeY="294.0" boxSizeZ="18.0" absX="-1626.8579" absY="2629.0547" absZ="2022.0" X="18.0" Y="-5.0" Z="2022.0" CX="0.0" CY="-2.0" CZ="1011.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="YHQJ" value="0" displayName="是否右后切角"/><Parameter name="YHQJS" value="0" displayName="右后切角深" min="0" max="284"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="294.0" displayName="深度" min="10" max="1200"/><Parameter name="BJBQ" value="3" displayName="板件标签"/><Parameter name="H" value="18.0" displayName="高度"/><Parameter name="Buttcode" value="Y_CBT_001" displayName="厂商编码"/><Parameter name="YHQJK" value="0" displayName="右后切角宽" min="0" max="554"/><Parameter name="ZHQJ" value="0" displayName="是否左后切角"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="ZHQJK" value="0" displayName="左后切角宽" min="0" max="554"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="564.0" displayName="宽度" min="10" max="2800"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="ZHQJS" value="0" displayName="左后切角深" min="0" max="284"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="94991779-f95c-57e9-89e6-42a7e7b0825a" isGroup="false" parentId="485C7325-98A7-4303-8D9E-E571EAB38063" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="564.0" sizeY="294.0" sizeZ="18.0" boxSizeX="564.0" boxSizeY="294.0" boxSizeZ="18.0" absX="-1626.8579" absY="2335.0547" absZ="2031.0" X="0.0" Y="-294.0" Z="9.0" CX="0.0" CY="0.0" CZ="0.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="bottomFaceMaterial" value="1053AG"/><Parameter name="textureAngleDegree" value="0.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;564.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;564.0,294.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;0.0,294.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionFaceMaterial" value="1053AG"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="564.0" Y="0.0"/><ParamPoint X="564.0" Y="294.0"/><ParamPoint X="0.0" Y="294.0"/></ParamPoints><ParamLines><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part><Part id="241C6F0D-94C3-40F2-BDC3-546D283DEBC9" isGroup="false" parentId="6146A812-A101-4216-A59F-3D6A399CBB07" type="ProductionParamModel" modelTypeId="1" productId="3FO3NRSPC5FY" prodCatId="713" name="单元柜左右切角脚线盒" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51438406802" W="564.0" D="294.0" H="68.0"><Space sizeX="564.0" sizeY="294.0" sizeZ="68.0" boxSizeX="564.0" boxSizeY="294.0" boxSizeZ="68.0" absX="-1626.8579" absY="2629.0547" absZ="0.0" X="18.0" Y="-5.0" Z="0.0" CX="0.0" CY="-2.0" CZ="-986.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="YHQJ" value="0" displayName="是否右后切角"/><Parameter name="YHQJS" value="0" displayName="右后切角深" min="0" max="244"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="294.0" displayName="深度" min="50" max="1200"/><Parameter name="JXHD" value="18" displayName="脚线厚度"/><Parameter name="H" value="68.0" displayName="高度"/><Parameter name="JXGD" value="50" displayName="脚线高度" min="0" max="1200"/><Parameter name="XGCHD" value="18" displayName="下固层厚"/><Parameter name="YHQJK" value="0" displayName="右后切角宽" min="0" max="514"/><Parameter name="ZHQJ" value="0" displayName="是否左后切角"/><Parameter name="XGCQS" value="0" displayName="下固层前缩" min="0" max="244"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="ZHQJK" value="0" displayName="左后切角宽" min="0" max="514"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="564.0" displayName="宽度" min="50" max="2800"/><Parameter name="HJXHS" value="22" displayName="后脚线缩进" min="0" max="257"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="ZHQJS" value="0" displayName="左后切角深" min="0" max="244"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="JXHLX" value="0" displayName="脚线盒类型"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="BBECB1C8-5D75-44FA-B071-9F1AB23B8EA3" isGroup="false" parentId="241C6F0D-94C3-40F2-BDC3-546D283DEBC9" type="ProductionParamModel" modelTypeId="1" productId="3FO3NS199DKK" prodCatId="713" name="下固层" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51330994966" W="564.0" D="294.0" H="18.0"><Space sizeX="564.0" sizeY="294.0" sizeZ="18.0" boxSizeX="564.0" boxSizeY="294.0" boxSizeZ="18.0" absX="-1626.8579" absY="2629.0547" absZ="50.0" X="0.0" Y="0.0" Z="50.0" CX="0.0" CY="0.0" CZ="25.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="YHQJ" value="0" displayName="是否右后切角"/><Parameter name="YHQJS" value="0" displayName="右后切角深" min="0" max="284"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="294.0" displayName="深度" min="10" max="1200"/><Parameter name="BJBQ" value="4" displayName="板件标签"/><Parameter name="H" value="18.0" displayName="高度"/><Parameter name="Buttcode" value="Y_CBB_001" displayName="厂商编码"/><Parameter name="YHQJK" value="0" displayName="右后切角宽" min="0" max="554"/><Parameter name="ZHQJ" value="0" displayName="是否左后切角"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="ZHQJK" value="0" displayName="左后切角宽" min="0" max="554"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="564.0" displayName="宽度" min="10" max="2800"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="ZHQJS" value="0" displayName="左后切角深" min="0" max="284"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="204f9369-eb76-5ca7-81a6-88de857105cb" isGroup="false" parentId="BBECB1C8-5D75-44FA-B071-9F1AB23B8EA3" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="564.0" sizeY="294.0" sizeZ="18.0" boxSizeX="564.0" boxSizeY="294.0" boxSizeZ="18.0" absX="-1626.8579" absY="2335.0547" absZ="59.0" X="0.0" Y="-294.0" Z="9.0" CX="0.0" CY="0.0" CZ="0.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="bottomFaceMaterial" value="1053AG"/><Parameter name="textureAngleDegree" value="0.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;564.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;564.0,294.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;0.0,294.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionFaceMaterial" value="1053AG"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="564.0" Y="0.0"/><ParamPoint X="564.0" Y="294.0"/><ParamPoint X="0.0" Y="294.0"/></ParamPoints><ParamLines><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part><Part id="8CF9C17A-3DD3-4424-AD60-D7F437E06EE9" isGroup="false" parentId="241C6F0D-94C3-40F2-BDC3-546D283DEBC9" type="ProductionParamModel" modelTypeId="1" productId="3FO3NS1TS31I" prodCatId="713" name="前脚线" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51356687462" W="564.0" D="18.0" H="50.0"><Space sizeX="564.0" sizeY="18.0" sizeZ="50.0" boxSizeX="564.0" boxSizeY="18.0" boxSizeZ="50.0" absX="-1626.8579" absY="2354.0547" absZ="0.0" X="0.0" Y="-275.0" Z="0.0" CX="0.0" CY="-137.0" CZ="-9.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="18.0" displayName="深度"/><Parameter name="BJBQ" value="7" displayName="板件标签"/><Parameter name="H" value="50.0" displayName="高度" min="10" max="1200"/><Parameter name="Buttcode" value="Y_JX_001" displayName="厂商编码"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="564.0" displayName="宽度" min="10" max="2800"/><Parameter name="textureAngleDegree" value="90" displayName="材质角度"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="a61961a1-7544-5de8-a22b-d92c77d0c3aa" isGroup="false" parentId="8CF9C17A-3DD3-4424-AD60-D7F437E06EE9" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="50.0" sizeY="564.0" sizeZ="18.0" boxSizeX="50.0" boxSizeY="564.0" boxSizeZ="18.0" absX="-1062.8579" absY="2345.0547" absZ="0.0" X="564.0" Y="-8.999975" Z="-0.0000003934025" CX="0.0" CY="0.000011444092" CZ="0.0" RX="0.0" RY="-90.0" RZ="90.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="bottomFaceMaterial" value="1053AG"/><Parameter name="textureAngleDegree" value="90.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;50.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;50.0,564.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;0.0,564.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionFaceMaterial" value="1053AG"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="50.0" Y="0.0"/><ParamPoint X="50.0" Y="564.0"/><ParamPoint X="0.0" Y="564.0"/></ParamPoints><ParamLines><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part><Part id="21605F17-99C3-4A7F-87C9-72BD33A0D822" isGroup="false" parentId="241C6F0D-94C3-40F2-BDC3-546D283DEBC9" type="ProductionParamModel" modelTypeId="1" productId="3FO3NRWL95BN" prodCatId="713" name="后脚线" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51548931259" W="564.0" D="18.0" H="50.0"><Space sizeX="564.0" sizeY="18.0" sizeZ="50.0" boxSizeX="564.0" boxSizeY="18.0" boxSizeZ="50.0" absX="-1626.8579" absY="2607.0547" absZ="0.0" X="0.0" Y="-22.0" Z="0.0" CX="0.0" CY="116.0" CZ="-9.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="18.0" displayName="深度"/><Parameter name="BJBQ" value="7" displayName="板件标签"/><Parameter name="H" value="50.0" displayName="高度" min="10" max="1200"/><Parameter name="Buttcode" value="Y_JX_001" displayName="厂商编码"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="564.0" displayName="宽度" min="10" max="2800"/><Parameter name="textureAngleDegree" value="90" displayName="材质角度"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="9bb23c7c-4ad0-551e-83b4-2ddd4f79b536" isGroup="false" parentId="21605F17-99C3-4A7F-87C9-72BD33A0D822" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="50.0" sizeY="564.0" sizeZ="18.0" boxSizeX="50.0" boxSizeY="564.0" boxSizeZ="18.0" absX="-1062.8579" absY="2598.0547" absZ="0.0" X="564.0" Y="-8.999975" Z="-0.0000003934025" CX="0.0" CY="0.000011444092" CZ="0.0" RX="0.0" RY="-90.0" RZ="90.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="bottomFaceMaterial" value="1053AG"/><Parameter name="textureAngleDegree" value="90.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;50.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;50.0,564.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;0.0,564.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionFaceMaterial" value="1053AG"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="50.0" Y="0.0"/><ParamPoint X="50.0" Y="564.0"/><ParamPoint X="0.0" Y="564.0"/></ParamPoints><ParamLines><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part></Part><Part id="60B9BC7B-110D-44A5-8450-7C22C5E87432" isGroup="false" parentId="6146A812-A101-4216-A59F-3D6A399CBB07" type="ProductionParamModel" modelTypeId="1" productId="3FO3NS1BVNK6" prodCatId="713" name="5mm背板" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51340180203" W="598.0" D="5.0" H="1985.0"><Space sizeX="598.0" sizeY="5.0" sizeZ="1985.0" boxSizeX="598.0" boxSizeY="5.0" boxSizeZ="1985.0" absX="-1643.8579" absY="2634.0547" absZ="52.5" X="1.0" Y="0.0" Z="52.5" CX="0.0" CY="147.5" CZ="25.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="5" displayName="深度"/><Parameter name="BJBQ" value="6" displayName="板件标签"/><Parameter name="H" value="1985.0" displayName="高度" min="10" max="2800"/><Parameter name="Buttcode" value="Y_BP_002" displayName="厂商编码"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="598.0" displayName="宽度" min="10" max="2400"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="f82bcda5-c5a7-5e25-a3fc-f446db7dcc2a" isGroup="false" parentId="60B9BC7B-110D-44A5-8450-7C22C5E87432" type="8" thickness="5.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="1985.0" sizeY="598.0" sizeZ="5.0" boxSizeX="1985.0" boxSizeY="598.0" boxSizeZ="5.0" absX="-1045.8579" absY="2631.5547" absZ="52.5" X="598.0" Y="-2.4999738" Z="-0.000000109278474" CX="0.0" CY="-0.00003027916" CZ="0.0" RX="0.0" RY="-90.0" RZ="90.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="5.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="bottomFaceMaterial" value="1053AG"/><Parameter name="textureAngleDegree" value="0.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;1985.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;1985.0,598.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;0.0,598.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionFaceMaterial" value="1053AG"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="1985.0" Y="0.0"/><ParamPoint X="1985.0" Y="598.0"/><ParamPoint X="0.0" Y="598.0"/></ParamPoints><ParamLines><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part></Part><Part id="F5239586-7D37-41E3-A691-503842F4ADFA" isGroup="false" type="ProductionParamModel" modelTypeId="1" productId="3FO3OWXEKIH3" prodCatId="719" name="靠墙立柜1格（左右）" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" roomId="20" jdProductId="51309529186" W="600.0" D="300.0" H="2040.0"><Space sizeX="600.0" sizeY="300.0" sizeZ="2040.0" boxSizeX="600.0" boxSizeY="300.0" boxSizeZ="2040.0" absX="-1044.8579" absY="2634.0547" absZ="0.0" X="-1044.8579" Y="2634.0547" Z="0.0" CX="-744.8579" CY="2484.0547" CZ="1020.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="YHQJ" value="0" displayName="是否右后切角"/><Parameter name="SGCJXK" value="0" displayName="检修口"/><Parameter name="D" value="300" displayName="深度" min="110" max="1000"/><Parameter name="ZCBSP" value="0" displayName="左侧板上飘" min="0" max="740"/><Parameter name="SHQJ" value="0" displayName="是否上后切角"/><Parameter name="H" value="2040" displayName="高度" min="110" max="2780"/><Parameter name="MKBQ" value="1" displayName="模块标签"/><Parameter name="JXGD" value="50" displayName="脚线高度" min="45" max="405"/><Parameter name="XGCHD" value="18" displayName="下固层厚"/><Parameter name="ZCBQP" value="0" displayName="左侧板前飘" min="0" max="900"/><Parameter name="YCBSP" value="0" displayName="右侧板上飘" min="0" max="740.0"/><Parameter name="YCBQP" value="0" displayName="右侧板前飘" min="0" max="900"/><Parameter name="offGround" value="0" displayName="离地"/><Parameter name="SHQJS" value="0" displayName="上后切角深" min="100" max="200"/><Parameter name="W" value="600" displayName="宽度" min="110" max="2000"/><Parameter name="SGCHD" value="18" displayName="上固层厚"/><Parameter name="YCBSDY" value="0" displayName="右侧板上倒圆"/><Parameter name="SGCQS" value="0" displayName="上固层前缩" min="-200" max="229"/><Parameter name="YHQJS" value="0" displayName="右后切角深" min="100" max="200"/><Parameter name="BBHD" value="5" displayName="背板厚度"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="ZCBFD" value="0" displayName="左侧板分段"/><Parameter name="ZCBJG" value="0" displayName="左侧板见光"/><Parameter name="ZCBHD" value="18" displayName="左侧板厚"/><Parameter name="JXHQS" value="0" displayName="脚线盒前缩" min="0" max="229"/><Parameter name="SHQJG" value="0" displayName="上后切角高" min="100" max="1200"/><Parameter name="YHQJK" value="0" displayName="右后切角宽" min="100" max="482"/><Parameter name="ZCBSDY" value="0" displayName="左侧板上倒圆"/><Parameter name="ZHQJ" value="0" displayName="是否左后切角"/><Parameter name="XGCQS" value="0" displayName="下固层前缩" min="0" max="229"/><Parameter name="YCBJG" value="0" displayName="右侧板见光"/><Parameter name="YCBHD" value="18" displayName="右侧板厚"/><Parameter name="ZHQJK" value="0" displayName="左后切角宽" min="100" max="482"/><Parameter name="YCBFD" value="0" displayName="右侧板分段"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="HJXHS" value="22" displayName="后脚线缩进" min="0" max="244"/><Parameter name="location" value="3" displayName="摆放方式"/><Parameter name="ZHQJS" value="0" displayName="左后切角深" min="100" max="200"/><Parameter name="JXHLX" value="0" displayName="脚线盒类型"/><Parameter name="JXHHS" value="0" displayName="脚线盒后缩" min="0" max="229"/></Parameters><ParamPlankPath/><Part id="4537F4EE-980F-4BA5-B335-A2A63AC7796E" isGroup="false" parentId="F5239586-7D37-41E3-A691-503842F4ADFA" type="ProductionParamModel" modelTypeId="1" productId="3FO3NRWVVC9Y" prodCatId="713" name="左侧板" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51559043642" W="18.0" D="295.0" H="2040.0"><Space sizeX="18.0" sizeY="295.0" sizeZ="2040.0" boxSizeX="18.0" boxSizeY="295.0" boxSizeZ="2040.0" absX="-1044.8579" absY="2629.0547" absZ="0.0" X="0.0" Y="-5.0" Z="0.0" CX="-291.0" CY="-2.5" CZ="0.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="295.0" displayName="深度" min="1" max="1200"/><Parameter name="BJBQ" value="1" displayName="板件标签"/><Parameter name="SHQJ" value="0" displayName="是否上后切角"/><Parameter name="H" value="2040.0" displayName="高度" min="1" max="2800"/><Parameter name="Buttcode" value="Y_CBL_001" displayName="厂商编码"/><Parameter name="SHQJG" value="0" displayName="上后切角高" min="0" max="1940"/><Parameter name="ZCBSDY" value="0" displayName="左侧板上倒圆" min="0" max="295"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="SHQJS" value="0" displayName="上后切角深" min="0" max="195"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="18.0" displayName="宽度" min="1" max="100"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="5dcf06fa-d22c-5040-b015-284c8fb57ea6" isGroup="false" parentId="4537F4EE-980F-4BA5-B335-A2A63AC7796E" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="2040.0" sizeY="295.0" sizeZ="18.0" boxSizeX="2040.0" boxSizeY="295.0" boxSizeZ="18.0" absX="-1035.8579" absY="2334.0547" absZ="2040.0" X="9.0" Y="-295.0" Z="2040.0" CX="-0.000044822693" CY="0.0" CZ="0.0" RX="0.0" RY="90.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="textureAngleDegree" value="0.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:1,&quot;radius&quot;:&quot;0.0&quot;},{&quot;position&quot;:&quot;0.0,295.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;2040.0,295.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;2040.0,0.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="0.0" Y="295.0"/><ParamPoint X="2040.0" Y="295.0"/><ParamPoint X="2040.0" Y="0.0"/></ParamPoints><ParamLines><ParamLine type="1" clockwise="true" minorArc="true" radius="0.0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part><Part id="56085A93-6A74-486B-A0ED-AF594A5159D3" isGroup="false" parentId="F5239586-7D37-41E3-A691-503842F4ADFA" type="ProductionParamModel" modelTypeId="1" productId="3FO3NS1DQFKJ" prodCatId="713" name="右侧板" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51341870624" W="18.0" D="295.0" H="2040.0"><Space sizeX="18.0" sizeY="295.0" sizeZ="2040.0" boxSizeX="18.0" boxSizeY="295.0" boxSizeZ="2040.0" absX="-462.8579" absY="2629.0547" absZ="0.0" X="582.0" Y="-5.0" Z="0.0" CX="291.0" CY="-2.5" CZ="0.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="295.0" displayName="深度" min="1" max="1200"/><Parameter name="BJBQ" value="2" displayName="板件标签"/><Parameter name="SHQJ" value="0" displayName="是否上后切角"/><Parameter name="H" value="2040.0" displayName="高度" min="1" max="2800"/><Parameter name="Buttcode" value="Y_CBR_001" displayName="厂商编码"/><Parameter name="SHQJG" value="0" displayName="上后切角高" min="0" max="1940"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="SHQJS" value="0" displayName="上后切角深" min="0" max="195"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="18.0" displayName="宽度" min="1" max="100"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="YCBSDY" value="0" displayName="右侧板上倒圆" min="0" max="295"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="d3d7c8b7-28cc-5b0a-bbbf-68d1b50adf56" isGroup="false" parentId="56085A93-6A74-486B-A0ED-AF594A5159D3" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="2040.0" sizeY="295.0" sizeZ="18.0" boxSizeX="2040.0" boxSizeY="295.0" boxSizeZ="18.0" absX="-453.8579" absY="2334.0547" absZ="2040.0" X="9.0" Y="-295.0" Z="2040.0" CX="-0.000044822693" CY="0.0" CZ="0.0" RX="0.0" RY="90.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="textureAngleDegree" value="0.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:1,&quot;radius&quot;:&quot;0.0&quot;},{&quot;position&quot;:&quot;0.0,295.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;2040.0,295.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;2040.0,0.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="0.0" Y="295.0"/><ParamPoint X="2040.0" Y="295.0"/><ParamPoint X="2040.0" Y="0.0"/></ParamPoints><ParamLines><ParamLine type="1" clockwise="true" minorArc="true" radius="0.0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part><Part id="EC14BFE9-8C0A-4C72-9818-64BCD6B007CA" isGroup="false" parentId="F5239586-7D37-41E3-A691-503842F4ADFA" type="ProductionParamModel" modelTypeId="1" productId="3FO3NRW8D86S" prodCatId="713" name="上固层" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51530791050" W="564.0" D="294.0" H="18.0"><Space sizeX="564.0" sizeY="294.0" sizeZ="18.0" boxSizeX="564.0" boxSizeY="294.0" boxSizeZ="18.0" absX="-1026.8579" absY="2629.0547" absZ="2022.0" X="18.0" Y="-5.0" Z="2022.0" CX="0.0" CY="-2.0" CZ="1011.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="YHQJ" value="0" displayName="是否右后切角"/><Parameter name="YHQJS" value="0" displayName="右后切角深" min="0" max="284"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="294.0" displayName="深度" min="10" max="1200"/><Parameter name="BJBQ" value="3" displayName="板件标签"/><Parameter name="H" value="18.0" displayName="高度"/><Parameter name="Buttcode" value="Y_CBT_001" displayName="厂商编码"/><Parameter name="YHQJK" value="0" displayName="右后切角宽" min="0" max="554"/><Parameter name="ZHQJ" value="0" displayName="是否左后切角"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="ZHQJK" value="0" displayName="左后切角宽" min="0" max="554"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="564.0" displayName="宽度" min="10" max="2800"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="ZHQJS" value="0" displayName="左后切角深" min="0" max="284"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="7dc81ad8-7819-5533-8faf-cb956cc24831" isGroup="false" parentId="EC14BFE9-8C0A-4C72-9818-64BCD6B007CA" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="564.0" sizeY="294.0" sizeZ="18.0" boxSizeX="564.0" boxSizeY="294.0" boxSizeZ="18.0" absX="-1026.8579" absY="2335.0547" absZ="2031.0" X="0.0" Y="-294.0" Z="9.0" CX="0.0" CY="0.0" CZ="0.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="bottomFaceMaterial" value="1053AG"/><Parameter name="textureAngleDegree" value="0.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;564.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;564.0,294.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;0.0,294.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionFaceMaterial" value="1053AG"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="564.0" Y="0.0"/><ParamPoint X="564.0" Y="294.0"/><ParamPoint X="0.0" Y="294.0"/></ParamPoints><ParamLines><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part><Part id="BF0A5267-D602-43C9-A5B0-BCD8E6029969" isGroup="false" parentId="F5239586-7D37-41E3-A691-503842F4ADFA" type="ProductionParamModel" modelTypeId="1" productId="3FO3NRSPC5FY" prodCatId="713" name="单元柜左右切角脚线盒" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51438406802" W="564.0" D="294.0" H="68.0"><Space sizeX="564.0" sizeY="294.0" sizeZ="68.0" boxSizeX="564.0" boxSizeY="294.0" boxSizeZ="68.0" absX="-1026.8579" absY="2629.0547" absZ="0.0" X="18.0" Y="-5.0" Z="0.0" CX="0.0" CY="-2.0" CZ="-986.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="YHQJ" value="0" displayName="是否右后切角"/><Parameter name="YHQJS" value="0" displayName="右后切角深" min="0" max="244"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="294.0" displayName="深度" min="50" max="1200"/><Parameter name="JXHD" value="18" displayName="脚线厚度"/><Parameter name="H" value="68.0" displayName="高度"/><Parameter name="JXGD" value="50" displayName="脚线高度" min="0" max="1200"/><Parameter name="XGCHD" value="18" displayName="下固层厚"/><Parameter name="YHQJK" value="0" displayName="右后切角宽" min="0" max="514"/><Parameter name="ZHQJ" value="0" displayName="是否左后切角"/><Parameter name="XGCQS" value="0" displayName="下固层前缩" min="0" max="244"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="ZHQJK" value="0" displayName="左后切角宽" min="0" max="514"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="564.0" displayName="宽度" min="50" max="2800"/><Parameter name="HJXHS" value="22" displayName="后脚线缩进" min="0" max="257"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="ZHQJS" value="0" displayName="左后切角深" min="0" max="244"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="JXHLX" value="0" displayName="脚线盒类型"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="B51C6EEE-06AF-42EE-8B9A-440115C11079" isGroup="false" parentId="BF0A5267-D602-43C9-A5B0-BCD8E6029969" type="ProductionParamModel" modelTypeId="1" productId="3FO3NS199DKK" prodCatId="713" name="下固层" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51330994966" W="564.0" D="294.0" H="18.0"><Space sizeX="564.0" sizeY="294.0" sizeZ="18.0" boxSizeX="564.0" boxSizeY="294.0" boxSizeZ="18.0" absX="-1026.8579" absY="2629.0547" absZ="50.0" X="0.0" Y="0.0" Z="50.0" CX="0.0" CY="0.0" CZ="25.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="YHQJ" value="0" displayName="是否右后切角"/><Parameter name="YHQJS" value="0" displayName="右后切角深" min="0" max="284"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="294.0" displayName="深度" min="10" max="1200"/><Parameter name="BJBQ" value="4" displayName="板件标签"/><Parameter name="H" value="18.0" displayName="高度"/><Parameter name="Buttcode" value="Y_CBB_001" displayName="厂商编码"/><Parameter name="YHQJK" value="0" displayName="右后切角宽" min="0" max="554"/><Parameter name="ZHQJ" value="0" displayName="是否左后切角"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="ZHQJK" value="0" displayName="左后切角宽" min="0" max="554"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="564.0" displayName="宽度" min="10" max="2800"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="ZHQJS" value="0" displayName="左后切角深" min="0" max="284"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="513e3e15-fadc-5eb0-b059-f500fcb50eed" isGroup="false" parentId="B51C6EEE-06AF-42EE-8B9A-440115C11079" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="564.0" sizeY="294.0" sizeZ="18.0" boxSizeX="564.0" boxSizeY="294.0" boxSizeZ="18.0" absX="-1026.8579" absY="2335.0547" absZ="59.0" X="0.0" Y="-294.0" Z="9.0" CX="0.0" CY="0.0" CZ="0.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="bottomFaceMaterial" value="1053AG"/><Parameter name="textureAngleDegree" value="0.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;564.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;564.0,294.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;0.0,294.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionFaceMaterial" value="1053AG"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="564.0" Y="0.0"/><ParamPoint X="564.0" Y="294.0"/><ParamPoint X="0.0" Y="294.0"/></ParamPoints><ParamLines><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part><Part id="FD25D0E3-F28B-49DA-9157-B5EE61410FD0" isGroup="false" parentId="BF0A5267-D602-43C9-A5B0-BCD8E6029969" type="ProductionParamModel" modelTypeId="1" productId="3FO3NS1TS31I" prodCatId="713" name="前脚线" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51356687462" W="564.0" D="18.0" H="50.0"><Space sizeX="564.0" sizeY="18.0" sizeZ="50.0" boxSizeX="564.0" boxSizeY="18.0" boxSizeZ="50.0" absX="-1026.8579" absY="2354.0547" absZ="0.0" X="0.0" Y="-275.0" Z="0.0" CX="0.0" CY="-137.0" CZ="-9.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="18.0" displayName="深度"/><Parameter name="BJBQ" value="7" displayName="板件标签"/><Parameter name="H" value="50.0" displayName="高度" min="10" max="1200"/><Parameter name="Buttcode" value="Y_JX_001" displayName="厂商编码"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="564.0" displayName="宽度" min="10" max="2800"/><Parameter name="textureAngleDegree" value="90" displayName="材质角度"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="a66f9bd2-acd4-57cf-812b-6e84457ad095" isGroup="false" parentId="FD25D0E3-F28B-49DA-9157-B5EE61410FD0" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="50.0" sizeY="564.0" sizeZ="18.0" boxSizeX="50.0" boxSizeY="564.0" boxSizeZ="18.0" absX="-462.8579" absY="2345.0547" absZ="0.0" X="564.0" Y="-8.999975" Z="-0.0000003934025" CX="0.0" CY="0.000011444092" CZ="0.0" RX="0.0" RY="-90.0" RZ="90.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="bottomFaceMaterial" value="1053AG"/><Parameter name="textureAngleDegree" value="90.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;50.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;50.0,564.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;0.0,564.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionFaceMaterial" value="1053AG"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="50.0" Y="0.0"/><ParamPoint X="50.0" Y="564.0"/><ParamPoint X="0.0" Y="564.0"/></ParamPoints><ParamLines><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part><Part id="A888972C-D80F-4C16-939B-3BB9BF35D08B" isGroup="false" parentId="BF0A5267-D602-43C9-A5B0-BCD8E6029969" type="ProductionParamModel" modelTypeId="1" productId="3FO3NRWL95BN" prodCatId="713" name="后脚线" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51548931259" W="564.0" D="18.0" H="50.0"><Space sizeX="564.0" sizeY="18.0" sizeZ="50.0" boxSizeX="564.0" boxSizeY="18.0" boxSizeZ="50.0" absX="-1026.8579" absY="2607.0547" absZ="0.0" X="0.0" Y="-22.0" Z="0.0" CX="0.0" CY="116.0" CZ="-9.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="18.0" displayName="深度"/><Parameter name="BJBQ" value="7" displayName="板件标签"/><Parameter name="H" value="50.0" displayName="高度" min="10" max="1200"/><Parameter name="Buttcode" value="Y_JX_001" displayName="厂商编码"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="564.0" displayName="宽度" min="10" max="2800"/><Parameter name="textureAngleDegree" value="90" displayName="材质角度"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="f997502e-118f-5e51-9443-e013caeaf65d" isGroup="false" parentId="A888972C-D80F-4C16-939B-3BB9BF35D08B" type="8" thickness="18.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="50.0" sizeY="564.0" sizeZ="18.0" boxSizeX="50.0" boxSizeY="564.0" boxSizeZ="18.0" absX="-462.8579" absY="2598.0547" absZ="0.0" X="564.0" Y="-8.999975" Z="-0.0000003934025" CX="0.0" CY="0.000011444092" CZ="0.0" RX="0.0" RY="-90.0" RZ="90.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="18.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="bottomFaceMaterial" value="1053AG"/><Parameter name="textureAngleDegree" value="90.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;50.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;50.0,564.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;0.0,564.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionFaceMaterial" value="1053AG"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="50.0" Y="0.0"/><ParamPoint X="50.0" Y="564.0"/><ParamPoint X="0.0" Y="564.0"/></ParamPoints><ParamLines><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part></Part><Part id="936DD9DD-2E7E-4F54-BEE2-35FE55DDAD3D" isGroup="false" parentId="F5239586-7D37-41E3-A691-503842F4ADFA" type="ProductionParamModel" modelTypeId="1" productId="3FO3NS1BVNK6" prodCatId="713" name="5mm背板" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃" jdProductId="51340180203" W="598.0" D="5.0" H="1985.0"><Space sizeX="598.0" sizeY="5.0" sizeZ="1985.0" boxSizeX="598.0" boxSizeY="5.0" boxSizeZ="1985.0" absX="-1043.8579" absY="2634.0547" absZ="52.5" X="1.0" Y="0.0" Z="52.5" CX="0.0" CY="147.5" CZ="25.0" RX="0.0" RY="0.0" RZ="0.0"/><Parameters><Parameter name="textureOffset" value="0,0" displayName="材质偏移"/><Parameter name="offset" value="0,0,0" displayName="偏移"/><Parameter name="D" value="5" displayName="深度"/><Parameter name="BJBQ" value="6" displayName="板件标签"/><Parameter name="H" value="1985.0" displayName="高度" min="10" max="2800"/><Parameter name="Buttcode" value="Y_BP_002" displayName="厂商编码"/><Parameter name="fit" value="{&quot;ruleId&quot;:&quot;3FO4K4VYBAIK&quot;}" displayName="适配规则"/><Parameter name="textureSize" value="," displayName="材质尺寸"/><Parameter name="CZ" value="1053AG" displayName="材质"/><Parameter name="W" value="598.0" displayName="宽度" min="10" max="2400"/><Parameter name="textureAngleDegree" value="0" displayName="材质角度"/><Parameter name="texturePavingStyle" value="0" displayName="铺贴方式"/><Parameter name="sectionTextureAngleDegree" value="0" displayName="侧边材质角度"/></Parameters><ParamPlankPath/><Part id="e079d508-2cca-5f5f-8eb0-29634db0e7bd" isGroup="false" parentId="936DD9DD-2E7E-4F54-BEE2-35FE55DDAD3D" type="8" thickness="5.0" modelTypeId="3" name="平面板件-1" textureId="3FO3NRWVE8QU" textureName="ML1053AG_可可胡桃"><Space sizeX="1985.0" sizeY="598.0" sizeZ="5.0" boxSizeX="1985.0" boxSizeY="598.0" boxSizeZ="5.0" absX="-445.8579" absY="2631.5547" absZ="52.5" X="598.0" Y="-2.4999738" Z="-0.000000109278474" CX="0.0" CY="-0.00003027916" CZ="0.0" RX="0.0" RY="-90.0" RZ="90.0"/><Parameters><Parameter name="textureOffset" value="0.0,0.0"/><Parameter name="thickness" value="5.0"/><Parameter name="CZ" value="1053AG"/><Parameter name="bottomFaceMaterial" value="1053AG"/><Parameter name="textureAngleDegree" value="0.0"/><Parameter name="texturePavingStyle" value="0"/><Parameter name="plankPath" value="{&quot;valueRef&quot;:null,&quot;path&quot;:{&quot;paramPoints&quot;:[{&quot;position&quot;:&quot;0.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;1985.0,0.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;1985.0,598.0&quot;,&quot;type&quot;:0},{&quot;position&quot;:&quot;0.0,598.0&quot;,&quot;type&quot;:0}],&quot;paramPathLines&quot;:[{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0},{&quot;type&quot;:0}],&quot;ignore&quot;:&quot;false&quot;,&quot;bizProperties&quot;:[]},&quot;holes&quot;:[],&quot;slots&quot;:[],&quot;arrays&quot;:{}}"/><Parameter name="sectionFaceMaterial" value="1053AG"/><Parameter name="sectionTextureAngleDegree" value="0.0"/></Parameters><ParamPlankPath><ParamPoints><ParamPoint X="0.0" Y="0.0"/><ParamPoint X="1985.0" Y="0.0"/><ParamPoint X="1985.0" Y="598.0"/><ParamPoint X="0.0" Y="598.0"/></ParamPoints><ParamLines><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/><ParamLine type="0"/></ParamLines></ParamPlankPath></Part></Part></Part></Part></Parts><Structures><Walls><Wall id="12" modelingType="sweep" thickness="240.0" intervalS="0.0" intervalE="2800.0" tp="LS2" P0X="-5294.035599342394" P0Y="-775.9453336313322" P1X="-5294.035599342394" P1Y="2754.0546663686678"/><Wall id="13" modelingType="sweep" thickness="240.0" intervalS="0.0" intervalE="2800.0" tp="LS2" P0X="-5294.035599342394" P0Y="2754.0546663686678" P1X="3335.9644006576063" P1Y="2754.0546663686678"/><Wall id="14" modelingType="sweep" thickness="240.0" intervalS="0.0" intervalE="2800.0" tp="LS2" P0X="3335.9644006576063" P0Y="2754.0546663686678" P1X="3335.9644006576063" P1Y="-775.9453336313322"/><Wall id="15" modelingType="sweep" thickness="240.0" intervalS="0.0" intervalE="2800.0" tp="LS2" P0X="3335.9644006576063" P0Y="-775.9453336313322" P1X="-5294.035599342394" P1Y="-775.9453336313322"/><Wall id="16" modelingType="sweep" thickness="240.0" intervalS="-120.0" intervalE="0.0" tp="LS2" P0X="-5294.035599342394" P0Y="-775.9453336313322" P1X="-5294.035599342394" P1Y="2754.0546663686678"/><Wall id="17" modelingType="sweep" thickness="240.0" intervalS="-120.0" intervalE="0.0" tp="LS2" P0X="-5294.035599342394" P0Y="2754.0546663686678" P1X="3335.9644006576063" P1Y="2754.0546663686678"/><Wall id="18" modelingType="sweep" thickness="240.0" intervalS="-120.0" intervalE="0.0" tp="LS2" P0X="3335.9644006576063" P0Y="2754.0546663686678" P1X="3335.9644006576063" P1Y="-775.9453336313322"/><Wall id="19" modelingType="sweep" thickness="240.0" intervalS="-120.0" intervalE="0.0" tp="LS2" P0X="3335.9644006576063" P0Y="-775.9453336313322" P1X="-5294.035599342394" P1Y="-775.9453336313322"/></Walls></Structures><Soft/></Root>
]]

local _M = {}
-- 检查单元下没有侧板（顶底）的时候 背板左右（上下）是否有是否有素材 若无 则报错
-- 1、背板（Parameter里面会有一个name=BJBQ的参数6指的是背板）
-- 2、判断柜子是否有侧板（Part节点找）、如果没有侧板，背板往左右外扩1mm，如果没有其他板件，就报错
-- 3、判断柜子是否有顶底板（Part节点找）、如果没有侧板背板往上下外扩1mm，如果没有其他板件，就报错

-- 加载所需模块
local xml_search_in_part = require("lib.xml-search-in-part")
local geometry = require("lib/geometry-engine-ins")
local xml_search = require("lib.xml-search-in-node")
local xmlua = require("xmlua")
local json = require("cjson")
local check_rule_result = require("lib.check-rule-result")

local relation_grouping = require("lib.relation-grouping")
local filter_unit_deep = require("dynamic.filter-unit-deep")
local filter_unit_top = require("dynamic.filter-unit-top")
local array_util = require("dynamic.array-util")

-- 定义规则执行入口
function _M.dowork(root, target, context)

	-- 存储结果
	local rule_result = check_rule_result.new("提示语", check_rule_result.LEVEL.INFO)
	local result = {}
	local logs = {}

    local xml_node = xmlua.XML.parse(root)
    local engine_instance = geometry:get_current_instance()
    local engine_object = engine_instance:create_engine_object(root)

    -- 1. 获取所有单元节点
	local xml_parts = xml_node:search("/Root/Parts")
	local xml_units = xml_search_in_part.get_part_xmllist_bykvs_in_part(xml_parts, "Parameters/Parameter",
		{
			{key = "name", value = "MKBQ"},
			{key = "value", value = "1"}
		}
	)
	if not xml_units or #xml_units == 0 then
        return rule_result:pass("获取Part节点失败")
    end

	local room_height = 0
	-- 通过 root/Structures/Walls/Wall的属性 intervalE 找到墙体高
	local walls = xml_node:search("/Root/Structures/Walls/Wall")
	if not walls or #walls == 0 then
		return rule_result:pass("获取Wall节点失败")
	end

	for _, wall in ipairs(walls) do
		local intervalE = tonumber(wall:get_attribute("intervalE")) or 0
		if intervalE > room_height then
			room_height = intervalE
		end
	end
	local safe_units = {}
	local unit_data_list = {}
	-- 遍历units 收集 单元id列表
	-- 收集满足深度检查的单元
	-- 收集满足到顶检查的单元
    for _, unit in ipairs(xml_units) do
		local left_side_boards = xml_search_in_part.get_part_xmllist_bykvs_in_part(unit, "Parameters/Parameter", {{key = "name", value = "BJBQ"}, {key = "value", value = "1"}})
		local right_side_boards = xml_search_in_part.get_part_xmllist_bykvs_in_part(unit, "Parameters/Parameter", {{key = "name", value = "BJBQ"}, {key = "value", value = "2"}})
		local left_d = 0
		local right_d = 0
		if left_side_boards and #left_side_boards > 0 then
			left_d = left_side_boards[1]:get_attribute("D") or 0
		end

		if right_side_boards and #right_side_boards > 0 then
			right_d = right_side_boards[1]:get_attribute("D") or 0
		end

		local unit_data = {
			id = unit:get_attribute("id"),
			name = unit:get_attribute("name"),
			h = tonumber(unit:get_attribute("H")) or 0,
			z = tonumber(unit:get_attribute("Z")) or 0,
			left_d = left_d,
			right_d = right_d,
		}

		table.insert(unit_data_list, unit_data)
	end

	-- 深度检查
	local deep_rst = filter_unit_deep.filter_unit_Deep(unit_data_list, 450)
	local deep_passed = deep_rst.passed
	local deep_failed = deep_rst.failed
	table.insert(logs, string.format("深度检查结果	 成功: %d, 失败: %d", #deep_rst.passed, #deep_rst.failed))

	-- 到顶检查
	local top_rst = filter_unit_top.filter_unit_Top(deep_failed, room_height)
	local top_passed = top_rst.passed
	local top_failed = top_rst.failed
	table.insert(logs, string.format("到顶检查结果	 成功: %d, 失败: %d", #top_rst.passed, #top_rst.failed))

	-- 顶封板检查
	local failed_untop_ids = {}
	for _, unit_data in ipairs(top_failed) do
		table.insert(failed_untop_ids, unit_data.id)
	end

    local top_cover_passed = {}
    local top_cover_failed = {}
    local top_relations = engine_object:get_objects_distance("prodCatId", "==", "713" , {"up"}, "<", 1, "prodCatId", "==", "713")
    if top_relations and #top_relations > 0 then
        local top_cover_rst = filter_unit_top.filter_unit_Top_cover(unit_data_list, top_relations)
        if top_cover_rst and top_cover_rst.passed and #top_cover_rst.passed > 0 then
            top_cover_passed = top_cover_rst.passed
        end
        if top_cover_rst and top_cover_rst.failed and #top_cover_rst.failed > 0 then
            top_cover_failed = top_cover_rst.failed
        end
    end

    table.insert(logs, string.format("顶封板检查结果	 成功: %d, 失败: %d", #top_cover_passed, #top_cover_failed))

	-- 收集所有安全单元
	-- 合并深度检查通过和到顶检查通过的单元到安全单元列表
	for _, unit_data in ipairs(deep_passed) do
		safe_units[unit_data.id] = true
	end

	for _, unit_data in ipairs(top_passed) do
		safe_units[unit_data.id] = true
	end

	for _, unit_data in ipairs(top_cover_passed) do
		safe_units[unit_data.id] = true
	end

	-- 组合校验最后一波
	-- 获取所有单元的关系
	-- 查找所有 BQ==1 之间的关系
	-- 吊柜往下 判断有没有相邻的地柜`
	-- 柜体往上 判断有没有顶封板
    local relations = engine_object:get_objects_distance("MKBQ", "==", "1" , {"down", "up", "left", "right", "front", "back"}, "<", 1, "MKBQ", "==", "1")
	local grouped_relations = relation_grouping.group_relations(relations)
	local group_units = {}
	ngx.log(ngx.INFO, "grouped_relations: ", json.encode(grouped_relations))
	for _, group in ipairs(grouped_relations) do
		local group_has_safe = false

		-- 组合内任意一个单元安全 则组合内所有单元都安全
		for _, unit_id in ipairs(group) do
			if safe_units[unit_id] then
				group_has_safe = true
			end

			-- 统计组合的uid列表
			if #group > 1 then
				table.insert(group_units, unit_id)
			end
		end		

		if group_has_safe then
			for _, unit_id in ipairs(group) do
				safe_units[unit_id] = true
			end
		end
	end		

	
	local safe_count = 0
	for _, unit_data in ipairs(unit_data_list) do
		local is_group = array_util.table_contains(group_units, unit_data.id)
		local is_safe = safe_units[unit_data.id] or false

		if not is_safe then
			table.insert(result, {
				prompt = string.format("单元 %s 是否安全: %s, 是否是组合: %s", unit_data.name, is_safe and "安全" or "不安全", is_group and "是" or "否"),
				related_ids = {unit_data.id}
			})
		end

		if safe_units[unit_data.id] then
			safe_count = safe_count + 1
		end
	end

	table.insert(logs, string.format("防倾斜检查汇总: %d, 不安全总数: %d", #unit_data_list, #result))

    return rule_result:error(result, logs)
end

-- 如果直接运行此文件，则执行自测试
if ngx then
    local res, msg = _M.dowork(test_xml)
    ngx.say(json.encode(res))
end

return _M
