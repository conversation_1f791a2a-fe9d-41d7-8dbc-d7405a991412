-- 常用的几何引擎工具类  几何引擎由外部传入

local _M = {}

-- 辅助函数：判断id_a 和 集合 ids_B 的角度是否满足输入值 返回满足条件的ids
function _M.filter_angle_boards(target_id, ids_B, engine_object, tar_angle)

	local result_table = {}
	if ids_B and #ids_B > 0 then
		for _, board_id in ipairs(ids_B) do
			local angle = engine_object:getObjectsAngle({{
				id1 = target_id,
				id2 = board_id
			}})
			
			if angle and angle[1].angle == tar_angle then
				table.insert(result_table, board_id)
			end
		end
	end
	return result_table
end

return _M

