local rule_test = require("test.orderrule.rule-test-chou-ti-dao-gui")
local cabinet = require("test.data.rule-data9")

local xmlua = require("xmlua")
local geometry_engine = require("lib.geometry-engine-ins")
local attribute_appender = require("lib.attribute-appender")


-- 解析 XML 文件
local xml_content = cabinet.getCabinetXml()
local document = xmlua.XML.parse(xml_content)
local root = document:root()
-- 初始化规则引擎
-- local engine_instance = geometry_engine:get_current_instance()
-- engine_instance:create_engine_object(xml_content)



-- rule-test9 测试
local target = root:search("//Part[@id='6430591C-2275-443E-BFBC-819B9F4CA9A9']")
-- rule-ce-ban-da-jie-guan-xi 测试
-- local target = root:search("//Part[@id='4169a68a-c306-5cc1-a1e9-e063623c6d17']")







-- 查找所有带有id的Part节点并附加属性
local part_nodes = document:search("//Part[@id]")
for _, node in ipairs(part_nodes) do
    attribute_appender.append_attributes(node)
end

if #target > 0 then
    
    local context = {}
    context.paramCode = "CTBS1"

    local result, error_msg = rule_test.dowork(root, target[1], context)
    if result then
        print("规则执行结果：" .. result)
    else
        print("规则执行错误：" .. error_msg)
    end
end



-- -- 使用 os.clock() 记录开始时间（CPU时间，单位秒）
-- local start_time = os.clock()
-- -- 遍历XML 10000次
-- for i = 1, 100 do
--     -- 1. 查找特定ID的部件
--     local parts_by_id = root:search("//Part[@ID='1']")

--     -- 2. 查找特定材质的部件
--     local parts_by_material = root:search("//Part[@Material='142JA_直纹柚木']")

--     -- 3. 查找特定尺寸的部件
--     local parts_by_size = root:search("//Part[@W='700' and @H='2000']")

--     -- 4. 查找特定位置的部件
--     local parts_by_position = root:search("//Part[Values[@PX='0' and @PY='0']]")

--     -- 5. 查找特定旋转角度的部件
--     local parts_by_rotation = root:search("//Part[Values[@RX='0' and @RY='0']]")
-- end

-- -- 计算并打印执行时间（精确到毫秒）
-- local end_time = os.clock()
-- local elapsed_time = (end_time - start_time) * 1000 -- 转换为毫秒
-- print(string.format("简单XPath查询100次耗时: %.2f 毫秒", elapsed_time))
