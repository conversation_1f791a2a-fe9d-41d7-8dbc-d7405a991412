-- 如果门后有贴镜，则检查贴镜是否与门内的板件干涉

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local geometry_engine = require('lib.oop-rule-engine')
local check_rule_result = require("lib.check-rule-result")

-- 定义规则执行入口
local M = {}

-- 定义规则执行入口
function M.dowork(root, target, context)
	local rule_result = check_rule_result.new("门后贴镜检查", check_rule_result.LEVEL.INFO)

	if not root then
		return rule_result:pass("xml is nil")
	end

	-- 存储结果
	local result = {}
    local logs = {}

	-- 几何引擎查找所有有干涉的对象
	local engine_object = geometry_engine.current()

	-- 获取所有的门
	local doors_xml = root:search("//Part[@prodCatId='498']")
	if not doors_xml or #doors_xml == 0 then
		return rule_result:pass("没有找到任何 prodCatId=498 的Part节点")
	end

	-- ngx.log(ngx.INFO, "------------------- doors_xml: ", #doors_xml)

	for _, door_xml in ipairs(doors_xml) do
		local MBTJ = xml_search.get_value_by_child_node(door_xml, "Parameter", "name", "MBTJ")
		if MBTJ ~= "1" and MBTJ ~= "3" and MBTJ ~= "4" then
			goto continue
		end

		-- ngx.log(ngx.INFO, "------------------- MBTJ: ", MBTJ)

		local MKBQ = xml_search.get_value_by_child_node(door_xml, "Parameter", "name", "MKBQ")
		if MKBQ ~= "3" then
			goto continue
		end

		-- ngx.log(ngx.INFO, "------------------- MKBQ: ", MKBQ)

		local tiejings = door_xml:search(".//Part[@prodCatId='785']")
		if not tiejings or #tiejings == 0 then
			goto continue
		end

		-- ngx.log(ngx.INFO, "------------------- tiejing: ", #tiejings)

		local door_uuid = door_xml:get_attribute("id")
		local door_name = door_xml:get_attribute("name")

		local tiejing_d = xml_search.get_value_by_child_node(tiejings[1], "Parameter", "name", "D")
		local tiejing_name = tiejings[1]:get_attribute("name")
		local door_intersect_inside_board = engine_object:get_door_intersect_inside_board(door_uuid, 25 + tonumber(tiejing_d))
		if #door_intersect_inside_board > 0 then
			for _, door_intersect_inside_board_xml in ipairs(door_intersect_inside_board) do
				local intersect_inside_board_uuid = door_intersect_inside_board_xml.uuid
				-- local intersect_inside_board_name = door_intersect_inside_board_xml.get_attribute("name")

				table.insert(result, {
					prompt = string.format("门 %s 后有贴镜 门后组件需内进≥25，请调整", door_name),
					related_ids = {door_uuid, intersect_inside_board_uuid}
				})
			end
		end

		::continue::
	end

    table.insert(logs, "门后贴镜检查完成，共有" .. #result .. "个对象干涉")
    return rule_result:error(result, logs)
end

return M