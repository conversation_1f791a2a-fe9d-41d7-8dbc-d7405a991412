=encoding utf-8

B<DO NOT EDIT THIS REPO DIRECTLY, CODE ARE AUTOMATICALLY GENERATED FROM TEMPLATES>


=head1 Install


There are some quirks when compiling the refactored stream module.

You will need NGINX 1.13.x for it to work as we targeted the refactor toward
that version only. The L<1.13.x branch|https://github.com/openresty/openresty/tree/1.13.x>
of OpenResty should serve this well.

You need to turn off FFI when compiling (for the time being) as not all FFI functions got
their respective guard.

Here is what I use for compiling:


    ./configure --prefix=/home/<USER>/openresty-stream-build \
                --with-stream --with-stream_ssl_module
                --add-module=/home/<USER>/orinc/stream-lua-nginx-module
                -j4 --with-cc-opt='-DNGX_LUA_NO_FFI_API' --with-debug


=head1 Status

The project can run simple "Hello, World" without any problem or Valgrind warnings.

I have also tested timer and variable support and they appears to work as well.

The only feature that has not been ported is the cosocket API due to it's
complexity. This does makes the module somewhat useless as input from user can not be read by
Lua programs right now. I will spend some time to sort this out shortly.

I will spend some time debugging and get the test suite running next.
