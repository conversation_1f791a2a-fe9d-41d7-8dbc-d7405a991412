=encoding utf-8


=head1 Name

ngx.req - Lua API for HTTP request handling.


=head1 Status

This Lua module is production ready.


=head1 Synopsis


    local ngx_req = require "ngx.req"
    
    -- add_header
    ngx_req.add_header("Foo", "bar")
    ngx_req.add_header("Foo", "baz")
    
    --> there will be two new headers in the HTTP request:
    --> Foo: bar and Foo: baz




=head1 Description

This module provides a Lua API to handle HTTP requests.




=head1 Methods

All methods provided by this module are static (or module-level). That is, you
do not need an object (or instance) to call these methods.




=head2 add_header

B<syntax:> I<ngx_req.add_header(header_name, header_value)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>>

This method adds the specified header and its value to the current
request. It works similarly as
L<ngx.req.set_header|https://github.com/openresty/lua-nginx-module#ngxreqset_header>,
with the exception that when the header already exists, the specified value(s)
will be appended instead of overriden.

The first argument C<header_name> must be a non-empty string.

When the specified C<header_name> is a builtin header (e.g. C<User-Agent>), this
method will override its values.

The C<header_value> argument can either be a string or a non-empty, array-like
table. A C<nil> or empty table value will cause this function to throw an error.

This feature was first introduced in the C<v0.1.18> release.




=head1 Community




=head2 English Mailing List

The L<openresty-en|https://groups.google.com/group/openresty-en> mailing list
is for English speakers.




=head2 Chinese Mailing List

The L<openresty|https://groups.google.com/group/openresty> mailing list is for
Chinese speakers.




=head1 Bugs and Patches

Please report bugs or submit patches by


=over


=item 1.

creating a ticket on the L<GitHub Issue Tracker|https://github.com/openresty/lua-resty-core/issues>,

=item 2.

or posting to the L<OpenResty community>.


=back




=head1 Copyright and License

This module is licensed under the BSD license.

Copyright (C) 2016-2019, by Yichun "agentzh" Zhang, OpenResty Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:


=over


=item *

Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.


=back


=over


=item *

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

library L<lua-resty-core|https://github.com/openresty/lua-resty-core>

=item *

the ngx_lua module: https://github.com/openresty/lua-nginx-module

=item *

OpenResty: https://openresty.org


=back



