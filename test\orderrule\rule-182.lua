-- 计算生产参数 - 模块：平板三边抽屉，厂商：三边抽芯，参数：CTBS1：抽侧标识，CTBS2：抽后标识，CTBS3：抽底标识

local _M = {}

local xml_search = require("lib.xml-search-in-node")

-- 抽屉导轨类型枚举
local DRAWER_GUIDE_TYPE = {
    [11] = {name = "国产三节GC3", code = "GC3", size = 8},
    [12] = {name = "国产三节GC3", code = "GC3", size = 10},
    [13] = {name = "国产三节GC3", code = "GC3", size = 12},
    [14] = {name = "国产三节GC3", code = "GC3", size = 14},
    [15] = {name = "国产三节GC3", code = "GC3", size = 16},
    [16] = {name = "国产三节GC3", code = "GC3", size = 18},
    [72] = {name = "全拉阻尼QLZ", code = "QLZ", size = 10},
    [73] = {name = "全拉阻尼QLZ", code = "QLZ", size = 12},
    [74] = {name = "全拉阻尼QLZ", code = "QLZ", size = 14},
    [75] = {name = "全拉阻尼QLZ", code = "QLZ", size = 16},
    [76] = {name = "全拉阻尼QLZ", code = "QLZ", size = 18},
    [82] = {name = "小全拉托XQL", code = "XQL", size = 10},
    [83] = {name = "小全拉托XQL", code = "XQL", size = 12},
    [84] = {name = "小全拉托XQL", code = "XQL", size = 14},
    [85] = {name = "小全拉托XQL", code = "XQL", size = 16},
    [86] = {name = "小全拉托XQL", code = "XQL", size = 18},
    [93] = {name = "全拉按弹QLA", code = "QLA", size = 12},
    [94] = {name = "全拉按弹QLA", code = "QLA", size = 14},
    [95] = {name = "全拉按弹QLA", code = "QLA", size = 16},
    [96] = {name = "全拉按弹QLA", code = "QLA", size = 18},
    [722] = {name = "百隆全拉阻尼BLQLZ", code = "BLQLZ", size = 10},
    [723] = {name = "百隆全拉阻尼BLQLZ", code = "BLQLZ", size = 12},
    [724] = {name = "百隆全拉阻尼BLQLZ", code = "BLQLZ", size = 14},
    [725] = {name = "百隆全拉阻尼BLQLZ", code = "BLQLZ", size = 16},
    [726] = {name = "百隆全拉阻尼BLQLZ", code = "BLQLZ", size = 18},
    [773] = {name = "百隆全拉按弹BLQLAT", code = "BLQLAT", size = 12},
    [774] = {name = "百隆全拉按弹BLQLAT", code = "BLQLAT", size = 14},
    [775] = {name = "百隆全拉按弹BLQLAT", code = "BLQLAT", size = 16},
    [776] = {name = "百隆全拉按弹BLQLAT", code = "BLQLAT", size = 18}
}

-- 定义规则执行入口
function _M.dowork(root, target, context)

	-- 存储结果
	local result = nil

	-- 生产参数
	local paramCode = context.paramCode
	local type = ""
	-- 根据参数代码设置类型
	if paramCode == "CTBS1" then
		type = "CC" -- 抽侧
	elseif paramCode == "CTBS2" then
		type = "CH" -- 抽后
	elseif paramCode == "CTBS3" then
		type = "CD" -- 抽底
	else
		return nil, "参数代码不匹配已知类型"
	end

	local target_id = target.id --具体要输出的抽屉id 目前传参进来的是抽芯xml
    

	-- 2. 获取所有抽屉
	local all_drawer_xml = xml_search.get_childs_node_by_kvs(root, "Part", "MKBQ", "4")
	if not all_drawer_xml or #all_drawer_xml == 0 then
		return result, "没有找到任何 抽屉节点"
	end

	-- 3. 按DGLX分组
	local drawer_group_map = {}
	for _, drawer_xml in ipairs(all_drawer_xml) do
		local dglx = xml_search.get_value_by_child_node(drawer_xml, "Parameter", "name", "DGLX")
		if dglx then
			if not drawer_group_map[dglx] then
				drawer_group_map[dglx] = {}
			end
			table.insert(drawer_group_map[dglx], drawer_xml)
		end
	end

	-- 3. 遍历每个抽屉组 收集数据
	for dglx, drawer_xmls in pairs(drawer_group_map) do
		-- 3.1 收集抽屉深度
		for index, drawer_xml in ipairs(drawer_xmls) do
			
			local chou_xin_xml = xml_search.get_childs_node_by_kvs(drawer_xml, "Part", "id", target_id)
			if chou_xin_xml then
				local s_rail = tonumber(target.S_RAIL)
				if not s_rail then
					return nil, "获取不到抽屉导轨参数S_RAIL"
				end
				local guide_info = DRAWER_GUIDE_TYPE[s_rail]
				if not guide_info then
					return nil, "未找到对应的导轨类型信息"
				end
				
				--  格式: code:type:size:index
				result = string.format("%s:%s:%s:%s", guide_info.code, type, guide_info.size, index)
				break
			end			
		end
	end

    return result
end

return _M
