具体实现为`lib/zstd.lua`脚本。

# 特别注意

- `zstd`工具所有的方法都是使用`:`进行方法调用，这里不能使用`.`进行调用。
- 使用完毕后必须调用`zstd:free()`释放内存，否则会导致内存泄漏。
- 如果仅在`Redis`场景下使用，那么不需要我们引入和使用`zstd`，直接使用`redisclient`提供的`zstd_get`和`zstd_set`方法即可。

# 压缩

```lua

-- 加载 zstd 模块
local zstandard = require "lib/zstd"
-- 实例化
local zstd = zstandard:new()
-- 需要压缩的文本
local text = string.rep("ABCD", 1000)
-- 执行压缩。第一个返回值为压缩后的二进制字符串（可以直接写入 redis），第二个返回值为压缩操作是否发生异常
local data, err = zstd:compress(text)
if err ~= nil then
    error('压缩动作发生了异常')
end
-- 最后必须释放占用的内存
zstd:free()

```

# 解压缩

```lua

-- 加载 zstd 模块
local zstandard = require "lib/zstd"
-- 实例化
local zstd = zstandard:new()
-- 需要解压缩的二进制文本
local data = zstd:compress('abcdefg')
-- 执行解压缩。第一个返回值为解压缩后的字符串，第二个返回值为解压缩操作是否发生异常
local text, err = zstd:decompress(data)
if err ~= nil then
    error('解压缩动作发生了异常')
end
-- 最后必须释放占用的内存
zstd:free()

assert(text == 'abcdefg')

```

# 测试用例

参考`test/zstd-test.lua`脚本，可以直接运行。
