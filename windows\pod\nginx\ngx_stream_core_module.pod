=encoding utf-8

=head1 NAME

ngx_stream_core_module - Module ngx_stream_core_module




=head1



The C<ngx_stream_core_module> module
is available since version 1.9.0.
This module is not built by default, it should be enabled with the
C<--with-stream>
configuration parameter.




=head1 Example Configuration




    
    worker_processes auto;
    
    error_log /var/log/nginx/error.log info;
    
    events {
        worker_connections  1024;
    }
    
    stream {
        upstream backend {
            hash $remote_addr consistent;
    
            server backend1.example.com:12345 weight=5;
            server 127.0.0.1:12345            max_fails=3 fail_timeout=30s;
            server unix:/tmp/backend3;
        }
    
        upstream dns {
           server 192.168.0.1:53535;
           server dns.example.com:53;
        }
    
        server {
            listen 12345;
            proxy_connect_timeout 1s;
            proxy_timeout 3s;
            proxy_pass backend;
        }
    
        server {
            listen 127.0.0.1:53 udp reuseport;
            proxy_timeout 20s;
            proxy_pass dns;
        }
    
        server {
            listen [::1]:12345;
            proxy_pass unix:/tmp/stream.socket;
        }
    }






=head1 Directives

=head2 listen


B<syntax:> listen I<
    I<C<address>>:I<C<port>>
    [C<default_server>]
    [C<ssl>]
    [C<udp>]
    [C<proxy_protocol>]
    [C<setfib>=I<C<number>>]
    [C<fastopen>=I<C<number>>]
    [C<backlog>=I<C<number>>]
    [C<rcvbuf>=I<C<size>>]
    [C<sndbuf>=I<C<size>>]
    [C<accept_filter>=I<C<filter>>]
    [C<deferred>]
    [C<bind>]
    [C<ipv6only>=C<on>E<verbar>C<off>]
    [C<reuseport>]
    [C<so_keepalive>=C<on>E<verbar>C<off>E<verbar>[I<C<keepidle>>]:[I<C<keepintvl>>]:[I<C<keepcnt>>]]>



B<context:> I<server>





Sets the I<C<address>> and I<C<port>> for the socket
on which the server will accept connections.
It is possible to specify just the port.
The address can also be a hostname, for example:

    
    listen 127.0.0.1:12345;
    listen *:12345;
    listen 12345;     # same as *:12345
    listen localhost:12345;


IPv6 addresses are specified in square brackets:

    
    listen [::1]:12345;
    listen [::]:12345;


UNIX-domain sockets are specified with the “C<unix:>”
prefix:

    
    listen unix:/var/run/nginx.sock;







Port ranges (1.15.10) are specified with the
first and last port separated by a hyphen:

    
    listen 127.0.0.1:12345-12399;
    listen 12345-12399;







The C<default_server> parameter, if present,
will cause the server to become the default server for the specified
I<C<address>>:I<C<port>> pair (1.25.5).
If none of the directives have the C<default_server>
parameter then the first server with the
I<C<address>>:I<C<port>> pair will be
the default server for this pair.





The C<ssl> parameter allows specifying that all
connections accepted on this port should work in SSL mode.





The C<udp> parameter configures a listening socket
for working with datagrams (1.9.13).
In order to handle packets from the same address and port in the same session,
the C<reuseport> parameter
should also be specified.





The C<proxy_protocol> parameter (1.11.4)
allows specifying that all connections accepted on this port should use the
L<PROXY
protocol|http://www.haproxy.org/download/1.8/doc/proxy-protocol.txt>.

B<NOTE>

The PROXY protocol version 2 is supported since version 1.13.11.






The C<listen> directive
can have several additional parameters specific to socket-related system calls.
These parameters can be specified in any
C<listen> directive, but only once for a given
I<C<address>>:I<C<port>> pair.

=over



=item 
C<setfib>=I<C<number>>





this parameter (1.25.5) sets the associated routing table, FIB
(the C<SO_SETFIB> option) for the listening socket.
This currently works only on FreeBSD.



=item 
C<fastopen>=I<C<number>>





enables
“L<TCP Fast Open|http://en.wikipedia.org/wiki/TCP_Fast_Open>”
for the listening socket (1.21.0) and
L<limits|https://datatracker.ietf.org/doc/html/rfc7413#section-5.1>
the maximum length for the queue of connections that have not yet completed
the three-way handshake.

B<NOTE>

Do not enable this feature unless the server can handle
receiving the
L<same SYN packet with data|https://datatracker.ietf.org/doc/html/rfc7413#section-6.1> more than once.




=item 
C<backlog>=I<C<number>>





sets the C<backlog> parameter in the
C<listen> call that limits
the maximum length for the queue of pending connections (1.9.2).
By default,
C<backlog> is set to -1 on FreeBSD, DragonFly BSD, and macOS,
and to 511 on other platforms.



=item 
C<rcvbuf>=I<C<size>>





sets the receive buffer size
(the C<SO_RCVBUF> option) for the listening socket (1.11.13).



=item 
C<sndbuf>=I<C<size>>





sets the send buffer size
(the C<SO_SNDBUF> option) for the listening socket (1.11.13).



=item 
C<accept_filter>=I<C<filter>>





sets the name of accept filter
(the C<SO_ACCEPTFILTER> option) for the listening socket
that filters incoming connections before passing them to
C<accept> (1.25.5).
This works only on FreeBSD and NetBSD 5.0+.
Possible values are
L<dataready|http://man.freebsd.org/accf_data>
and
L<httpready|http://man.freebsd.org/accf_http>.



=item 
C<deferred>





instructs to use a deferred C<accept>
(the C<TCP_DEFER_ACCEPT> socket option) on Linux (1.25.5).



=item 
C<bind>





this parameter instructs to make a separate C<bind>
call for a given address:port pair.
The fact is that if there are several C<listen> directives with
the same port but different addresses, and one of the
C<listen> directives listens on all addresses
for the given port (C<*:>I<C<port>>), nginx will
C<bind> only to C<*:>I<C<port>>.
It should be noted that the C<getsockname> system call will be
made in this case to determine the address that accepted the connection.
If the C<setfib>,
C<fastopen>,
C<backlog>, C<rcvbuf>,
C<sndbuf>, C<accept_filter>,
C<deferred>, C<ipv6only>,
C<reuseport>,
or C<so_keepalive> parameters
are used then for a given
I<C<address>>:I<C<port>> pair
a separate C<bind> call will always be made.



=item 
C<ipv6only>=C<on>E<verbar>C<off>





this parameter determines
(via the C<IPV6_V6ONLY> socket option)
whether an IPv6 socket listening on a wildcard address C<[::]>
will accept only IPv6 connections or both IPv6 and IPv4 connections.
This parameter is turned on by default.
It can only be set once on start.



=item 
C<reuseport>





this parameter (1.9.1) instructs to create an individual listening socket
for each worker process
(using the
C<SO_REUSEPORT> socket option on Linux 3.9+ and DragonFly BSD,
or C<SO_REUSEPORT_LB> on FreeBSD 12+), allowing a kernel
to distribute incoming connections between worker processes.
This currently works only on Linux 3.9+, DragonFly BSD,
and FreeBSD 12+ (1.15.1).

B<NOTE>

Inappropriate use of this option may have its security
L<implications|http://man7.org/linux/man-pages/man7/socket.7.html>.




=item 
C<so_keepalive>=C<on>E<verbar>C<off>E<verbar>[I<C<keepidle>>]:[I<C<keepintvl>>]:[I<C<keepcnt>>]





this parameter configures the “TCP keepalive” behavior
for the listening socket.
If this parameter is omitted then the operating system’s settings will be
in effect for the socket.
If it is set to the value “C<on>”, the
C<SO_KEEPALIVE> option is turned on for the socket.
If it is set to the value “C<off>”, the
C<SO_KEEPALIVE> option is turned off for the socket.
Some operating systems support setting of TCP keepalive parameters on
a per-socket basis using the C<TCP_KEEPIDLE>,
C<TCP_KEEPINTVL>, and C<TCP_KEEPCNT> socket options.
On such systems (currently, Linux 2.4+, NetBSD 5+, and
FreeBSD 9.0-STABLE), they can be configured
using the I<C<keepidle>>, I<C<keepintvl>>, and
I<C<keepcnt>> parameters.
One or two parameters may be omitted, in which case the system default setting
for the corresponding socket option will be in effect.
For example,

    so_keepalive=30m::10

will set the idle timeout (C<TCP_KEEPIDLE>) to 30 minutes,
leave the probe interval (C<TCP_KEEPINTVL>) at its system default,
and set the probes count (C<TCP_KEEPCNT>) to 10 probes.




=back








B<NOTE>

Before version 1.25.5, different servers must listen on different
I<C<address>>:I<C<port>> pairs.








=head2 preread_buffer_size


B<syntax:> preread_buffer_size I<I<C<size>>>


B<default:> I<16k>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.11.5.





Specifies a I<C<size>> of the
L<preread|stream_processing> buffer.







=head2 preread_timeout


B<syntax:> preread_timeout I<I<C<timeout>>>


B<default:> I<30s>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.11.5.





Specifies a I<C<timeout>> of the
L<preread|stream_processing> phase.







=head2 proxy_protocol_timeout


B<syntax:> proxy_protocol_timeout I<I<C<timeout>>>


B<default:> I<30s>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.11.4.





Specifies a I<C<timeout>> for
reading the PROXY protocol header to complete.
If no entire header is transmitted within this time,
the connection is closed.







=head2 resolver


B<syntax:> resolver I<
    I<C<address>> ...
    [C<valid>=I<C<time>>]
    [C<ipv4>=C<on>E<verbar>C<off>]
    [C<ipv6>=C<on>E<verbar>C<off>]
    [C<status_zone>=I<C<zone>>]>



B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.11.3.





Configures name servers used to resolve names of upstream servers
into addresses, for example:

    
    resolver 127.0.0.1 [::1]:5353;


The address can be specified as a domain name or IP address,
with an optional port.
If port is not specified, the port 53 is used.
Name servers are queried in a round-robin fashion.





By default, nginx will look up both IPv4 and IPv6 addresses while resolving.
If looking up of IPv4 or IPv6 addresses is not desired,
the C<ipv4=off> (1.23.1) or
the C<ipv6=off> parameter can be specified.





By default, nginx caches answers using the TTL value of a response.
The optional C<valid> parameter allows overriding it:

    
    resolver 127.0.0.1 [::1]:5353 valid=30s;



B<NOTE>

To prevent DNS spoofing, it is recommended
configuring DNS servers in a properly secured trusted local network.






The optional C<status_zone> parameter (1.17.1)
enables
L<collection|ngx_http_api_module>
of DNS server statistics of requests and responses
in the specified I<C<zone>>.
The parameter is available as part of our
commercial subscription.






B<NOTE>

Before version 1.11.3, this directive was available as part of our
commercial subscription.








=head2 resolver_timeout


B<syntax:> resolver_timeout I<I<C<time>>>


B<default:> I<30s>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.11.3.





Sets a timeout for name resolution, for example:

    
    resolver_timeout 5s;



B<NOTE>

Before version 1.11.3, this directive was available as part of our
commercial subscription.








=head2 server


server { B<...> }



B<context:> I<stream>





Sets the configuration for a virtual server.
There is no clear separation between IP-based (based on the IP address)
and name-based (based on the
L<TLS
Server Name Indication extension|http://en.wikipedia.org/wiki/Server_Name_Indication> (SNI, RFC 6066)) (1.25.5)
virtual servers.
Instead, the L</listen> directives describe all
addresses and ports that should accept connections for the server, and the
L</server_name> directive lists all server names.







=head2 server_name


B<syntax:> server_name I<I<C<name>> ...>


B<default:> I<"">


B<context:> I<server>



This directive appeared in version 1.25.5.





Sets names of a virtual server, for example:

    
    server {
        server_name example.com www.example.com;
    }







The first name becomes the primary server name.





Server names can include an asterisk (“C<*>”)
replacing the first or last part of a name:

    
    server {
        server_name example.com *.example.com www.example.*;
    }


Such names are called wildcard names.





The first two of the names mentioned above can be combined in one:

    
    server {
        server_name .example.com;
    }







It is also possible to use regular expressions in server names,
preceding the name with a tilde (“C<~>”):

    
    server {
        server_name www.example.com ~^www\d+\.example\.com$;
    }







Regular expressions can contain captures that can later
be used in other directives:

    
    server {
        server_name ~^(www\.)?(.+)$;
    
        proxy_pass www.$2:12345;
    }







Named captures in regular expressions create variables
that can later be used in other directives:

    
    server {
        server_name ~^(www\.)?(?<domain>.+)$;
    
        proxy_pass www.$domain:12345;
    }







If the directive’s parameter is set to “C<$hostname>”, the
machine’s hostname is inserted.





During searching for a virtual server by name,
if the name matches more than one of the specified variants,
(e.g. both a wildcard name and regular expression match), the first matching
variant will be chosen, in the following order of priority:

=over




=item 1.

the exact name



=item 2.

the longest wildcard name starting with an asterisk,
e.g. “C<*.example.com>”



=item 3.

the longest wildcard name ending with an asterisk,
e.g. “C<mail.*>”



=item 4.

the first matching regular expression
(in order of appearance in the configuration file)



=back









=head2 server_names_hash_bucket_size


B<syntax:> server_names_hash_bucket_size I<I<C<size>>>


B<default:> I<32E<verbar>64E<verbar>128>


B<context:> I<stream>



This directive appeared in version 1.25.5.





Sets the bucket size for the server names hash tables.
The default value depends on the size of the processor’s cache line.
The details of setting up hash tables are provided in a separate
L<document|hash>.







=head2 server_names_hash_max_size


B<syntax:> server_names_hash_max_size I<I<C<size>>>


B<default:> I<512>


B<context:> I<stream>



This directive appeared in version 1.25.5.





Sets the maximum I<C<size>> of the server names hash tables.
The details of setting up hash tables are provided in a separate
L<document|hash>.







=head2 stream


stream { B<...> }



B<context:> I<main>





Provides the configuration file context in which the stream server directives
are specified.







=head2 tcp_nodelay


B<syntax:> tcp_nodelay I<C<on> E<verbar> C<off>>


B<default:> I<on>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.9.4.





Enables or disables the use of the C<TCP_NODELAY> option.
The option is enabled for both client and proxied server connections.







=head2 variables_hash_bucket_size


B<syntax:> variables_hash_bucket_size I<I<C<size>>>


B<default:> I<64>


B<context:> I<stream>



This directive appeared in version 1.11.2.





Sets the bucket size for the variables hash table.
The details of setting up hash tables are provided in a separate
L<document|hash>.







=head2 variables_hash_max_size


B<syntax:> variables_hash_max_size I<I<C<size>>>


B<default:> I<1024>


B<context:> I<stream>



This directive appeared in version 1.11.2.





Sets the maximum I<C<size>> of the variables hash table.
The details of setting up hash tables are provided in a separate
L<document|hash>.







=head1 Embedded Variables



The C<ngx_stream_core_module> module supports variables
since 1.11.2.

=over



=item C<$binary_remote_addr>




client address in a binary form, value’s length is always 4 bytes
for IPv4 addresses or 16 bytes for IPv6 addresses



=item C<$bytes_received>




number of bytes received from a client (1.11.4)



=item C<$bytes_sent>




number of bytes sent to a client



=item C<$connection>




connection serial number



=item C<$hostname>




host name



=item C<$msec>




current time in seconds with the milliseconds resolution



=item C<$nginx_version>




nginx version



=item C<$pid>




PID of the worker process



=item C<$protocol>




protocol used to communicate with the client:
C<TCP> or C<UDP> (1.11.4)



=item C<$proxy_protocol_addr>




client address from the PROXY protocol header (1.11.4)


The PROXY protocol must be previously enabled by setting the
C<proxy_protocol> parameter
in the L</listen> directive.





=item C<$proxy_protocol_port>




client port from the PROXY protocol header (1.11.4)


The PROXY protocol must be previously enabled by setting the
C<proxy_protocol> parameter
in the L</listen> directive.





=item C<$proxy_protocol_server_addr>




server address from the PROXY protocol header (1.17.6)


The PROXY protocol must be previously enabled by setting the
C<proxy_protocol> parameter
in the L</listen> directive.





=item C<$proxy_protocol_server_port>




server port from the PROXY protocol header (1.17.6)


The PROXY protocol must be previously enabled by setting the
C<proxy_protocol> parameter
in the L</listen> directive.





=item C<$proxy_protocol_tlv_>I<C<name>>




TLV from the PROXY Protocol header (1.23.2).
The C<name> can be a TLV type name or its numeric value.
In the latter case, the value is hexadecimal
and should be prefixed with C<0x>:


    
    $proxy_protocol_tlv_alpn
    $proxy_protocol_tlv_0x01


SSL TLVs can also be accessed by TLV type name or its numeric value,
both prefixed by C<ssl_>:

    
    $proxy_protocol_tlv_ssl_version
    $proxy_protocol_tlv_ssl_0x21





The following TLV type names are supported:

=over




=item *

C<alpn> (C<0x01>)E<mdash>
upper layer protocol used over the connection



=item *

C<authority> (C<0x02>)E<mdash>
host name value passed by the client



=item *

C<unique_id> (C<0x05>)E<mdash>
unique connection id



=item *

C<netns> (C<0x30>)E<mdash>
name of the namespace



=item *

C<ssl> (C<0x20>)E<mdash>
binary SSL TLV structure



=back







The following SSL TLV type names are supported:

=over




=item *

C<ssl_version> (C<0x21>)E<mdash>
SSL version used in client connection



=item *

C<ssl_cn> (C<0x22>)E<mdash>
SSL certificate Common Name



=item *

C<ssl_cipher> (C<0x23>)E<mdash>
name of the used cipher



=item *

C<ssl_sig_alg> (C<0x24>)E<mdash>
algorithm used to sign the certificate



=item *

C<ssl_key_alg> (C<0x25>)E<mdash>
public-key algorithm



=back







Also, the following special SSL TLV type name is supported:

=over




=item *

C<ssl_verify>E<mdash>
client SSL certificate verification result,
zero if the client presented a certificate
and it was successfully verified, and non-zero otherwise



=back







The PROXY protocol must be previously enabled by setting the
C<proxy_protocol> parameter
in the L</listen> directive.





=item C<$remote_addr>




client address



=item C<$remote_port>




client port



=item C<$server_addr>




an address of the server which accepted a connection


Computing a value of this variable usually requires one system call.
To avoid a system call, the L</listen> directives
must specify addresses and use the C<bind> parameter.





=item C<$server_port>




port of the server which accepted a connection



=item C<$session_time>




session duration in seconds with a milliseconds resolution
(1.11.4);



=item C<$status>




session status (1.11.4), can be one of the following:

=over



=item C<200>




session completed successfully



=item C<400>




client data could not be parsed, for example,
the PROXY protocol header



=item C<403>




access forbidden, for example, when access is limited for
L<certain client addresses|ngx_stream_access_module>



=item C<500>




internal server error



=item C<502>




bad gateway, for example,
if an upstream server could not be selected or reached.



=item C<503>




service unavailable, for example, when access is limited by the
L<number of connections|ngx_stream_limit_conn_module>




=back





=item C<$time_iso8601>




local time in the ISO 8601 standard format



=item C<$time_local>




local time in the Common Log Format




=back






