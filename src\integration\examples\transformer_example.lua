-- src/integration/examples/transformer_example.lua

-- Hypothetical data structure from "ThirdPartyA"
local ThirdPartyA_Data = {
    project_id = "PJT1001",
    customer_name = "Mr<PERSON> <PERSON>",
    design_elements = {
        {
            type = "Cabinet",
            id = "cab001",
            dimensions = { w = 800, h = 2000, d = 600 },
            material = "Oak Wood",
            finish = "Matte Varnish"
        },
        {
            type = "Shelf",
            id = "shf002",
            parent_id = "cab001",
            size = { length = 760, depth = 550 },
            material = "Pine Wood"
        }
    },
    notes = "Handle with care."
}

-- Hypothetical NH-AI Canonical Data Model (simplified for example)
-- We aim to transform ThirdPartyA_Data into something like this:
-- local NHAI_Canonical_Scheme = {
--     schemeId = "", -- string, unique ID for the scheme
--     clientInfo = {
--         name = "", -- string
--     },
--     components = { -- array of components
--         {
--             componentType = "", -- e.g., "Cabinet", "Shelf", "Drawer"
--             componentId = "", -- string, unique ID within the scheme
--             attributes = { -- key-value pairs
--                 width = 0, -- number
--                 height = 0, -- number
--                 depth = 0, -- number
--                 material = "", -- string
--                 finish = "", -- string
--                 -- ... other common attributes
--             },
--             subComponents = { -- array of nested components (optional)
--                 -- similar structure to a component
--             }
--         }
--     },
--     additionalInfo = "" -- string
-- }

-- Transformer module for ThirdPartyA
local TransformerA = {}

function TransformerA.transform(third_party_data)
    if not third_party_data then
        return nil, "Input data is nil"
    end

    local nhai_scheme = {
        schemeId = third_party_data.project_id or "UNKNOWN_PROJECT_ID",
        clientInfo = {
            name = third_party_data.customer_name or "N/A"
        },
        components = {},
        additionalInfo = third_party_data.notes or ""
    }

    if third_party_data.design_elements and type(third_party_data.design_elements) == "table" then
        for _, element in ipairs(third_party_data.design_elements) do
            local component = {
                componentType = element.type or "GenericComponent",
                componentId = element.id or "UNKNOWN_ID",
                attributes = {},
                subComponents = {} -- For simplicity, not populating sub-components in this example
            }

            if element.dimensions then
                component.attributes.width = element.dimensions.w
                component.attributes.height = element.dimensions.h
                component.attributes.depth = element.dimensions.d
            end
            if element.size then -- For elements like shelves
                component.attributes.length = element.size.length
                component.attributes.depth = element.size.depth
            end
            component.attributes.material = element.material
            component.attributes.finish = element.finish -- Might be nil for some elements

            -- Basic parent-child relationship (can be made more robust)
            -- This example assumes top-level elements are added directly.
            -- A more complex scenario would involve building a tree.
            if element.parent_id then
                -- Find parent and add as subComponent (simplified)
                for _, parent_comp in ipairs(nhai_scheme.components) do
                    if parent_comp.componentId == element.parent_id then
                        table.insert(parent_comp.subComponents, component)
                        goto next_element -- skip adding as top-level
                    end
                end
                -- If parent not found yet, add as top-level (or handle error)
                table.insert(nhai_scheme.components, component)
            else
                table.insert(nhai_scheme.components, component)
            end
            ::next_element::
        end
    end

    return nhai_scheme
end

-- Example Usage
local function main()
    print("Original ThirdPartyA Data:")
    -- Basic print for tables (a proper pretty-printer would be better for complex tables)
    for k, v in pairs(ThirdPartyA_Data) do
        if type(v) == "table" then
            print(k .. ": [table - see details below]")
            for sk, sv in pairs(v) do
                if type(sv) == "table" then
                     print("  " .. sk .. ": [nested table]")
                else
                    print("  " .. sk .. ": " .. tostring(sv))
                end
            end
        else
            print(k .. ": " .. tostring(v))
        end
    end
    print("---")

    local nhai_transformed_data, err = TransformerA.transform(ThirdPartyA_Data)

    if err then
        print("Error during transformation: " .. err)
        return
    end

    print("\nTransformed NH-AI Canonical Data:")
    -- Basic print for the transformed data
    for k, v in pairs(nhai_transformed_data) do
        if type(v) == "table" then
            print(k .. ": [table - see details below]")
            for sk, sv in pairs(v) do
                 if type(sv) == "table" then
                    print("  " .. sk .. ": [nested table - further details omitted for brevity]")
                 else
                    print("  " .. sk .. ": " .. tostring(sv))
                 end
            end
        else
            print(k .. ": " .. tostring(v))
        end
    end

    -- To properly inspect the nested tables, you'd use a Lua pretty printer library
    -- or debug print. For example, to see the first component's attributes:
    if nhai_transformed_data.components and #nhai_transformed_data.components > 0 then
        print("\nDetails of the first component's attributes:")
        local first_component = nhai_transformed_data.components[1]
        if first_component and first_component.attributes then
            for attr_name, attr_value in pairs(first_component.attributes) do
                print("  " .. attr_name .. ": " .. tostring(attr_value))
            end
        end
        if first_component and first_component.subComponents and #first_component.subComponents > 0 then
            print("  SubComponents exist for the first component.")
        end
    end
end

-- To run this example directly:
-- local example = require("src.integration.examples.transformer_example")
-- example.main()
-- Or, if running the file as a script:
-- main()

local M = {
    ThirdPartyA_Data = ThirdPartyA_Data, -- Exporting for potential use in adapter example
    TransformerA = TransformerA,
    main = main -- Exporting main if needed to be called externally
}

return M
