package.path = package.path .. ';../windows/lua_scripts/?.lua;'

local engine = require('lib/rule-engine')
print(engine.make_box(20, 20, 20))
print(engine.make_box(20, 20, 20))

-- local xml = engine.load_xml_file('xmlStr.xml')
-- engine.scene_init(xml)
-- engine.export_step_file('stepFile.step')
-- engine.scene_clear()

print(engine.addTest(1, 2))













local json = require("cjson")
local geometry_engine = require("lib/geometry-engine-ins")

-- 测试主接口初始化几何引擎   动态接口 访问当前几何引擎的是否支持并发下不干扰 且单次请求是同实例
local function main_dynamic_test()
    local xml_data = [[
    <Root>
        <Part prodCatId="498">
            <Part prodCatId="2059" />
        </Part>
    </Root>
    ]]
    
    -- 获取当前请求的几何引擎实例
    local engine_instance = geometry_engine:get_current_instance()
    local engine_object = engine_instance:create_engine_object(xml_data)
    local t_id = engine_object:id()
    print(t_id)

    local lua_script = [[
        local geometry_engine = require("lib/geometry-engine-ins")
        local function test()
            local is_engine_object_exist = geometry_engine:is_engine_object_exist()
            if not is_engine_object_exist then
                return nil, "几何引擎实例不存在"
            end

            local engine_instance = geometry_engine:get_current_instance()
            local t_id = engine_instance:get_current_engine_object():id()
            return t_id
        end
        return test()
    ]]

    local func, err = load(lua_script, "script", "t", _G)
    if not func or err then
        ngx.say(json.encode({
            success = false,
            message = "脚本加载失败: " .. tostring(err)
        }))
        return
    end

    -- 执行加载的函数
    local status, result, err_msg = pcall(func)
    if not status then
        ngx.say(json.encode({
            success = false,
            message = "执行失败: " .. tostring(result)
        }))
        return
    end

    if not result then
        ngx.say(json.encode({
            success = false,
            message = "执行失败: " .. tostring(err_msg)
        }))
        return
    end

    local child_id = result

    -- 返回结果
    ngx.say(json.encode({
        success = true,
        message = "执行成功",
        results = {
            data = "p_id--" .. t_id .. "   c_id--" .. tostring(child_id)
        }
    }))
    
    -- 清理当前请求的几何引擎实例
    geometry_engine:clear_current_instance()
end

main_dynamic_test()