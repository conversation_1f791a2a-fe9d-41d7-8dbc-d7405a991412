--[[
XML节点扩展
功能：为xmlua元素节点添加新的方法
1. pattr: 获取父节点的属性值
]]

local _M = {}

-- 获取父节点的属性值
local function get_parent_attribute(node, attr_name)
    if not node then
        return nil, "节点不能为空"
    end

    local parent_id = node:get_attribute("parentId")
    if not parent_id then
        return nil, string.format("节点[%s]没有parentId属性", node:get_attribute("id") or "unknown")
    end

    -- 从当前节点开始向上查找Part节点
    local current = node
    while current do
        -- 查找id等于parent_id的Part节点
        local parent = current:search(string.format("//Part[@id='%s']", parent_id))
        if parent and #parent > 0 then
            -- 将Part节点子节点属性附加到Part节点
            local attribute_appender = require("lib.attribute-appender")
            attribute_appender.append_attributes(parent[1])
            -- 获取父节点属性
            local value = parent[1]:get_attribute(attr_name)
            if not value then
                return nil, string.format("父节点[%s]没有[%s]属性", parent_id, attr_name)
            end
            return value
        end
        -- 继续向上查找
        current = current:parent()
    end

    return nil, string.format("找不到ID为[%s]的父节点", parent_id)
end

-- 扩展xmlua节点
local xmlua = require("xmlua")

-- 创建一个临时的Part节点实例来获取元表
local temp_doc = xmlua.XML.parse("<root><Part id='temp'/></root>")
local temp_element = temp_doc:search("//Part")[1]
local element_mt = getmetatable(temp_element)

-- 保存原始__index
local original_index = element_mt.__index

-- 扩展__index
element_mt.__index = function(t, k)
    if k == "pattr" then
        return function(self, attr_name)
            return get_parent_attribute(self, attr_name)
        end
    end
    return original_index(t, k)
end

return _M 