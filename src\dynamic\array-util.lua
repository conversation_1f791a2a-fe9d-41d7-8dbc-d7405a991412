
-- 常用的数组工具类
local _M = {}


-- 辅助函数：合并数组
function _M.contact_table(array1, array2)
	local result = {}
	if array1 and #array1 > 0 then
		for _, item in ipairs(array1) do
			table.insert(result, item)
		end
	end
	if array2 and #array2 > 0 then
		for _, item in ipairs(array2) do
			table.insert(result, item)
		end
	end
	return result
end


-- 辅助函数：检查表中是否包含某个值
function _M.table_contains(tbl, val)
    for _, v in ipairs(tbl) do
        if v == val then
            return true
        end
    end
    return false
end

-- 辅助函数：数组内去重
function _M.unique_table(tbl)
    local result = {}
    for _, v in ipairs(tbl) do
        if not _M.table_contains(result, v) then
            table.insert(result, v)
        end
    end
    return result
end


return _M


