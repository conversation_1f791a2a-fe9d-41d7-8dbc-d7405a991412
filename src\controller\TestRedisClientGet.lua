local rc = require('lib/redisclient')
local json = require('cjson')

--[[
接口请求体格式为：
{
    "method": "get|mget|zstd_get|zstd_mget", // 执行的操作，四选一
    "key": "string", // 单个 key，针对 get 和 zstd_get 操作
    "keys": ["string", "string"] // 多个 key，针对 mget 和 zstd_mget 操作
}
]]

local function get(key)
    local val, err = rc.get(key)
    if err then
        ngx.say('{"code":1,"msg":"get 操作失败：' .. err .. '"}')
        return
    end
    ngx.say(val)
end

local function mget(keys)
    local val, err = rc.mget(keys)
    if err then
        ngx.say('{"code":1,"msg":"mget 操作失败：' .. err .. '"}')
        return
    end
    ngx.say(json.encode(val))
end

local function zstd_get(key)
    local val, err = rc.zstd_get(key)
    if err then
        ngx.say('{"code":1,"msg":"zstd_get 操作失败：' .. err .. '"}')
        return
    end
    ngx.say(val)
end

local function zstd_mget(keys)
    local val, err = rc.zstd_mget(keys)
    if err then
        ngx.say('{"code":1,"msg":"zstd_mget 操作失败：' .. err .. '"}')
        return
    end
    ngx.say(json.encode(val))
end

local function main()
    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    if data == nil then
        ngx.say('{"code":1,"msg":"请求体不能为空"}')
        return
    end

    local req = json.decode(data)

    if req.method == "get" then
        get(req.key)
    elseif req.method == "mget" then
        mget(req.keys)
    elseif req.method == "zstd_get" then
        zstd_get(req.key)
    elseif req.method == "zstd_mget" then
        zstd_mget(req.keys)
    else
        ngx.say('{"code":1,"msg":"不支持的操作"}')
    end
end

main()
