
local engine = require("lib/rule-engine")
local GeometryObject = {}
GeometryObject.__index = GeometryObject

-- 构造函数
function GeometryObject.new(t_id)

    local instance = {
        t_id = t_id,
        rule_engine = engine
    }
    setmetatable(instance, { __index = GeometryObject })

    return instance
end

-- 获取 tId 的方法
function GeometryObject:id()
    return self.t_id
end

function GeometryObject:add_test(a, b)
    return self.rule_engine.add_test(a, b)
end

-- -- 清理引擎
-- geometry:clear()
function GeometryObject:scene_clear()
    self.rule_engine.scene_clear(self.t_id)
end

function GeometryObject:scene_export()
    self.rule_engine.scene_export(self.t_id)
end

-- -- 获取几何关系
-- local relations = geometry:get_objects_distance(
--   "BQ", "==", "1",           -- 第一个对象的属性条件
--   {"up", "down"},            -- 方向列表
--   "<=", 1,                   -- 距离条件
--   "BQ", "==", "2"            -- 第二个对象的属性条件
-- )

function GeometryObject:get_objects_distance(attr_key1, op1, value1, directions, distance_op, distance, attr_key2, op2, value2)
    return self.rule_engine.get_objects_distance(self.t_id, attr_key1, op1, value1, directions, distance_op, distance, attr_key2, op2, value2)
end

function GeometryObject:get_objects_distance_value_array(attr_key1, op1, values1, directions, distance_op, distance, attr_key2, op2, values2)
    return self.rule_engine.get_objects_distance_value_array(self.t_id, attr_key1, op1, values1, directions, distance_op, distance, attr_key2, op2, values2)
end

function GeometryObject:get_objects_overlap(objects, ignoreSameRoot)
    return self.rule_engine.get_objects_overlap(self.t_id, objects, ignoreSameRoot)
end

function GeometryObject:add_test_by_id(a, b)
    return self.rule_engine.add_test_by_id(self.t_id, a, b)
end

-- 获取两个对象之间的夹角
function GeometryObject:getObjectsAngle(angleObjects)
    return self.rule_engine.getObjectsAngle(self.t_id, angleObjects)
end

function GeometryObject:get_door_hinge_face_by_pos(uid)
    return self.rule_engine.get_door_hinge_face_by_pos(self.t_id, uid)
end

function GeometryObject:get_door_hinge_cover_info(uid, cover_type)
    return self.rule_engine.get_door_hinge_cover_info(self.t_id, uid, cover_type)
end

function GeometryObject:aabb_cover_detect(id, direction, distance, detectIDs)
    return self.rule_engine.aabb_cover_detect(self.t_id, id, direction, distance, detectIDs)
end

function GeometryObject:get_internal_space(id)
    return self.rule_engine.get_internal_space(self.t_id, id)
end

function GeometryObject:get_internal_space_distance(id, internalSpace)
    return self.rule_engine.get_internal_space_distance(self.t_id, id, internalSpace)
end

function GeometryObject:get_door_intersect_inside_board(uuid, distance)
    return self.rule_engine.get_door_intersect_inside_board(self.t_id, uuid, distance)
end

function GeometryObject:get_sceen_door_aligned_group()
    return self.rule_engine.get_sceen_door_aligned_group(self.t_id)
end

return GeometryObject
