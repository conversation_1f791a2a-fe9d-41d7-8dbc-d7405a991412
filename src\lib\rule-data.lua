-- 规则数据访问模块
local redis = require "lib.redisclient"
local json = require "cjson"
local string_util = require "lib.string-util"
local lrucache = require "resty.lrucache"
local os = os

local _M = {
    -- 模块版本
    _version = '0.1.1'
}

-- Redis 中规则数据的键前缀
local rule_key_prefix = "rule_platform:rule:"
local rules_scene_prefix = "rule_platform:rules:"

-- 创建LRU缓存实例，最多缓存1000条规则
local rule_cache, err = lrucache.new(10000)
if not rule_cache then
    error("创建规则缓存失败: " .. (err or "未知错误"))
end

-- 缓存过期时间（秒）
local CACHE_TTL = 60 * 60

-- 规则对象属性定义
local rule_properties = {
    id = "",          -- 规则ID
    luaScript = "",   -- 规则脚本
    versionCode = "", -- 规则版本
    ruleType = "",    -- 规则类型
    needGeometry = "" -- 是否依赖几何引擎
}

-- 获取缓存统计信息
function _M.get_cache_stats()
    return {
        count = rule_cache:count(),
        capacity = rule_cache:capacity()
    }
end

-- 根据规则ID获取规则对象
-- @param rule_id 规则ID
-- @return rule 规则对象，如果不存在则返回nil
-- @return err 错误信息
function _M.get_rule_by_id(rule_id)
    if string_util.is_empty(rule_id) then
        return nil, "规则ID不能为空"
    end

    -- 构建Redis键
    local key = rule_key_prefix .. rule_id

    -- 从Redis获取规则数据
    local rule_data, err = redis.get(key)
    if err then
        return nil, "规则ID[" .. rule_id .. "]，" .. err
    end

    if not rule_data then
        return nil, "规则[" .. rule_id .. "]数据不存在"
    end

    -- 解析JSON数据
    local success, rule = pcall(json.decode, rule_data)
    if not success then
        return nil, "规则[" .. rule_id .. "]数据格式错误"
    end

    return rule, nil
end

-- 根据规则ID获取规则对象（带缓存）
-- @param rule_id 规则ID
-- @return rule 规则对象，如果不存在则返回nil
-- @return err 错误信息
function _M.get_rule_by_id_with_cache(rule_id)
    if string_util.is_empty(rule_id) then
        return nil, "规则ID不能为空"
    end

    -- 构建Redis键
    local key = rule_key_prefix .. rule_id

    -- 先从缓存中获取
    local cached_rule = rule_cache:get(key)
    if cached_rule then
        return cached_rule, nil
    end

    -- 从Redis获取规则数据
    local rule_data, err = redis.get(key)
    if err then
        return nil, "规则ID[" .. rule_id .. "]，" .. err
    end

    if not rule_data then
        return nil, "规则[" .. rule_id .. "]数据不存在"
    end

    -- 解析JSON数据
    local success, rule = pcall(json.decode, rule_data)
    if not success then
        return nil, "规则[" .. rule_id .. "]数据格式错误"
    end

    -- 将规则存入缓存
    rule_cache:set(key, rule, CACHE_TTL)

    return rule, nil
end

-- 根据业务场景获取规则列表
-- @param scene 业务场景
-- @return rules 规则列表，如果不存在则返回空表
-- @return err 错误信息
function _M.get_rules_by_scene(scene)
    if string_util.is_empty(scene) then
        return {}, "业务场景不能为空"
    end

    -- 构建Redis键
    local key = rules_scene_prefix .. scene

    -- 从Redis获取规则集合数据
    local rules_data, err = redis.get(key)
    if err then
        return {}, "业务场景[" .. scene .. "]" .. err
    end

    -- 直接反序列化规则集合数据
    local success, rules = pcall(json.decode, rules_data)
    if not success then
        return {}, "业务场景[" .. scene .. "]规则数据格式错误"
    end

    return rules, nil
end

-- 根据传入的keys 获取所有相似的规则列表
-- @param pattern 规则keys
-- @return rules 规则列表，如果不存在则返回空表
-- @return err 错误信息
function _M.get_rules_by_pattern(pattern)
    if string_util.is_empty(pattern) then
        return {}, "规则keys不能为空"
    end

    local keys, err = redis.keys(pattern)
    if err then
        return {}, "规则keys[" .. pattern .. "]，" .. err
    end

    local rules = {}
    for _, key in ipairs(keys) do
        local rule_data, err = redis.get(key)
        if rule_data then
            table.insert(rules, rule_data)
        end
    end

    return rules, nil
end

-- 保存规则
-- @param rule_id 规则ID
-- @param rule_data 规则数据
-- @return ok 是否保存成功
-- @return err 错误信息
function _M.save_rule(rule_id, rule_data)
    if string_util.is_empty(rule_id) then
        return false, "规则ID不能为空"
    end

    -- 构建Redis键  
    local key = rule_key_prefix .. rule_id

    -- 保存规则数据到Redis
    local ok, err = redis.set(key, rule_data)
    if not ok then
        return false, "规则ID[" .. rule_id .. "]，" .. err
    end 

    -- 清除缓存
    rule_cache:delete(key)

    -- 更新缓存
    local success, rule = pcall(json.decode, rule_data)
    if success then
        rule_cache:set(key, rule, CACHE_TTL)
    end

    return true, nil
end

-- 清除所有规则缓存
function _M.clear_all_rule_cache()
    -- 清除LRU缓存中的所有数据
    local success = rule_cache:flush_all()
    if not success then
        return false, "清除规则缓存失败"
    end
    return true, nil
end

-- 模块版本信息
function _M.version()
    return _M._version
end

return _M
