=encoding utf-8

=head1 NAME

ngx_http_grpc_module - Module ngx_http_grpc_module




=head1



The C<ngx_http_grpc_module> module allows passing requests
to a gRPC server (1.13.10).
The module requires the
L<ngx_http_v2_module|ngx_http_v2_module> module.




=head1 Example Configuration




    
    server {
        listen 9000;
    
        http2 on;
    
        location / {
            grpc_pass 127.0.0.1:9000;
        }
    }






=head1 Directives

=head2 grpc_bind


B<syntax:> grpc_bind I<
    I<C<address>>
    [C<transparent >] E<verbar>
    C<off>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Makes outgoing connections to a gRPC server originate
from the specified local IP address with an optional port.
Parameter value can contain variables.
The special value C<off> cancels the effect
of the C<grpc_bind> directive
inherited from the previous configuration level, which allows the
system to auto-assign the local IP address and port.





The C<transparent> parameter allows
outgoing connections to a gRPC server originate
from a non-local IP address,
for example, from a real IP address of a client:

    
    grpc_bind $remote_addr transparent;


In order for this parameter to work,
it is usually necessary to run nginx worker processes with the
L<superuser|ngx_core_module> privileges.
On Linux it is not required as if
the C<transparent> parameter is specified, worker processes
inherit the C<CAP_NET_RAW> capability from the master process.
It is also necessary to configure kernel routing table
to intercept network traffic from the gRPC server.







=head2 grpc_buffer_size


B<syntax:> grpc_buffer_size I<I<C<size>>>


B<default:> I<4kE<verbar>8k>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets the I<C<size>> of the buffer used for reading the response
received from the gRPC server.
The response is passed to the client synchronously, as soon as it is received.







=head2 grpc_connect_timeout


B<syntax:> grpc_connect_timeout I<I<C<time>>>


B<default:> I<60s>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines a timeout for establishing a connection with a gRPC server.
It should be noted that this timeout cannot usually exceed 75 seconds.







=head2 grpc_hide_header


B<syntax:> grpc_hide_header I<I<C<field>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





By default,
nginx does not pass the header fields C<Date>,
C<Server>, and
C<X-Accel-...> from the response of a gRPC
server to a client.
The C<grpc_hide_header> directive sets additional fields
that will not be passed.
If, on the contrary, the passing of fields needs to be permitted,
the L</grpc_pass_header> directive can be used.







=head2 grpc_ignore_headers


B<syntax:> grpc_ignore_headers I<I<C<field>> ...>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Disables processing of certain response header fields from the gRPC server.
The following fields can be ignored: C<X-Accel-Redirect>
and C<X-Accel-Charset>.





If not disabled, processing of these header fields has the following
effect:

=over




=item *

C<X-Accel-Redirect> performs an
L<internal
redirect|ngx_http_core_module> to the specified URI;



=item *

C<X-Accel-Charset> sets the desired
L<ngx_http_charset_module>
of a response.



=back









=head2 grpc_intercept_errors


B<syntax:> grpc_intercept_errors I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Determines whether gRPC server responses with codes greater than or equal
to 300 should be passed to a client
or be intercepted and redirected to nginx for processing
with the L<ngx_http_core_module> directive.







=head2 grpc_next_upstream


B<syntax:> grpc_next_upstream I<
    C<error> E<verbar>
    C<timeout> E<verbar>
    C<invalid_header> E<verbar>
    C<http_500> E<verbar>
    C<http_502> E<verbar>
    C<http_503> E<verbar>
    C<http_504> E<verbar>
    C<http_403> E<verbar>
    C<http_404> E<verbar>
    C<http_429> E<verbar>
    C<non_idempotent> E<verbar>
    C<off>
    ...>


B<default:> I<error timeout>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Specifies in which cases a request should be passed to the next server:

=over



=item C<error>



an error occurred while establishing a connection with the
server, passing a request to it, or reading the response header;


=item C<timeout>



a timeout has occurred while establishing a connection with the
server, passing a request to it, or reading the response header;


=item C<invalid_header>



a server returned an empty or invalid response;


=item C<http_500>



a server returned a response with the code 500;


=item C<http_502>



a server returned a response with the code 502;


=item C<http_503>



a server returned a response with the code 503;


=item C<http_504>



a server returned a response with the code 504;


=item C<http_403>



a server returned a response with the code 403;


=item C<http_404>



a server returned a response with the code 404;


=item C<http_429>



a server returned a response with the code 429;


=item C<non_idempotent>



normally, requests with a
L<non-idempotent|https://datatracker.ietf.org/doc/html/rfc7231#section-4.2.2>
method
(C<POST>, C<LOCK>, C<PATCH>)
are not passed to the next server
if a request has been sent to an upstream server;
enabling this option explicitly allows retrying such requests;



=item C<off>



disables passing a request to the next server.



=back







One should bear in mind that passing a request to the next server is
only possible if nothing has been sent to a client yet.
That is, if an error or timeout occurs in the middle of the
transferring of a response, fixing this is impossible.





The directive also defines what is considered an
L<unsuccessful
attempt|ngx_http_upstream_module> of communication with a server.
The cases of C<error>, C<timeout> and
C<invalid_header> are always considered unsuccessful attempts,
even if they are not specified in the directive.
The cases of C<http_500>, C<http_502>,
C<http_503>, C<http_504>,
and C<http_429> are
considered unsuccessful attempts only if they are specified in the directive.
The cases of C<http_403> and C<http_404>
are never considered unsuccessful attempts.





Passing a request to the next server can be limited by
the number of tries
and by time.







=head2 grpc_next_upstream_timeout


B<syntax:> grpc_next_upstream_timeout I<I<C<time>>>


B<default:> I<0>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Limits the time during which a request can be passed to the
next server.
The C<0> value turns off this limitation.







=head2 grpc_next_upstream_tries


B<syntax:> grpc_next_upstream_tries I<I<C<number>>>


B<default:> I<0>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Limits the number of possible tries for passing a request to the
next server.
The C<0> value turns off this limitation.







=head2 grpc_pass


B<syntax:> grpc_pass I<I<C<address>>>



B<context:> I<location>


B<context:> I<if in location>





Sets the gRPC server address.
The address can be specified as a domain name or IP address,
and a port:

    
    grpc_pass localhost:9000;


or as a UNIX-domain socket path:

    
    grpc_pass unix:/tmp/grpc.socket;


Alternatively, the “C<grpc:E<sol>E<sol>>” scheme can be used:

    
    grpc_pass grpc://127.0.0.1:9000;


To use gRPC over SSL, the “C<grpcs:E<sol>E<sol>>” scheme should be used:

    
    grpc_pass grpcs://127.0.0.1:443;







If a domain name resolves to several addresses, all of them will be
used in a round-robin fashion.
In addition, an address can be specified as a
L<server group|ngx_http_upstream_module>.





Parameter value can contain variables (1.17.8).
In this case, if an address is specified as a domain name,
the name is searched among the described
L<server groups|ngx_http_upstream_module>,
and, if not found, is determined using a
L<ngx_http_core_module>.







=head2 grpc_pass_header


B<syntax:> grpc_pass_header I<I<C<field>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Permits passing otherwise disabled header
fields from a gRPC server to a client.







=head2 grpc_read_timeout


B<syntax:> grpc_read_timeout I<I<C<time>>>


B<default:> I<60s>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines a timeout for reading a response from the gRPC server.
The timeout is set only between two successive read operations,
not for the transmission of the whole response.
If the gRPC server does not transmit anything within this time,
the connection is closed.







=head2 grpc_send_timeout


B<syntax:> grpc_send_timeout I<I<C<time>>>


B<default:> I<60s>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets a timeout for transmitting a request to the gRPC server.
The timeout is set only between two successive write operations,
not for the transmission of the whole request.
If the gRPC server does not receive anything within this time,
the connection is closed.







=head2 grpc_set_header


B<syntax:> grpc_set_header I<I<C<field>> I<C<value>>>


B<default:> I<Content-Length $content_length>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Allows redefining or appending fields to the request header
passed to the gRPC server.
The I<C<value>> can contain text, variables, and their combinations.
These directives are inherited from the previous configuration level
if and only if there are no C<grpc_set_header> directives
defined on the current level.





If the value of a header field is an empty string then this
field will not be passed to a gRPC server:

    
    grpc_set_header Accept-Encoding "";









=head2 grpc_socket_keepalive


B<syntax:> grpc_socket_keepalive I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.15.6.





Configures the “TCP keepalive” behavior
for outgoing connections to a gRPC server.
By default, the operating system’s settings are in effect for the socket.
If the directive is set to the value “C<on>”, the
C<SO_KEEPALIVE> socket option is turned on for the socket.







=head2 grpc_ssl_certificate


B<syntax:> grpc_ssl_certificate I<I<C<file>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Specifies a I<C<file>> with the certificate in the PEM format
used for authentication to a gRPC SSL server.





Since version 1.21.0, variables can be used in the I<C<file>> name.







=head2 grpc_ssl_certificate_cache


B<syntax:> grpc_ssl_certificate_cache I<C<off>>


B<syntax:> grpc_ssl_certificate_cache I<
    C<max>=I<C<N>>
    [C<inactive>=I<C<time>>]
    [C<valid>=I<C<time>>]>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.27.4.





Defines a cache that stores
SSL certificates and
secret keys
specified with variables.





The directive has the following parameters:

=over



=item 
C<max>





sets the maximum number of elements in the cache;
on cache overflow the least recently used (LRU) elements are removed;



=item 
C<inactive>





defines a time after which an element is removed from the cache
if it has not been accessed during this time;
by default, it is 10 seconds;



=item 
C<valid>





defines a time during which
an element in the cache is considered valid
and can be reused;
by default, it is 60 seconds.
Certificates that exceed this time will be reloaded or revalidated;



=item 
C<off>





disables the cache.




=back







Example:

    
    grpc_ssl_certificate       $grpc_ssl_server_name.crt;
    grpc_ssl_certificate_key   $grpc_ssl_server_name.key;
    grpc_ssl_certificate_cache max=1000 inactive=20s valid=1m;









=head2 grpc_ssl_certificate_key


B<syntax:> grpc_ssl_certificate_key I<I<C<file>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Specifies a I<C<file>> with the secret key in the PEM format
used for authentication to a gRPC SSL server.





The value
C<engine>:I<C<name>>:I<C<id>>
can be specified instead of the I<C<file>>,
which loads a secret key with a specified I<C<id>>
from the OpenSSL engine I<C<name>>.





Since version 1.21.0, variables can be used in the I<C<file>> name.







=head2 grpc_ssl_ciphers


B<syntax:> grpc_ssl_ciphers I<I<C<ciphers>>>


B<default:> I<DEFAULT>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Specifies the enabled ciphers for requests to a gRPC SSL server.
The ciphers are specified in the format understood by the OpenSSL library.





The full list can be viewed using the
“C<openssl ciphers>” command.







=head2 grpc_ssl_conf_command


B<syntax:> grpc_ssl_conf_command I<I<C<name>> I<C<value>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.19.4.





Sets arbitrary OpenSSL configuration
L<commands|https://www.openssl.org/docs/man1.1.1/man3/SSL_CONF_cmd.html>
when establishing a connection with the gRPC SSL server.

B<NOTE>

The directive is supported when using OpenSSL 1.0.2 or higher.






Several C<grpc_ssl_conf_command> directives
can be specified on the same level.
These directives are inherited from the previous configuration level
if and only if there are
no C<grpc_ssl_conf_command> directives
defined on the current level.






B<NOTE>

Note that configuring OpenSSL directly
might result in unexpected behavior.








=head2 grpc_ssl_crl


B<syntax:> grpc_ssl_crl I<I<C<file>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Specifies a I<C<file>> with revoked certificates (CRL)
in the PEM format used to verify
the certificate of the gRPC SSL server.







=head2 grpc_ssl_key_log


B<syntax:> grpc_ssl_key_log I<path>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.27.2.





Enables logging of gRPC SSL server connection SSL keys
and specifies the path to the key log file.
Keys are logged in the
L<SSLKEYLOGFILE|https://datatracker.ietf.org/doc/html/draft-ietf-tls-keylogfile>
format compatible with Wireshark.






B<NOTE>

This directive is available as part of our
commercial subscription.








=head2 grpc_ssl_name


B<syntax:> grpc_ssl_name I<I<C<name>>>


B<default:> I<host from grpc_pass>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Allows overriding the server name used to
verify
the certificate of the gRPC SSL server and to be
passed through SNI
when establishing a connection with the gRPC SSL server.





By default, the host part from L</grpc_pass> is used.







=head2 grpc_ssl_password_file


B<syntax:> grpc_ssl_password_file I<I<C<file>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Specifies a I<C<file>> with passphrases for
secret keys
where each passphrase is specified on a separate line.
Passphrases are tried in turn when loading the key.







=head2 grpc_ssl_protocols


B<syntax:> grpc_ssl_protocols I<
    [C<SSLv2>]
    [C<SSLv3>]
    [C<TLSv1>]
    [C<TLSv1.1>]
    [C<TLSv1.2>]
    [C<TLSv1.3>]>


B<default:> I<TLSv1.2 TLSv1.3>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Enables the specified protocols for requests to a gRPC SSL server.






B<NOTE>

The C<TLSv1.3> parameter is used by default
since 1.23.4.








=head2 grpc_ssl_server_name


B<syntax:> grpc_ssl_server_name I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Enables or disables passing of the server name through
L<TLS
Server Name Indication extension|http://en.wikipedia.org/wiki/Server_Name_Indication> (SNI, RFC 6066)
when establishing a connection with the gRPC SSL server.







=head2 grpc_ssl_session_reuse


B<syntax:> grpc_ssl_session_reuse I<C<on> E<verbar> C<off>>


B<default:> I<on>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Determines whether SSL sessions can be reused when working with
the gRPC server.
If the errors
“C<digest check failed>”
appear in the logs, try disabling session reuse.







=head2 grpc_ssl_trusted_certificate


B<syntax:> grpc_ssl_trusted_certificate I<I<C<file>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Specifies a I<C<file>> with trusted CA certificates in the PEM format
used to verify
the certificate of the gRPC SSL server.







=head2 grpc_ssl_verify


B<syntax:> grpc_ssl_verify I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Enables or disables verification of the gRPC SSL server certificate.







=head2 grpc_ssl_verify_depth


B<syntax:> grpc_ssl_verify_depth I<I<C<number>>>


B<default:> I<1>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets the verification depth in the gRPC SSL server certificates chain.







