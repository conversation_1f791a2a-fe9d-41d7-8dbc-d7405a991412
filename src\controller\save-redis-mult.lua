-- 保存脚本到redis

local rule_data = require("lib/rule-data")
local xml_data = require("lib/xml-data")
local function_data = require("lib/function-data")
local json = require "cjson"
local cjson = require("cjson.safe")

local function save_to_redis_script()
    -- 读取POST请求体
    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    if data == nil then
        ngx.say('{"code":1,"msg":"请求数据不能为空"}')
        return
    end

    -- 解析JSON数据
    local success, req = pcall(json.decode, data)
    if not success then
        ngx.say('{"code":1,"msg":"请求数据格式错误，需要JSON格式"}')
        return
    end

    -- 检查保存类型
    local save_type = req.save_type
    if not save_type then
        ngx.say('{"code":1,"msg":"保存类型不能为空"}')
        return
    end
    
    local success_count = 0
    local error_msgs = {}

    -- 根据保存类型执行相应的保存操作
    if save_type == "function" then
        if req.content then
            local json_content = cjson.encode(req.content)
            local ok, err = function_data.save_function(req.id, json_content)
            if ok then
                success_count = success_count + 1
            else
                table.insert(error_msgs, "保存函数失败: " .. (err or "未知错误"))
            end
        end
    elseif save_type == "rule" then
        if req.content then
            local json_content = cjson.encode(req.content)
            local ok, err = rule_data.save_rule(req.id, json_content)
            if ok then
                success_count = success_count + 1
            else
                table.insert(error_msgs, "保存规则失败: " .. (err or "未知错误"))
            end
        end
    elseif save_type == "xml" then
        if req.content then
            local ok, err = xml_data.save_xml_to_cache(req.id, req.content)
            if ok then
                success_count = success_count + 1
            else
                table.insert(error_msgs, "保存XML失败: " .. (err or "未知错误"))
            end
        end
    else
        ngx.say('{"code":1,"msg":"不支持的保存类型: ' .. save_type .. '"}')
        return
    end

    -- 返回处理结果
    if #error_msgs > 0 then
        ngx.say('{"code":1,"msg":"保存失败: ' .. table.concat(error_msgs, "; ") .. '","success_count":' .. success_count .. '}')
    else
        ngx.say('{"code":0,"msg":"保存成功","success_count":' .. success_count .. '}')
    end
end

-- 执行主函数
local ok, err = pcall(save_to_redis_script)
if not ok then
    ngx.say('{"code":1,"msg":"处理异常: ' .. err .. '"}')
end 