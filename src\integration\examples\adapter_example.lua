-- src/integration/examples/adapter_example.lua

-- Attempt to load cjson library
local cjson_status, cjson = pcall(require, "cjson")
if not cjson_status then
    print("Error: Failed to load cjson library. Please ensure it is installed and accessible.")
    print("You might need to run: lua<PERSON>s install lua-cjson")
    -- For OpenResty, cjson is usually available as ngx.decode_json or via cjson.safe
    -- local cjson_safe = require "cjson.safe"
    -- cjson = cjson_safe -- or handle appropriately
    return -- Stop execution if cjson is not available for this example
end

-- Require the transformer module from the previous example
-- Adjust the path if your Lua path setup is different or if files are moved.
-- Lua's require uses dots as path separators and typically searches in LUA_PATH.
-- Assuming 'src' is in LUA_PATH or the script is run from the project root.
local transformer_module_path = "src.integration.examples.transformer_example"
local transformer_loader_status, transformer_module = pcall(require, transformer_module_path)

if not transformer_loader_status then
    print("Error: Failed to load transformer module from: " .. transformer_module_path)
    print("Details: " .. tostring(transformer_module)) -- transformer_module here is the error message
    return
end
local TransformerA = transformer_module.TransformerA

-- Hypothetical raw JSON data string from "ThirdPartyA"
-- This structure matches the ThirdPartyA_Data table in transformer_example.lua
local third_party_json_data = [[
{
    "project_id": "PJT1002",
    "customer_name": "Ms. Li",
    "design_elements": [
        {
            "type": "Wardrobe",
            "id": "wardrobe001",
            "dimensions": { "w": 1200, "h": 2200, "d": 650 },
            "material": "Cherry Wood",
            "finish": "Glossy Lacquer"
        },
        {
            "type": "DrawerSet",
            "id": "drawer003",
            "parent_id": "wardrobe001",
            "size": { "length": 500, "depth": 450, "height_internal": 150 },
            "material": "Birch Plywood"
        }
    ],
    "notes": "Requires assembly on site."
}
]]

-- Adapter module for ThirdPartyA
local AdapterA = {}

function AdapterA.parse_json_string(json_string)
    if not json_string or json_string == "" then
        return nil, "Input JSON string is empty or nil"
    end

    local success, lua_table = pcall(cjson.decode, json_string)
    if not success then
        return nil, "Failed to decode JSON string: " .. tostring(lua_table) -- lua_table is error msg here
    end
    return lua_table
end

-- Example Usage
local function main()
    print("Raw JSON data from ThirdPartyA:")
    print(third_party_json_data)
    print("---")

    local parsed_data, err = AdapterA.parse_json_string(third_party_json_data)
    if err then
        print("Error during JSON parsing: " .. err)
        return
    end

    print("\nParsed Lua Table (from JSON):")
    -- A proper pretty printer would be better here.
    -- This is a very basic way to show some structure.
    if parsed_data then
        for k,v in pairs(parsed_data) do
            if type(v) == "table" then
                print(k .. ": [table]")
            else
                print(k .. ": " .. tostring(v))
            end
        end
    else
        print("Parsed data is nil.")
    end
    print("---")

    if not TransformerA then
        print("TransformerA module not loaded, cannot proceed with transformation.")
        return
    end
    
    local nhai_transformed_data, transform_err = TransformerA.transform(parsed_data)
    if transform_err then
        print("Error during transformation: " .. transform_err)
        return
    end

    print("\nTransformed NH-AI Canonical Data (after Adapter + Transformer):")
    if nhai_transformed_data then
        -- Basic print for the transformed data
        for k, v in pairs(nhai_transformed_data) do
            if type(v) == "table" then
                print(k .. ": [table - details omitted for brevity, see transformer_example for structure]")
            else
                print(k .. ": " .. tostring(v))
            end
        end
        -- Example: Print details of the first component if it exists
        if nhai_transformed_data.components and #nhai_transformed_data.components > 0 then
            local first_comp = nhai_transformed_data.components[1]
            print("  First component type: " .. (first_comp.componentType or "N/A"))
            print("  First component ID: " .. (first_comp.componentId or "N/A"))
        end
    else
        print("Transformed data is nil.")
    end
end

-- Run the example
main()

return AdapterA
