-- 计算生产参数 - 模块：靠墙立柜1格【左右】，厂商：中固层【立柜】，参数：CutOut

local _M = {}

function _M.dowork(root, target, context)
    -- 获取参数值并转换为数字
    local zhqj = tonumber(target.ZHQJ)
    local yhqj = tonumber(target.YHQJ)
    
    -- 参数检查
    if not zhqj then
        return nil, "获取不到ZHQJ参数值或参数值不是有效的数字"
    end
    if not yhqj then
        return nil, "获取不到YHQJ参数值或参数值不是有效的数字"
    end

    -- 判断逻辑
    if zhqj == 0 and yhqj == 0 then
        return 0
    elseif zhqj ~= 0 and yhqj ~= 0 then
        return 234
    elseif zhqj == 0 then
        return 24
    else
        return 23
    end
end

return _M