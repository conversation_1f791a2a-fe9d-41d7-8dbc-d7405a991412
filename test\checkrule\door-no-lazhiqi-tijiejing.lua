-- 如果门后没有拉直器，没有贴镜，则检查门是否与门内的板件干涉

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local geometry_engine = require('lib.oop-rule-engine')
local check_rule_result = require("lib.check-rule-result")
local xml_search_in_part = require("lib.xml-search-in-part")

-- 定义规则执行入口
local M = {}

-- 定义规则执行入口
function M.dowork(root, target, context)
	local rule_result = check_rule_result.new("门后物品检查", check_rule_result.LEVEL.INFO)

	if not root then
		return rule_result:pass("xml is nil")
	end

	-- 存储结果
	local result = {}
    local logs = {}

	-- 几何引擎查找所有有干涉的对象
	local engine_object = geometry_engine.current()

	-- 获取所有的门
	-- 过滤掉真门的情况
	local xml_parts_p = root:search("/Root/Parts/Part")
	local doors_xml = xml_search_in_part.get_part_xmllist_bykvs_in_part(xml_parts_p, "Parameters/Parameter",
		{
			{key = "name", value = "MKBQ"},
			{key = "value", value = "3"}
		}
	)

	if not doors_xml or #doors_xml == 0 then
		return rule_result:pass("没有找到任何门")
	end

	local door_intersect_inside_board_count = 0

	for _, door_xml in ipairs(doors_xml) do
		local MBLZQ = xml_search.get_value_by_child_node(door_xml, "Parameter", "name", "MBLZQ")
		if MBLZQ ~= "0" then
			goto continue
		end

		local MBTJ = xml_search.get_value_by_child_node(door_xml, "Parameter", "name", "MBTJ")
		if MBTJ ~= "0" then
			goto continue
		end

		local unit_xml = door_xml:parent()
		local all_unit_child_xml = unit_xml:search("./Part")
		-- 如果门后本来就没有东西 则不需要检查
		local other_child = false;
		for _, child_xml in ipairs(all_unit_child_xml) do
			local bjbq = xml_search.get_value_by_child_node(child_xml, "Parameter", "name", "BJBQ")
			-- 单元下有 中固板 抽芯  抽屉就当有其他素材 需要往下校验 否则 当没有素材 不再往下检查
			if bjbq == "5" or bjbq == "11" then
				other_child = true
				break
			end

			local mkbq = xml_search.get_value_by_child_node(child_xml, "Parameter", "name", "MKBQ")
			if mkbq == "4" then
				other_child = true
				break
			end
		end

		if not other_child then
			table.insert(logs, string.format("单元 %s 下没有其他素材，跳过", unit_xml:get_attribute("name")))
			goto continue
		end

		local door_uuid = door_xml:get_attribute("id")
		local door_name = door_xml:get_attribute("name")

		local door_intersect_inside_board = engine_object:get_door_intersect_inside_board(door_uuid, 7)

		-- ngx.log(ngx.INFO, "----------------- door_intersect_inside_board: ", #door_intersect_inside_board)

		if #door_intersect_inside_board == 0 then
			-- table.insert(logs, {
			-- 	prompt = string.format("门 %s 后面存在缩进＞7mm的物件，请确认是否继续保存", door_name),
			-- 	related_ids = {door_uuid}
			-- })
			table.insert(result, {
				prompt = string.format("门 %s 后面存在缩进＞7mm的物件，请确认是否继续保存", door_name),
				related_ids = {door_uuid}
			})
		end

		door_intersect_inside_board_count = #door_intersect_inside_board
		::continue::
	end

    table.insert(logs, "门后物品检查 检查完成 " .. door_intersect_inside_board_count)
    return rule_result:error(result, logs)
end

return M