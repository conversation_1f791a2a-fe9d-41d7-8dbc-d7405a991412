local lrucache = require "resty.lrucache"
local xmlua = require "xmlua"

-- 创建LRU缓存实例
local expr_cache, err = lrucache.new(10000)
if not expr_cache then
    error("failed to create expr cache: " .. (err or "unknown"))
end

local _M = {}

-- 计算字符串的MD5值
local function md5(str)
    return ngx.md5(str)
end

-- 创建基础执行环境
local function create_base_env()
    return {
        -- 基础数学函数
        math = math,
        -- 基础字符串函数
        string = string,
        -- 基础表操作
        table = table,
        -- 类型检查
        type = type,
        tonumber = tonumber,
        tostring = tostring,
        -- 直接暴露xmlua方法
        xmlua = xmlua
    }
end

-- 编译表达式
local function compile_expr(expr)
    -- 创建基础环境
    local env = create_base_env()
    -- 直接编译表达式
    local func, err = load("return " .. expr, "expr", "t", env)
    if not func then
        return nil, err
    end
    return func, env
end

-- 执行规则
function _M.execute_rule(rule, root, target, context)
    if not rule then
        return nil, "invalid arguments: rule is required"
    end

    -- 验证规则对象的基本属性
    if not rule.id or not rule.versionCode or not rule.luaScript then
        return nil, "invalid rule object: missing required fields (id, versionCode, luaScript)"
    end

    -- 使用表达式的MD5作为缓存键
    local cache_key = md5(rule.luaScript)

    -- 尝试从缓存中获取已编译的表达式
    local cache_data = expr_cache:get(cache_key)
    local expr_func, env

    if not cache_data then
        -- 编译表达式
        expr_func, env = compile_expr(rule.luaScript)
        if not expr_func then
            return nil, "failed to compile expr: " .. (env or "unknown")
        end
        -- 缓存函数和环境
        expr_cache:set(cache_key, { func = expr_func, env = env })
        cache_data = { func = expr_func, env = env }
    else
        expr_func = cache_data.func
        env = cache_data.env
    end

    -- 注入运行时变量到环境
    env.root = root or {}
    env.target = target or {}
    env.context = context or {}

    -- 执行表达式
    local success, result = pcall(expr_func)
    if not success then
        return nil, "failed to execute expr: " .. (result or "unknown")
    end

    return result
end

-- 清除缓存
function _M.clear_cache()
    local success = expr_cache:flush_all()
    if not success then
        return false, "清除表达式缓存失败"
    end
    return true, nil
end

-- 获取缓存统计信息
function _M.get_cache_stats()
    return {
        count = expr_cache:count(),
        capacity = expr_cache:capacity()
    }
end

return _M
