-- 常用的板件xml工具类


local _M = {}

local xml_search = require("lib.xml-search-in-node")

-- 辅助函数：判断B 是否完全在A的高度范围内 误差区间为offset
function _M.is_top_parallel(board_a_xml, board_b_xml, offset)
	if not board_a_xml or not board_b_xml then
		return false
	end

	local board_a_z = xml_search.find_value_in_node(board_a_xml, "Space", "absZ")
	local board_a_z_value = board_a_z and tonumber(board_a_z[1]) or 0
	local board_a_h = board_a_xml:get_attribute("H")
	local board_a_h_value = board_a_h and tonumber(board_a_h) or 0

	local board_b_z = xml_search.find_value_in_node(board_b_xml, "Space", "absZ")
	local board_b_z_value = board_b_z and tonumber(board_b_z[1]) or 0
	local board_b_h = board_b_xml:get_attribute("H")
	local board_b_h_value = board_b_h and tonumber(board_b_h) or 0

	local total_a = board_a_z_value + board_a_h_value
	local total_b = board_b_z_value + board_b_h_value

	local diff = total_a - total_b

	return diff >= -offset and diff <= offset
end


return _M
