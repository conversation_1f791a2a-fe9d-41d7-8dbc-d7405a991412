--[[
规则列表执行接口
接口路径：/api/v1/rule/process/check
请求方式：POST

请求参数结构：
{
    "xmlContent": "string", // XML文本内容，优先使用此字段
    "xmlCacheKey": "string", // Redis缓存中XML数据的键名，当xmlContent为空时使用
    "ruleIds": ["string"], // 规则ID列表
    "test_model"?: boolean // 是否为测试模式
}

执行逻辑：
1. 前置数据获取
1.1 获取XML数据
   - 优先使用xmlContent字段
   - 如果xmlContent为空，则从Redis缓存中获取
   - 测试模式下可以不传XML数据
1.2 获取规则列表
   - 根据ruleIds获取规则详情
   - 规则详情包含：规则ID、规则名称、执行脚本等

2. 初始化执行环境
2.1 初始化全局缓存
   - 用于存储规则间共享数据
2.2 初始化日志收集器
   - 同时输出到nginx日志和响应结果
2.3 初始化几何引擎(如需)
   - 用于处理几何相关的计算

3. 规则执行
3.1 串行执行规则列表
   - 执行环境包含：XML数据、全局缓存、日志收集器等
   - 每条规则执行后收集结果和日志
3.2 错误处理
   - 记录执行过程中的错误信息
   - 决定是否继续执行后续规则

响应参数结构：
{
    "success": boolean, // 执行是否成功
    "message": string, // 执行结果描述
    "data": [ // 规则执行结果列表
        {
            "ruleId": "string", // 规则ID
            "success": boolean, // 规则执行是否成功
            "data": any, // 规则执行结果数据
            "error": string // 规则执行错误信息
        }
    ],
    "logs": ["string"] // 执行过程日志列表
}
]]

local json = require("cjson")
local global_cache_util = require("lib/global-cache-util")
local rule_processor = require("lib/rule-processor")
local ngx = require("ngx")
local xmlua = require("xmlua")
-- 几何引擎
local geometry_engine = require("lib/oop-rule-engine")
local rule_data = require("lib/rule-data")
local xml_data = require("lib/xml-data")
-- 创建全局缓存实例
local global_cache = global_cache_util:new()

-- 引入测试数据
local test_data = require("mock/test_data")
-- 测试模式标志
local is_test_mode = false

-- 1. 前置数据获取 返回xmlNode 和 ruleList
local function get_pre_data(request)
    local xml_string = request.xmlContent
    local xml_cache_key = request.xmlCacheKey
    local ruleIds = request.ruleIds
    local type = request.type
    local rule_list = {}

    -- 1.1 获取XML数据
    if not xml_string and request.xmlCacheKey then
        xml_string = xml_data.get_xml_from_cache(xml_cache_key)
    end

    if not xml_string and not is_test_mode then
        return nil, "XML数据获取失败: "
    end

    -- 1.2 根据业务类型 获取规则列表
    -- 后续会修改成 外部直接传需要执行的规则列表keys
    if ruleIds and #ruleIds > 0 then
        for _, ruleId in ipairs(ruleIds) do
            local single_rule, err = rule_data.get_rule_by_id(ruleId)
            if not err then
                table.insert(rule_list, single_rule)
            end
        end
    end
    ngx.log(ngx.INFO, "收集到规则列表数量: " .. #rule_list)

    -- mvp特殊逻辑  如果没有传ruleIds 则根据type获取所有规则列表
    if rule_list and #rule_list == 0 and type == "detect-design" then
        rule_list = rule_data.get_rules_by_scene("check")
    end

    if not rule_list or #rule_list == 0 then
        return nil, "规则列表为空"
    end

    if is_test_mode then
        local data = test_data.mock_get_pre_data()
        xml_string = data.xmlNode
        rule_list = data.ruleList
    end

    local xml_node = xmlua.XML.parse(xml_string)

    return true, {
        xmlNode = xml_node,
        ruleList = rule_list,
        xmlString = xml_string
    }
end

-- 3. 规则分析
local function analyze_rules(pre_data)
    ngx.log(ngx.INFO, "开始规则分析")

    -- 3.1 检查是否需要几何引擎
    local need_geometry_engine = true

    if not pre_data or not pre_data.ruleList then
        return nil, "规则数据为空"
    end

    for _, rule in ipairs(pre_data.ruleList) do
        if rule.needGeometry then
            need_geometry_engine = true
            break ;
        end
    end

    -- 初始化几何引擎
    if need_geometry_engine then
        ngx.log(ngx.INFO, "需要初始化  几何引擎")
        -- 获取当前请求的几何引擎实例
        local engine = geometry_engine.new(pre_data.xmlString)
        -- 初始化几何引擎
        geometry_engine.set(engine)
        ngx.log(ngx.INFO, "初始化几何引擎完成")
    end

    -- 3.2 规则过滤？
    -- 3.3 规则前置处理？？

    return true, true
end

-- 4. 规则执行
local function execute_rule_lists(pre_data, additional_data)
    ngx.log(ngx.INFO, "开始执行规则")

    if not pre_data or not pre_data.ruleList then
        return nil, "规则数据为空"
    end

    -- 4.1 初始化全局缓存
    global_cache:clear_cache()

    local results = {}

    -- 4.4 串行执行规则 （考虑是否需要并发）
    for i, rule in ipairs(pre_data.ruleList) do
        ngx.log(ngx.INFO, string.format("执行规则 [%d/%d]: %s", i, #pre_data.ruleList, rule.id))
        -- 执行规则并捕获可能的错误
        local ok, data, err = pcall(rule_processor.execute_rule, rule, pre_data.xmlNode, nil, additional_data)
        
        local execute_result = {
            ruleId = rule.id,
            success = ok and data ~= nil,
            data = ok and data or nil
        }

        ngx.log(ngx.INFO, "执行规则结果: " .. json.encode(execute_result))

        table.insert(results, execute_result)

        if not execute_result.success then
            ngx.log(ngx.ERR, string.format("规则[%s]执行失败: %s", rule.id, data))
        end
    end

    -- 4.6 组织返回数据
    ngx.log(ngx.INFO, string.format("规则执行完成, 总计: %d", #results))
    return true, results
end

-- 清理引擎
local function clean_engine()
    local engine = geometry_engine.current()
    if engine then
        engine:scene_clear()
    end
    global_cache:clear_cache()
end

-- 失败处理辅助函数
local function handle_failure(step, error)
    ngx.log(ngx.ERR, string.format("%s失败: %s", step, error))
    clean_engine()
    return {
        success = false,
        message = string.format("%s失败：%s", step, error),
    }
end

-- 主处理函数
local function handle_script_execute()
    -- 读取和解析请求数据
    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    if not data then
        return ngx.say(json.encode({
            success = false,
            message = "请求体为空"
        }))
    end

    -- is_test_mode 从请求入参data中获得
    -- 如果 data包含属性 test_model 则设置is_test_mode为true
    local data_obj = json.decode(data)
    if data_obj and data_obj.test_model ~= nil and data_obj.test_model == true then
        is_test_mode = true
    end

    local request = json.decode(data)
    if not request then
        return ngx.say(json.encode({
            success = false,
            message = "JSON格式错误"
        }))
    end

    -- 执行规则处理流程
    local ok, result = xpcall(function()
        -- 1. 获取规则列表 组织xml数据
        ngx.log(ngx.INFO, "开始获取规则列表..." .. tostring(is_test_mode))
        local ok1, pre_data = get_pre_data(request)
        if not ok1 then
            return handle_failure("获取前置数据", pre_data)
        end

        -- 3. 分析规则 看要不要初始化几何引起等
        ngx.log(ngx.INFO, "开始分析规则...")
        local ok3, analyze_result = analyze_rules(pre_data)
        if not ok3 then
            return handle_failure("规则分析", analyze_result)
        end

        -- 4. 执行规则
        ngx.log(ngx.INFO, "开始执行规则...")
        local ok4, execute_result = execute_rule_lists(pre_data)
        if not ok4 then
            return handle_failure("规则执行", execute_result)
        end
        ngx.log(ngx.INFO, "规则执行成功 ")

        -- 5. 清理环境
        clean_engine()

        return {
            success = true,
            message = "执行成功",
            data = execute_result
        }
    end, debug.traceback)

    -- 返回结果
    if not ok then
        -- 处理Lua错误
        ngx.log(ngx.ERR, "Lua执行错误: " .. tostring(result))
        local final_result = {
            success = false,
            message = "系统执行异常"
        }
        return ngx.say(json.encode(final_result))
    end

    -- 统计执行结果
    local total_count = 0
    local error_count = 0
    if result.data and type(result.data) == "table" then
        total_count = #result.data
        for _, item in ipairs(result.data) do
            if not item.success then
                error_count = error_count + 1
            end
        end
    end

    local final_result = {
        success = result.success,
        message = result.message,
        results = result.data,
        summary = string.format("规则执行成功: %d, 执行失败: %d", total_count, error_count)
    }

    return ngx.say(json.encode(final_result))
end

-- 执行主函数
handle_script_execute()
