=encoding utf-8


=head1 Name

lua-resty-upstream-healthcheck - Health-checker for Nginx upstream servers


=head1 Status

This library is still under early development but is already production ready.


=head1 Synopsis


    http {
        lua_package_path "/path/to/lua-resty-upstream-healthcheck/lib/?.lua;;";
    
        # sample upstream block:
        upstream foo.com {
            server 127.0.0.1:12354;
            server 127.0.0.1:12355;
            server 127.0.0.1:12356 backup;
        }
    
        # the size depends on the number of servers in upstream {}:
        lua_shared_dict healthcheck 1m;
    
        lua_socket_log_errors off;
    
        init_worker_by_lua_block {
            local hc = require "resty.upstream.healthcheck"
    
            local ok, err = hc.spawn_checker{
                shm = "healthcheck",  -- defined by "lua_shared_dict"
                upstream = "foo.com", -- defined by "upstream"
                type = "http", -- support "http" and "https"
    
                http_req = "GET /status HTTP/1.0\r\nHost: foo.com\r\n\r\n",
                        -- raw HTTP request for checking
    
                port = nil,  -- the check port, it can be different than the original backend server port, default means the same as the original backend server
                interval = 2000,  -- run the check cycle every 2 sec
                timeout = 1000,   -- 1 sec is the timeout for network operations
                fall = 3,  -- # of successive failures before turning a peer down
                rise = 2,  -- # of successive successes before turning a peer up
                valid_statuses = {200, 302},  -- a list valid HTTP status code
                concurrency = 10,  -- concurrency level for test requests
                -- ssl_verify = true, -- https type only, verify ssl certificate or not, default true
                -- host = foo.com, -- https type only, host name in ssl handshake, default nil
            }
            if not ok then
                ngx.log(ngx.ERR, "failed to spawn health checker: ", err)
                return
            end
    
            -- Just call hc.spawn_checker() for more times here if you have
            -- more upstream groups to monitor. One call for one upstream group.
            -- They can all share the same shm zone without conflicts but they
            -- need a bigger shm zone for obvious reasons.
        }
    
        server {
            ...
    
            # status page for all the peers:
            location = /status {
                access_log off;
                allow 127.0.0.1;
                deny all;
    
                default_type text/plain;
                content_by_lua_block {
                    local hc = require "resty.upstream.healthcheck"
                    ngx.say("Nginx Worker PID: ", ngx.worker.pid())
                    ngx.print(hc.status_page())
                }
            }
    
    	# status page for all the peers (prometheus format):
            location = /metrics {
                access_log off;
                default_type text/plain;
                content_by_lua_block {
                    local hc = require "resty.upstream.healthcheck"
                    st , err = hc.prometheus_status_page()
                    if not st then
                        ngx.say(err)
                        return
                    end
                    ngx.print(st)
                }
            }
        }
    }


=head1 Description

This library performs healthcheck for server peers defined in NGINX C<upstream> groups specified by names.




=head1 Methods


=head2 spawn_checker

B<syntax:> C<ok, err = healthcheck.spawn_checker(options)>

B<context:> I<init_worker_by_luaE<42>>

Spawns background timer-based "light threads" to perform periodic healthchecks on
the specified NGINX upstream group with the specified shm storage.

The healthchecker does not need any client traffic to function. The checks are performed actively
and periodically.

This method call is asynchronous and returns immediately.

Returns true on success, or C<nil> and a string describing an error otherwise.




=head2 status_page

B<syntax:> C<str, err = healthcheck.status_page()>

B<context:> I<any>

Generates a detailed status report for all the upstreams defined in the current NGINX server.

One typical output is


    Upstream foo.com
        Primary Peers
            127.0.0.1:12354 UP
            127.0.0.1:12355 DOWN
        Backup Peers
            127.0.0.1:12356 UP
    
    Upstream bar.com
        Primary Peers
            127.0.0.1:12354 UP
            127.0.0.1:12355 DOWN
            127.0.0.1:12357 DOWN
        Backup Peers
            127.0.0.1:12356 UP

If an upstream has no health checkers, then it will be marked by C<(NO checkers)>, as in


    Upstream foo.com (NO checkers)
        Primary Peers
            127.0.0.1:12354 UP
            127.0.0.1:12355 UP
        Backup Peers
            127.0.0.1:12356 UP

If you indeed have spawned a healthchecker in C<init_worker_by_lua*>, then you should really
check out the NGINX error log file to see if there is any fatal errors aborting the healthchecker threads.




=head1 Multiple Upstreams

One can perform healthchecks on multiple C<upstream> groups by calling the L<spawn_checker> method
multiple times in the C<init_worker_by_lua*> handler. For example,


    upstream foo {
        ...
    }
    
    upstream bar {
        ...
    }
    
    lua_shared_dict healthcheck 1m;
    
    lua_socket_log_errors off;
    
    init_worker_by_lua_block {
        local hc = require "resty.upstream.healthcheck"
    
        local ok, err = hc.spawn_checker{
            shm = "healthcheck",
            upstream = "foo",
            ...
        }
    
        ...
    
        ok, err = hc.spawn_checker{
            shm = "healthcheck",
            upstream = "bar",
            ...
        }
    }

Different upstreams' healthcheckers use different keys (by always prefixing the keys with the
upstream name), so sharing a single C<lua_shared_dict> among multiple checkers should not have
any issues at all. But you need to compensate the size of the shared dict for multiple users (i.e., multiple checkers).
If you have many upstreams (thousands or even more), then it is more optimal to use separate shm zones
for each (group) of the upstreams.




=head1 Installation

If you are using L<OpenResty|http://openresty.org> ******* or later, then you should already have this library (and all of its dependencies) installed by default (and this is also the recommended way of using this library). Otherwise continue reading:

You need to compile both the L<ngx_lua|https://github.com/openresty/lua-nginx-module> and L<ngx_lua_upstream|https://github.com/openresty/lua-upstream-nginx-module> modules into your Nginx.

The latest git master branch of L<ngx_lua|https://github.com/openresty/lua-nginx-module> is required.

You need to configure
the L<lua_package_path|https://github.com/openresty/lua-nginx-module#lua_package_path> directive to
add the path of your C<lua-resty-upstream-healthcheck> source tree to L<ngx_lua|https://github.com/openresty/lua-nginx-module>'s Lua module search path, as in


    # nginx.conf
    http {
        lua_package_path "/path/to/lua-resty-upstream-healthcheck/lib/?.lua;;";
        ...
    }




=head1 TODO




=head1 Community




=head2 Contributing

Use C<make lint> to lint the code before you open a PR. This uses the widely used L<LuaFormatter|https://github.com/Koihik/LuaFormatter>.

The code style is described in the L<`.lua-format`|.lua-format> file.\
If you are using VS Code, you can install the wrapper for that formatter by clicking L<here|vscode:extension/Koihik.vscode-lua-format>.




=head2 English Mailing List

The L<openresty-en|https://groups.google.com/group/openresty-en> mailing list is for English speakers.




=head2 Chinese Mailing List

The L<openresty|https://groups.google.com/group/openresty> mailing list is for Chinese speakers.




=head1 Bugs and Patches

Please report bugs or submit patches by


=over


=item 1.

creating a ticket on the L<GitHub Issue Tracker|http://github.com/openresty/lua-resty-upstream-healthcheck/issues>,

=item 2.

or posting to the L<OpenResty community>.


=back




=head1 Author

Yichun "agentzh" Zhang (章亦春) E<lt><EMAIL><gt>, OpenResty Inc.




=head1 Copyright and License

This module is licensed under the BSD license.

Copyright (C) 2014-2017, by Yichun "agentzh" Zhang, OpenResty Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:


=over


=item *

Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.


=back


=over


=item *

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

the ngx_lua module: https://github.com/openresty/lua-nginx-module

=item *

the ngx_lua_upstream module: https://github.com/openresty/lua-upstream-nginx-module

=item *

OpenResty: http://openresty.org


=back



