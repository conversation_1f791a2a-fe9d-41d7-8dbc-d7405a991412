local redis = require "resty.redis"
local redis_cluster = require "resty.rediscluster"
local zstandard = require "lib/zstd"
local safe_json = require('cjson')

local DEFAULT_CLUSTER_NODES = {
    { ip = "***********", port = 7001 },
    { ip = "***********", port = 7002 },
    { ip = "***********", port = 7003 },
    { ip = "***********", port = 7004 },
    { ip = "***********", port = 7005 },
    { ip = "***********", port = 7006 }
}
local DEFAULT_PASSWORD = "3dxt"

local _M = {
    -- 模块版本
    _VERSION = '0.1.0',
    -- 访问模式：仅支持集群访问，不支持单机访问
    _MODE = 'cluster',
}

-- 仅考虑集群访问
local config = {
    name = 'RuleEngineRedis',
    serv_list = nil,
    auth = nil,
    -- redis connection pool idle timeout
    keepalive_timeout = 60000,
    -- redis connection pool size
    keepalive_cons = 1000,
    -- timeout while connecting
    connect_timeout = 1000,
    -- maximum retry attempts for redirection
    max_redirection = 5,
    -- maximum retry attempts for connection
    max_connection_attempts = 3
}
local instance = nil

local function load_config()
    if config.serv_list ~= nil then
        return config
    end

    local env_nodes = os.getenv('REDIS_CLUSTER_NODES')
    if env_nodes ~= nil then
        local nodes, err = safe_json.decode(env_nodes)
        if #nodes > 0 and err == nil then
            config.serv_list = nodes
        end
    end
    if config.serv_list == nil then
        -- 依然为空，则使用默认配置
        config.serv_list = DEFAULT_CLUSTER_NODES
    end

    config.auth = os.getenv("REDIS_CLUSTER_PWD") or DEFAULT_PASSWORD
end

local function client()
    if instance == nil then
        instance = redis_cluster:new(config)
    end
    return instance
end

-- 直接加载配置
load_config()

-- ngx.log(ngx.ERR, "redis 集群配置: " .. safe_json.encode(config))

function _M.version()
    return _M._VERSION
end

function _M.mode()
    return _M._MODE
end

-- 获取键值
-- @param key 键名
-- @return val 值
-- @return err 错误信息
function _M.get(key)
    if key == nil or key == '' then
        return nil, 'key 不能为空'
    end
    local val, err = client():get(key)
    if err then
        return nil, err
    end
    if val == ngx.null then
        return nil, nil
    end
    return val, nil
end

-- 设置键值对
-- @param key 键名
-- @param val 值（不支持 table 对象，需要自己通过 cjson 完成编码）
-- @param expire_seconds 过期时间（秒），可选
-- @return success 是否成功
-- @return err 错误信息
function _M.set(key, val, expire_seconds)
    if key == nil or key == '' then
        return false, 'key 不能为空'
    end
    local ok, err
    if expire_seconds ~= nil and tonumber(expire_seconds) > 0 then
        ok, err = client():set(key, val, 'EX', expire_seconds)
    else
        ok, err = client():set(key, val)
    end
    if not ok then
        return false, err
    end
    return true, nil
end

-- 删除键
-- @param keys 键名或键名数组
-- @return success 删除是否成功
-- @return err 错误信息
function _M.del(keys)
    if type(keys) == 'string' and keys ~= '' then
        local count, err = client():del(keys)
        if err ~= nil then
            return false, err
        end
        return true, nil
    end

    if type(keys) ~= 'table' or #keys == 0 then
        return 0, 'keys 不能为空'
    end

    -- 使用管道删除
    client():init_pipeline()
    for i, v in ipairs(keys) do
        client():del(v)
    end
    local _, err = client():commit_pipeline()
    if err ~= nil then
        return false, err
    end
    return true, nil
end

-- 获取键值（zstd 解压缩）
-- @param key 键名
-- @return val 解压缩后的值
-- @return err 错误信息
function _M.zstd_get(key)
    local val, err = _M.get(key)
    if err ~= nil then
        return nil, err
    end
    if val == nil then
        return nil, nil
    end
    -- zstd 解压缩
    local zstd = zstandard:new()
    local data, decompress_err = zstd:decompress(val)
    zstd:free()
    if decompress_err ~= nil then
        return nil, decompress_err
    end
    return data, nil
end

-- 设置键值对（zstd 压缩）
-- @param key 键名
-- @param value 值（将自动执行 zstd 压缩）
-- @param expire_seconds 过期时间（秒），可选
-- @return success 是否成功
-- @return err 错误信息
function _M.zstd_set(key, value, expire_seconds)
    if (key == nil or key == '') or value == nil then
        return false, "key 和 value 不能为空"
    end

    -- zstd 压缩
    local zstd = zstandard:new()
    local data, err = zstd:compress(value)
    zstd:free()

    if err ~= nil then
        return false, err
    end

    return _M.set(key, data, expire_seconds)
end

-- 批量获取多个键值对
-- @param keys 键名数组
-- @return map 键值对表，键名和键值一一对应（如果某个键不存在则 map 中该值为 nil）
-- @return err 错误信息
function _M.mget(keys)
    if keys == nil or #keys == 0 then
        return nil, 'keys 不能为空'
    end

    client():init_pipeline()
    for i, v in ipairs(keys) do
        client():get(v)
    end
    local res, err = client():commit_pipeline()
    if err ~= nil then
        return nil, err
    end

    local map = {}
    for i, v in ipairs(res) do
        if v == ngx.null then
            -- 注意 nil 在 cjson 序列化时会被忽略
            map[keys[i]] = nil
        else
            map[keys[i]] = v
        end
    end
    return map, nil
end

-- 批量设置多个键值对
-- @param array 键值对数组，格式为 { { key = 'key1', value = 'value1', expire_seconds = 100 }, { key = 'key2', value = 'value2' } }
-- @return success 是否成功
-- @return err 错误信息
function _M.mset(array)
    if array == nil or #array == 0 then
        return false, 'array 不能为空'
    end

    client():init_pipeline()
    for i, obj in ipairs(array) do
        local k = obj.key
        local v = obj.value
        if (k == nil or k == '') or v == nil then
            -- 任何一个不满足直接快速失败
            return false, 'key 和 value 不能为空'
        end
        if obj.expire_seconds ~= nil and tonumber(obj.expire_seconds) > 0 then
            client():set(k, v, 'EX', obj.expire_seconds)
        else
            client():set(k, v)
        end
    end
    local res, err = client():commit_pipeline()
    if err ~= nil then
        return false, err
    end
    return true, nil
end

-- 批量获取多个键值对（zstd 解压缩）
-- @param keys 键名数组
-- @return map 键值对表，键名和解压缩后的键值一一对应（如果某个键不存在则 map 中该值为 nil）
-- @return err 错误信息
function _M.zstd_mget(keys)
    local map, err = _M.mget(keys)
    if err ~= nil then
        return nil, err
    end
    if map == nil then
        return nil, nil
    end

    local dmap = {}
    for k, v in pairs(map) do
        if v == nil then
            dmap[k] = nil
        else
            -- zstd 解压缩
            local zstd = zstandard:new()
            local data, decompress_err = zstd:decompress(v)
            zstd:free()
            -- 只要有一个解压缩失败就直接返回错误
            if decompress_err ~= nil then
                return nil, decompress_err
            end
            dmap[k] = data
        end
    end

    return dmap, nil
end

-- 批量设置多个键值对（zstd 压缩）
-- @param array 键值对数组，格式为 { { key = 'key1', value = 'value1', expire_seconds = 100 }, { key = 'key2', value = 'value2' } }
-- @return success 是否成功
-- @return err 错误信息
function _M.zstd_mset(array)
    if array == nil or #array == 0 then
        return false, 'array 不能为空'
    end

    local map = {}
    local ttl_map = {}

    for i, obj in ipairs(array) do
        local k = obj.key
        local v = obj.value
        if (k == nil or k == '') or v == nil then
            -- 任何一个不满足直接快速失败
            return false, 'key 和 value 不能为空'
        end

        -- zstd 压缩
        local zstd = zstandard:new()
        local data, err = zstd:compress(v)
        zstd:free()

        if err ~= nil then
            return false, 'zstd 压缩失败：' .. err
        end

        if obj.expire_seconds ~= nil and tonumber(obj.expire_seconds) > 0 then
            table.insert(ttl_map, { key = k, value = data, expire_seconds = obj.expire_seconds })
        else
            table.insert(map, { key = k, value = data })
        end
    end

    client():init_pipeline()
    for _, obj in ipairs(map) do
        client():set(obj.key, obj.value)
    end
    for _, obj in ipairs(ttl_map) do
        client():set(obj.key, obj.value, 'EX', obj.expire_seconds)
    end
    local _, err = client():commit_pipeline()
    if err ~= nil then
        return false, err
    end
    return true, nil
end

-- 在单个节点上执行 scan 命令
-- @param node_ip 节点 IP
-- @param node_port 节点端口
-- @param pattern 匹配模式
-- @return keys 键列表
-- @return err 错误信息
local function scan_node(node_ip, node_port, pattern)
    local redis_client = redis:new()
    redis_client:set_timeout(config.connect_timeout or 1000)

    local ok, err = redis_client:connect(node_ip, node_port)
    if not ok then
        return nil, "连接节点失败: " .. (err or "unknown")
    end

    -- 认证
    if config.auth then
        local auth_ok, auth_err = redis_client:auth(config.auth)
        if not auth_ok then
            redis_client:close()
            return nil, "认证失败: " .. (auth_err or "unknown")
        end
    end

    local cursor = "0"
    local keys = {}

    repeat
        local res, scan_err = redis_client:scan(cursor, "MATCH", pattern)
        if not res then
            redis_client:close()
            return nil, "scan 失败: " .. (scan_err or "unknown")
        end

        cursor = res[1]
        for i, key in ipairs(res[2]) do
            table.insert(keys, key)
        end
    until cursor == "0"

    redis_client:set_keepalive(config.keepalive_timeout, config.keepalive_cons)
    return keys, nil
end

-- 获取集群中所有主节点
-- @return nodes 主节点列表
-- @return err 错误信息
local function get_master_nodes()
    local nodes_res, err = client():cluster("nodes")
    if not nodes_res then
        return nil, "获取集群节点失败: " .. (err or "unknown")
    end

    local nodes = {}
    local nodes_info = {}
    for line in string.gmatch(nodes_res, "[^\n]+") do
        table.insert(nodes_info, line)
    end

    for _, node in ipairs(nodes_info) do
        local parts = {}
        for part in string.gmatch(node, "[^ ]+") do
            table.insert(parts, part)
        end

        if #parts > 2 then
            local is_master = string.find(parts[3], "master") ~= nil
            if is_master then
                local ip_port = string.match(parts[2], "([^@:]+:[^@:]+)")
                if ip_port then
                    local ip, port = string.match(ip_port, "([^:]+):([^:]+)")
                    if ip and port then
                        table.insert(nodes, { ip = ip, port = tonumber(port) })
                    end
                end
            end
        end
    end

    if #nodes == 0 then
        return nil, "未找到主节点"
    end

    return nodes, nil
end

-- 扫描整个集群中匹配模式的键
-- @param pattern 匹配模式
-- @return keys 键列表
-- @return err 错误信息
function _M.keys(pattern)
    if pattern == nil or pattern == "" then
        return nil, "pattern 不能为空"
    end

    -- 获取所有主节点
    local master_nodes, err = get_master_nodes()
    if not master_nodes then
        return nil, err
    end

    local all_keys = {}
    local errors = {}

    -- 对每个主节点执行 scan 命令
    for _, node in ipairs(master_nodes) do
        local node_keys, node_err = scan_node(node.ip, node.port, pattern)
        if node_keys then
            for _, key in ipairs(node_keys) do
                table.insert(all_keys, key)
            end
        else
            table.insert(errors, node_err)
        end
    end

    if #errors > 0 then
        return nil, table.concat(errors, "; ")
    end

    return all_keys, nil
end

return _M

