-- 当前基于 xmlua 实现（后续可能改成 pugilua 实现）
local xmlua = require('xmlua')

local _M = {
    -- 模块版本
    _VERSION = '0.1.0',
}

--- 解析xml文本
---@param xml_text string xml文本
---@return table|nil document 文档对象
---@return string|nil err 错误信息
function _M.parse(xml_text)
    if xml_text == nil or xml_text == '' then
        return nil, 'xml文本不能为空'
    end
    local success, doc = pcall(xmlua.XML.parse, xml_text)
    if not success then
        return nil, 'xml解析失败: ' .. tostring(doc)
    end
    return doc, nil
end

--- xpath查询
---@param doc table 文档对象
---@param xpath string xpath表达式
---@return table|nil nodes 节点列表
---@return string|nil err 错误信息
function _M.search(doc, xpath)
    local ok, nodes = pcall(function()
        return doc:search(xpath)
    end)
    if not ok then
        return nil, 'xpath查询失败: ' .. tostring(nodes)
    end
    return nodes, nil
end

--- 获取节点文本内容
---@param element table 节点对象
---@return string|nil 节点文本内容
function _M.text(element)
    return element:text()
end

--- 获取节点文本内容，与 text 方法效果一致，但会返回错误信息
---@param element table 节点对象
---@return string|nil text 节点文本内容
---@return string|nil err 错误信息
function _M.text_safe(element)
    local ok, text = pcall(function()
        return element:text()
    end)
    if not ok then
        return nil, '获取节点内容失败: ' .. tostring(text)
    end
    return text, nil

end

--[[
--- 获取节点文本内容，与 text 方法效果一致
---@param element table 节点对象
---@return string|nil 节点文本内容
function _M.content(element)
    return element:content()
end
]]

--- 获取节点属性
---@param element table 节点对象
---@param attr string 属性名称
---@return string|nil 节点属性值
function _M.attribute(element, attr)
    return element:get_attribute(attr)
end

--- 获取节点属性，与 attribute 方法效果一致，但会返回错误信息
---@param element table 节点对象
---@param attr string 属性名称
---@return string|nil value 节点属性值
---@return string|nil err 错误信息
function _M.attribute_safe(element, attr)
    local ok, value = pcall(function()
        return element:get_attribute(attr)
    end)
    if not ok then
        return nil, '获取节点属性失败: ' .. tostring(value)
    end
    return value, nil
end

--- 获取xml文本
---@param doc table 节点对象
---@return string|nil xml文本
function _M.to_xml(doc)
    return doc:to_xml({ declaration = false })
end

--- 获取xml文本，与 to_xml 方法效果一致，但会返回错误信息
---@param doc table 节点对象
---@return string|nil xml xml文本
---@return string|nil err 错误信息
function _M.to_xml_safe(doc)
    local ok, xml = pcall(function()
        return doc:to_xml({ declaration = false })
    end)
    if not ok then
        return nil, '获取xml文本失败: ' .. tostring(xml)
    end
    return xml, nil
end

--- 获取单个节点对象
---@param doc table 文档对象
---@param xpath string xpath表达式
---@return table|nil node 节点对象
---@return string|nil err 错误信息
function _M.get_single_node(doc, xpath)
    local ok, nodes = pcall(function()
        return doc:search(xpath)
    end)
    if not ok then
        return nil, 'xpath查询失败: ' .. tostring(nodes)
    end
    if not nodes or #nodes == 0 then
        return nil, '未找到匹配的节点'
    end
    return nodes[1], nil
end

return _M
