-- 计算生产参数 - 模块：平板三边抽屉，厂商：三边抽芯，参数：CXHS

local _M = {}

function _M.dowork(root, target, context)
    -- 获取参数值并转换为数字
    local cxhd = tonumber(target:parent().CXHD)
    local sfkxcxhs = tonumber(target:parent().SFKXCXHS)
    
    -- 参数检查
    if not cxhd then
        return nil, "获取不到CXHD参数值或参数值不是有效的数字"
    end

    -- 判断逻辑
    if cxhd == 12 then
        return target:parent().CXHS
    elseif cxhd == 18 then
        if not sfkxcxhs then
            return nil, "获取不到SFKXCXHS参数值或参数值不是有效的数字"
        end
        if sfkxcxhs == 0 then
            return target:parent().CXHS18
        elseif sfkxcxhs == 1 then
            return target:parent().CXHS18KX
        end
    end
    
    return ""
end

return _M