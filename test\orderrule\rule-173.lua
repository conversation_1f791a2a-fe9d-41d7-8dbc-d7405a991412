-- 计算生产参数 - 模块：靠墙立柜1格【左右】，厂商：上固层【立柜】，参数：EXIST_SGT

local _M = {}

local Engine = require("lib.oop-rule-engine")

function _M.dowork(root, target, context)
     -- 获取参数值并转换为数字
     local height = tonumber(target.absZ)

     -- 参数检查
     if not height then
          return nil, "获取不到Z参数值或参数值不是有效的数字"
     end

     -- 判断逻辑
     if height < 1600 then
          return 1
     end

     local engine_object = Engine.current()
     if not engine_object then
          return nil, "获取不到几何引擎对象"
     end

     -- 高度大于等于1600时，判断是否有顶线
     local directions = { "up" }
     local relations = engine_object:get_objects_distance("id", "==", target.id, directions, "<=", 1, "MKBQ", "==", "2")
     local has_top_line = relations and #relations > 0

     -- 高度≥1600且无顶线时返回1，否则返回0
     if not has_top_line then
          return 1
     end

     return 0
end

return _M
