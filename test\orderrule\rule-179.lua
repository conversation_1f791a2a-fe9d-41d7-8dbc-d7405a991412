-- 计算生产参数 - 模块：平板三边抽屉，厂商：三边抽芯，参数：CHBZ

local _M = {}

function _M.dowork(root, target, context)
    -- 定义CXHD和S_RAIL的对应关系
    local rail_code_map = {
        [12] = {  -- CXHD = 12
            -- S_RAIL = 11-16
            [11] = "S12GC3:",
            [12] = "S12GC3:",
            [13] = "S12GC3:",
            [14] = "S12GC3:",
            [15] = "S12GC3:",
            [16] = "S12GC3:",
            -- S_RAIL = 72-76
            [72] = "HS12GCZ:",
            [73] = "HS12GCZ:",
            [74] = "HS12GCZ:",
            [75] = "HS12GCZ:",
            [76] = "HS12GCZ:",
            -- S_RAIL = 82-86
            [82] = "HS12XQL:",
            [83] = "HS12XQL:",
            [84] = "HS12XQL:",
            [85] = "HS12XQL:",
            [86] = "HS12XQL:",
            -- S_RAIL = 93-96
            [93] = "HS12GCT:",
            [94] = "HS12GCT:",
            [95] = "HS12GCT:",
            [96] = "HS12GCT:",
            -- S_RAIL = 722-726
            [722] = "HS12BLZ:",
            [723] = "HS12BLZ:",
            [724] = "HS12BLZ:",
            [725] = "HS12BLZ:",
            [726] = "HS12BLZ:",
            -- S_RAIL = 773-776
            [773] = "HS12BLT:",
            [774] = "HS12BLT:",
            [775] = "HS12BLT:",
            [776] = "HS12BLT:"
        },
        [18] = {  -- CXHD = 18
            -- S_RAIL = 11-16
            [11] = "S18GC3:",
            [12] = "S18GC3:",
            [13] = "S18GC3:",
            [14] = "S18GC3:",
            [15] = "S18GC3:",
            [16] = "S18GC3:",
            -- S_RAIL = 72-76
            [72] = "HS18GCZ:",
            [73] = "HS18GCZ:",
            [74] = "HS18GCZ:",
            [75] = "HS18GCZ:",
            [76] = "HS18GCZ:",
            -- S_RAIL = 82-86
            [82] = "HS18XQL:",
            [83] = "HS18XQL:",
            [84] = "HS18XQL:",
            [85] = "HS18XQL:",
            [86] = "HS18XQL:",
            -- S_RAIL = 93-96
            [93] = "HS18GCT:",
            [94] = "HS18GCT:",
            [95] = "HS18GCT:",
            [96] = "HS18GCT:",
            -- S_RAIL = 722-726
            [722] = "HS18BLZ:",
            [723] = "HS18BLZ:",
            [724] = "HS18BLZ:",
            [725] = "HS18BLZ:",
            [726] = "HS18BLZ:",
            -- S_RAIL = 773-776
            [773] = "HS18BLT:",
            [774] = "HS18BLT:",
            [775] = "HS18BLT:",
            [776] = "HS18BLT:"
        }
    }

    -- 获取参数值并转换为数字
    local cxhd = tonumber(target.CXHD)
    local s_rail = tonumber(target.S_RAIL)
    
    -- 参数检查
    if not cxhd then
        return nil, "获取不到CXHD参数值或参数值不是有效的数字"
    end
    if not s_rail then
        return nil, "获取不到S_RAIL参数值或参数值不是有效的数字"
    end
    
    -- 检查CXHD是否有效
    local cxhd_map = rail_code_map[cxhd]
    if not cxhd_map then
        return nil, "CXHD参数值不在有效范围内（应为12或18）"
    end
    
    -- 获取对应的返回值
    local result = cxhd_map[s_rail]
    if not result then
        return nil, "S_RAIL参数值不在有效范围内"
    end
    
    return result
end

return _M