local lrucache = require "resty.lrucache"

-- 创建LRU缓存实例，设置最大缓存项数为10000
-- 使用更大的缓存容量，因为规则编译是相对昂贵的操作
local rule_cache, err = lrucache.new(10000)
if not rule_cache then
    error("failed to create rule cache: " .. (err or "unknown"))
end

local _M = {}

-- 编译规则字符串为模块
local function compile_rule(lua_script)
    -- 使用_G作为环境，允许访问所有全局函数
    local func, err = load(lua_script, "rule", "t", _G)
    if not func then
        return nil, err
    end
    -- 执行一次，拿到模块table
    local ok, mod = pcall(func)
    if not ok then
        return nil, "failed to load module: " .. (mod or "unknown")
    end
    if type(mod) ~= "table" or type(mod.dowork) ~= "function" then
        return nil, "module must return a table with dowork function"
    end
    return mod
end

-- 执行规则
function _M.execute_rule(rule, root, target, context)
    if not rule then
        return nil, "invalid arguments: rule is required"
    end

    -- 验证规则对象的基本属性
    if not rule.id or not rule.versionCode or not rule.luaScript then
        return nil, "invalid rule object: missing required fields (id, versionCode, luaScript)"
    end

    -- 使用规则ID和版本号作为缓存键
    local cache_key = rule.id .. ":" .. rule.versionCode

    -- 尝试从缓存中获取已编译的规则
    local compiled_mod = rule_cache:get(cache_key)

    if not compiled_mod then
        -- 编译规则
        compiled_mod, err = compile_rule(rule.luaScript)
        if not compiled_mod then
            return nil, "failed to compile rule: " .. (err or "unknown")
        end
        -- 缓存编译后的模块
        rule_cache:set(cache_key, compiled_mod)
    end
    -- 调用模块的 dowork 方法
    local success, result, err_msg = pcall(compiled_mod.dowork, root or {}, target or {}, context or {})
    if not success then
        return nil, "failed to execute rule: " .. (result or "unknown")
    end
    
    -- 如果结果是nil，说明规则返回了错误信息
    if result == nil then
        return nil, err_msg
    end
    
    return result
end

-- 清除缓存
function _M.clear_cache()
    local success = rule_cache:flush_all()
    if not success then
        return false, "清除规则缓存失败"
    end
    return true, nil
end

-- 获取缓存统计信息
function _M.get_cache_stats()
    return {
        count = rule_cache:count(),
        capacity = rule_cache:capacity()
    }
end

return _M
