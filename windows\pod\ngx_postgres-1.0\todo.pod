=encoding utf-8

Features that sooner or later will be added to C<ngx_postgres>:


=over


=item *

Add support for SSL connections to the database.


=back


=over


=item *

Add support for dropping of idle keep-alived connections to the
database.


=back


=over


=item *

Add C<$postgres_error> variable.


=back


=over


=item *

Add support for sending mulitple queries in one go (transactions,
multiple SELECTs, etc), this will require changes in the processing
flow B<and> RDS format.


=back


=over


=item *

Add C<postgres_escape_bytea> or C<postgres_escape_binary>.


=back


=over


=item *

Use C<PQescapeStringConn()> instead of C<PQescapeString()>, this will
require lazy-evaluation of the variables after we acquire connection,
but before we send query to the database.
Notes: Don't break C<$postgres_query>.


=back


=over


=item *

Cancel long-running queries using C<PQcancel()>.


=back


=over


=item *

Detect server version using C<PQserverVersion()>.

=back

