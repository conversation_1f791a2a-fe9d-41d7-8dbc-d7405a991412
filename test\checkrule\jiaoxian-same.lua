-- 检测同一个空间内的脚线参数是否一致

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local check_rule_result = require("lib.check-rule-result")

local M = {}

-- 定义规则执行入口
function M.dowork(root, target, context)
	local rule_result = check_rule_result.new("同空间脚线高度不一致", check_rule_result.LEVEL.INFO)

	if not root then
		return rule_result:pass("xml is nil")
	end
	
	-- 存储结果
	local result = {}
	local logs = {}

	-- 获取脚线
	local kick_xml = root:search("//Part[@productId='3FO3OWXEKIH3']")

	local groups = {}
	for _, node in ipairs(kick_xml) do
		-- 创建分组键
		local id = node:get_attribute("id")
		local roomid = node:get_attribute("roomId")
		local group_key = roomid

		-- 创建或添加到分组
		if not groups[group_key] then
			groups[group_key] = {}
		end

		table.insert(groups[group_key], node)
	end

	local err_list = {}

	for group_key, nodes in pairs(groups) do
		-- 获取第一个节点的 JXGD 值作为参考值
		local first_jxgd = nil
		local has_different_jxgd = false

		for _, node in ipairs(nodes) do
			local jxgd_value = xml_search.get_value_by_child_node(node, "Parameter", "name", "JXGD")
			if jxgd_value then
				-- 如果是第一个节点，记录 JXGD 值
				if not first_jxgd then
					first_jxgd = jxgd_value
				else
					-- 如果与第一个节点的 JXGD 值不同
					if jxgd_value ~= first_jxgd then
						has_different_jxgd = true
						break
					end
				end
			end
		end

		-- 如果组内有不同的 JXGD 值，将该组所有节点加入 err_list
		if has_different_jxgd then
			for _, node in ipairs(nodes) do
				table.insert(err_list, {
					prompt = string.format("房间 %s 中的脚线高度不一致", group_key),
					related_ids = {node:get_attribute("id")}
				})
			end
		end
	end

	local groups_count = 0
	for _, group in pairs(groups) do
		groups_count = groups_count + 1
	end

	table.insert(logs, string.format("已收集 %d 个房间的脚线数据，其中异常的数据有 %d 个", groups_count, #err_list))

	return rule_result:error(err_list, logs)
end

return M