=encoding utf-8

=head1 NAME

ngx_http_memcached_module - Module ngx_http_memcached_module




=head1



The C<ngx_http_memcached_module> module is used to obtain
responses from a memcached server.
The key is set in the C<$memcached_key> variable.
A response should be put in memcached in advance by means
external to nginx.




=head1 Example Configuration




    
    server {
        location / {
            set            $memcached_key "$uri?$args";
            memcached_pass host:11211;
            error_page     *********** = @fallback;
        }
    
        location @fallback {
            proxy_pass     http://backend;
        }
    }






=head1 Directives

=head2 memcached_bind


B<syntax:> memcached_bind I<
    I<C<address>>
    [C<transparent >] E<verbar>
    C<off>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 0.8.22.





Makes outgoing connections to a memcached server originate
from the specified local IP address with an optional port (1.11.2).
Parameter value can contain variables (1.3.12).
The special value C<off> (1.3.12) cancels the effect
of the C<memcached_bind> directive
inherited from the previous configuration level, which allows the
system to auto-assign the local IP address and port.





The C<transparent> parameter (1.11.0) allows
outgoing connections to a memcached server originate
from a non-local IP address,
for example, from a real IP address of a client:

    
    memcached_bind $remote_addr transparent;


In order for this parameter to work,
it is usually necessary to run nginx worker processes with the
L<superuser|ngx_core_module> privileges.
On Linux it is not required (1.13.8) as if
the C<transparent> parameter is specified, worker processes
inherit the C<CAP_NET_RAW> capability from the master process.
It is also necessary to configure kernel routing table
to intercept network traffic from the memcached server.







=head2 memcached_buffer_size


B<syntax:> memcached_buffer_size I<I<C<size>>>


B<default:> I<4kE<verbar>8k>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets the I<C<size>> of the buffer used for reading the response
received from the memcached server.
The response is passed to the client synchronously, as soon as it is received.







=head2 memcached_connect_timeout


B<syntax:> memcached_connect_timeout I<I<C<time>>>


B<default:> I<60s>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines a timeout for establishing a connection with a memcached server.
It should be noted that this timeout cannot usually exceed 75 seconds.







=head2 memcached_gzip_flag


B<syntax:> memcached_gzip_flag I<I<C<flag>>>


B<default:> I<>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.3.6.





Enables the test for the I<C<flag>> presence in the memcached
server response and sets the “C<Content-Encoding>”
response header field to “C<gzip>”
if the flag is set.







=head2 memcached_next_upstream


B<syntax:> memcached_next_upstream I<
    C<error> E<verbar>
    C<timeout> E<verbar>
    C<invalid_response> E<verbar>
    C<not_found> E<verbar>
    C<off>
    ...>


B<default:> I<error timeout>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Specifies in which cases a request should be passed to the next server:

=over



=item C<error>



an error occurred while establishing a connection with the
server, passing a request to it, or reading the response header;


=item C<timeout>



a timeout has occurred while establishing a connection with the
server, passing a request to it, or reading the response header;


=item C<invalid_response>



a server returned an empty or invalid response;


=item C<not_found>



a response was not found on the server;


=item C<off>



disables passing a request to the next server.



=back







One should bear in mind that passing a request to the next server is
only possible if nothing has been sent to a client yet.
That is, if an error or timeout occurs in the middle of the
transferring of a response, fixing this is impossible.





The directive also defines what is considered an
L<unsuccessful
attempt|ngx_http_upstream_module> of communication with a server.
The cases of C<error>, C<timeout> and
C<invalid_response> are always considered unsuccessful attempts,
even if they are not specified in the directive.
The case of C<not_found>
is never considered an unsuccessful attempt.





Passing a request to the next server can be limited by
the number of tries
and by time.







=head2 memcached_next_upstream_timeout


B<syntax:> memcached_next_upstream_timeout I<I<C<time>>>


B<default:> I<0>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.7.5.





Limits the time during which a request can be passed to the
next server.
The C<0> value turns off this limitation.







=head2 memcached_next_upstream_tries


B<syntax:> memcached_next_upstream_tries I<I<C<number>>>


B<default:> I<0>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.7.5.





Limits the number of possible tries for passing a request to the
next server.
The C<0> value turns off this limitation.







=head2 memcached_pass


B<syntax:> memcached_pass I<I<C<address>>>



B<context:> I<location>


B<context:> I<if in location>





Sets the memcached server address.
The address can be specified as a domain name or IP address,
and a port:

    
    memcached_pass localhost:11211;


or as a UNIX-domain socket path:

    
    memcached_pass unix:/tmp/memcached.socket;







If a domain name resolves to several addresses, all of them will be
used in a round-robin fashion.
In addition, an address can be specified as a
L<server group|ngx_http_upstream_module>.







=head2 memcached_read_timeout


B<syntax:> memcached_read_timeout I<I<C<time>>>


B<default:> I<60s>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines a timeout for reading a response from the memcached server.
The timeout is set only between two successive read operations,
not for the transmission of the whole response.
If the memcached server does not transmit anything within this time,
the connection is closed.







=head2 memcached_send_timeout


B<syntax:> memcached_send_timeout I<I<C<time>>>


B<default:> I<60s>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets a timeout for transmitting a request to the memcached server.
The timeout is set only between two successive write operations,
not for the transmission of the whole request.
If the memcached server does not receive anything within this time,
the connection is closed.







=head2 memcached_socket_keepalive


B<syntax:> memcached_socket_keepalive I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.15.6.





Configures the “TCP keepalive” behavior
for outgoing connections to a memcached server.
By default, the operating system’s settings are in effect for the socket.
If the directive is set to the value “C<on>”, the
C<SO_KEEPALIVE> socket option is turned on for the socket.







=head1 Embedded Variables




=over



=item C<$memcached_key>




Defines a key for obtaining response from a memcached server.




=back






