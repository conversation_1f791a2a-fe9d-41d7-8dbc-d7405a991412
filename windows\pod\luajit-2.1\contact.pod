=pod

LuaJIT

=head1 Contact

=over

=item * LuaJIT

=over

=item * Download E<rchevron>

=item * Installation

=item * Running

=back

=item * Extensions

=over

=item * FFI Library

=over

=item * FFI Tutorial

=item * ffi.* API

=item * FFI Semantics

=back

=item * jit.* Library

=item * Lua/C API

=item * Profiler

=back

=item * Status

=over

=item * Changes

=back

=item * FAQ

=item * Performance E<rchevron>

=item * Wiki E<rchevron>

=item * Mailing List E<rchevron>

=back

If you want to report bugs, propose fixes or suggest enhancements,
please use the GitHub issue tracker.

Please send general questions to the E<rchevron> LuaJIT mailing list.

You can also send any questions you have directly to me:

Contact info in image

=head2 Copyright

All documentation is Copyright E<copy> 2005-2017 Mike <PERSON>.

----

Copyright E<copy> 2005-2017 Mike <PERSON>ll E<middot> Contact

=cut

#Pod::HTML2Pod conversion notes:
#From file contact.html
# 2989 bytes of input
#Mon May 14 13:19:15 2018 agentzh
# No a_name switch not specified, so will not try to render <a name='...'>
# No a_href switch not specified, so will not try to render <a href='...'>
