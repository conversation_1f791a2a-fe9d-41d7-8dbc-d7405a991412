local _M = {}
-- 门铰安装是否正常 异常检测
-- 1、判读门的子级是否有门铰，有则继续往下检查，否则不检查；
-- 2、根据门铰的坐标定位到门板的门铰边；
-- 3、判断门铰边是否是内嵌，是则检查，不是则不需要检查；（例如判断到门铰是在门的左边，则需要拿参数里面的lCoverType是否等于0来判断是否内嵌，0是内嵌。rCoverType是右掩盖方式，uCoverType是上掩盖方式，dCoverType是下掩方式）
-- 4、检查是内嵌且有门铰的门铰边是否见光（是否被完全遮挡住）

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local geometry_engine = require("lib.oop-rule-engine")
local check_rule_result = require("lib.check-rule-result")

-- 定义规则执行入口
function _M.dowork(root, target, context)
	local rule_result = check_rule_result.new("入柱门铰边检查", check_rule_result.LEVEL.ERROR)

	-- 存储结果
	local result = {}
	local logs = {}
    
	-- 1. 获取所有门板节点
	local engine_object = geometry_engine.current()

	-- 过滤掉真门的情况
	local xml_parts_p = root:search("/Root/Parts/Part")
	local doors_xml = xml_search.get_part_by_mkbq(xml_parts_p, 3)
    
	if not doors_xml or #doors_xml == 0 then
		return rule_result:pass("没有找到任何门板 MKBQ=3 的Part节点")
	end

	-- 2. 获取所有门铰节点 并收集盖值数据
	local door_hinge_list = {}
	for _, door_xml in ipairs(doors_xml) do
		local men_jiao_xmls = xml_search.get_childs_node_by_kvs(door_xml, "Part", "prodCatId", "2059")

		local l_cover_type = xml_search.get_value_in_parameter(door_xml, "lCoverType")
		local r_cover_type = xml_search.get_value_in_parameter(door_xml, "rCoverType")
		local u_cover_type = xml_search.get_value_in_parameter(door_xml, "uCoverType")
		local d_cover_type = xml_search.get_value_in_parameter(door_xml, "dCoverType")

		if men_jiao_xmls and #men_jiao_xmls > 0 then
			table.insert(door_hinge_list, {
				id = door_xml.id,
				name = door_xml:get_attribute("name"),
				mjId = men_jiao_xmls[1].id,
				lCoverType = l_cover_type,
				rCoverType = r_cover_type,
				uCoverType = u_cover_type,
				dCoverType = d_cover_type
			})
		end
	end

	if not door_hinge_list or #door_hinge_list == 0 then
		return rule_result:pass("没有找到任何门铰数据")
	end

	-- 3、判断门铰边是否是内嵌
	-- 通过几何引擎函数 判断 门铰在门的哪一侧 --> 上下左右 --> "lCoverType" or "rCoverType" or "uCoverType" or "dCoverType"
	-- engine_object:get_door_hinge_face_by_pos
	-- 通过上一个值 取value  如果value == 0 内嵌 value == 1 半盖 value == 2 全盖
	-- 取出门铰 == 0 的 门的id列表 进行4的判断
	local inner_list = {}
	for _, door_hinge in ipairs(door_hinge_list) do
		local mjId = door_hinge.mjId
		local dir = engine_object:get_door_hinge_face_by_pos(mjId)
		if dir then
			table.insert(logs, string.format("门铰组ID: %s 门铰组名称: %s 门铰组类型: %s 门板盖值方式: %s", 
				mjId, door_hinge.name, dir.coverType, door_hinge[dir.coverType]))
				
			local inner = tonumber(door_hinge[dir.coverType]) == 0
			if inner then
				door_hinge.coverType = dir.coverType
				table.insert(inner_list, door_hinge)
			end
		end
	end

	if not inner_list or #inner_list == 0 then
		table.insert(logs, "没有找到任何内嵌的门铰数据")
		return rule_result:pass(logs)
	end

	-- 4、检查是内嵌且有门铰的门铰边是否见光（是否被完全遮挡住）
	-- 通过几何引擎判断门是否完全遮挡
	local inner_list_result = {}
	for _, door_data in ipairs(inner_list) do
		local doorId = door_data.id
		local cover_info = engine_object:get_door_hinge_cover_info(doorId, door_data.coverType)
		if cover_info then
			local bCover = cover_info.bCover
			if not bCover then
				-- （门的name）突出门铰边，请检查！
				table.insert(inner_list_result, {
					prompt = string.format("%s 突出门铰边，请检查！", door_data.name),
					related_ids = {door_data.id}
				})
			end
		end
	end

	table.insert(logs, string.format("已收集 %d 个带门铰组的门板数据，其中符合内嵌条件的门铰有 %d 个，未完全遮挡的门有 %d 个", 
		#door_hinge_list, #inner_list, #inner_list_result))

	return rule_result:error(inner_list_result, logs)
end

return _M
