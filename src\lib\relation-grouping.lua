-- 组合数据的聚合工具类
local M = {}

-- 将关系数据聚合为组
-- @param relations: 关系数据数组 [{object=id1, subObject=id2, distance=0.8, dir="up"}, ...]
-- @return: 聚合后的组数据 [{group1=[id1,id2,id3], group2=[id4,id5]}, ...]
function M.group_relations(relations)
    if not relations or #relations == 0 then
        return {}
    end

    -- 存储所有对象ID到组的映射
    local id_to_group = {}
    -- 存储所有组
    local groups = {}
    
    -- 遍历每个关系
    for _, relation in ipairs(relations) do
        local obj1 = relation.object
        local obj2 = relation.subObject
        
        local group1 = id_to_group[obj1]
        local group2 = id_to_group[obj2]
        
        if not group1 and not group2 then
            -- 两个对象都不在任何组中,创建新组
            local new_group = {obj1, obj2}
            table.insert(groups, new_group)
            id_to_group[obj1] = new_group
            id_to_group[obj2] = new_group
            
        elseif group1 and not group2 then
            -- obj1在组中,obj2不在
            table.insert(group1, obj2)
            id_to_group[obj2] = group1
            
        elseif not group1 and group2 then
            -- obj2在组中,obj1不在
            table.insert(group2, obj1)
            id_to_group[obj1] = group2
            
        elseif group1 ~= group2 then
            -- 两个对象在不同组中,合并这两个组
            for _, id in ipairs(group2) do
                table.insert(group1, id)
                id_to_group[id] = group1
            end
            -- 从groups中移除group2
            for i, g in ipairs(groups) do
                if g == group2 then
                    table.remove(groups, i)
                    break
                end
            end
        end
    end
    
    return groups
end

return M
