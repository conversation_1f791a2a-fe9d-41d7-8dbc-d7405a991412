#!/usr/bin/env bash

# 处理 Nginx worker 进程数（从环境变量中读取，没设置则默认为 4）
workers=${NGINX_WORKER_PROCESSES:-4}
sed -i "s/worker_processes  1;/worker_processes  ${workers};/g" /usr/local/openresty/nginx/conf/nginx.conf

DNS_SERVERS=$(grep -E '^nameserver\s+' /etc/resolv.conf | awk '{print $2}' | tr '\n' ' ')
# 检查 DNS_SERVERS 是否为空
if [[ -z "$DNS_SERVERS" ]]; then
    echo "ERROR: No valid DNS servers found in /etc/resolv.conf" >&2
    # 失败退出，容器启动失败
    exit 1
fi

# RESOLVER_DIRECTIVE="resolver ${DNS_SERVERS%% } valid=30s;"
RESOLVER_DIRECTIVE="resolver ${DNS_SERVERS%% };"
sed -i "s/#RESOLVER_PLACEHOLDER#/${RESOLVER_DIRECTIVE}/g" /usr/local/openresty/nginx/conf/nginx.conf

# 启动 OpenResty
exec /usr/local/openresty/bin/openresty -g "daemon off;"
