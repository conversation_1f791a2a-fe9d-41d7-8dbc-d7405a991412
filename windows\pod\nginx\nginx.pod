=encoding utf-8


=head1 Name


index - nginx documentation


=head1 Introduction




=over




=item *

L<install>



=item *

L<configure>



=item *

L<beginners_guide>



=item *

L<Admin’s Guide|https://docs.nginx.com/nginx/admin-guide/>



=item *

L<control>



=item *

L<events>



=item *

L<hash>



=item *

L<debugging_log>



=item *

L<syslog>



=item *

L<syntax>



=item *

L<switches>



=item *

L<windows>



=item *

L<quic>



=back




=over




=item *

L<request_processing>



=item *

L<server_names>



=item *

L<load_balancing>



=item *

L<configuring_https_servers>



=back




=over




=item *

L<stream_processing>



=back




=over




=item *

L<Scripting with njs|index>



=back




=over




=item *

L<Chapter “nginx” in
“The Architecture of Open Source Applications”|http://www.aosabook.org/en/nginx.html>



=back






=head1 How-To




=over




=item *

L<howto_build_on_win32>



=item *

L<Installing NGINX Plus AMIs on Amazon EC2|https://docs.nginx.com/nginx/admin-guide/installing-nginx/installing-nginx-plus-amazon-web-services/>



=item *

L<nginx_dtrace_pid_provider>



=back




=over




=item *

L<converting_rewrite_rules>



=item *

L<websocket>



=back






=head1 Development




=over




=item *

L<contributing_changes>



=item *

L<development_guide>



=back






=head1 Modules reference




=over




=item *

L<dirindex>



=item *

L<varindex>



=back








=over




=item *

L<ngx_core_module>



=back








=over




=item *

L<ngx_http_core_module|ngx_http_core_module>



=item *

L<ngx_http_access_module|ngx_http_access_module>



=item *

L<ngx_http_addition_module|ngx_http_addition_module>



=item *

L<ngx_http_api_module|ngx_http_api_module>



=item *

L<ngx_http_auth_basic_module|ngx_http_auth_basic_module>



=item *

L<ngx_http_auth_jwt_module|ngx_http_auth_jwt_module>



=item *

L<ngx_http_auth_request_module|ngx_http_auth_request_module>



=item *

L<ngx_http_autoindex_module|ngx_http_autoindex_module>



=item *

L<ngx_http_browser_module|ngx_http_browser_module>



=item *

L<ngx_http_charset_module|ngx_http_charset_module>



=item *

L<ngx_http_dav_module|ngx_http_dav_module>



=item *

L<ngx_http_empty_gif_module|ngx_http_empty_gif_module>



=item *

L<ngx_http_f4f_module|ngx_http_f4f_module>



=item *

L<ngx_http_fastcgi_module|ngx_http_fastcgi_module>



=item *

L<ngx_http_flv_module|ngx_http_flv_module>



=item *

L<ngx_http_geo_module|ngx_http_geo_module>



=item *

L<ngx_http_geoip_module|ngx_http_geoip_module>



=item *

L<ngx_http_grpc_module|ngx_http_grpc_module>



=item *

L<ngx_http_gunzip_module|ngx_http_gunzip_module>



=item *

L<ngx_http_gzip_module|ngx_http_gzip_module>



=item *

L<ngx_http_gzip_static_module|ngx_http_gzip_static_module>



=item *

L<ngx_http_headers_module|ngx_http_headers_module>



=item *

L<ngx_http_hls_module|ngx_http_hls_module>



=item *

L<ngx_http_image_filter_module|ngx_http_image_filter_module>



=item *

L<ngx_http_index_module|ngx_http_index_module>



=item *

L<ngx_http_internal_redirect_module|ngx_http_internal_redirect_module>



=item *

L<ngx_http_js_module|ngx_http_js_module>



=item *

L<ngx_http_keyval_module|ngx_http_keyval_module>



=item *

L<ngx_http_limit_conn_module|ngx_http_limit_conn_module>



=item *

L<ngx_http_limit_req_module|ngx_http_limit_req_module>



=item *

L<ngx_http_log_module|ngx_http_log_module>



=item *

L<ngx_http_map_module|ngx_http_map_module>



=item *

L<ngx_http_memcached_module|ngx_http_memcached_module>



=item *

L<ngx_http_mirror_module|ngx_http_mirror_module>



=item *

L<ngx_http_mp4_module|ngx_http_mp4_module>



=item *

L<ngx_http_perl_module|ngx_http_perl_module>



=item *

L<ngx_http_proxy_module|ngx_http_proxy_module>



=item *

L<ngx_http_proxy_protocol_vendor_module|ngx_http_proxy_protocol_vendor_module>



=item *

L<ngx_http_random_index_module|ngx_http_random_index_module>



=item *

L<ngx_http_realip_module|ngx_http_realip_module>



=item *

L<ngx_http_referer_module|ngx_http_referer_module>



=item *

L<ngx_http_rewrite_module|ngx_http_rewrite_module>



=item *

L<ngx_http_scgi_module|ngx_http_scgi_module>



=item *

L<ngx_http_secure_link_module|ngx_http_secure_link_module>



=item *

L<ngx_http_session_log_module|ngx_http_session_log_module>



=item *

L<ngx_http_slice_module|ngx_http_slice_module>



=item *

L<ngx_http_split_clients_module|ngx_http_split_clients_module>



=item *

L<ngx_http_ssi_module|ngx_http_ssi_module>



=item *

L<ngx_http_ssl_module|ngx_http_ssl_module>



=item *

L<ngx_http_status_module|ngx_http_status_module>



=item *

L<ngx_http_stub_status_module|ngx_http_stub_status_module>



=item *

L<ngx_http_sub_module|ngx_http_sub_module>



=item *

L<ngx_http_upstream_module|ngx_http_upstream_module>



=item *

L<ngx_http_upstream_conf_module|ngx_http_upstream_conf_module>



=item *

L<ngx_http_upstream_hc_module|ngx_http_upstream_hc_module>



=item *

L<ngx_http_userid_module|ngx_http_userid_module>



=item *

L<ngx_http_uwsgi_module|ngx_http_uwsgi_module>



=item *

L<ngx_http_v2_module|ngx_http_v2_module>



=item *

L<ngx_http_v3_module|ngx_http_v3_module>



=item *

L<ngx_http_xslt_module|ngx_http_xslt_module>



=back








=over




=item *

L<ngx_mail_core_module|ngx_mail_core_module>



=item *

L<ngx_mail_auth_http_module|ngx_mail_auth_http_module>



=item *

L<ngx_mail_proxy_module|ngx_mail_proxy_module>



=item *

L<ngx_mail_realip_module|ngx_mail_realip_module>



=item *

L<ngx_mail_ssl_module|ngx_mail_ssl_module>



=item *

L<ngx_mail_imap_module|ngx_mail_imap_module>



=item *

L<ngx_mail_pop3_module|ngx_mail_pop3_module>



=item *

L<ngx_mail_smtp_module|ngx_mail_smtp_module>



=back








=over




=item *

L<ngx_stream_core_module|ngx_stream_core_module>



=item *

L<ngx_stream_access_module|ngx_stream_access_module>



=item *

L<ngx_stream_geo_module|ngx_stream_geo_module>



=item *

L<ngx_stream_geoip_module|ngx_stream_geoip_module>



=item *

L<ngx_stream_js_module|ngx_stream_js_module>



=item *

L<ngx_stream_keyval_module|ngx_stream_keyval_module>



=item *

L<ngx_stream_limit_conn_module|ngx_stream_limit_conn_module>



=item *

L<ngx_stream_log_module|ngx_stream_log_module>



=item *

L<ngx_stream_map_module|ngx_stream_map_module>



=item *

L<ngx_stream_mqtt_preread_module|ngx_stream_mqtt_preread_module>



=item *

L<ngx_stream_mqtt_filter_module|ngx_stream_mqtt_filter_module>



=item *

L<ngx_stream_pass_module|ngx_stream_pass_module>



=item *

L<ngx_stream_proxy_module|ngx_stream_proxy_module>



=item *

L<ngx_stream_proxy_protocol_vendor_module|ngx_stream_proxy_protocol_vendor_module>



=item *

L<ngx_stream_realip_module|ngx_stream_realip_module>



=item *

L<ngx_stream_return_module|ngx_stream_return_module>



=item *

L<ngx_stream_set_module|ngx_stream_set_module>



=item *

L<ngx_stream_split_clients_module|ngx_stream_split_clients_module>



=item *

L<ngx_stream_ssl_module|ngx_stream_ssl_module>



=item *

L<ngx_stream_ssl_preread_module|ngx_stream_ssl_preread_module>



=item *

L<ngx_stream_upstream_module|ngx_stream_upstream_module>



=item *

L<ngx_stream_upstream_hc_module|ngx_stream_upstream_hc_module>



=item *

L<ngx_stream_zone_sync_module|ngx_stream_zone_sync_module>



=back








=over




=item *

L<ngx_google_perftools_module|ngx_google_perftools_module>



=item *

L<ngx_mgmt_module|ngx_mgmt_module>



=back








=over




=item *

L<ngx_otel_module|ngx_otel_module>



=back






