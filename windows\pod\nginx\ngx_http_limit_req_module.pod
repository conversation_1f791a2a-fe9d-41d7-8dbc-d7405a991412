=encoding utf-8

=head1 NAME

ngx_http_limit_req_module - Module ngx_http_limit_req_module




=head1



The C<ngx_http_limit_req_module> module (0.7.21) is used
to limit the request processing rate per a defined key,
in particular, the processing rate of requests coming
from a single IP address.
The limitation is done using the “leaky bucket” method.




=head1 Example Configuration




    
    http {
        limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;
    
        ...
    
        server {
    
            ...
    
            location /search/ {
                limit_req zone=one burst=5;
            }






=head1 Directives

=head2 limit_req


B<syntax:> limit_req I<
    C<zone>=I<C<name>>
    [C<burst>=I<C<number>>]
    [C<nodelay> E<verbar>
     C<delay>=I<C<number>>]>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets the shared memory zone
and the maximum burst size of requests.
If the requests rate exceeds the rate configured for a zone,
their processing is delayed such that requests are processed
at a defined rate.
Excessive requests are delayed until their number exceeds the
maximum burst size
in which case the request is terminated with an
error.
By default, the maximum burst size is equal to zero.
For example, the directives

    
    limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;
    
    server {
        location /search/ {
            limit_req zone=one burst=5;
        }


allow not more than 1 request per second at an average,
with bursts not exceeding 5 requests.





If delaying of excessive requests while requests are being limited is not
desired, the parameter C<nodelay> should be used:

    
    limit_req zone=one burst=5 nodelay;







The C<delay> parameter (1.15.7) specifies a limit
at which excessive requests become delayed.
Default value is zero, i.e. all excessive requests are delayed.





There could be several C<limit_req> directives.
For example, the following configuration will limit the processing rate
of requests coming from a single IP address and, at the same time,
the request processing rate by the virtual server:

    
    limit_req_zone $binary_remote_addr zone=perip:10m rate=1r/s;
    limit_req_zone $server_name zone=perserver:10m rate=10r/s;
    
    server {
        ...
        limit_req zone=perip burst=5 nodelay;
        limit_req zone=perserver burst=10;
    }








These directives are inherited from the previous configuration level
if and only if there are no C<limit_req> directives
defined on the current level.







=head2 limit_req_dry_run


B<syntax:> limit_req_dry_run I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.17.1.





Enables the dry run mode.
In this mode, requests processing rate is not limited, however,
in the shared memory zone, the number of excessive requests is accounted
as usual.







=head2 limit_req_log_level


B<syntax:> limit_req_log_level I<
C<info> E<verbar>
C<notice> E<verbar>
C<warn> E<verbar>
C<error>>


B<default:> I<error>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 0.8.18.





Sets the desired logging level
for cases when the server refuses to process requests
due to rate exceeding,
or delays request processing.
Logging level for delays is one point less than for refusals; for example,
if “C<limit_req_log_level notice>” is specified,
delays are logged with the C<info> level.







=head2 limit_req_status


B<syntax:> limit_req_status I<I<C<code>>>


B<default:> I<503>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.3.15.





Sets the status code to return in response to rejected requests.







=head2 limit_req_zone


B<syntax:> limit_req_zone I<
    I<C<key>>
    C<zone>=I<C<name>>:I<C<size>>
    C<rate>=I<C<rate>>
    [C<sync>]>



B<context:> I<http>





Sets parameters for a shared memory zone
that will keep states for various keys.
In particular, the state stores the current number of excessive requests.
The I<C<key>> can contain text, variables, and their combination.
Requests with an empty key value are not accounted.

B<NOTE>

Prior to version 1.7.6, a I<C<key>> could contain exactly one variable.

Usage example:

    
    limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;







Here, the states are kept in a 10 megabyte zone “one”, and an
average request processing rate for this zone cannot exceed
1 request per second.





A client IP address serves as a key.
Note that instead of C<$remote_addr>, the
C<$binary_remote_addr> variable is used here.
The C<$binary_remote_addr> variable’s size
is always 4 bytes for IPv4 addresses or 16 bytes for IPv6 addresses.
The stored state always occupies
64 bytes on 32-bit platforms and 128 bytes on 64-bit platforms.
One megabyte zone can keep about 16 thousand 64-byte states
or about 8 thousand 128-byte states.





If the zone storage is exhausted, the least recently used state is removed.
If even after that a new state cannot be created, the request is terminated with
an error.





The rate is specified in requests per second (rE<sol>s).
If a rate of less than one request per second is desired,
it is specified in request per minute (rE<sol>m).
For example, half-request per second is 30rE<sol>m.





The C<sync> parameter (1.15.3) enables
L<synchronization|ngx_stream_zone_sync_module>
of the shared memory zone.

B<NOTE>

The C<sync> parameter is available as part of our
commercial subscription.







B<NOTE>

Additionally, as part of our
commercial subscription,
the
L<status information|ngx_http_api_module>
for each such shared memory zone can be
L<obtained|ngx_http_api_module> or
L<reset|ngx_http_api_module>
with the L<API|ngx_http_api_module> since 1.17.7.








=head1 Embedded Variables




=over



=item C<$limit_req_status>




keeps the result of limiting the request processing rate (1.17.6):
C<PASSED>,
C<DELAYED>,
C<REJECTED>,
C<DELAYED_DRY_RUN>, or
C<REJECTED_DRY_RUN>




=back






