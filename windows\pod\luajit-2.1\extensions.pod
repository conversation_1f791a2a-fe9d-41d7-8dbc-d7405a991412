=pod

LuaJIT

=head1 Extensions

=over

=item * LuaJIT

=over

=item * Download E<rchevron>

=item * Installation

=item * Running

=back

=item * Extensions

=over

=item * FFI Library

=over

=item * FFI Tutorial

=item * ffi.* API

=item * FFI Semantics

=back

=item * jit.* Library

=item * Lua/C API

=item * Profiler

=back

=item * Status

=over

=item * Changes

=back

=item * FAQ

=item * Performance E<rchevron>

=item * Wiki E<rchevron>

=item * Mailing List E<rchevron>

=back

LuaJIT is fully upwards-compatible with Lua 5.1. It supports all
E<rchevron> standard Lua library functions and the full set of
E<rchevron> Lua/C API functions.

LuaJIT is also fully ABI-compatible to Lua 5.1 at the linker/dynamic
loader level. This means you can compile a C module against the
standard Lua headers and load the same shared library from either Lua
or LuaJIT.

LuaJIT extends the standard Lua VM with new functionality and adds
several extension modules. Please note this page is only about
I<functional> enhancements and not about performance enhancements, such
as the optimized VM, the faster interpreter or the JIT compiler.

=head2 Extensions Modules

LuaJIT comes with several built-in extension modules:

=head2 C<bit.*> E<mdash> Bitwise operations

LuaJIT supports all bitwise operations as defined by E<rchevron> Lua
BitOp:

 bit.tobit  bit.tohex  bit.bnot    bit.band bit.bor  bit.bxor
 bit.lshift bit.rshift bit.arshift bit.rol  bit.ror  bit.bswap

This module is a LuaJIT built-in E<mdash> you don't need to download or
install Lua BitOp. The Lua BitOp site has full documentation for all
E<rchevron> Lua BitOp API functions. The FFI adds support for 64 bit
bitwise operations, using the same API functions.

Please make sure to C<require> the module before using any of its
functions:

 local bit = require("bit")

An already installed Lua BitOp module is ignored by LuaJIT. This way
you can use bit operations from both Lua and LuaJIT on a shared
installation.

=head2 C<ffi.*> E<mdash> FFI library

The FFI library allows calling external C functions and the use of C
data structures from pure Lua code.

=head2 C<jit.*> E<mdash> JIT compiler control

The functions in this module control the behavior of the JIT compiler
engine.

=head2 C API extensions

LuaJIT adds some extra functions to the Lua/C API.

=head2 Profiler

LuaJIT has an integrated profiler.

=head2 Enhanced Standard Library Functions

=head2 C<xpcall(f, err [,args...])> passes arguments

Unlike the standard implementation in Lua 5.1, C<xpcall()> passes any
arguments after the error function to the function which is called in a
protected context.

=head2 C<loadfile()> etc. handle UTF-8 source code

Non-ASCII characters are handled transparently by the Lua source code
parser. This allows the use of UTF-8 characters in identifiers and
strings. A UTF-8 BOM is skipped at the start of the source code.

=head2 C<tostring()> etc. canonicalize NaN and E<plusmn>Inf

All number-to-string conversions consistently convert non-finite
numbers to the same strings on all platforms. NaN results in C<"nan">,
positive infinity results in C<"inf"> and negative infinity results in
C<"-inf">.

=head2 C<tonumber()> etc. use builtin string to number conversion

All string-to-number conversions consistently convert integer and
floating-point inputs in decimal, hexadecimal and binary on all
platforms. C<strtod()> is I<not> used anymore, which avoids numerous
problems with poor C library implementations. The builtin conversion
function provides full precision according to the IEEE-754 standard, it
works independently of the current locale and it supports hex
floating-point numbers (e.g. C<0x1.5p-3>).

=head2 C<string.dump(f [,strip])> generates portable bytecode

An extra argument has been added to C<string.dump()>. If set to
C<true>, 'stripped' bytecode without debug information is generated.
This speeds up later bytecode loading and reduces memory usage. See
also the C<-b> command line option.

The generated bytecode is portable and can be loaded on any
architecture that LuaJIT supports, independent of word size or
endianness. However the bytecode compatibility versions must match.
Bytecode stays compatible for dot releases (x.y.0 E<rarr> x.y.1), but
may change with major or minor releases (2.0 E<rarr> 2.1) or between
any beta release. Foreign bytecode (e.g. from Lua 5.1) is incompatible
and cannot be loaded.

Note: C<LJ_GC64> mode requires a different frame layout, which implies
a different, incompatible bytecode format for ports that use this mode
(e.g. ARM64 or MIPS64) or when explicitly enabled for x64. This may be
rectified in the future.

=head2 C<table.new(narray, nhash)> allocates a pre-sized table

An extra library function C<table.new()> can be made available via
C<require("table.new")>. This creates a pre-sized table, just like the
C API equivalent C<lua_createtable()>. This is useful for big tables if
the final table size is known and automatic table resizing is too
expensive.

=head2 C<table.clear(tab)> clears a table

An extra library function C<table.clear()> can be made available via
C<require("table.clear")>. This clears all keys and values from a
table, but preserves the allocated array/hash sizes. This is useful
when a table, which is linked from multiple places, needs to be cleared
and/or when recycling a table for use by the same context. This avoids
managing backlinks, saves an allocation and the overhead of incremental
array/hash part growth.

Please note this function is meant for very specific situations. In
most cases it's better to replace the (usually single) link with a new
table and let the GC do its work.

=head2 Enhanced PRNG for C<math.random()>

LuaJIT uses a Tausworthe PRNG with period 2^223 to implement
C<math.random()> and C<math.randomseed()>. The quality of the PRNG
results is much superior compared to the standard Lua implementation
which uses the platform-specific ANSI rand().

The PRNG generates the same sequences from the same seeds on all
platforms and makes use of all bits in the seed argument.
C<math.random()> without arguments generates 52 pseudo-random bits for
every call. The result is uniformly distributed between 0.0 and 1.0.
It's correctly scaled up and rounded for C<math.random(n [,m])> to
preserve uniformity.

=head2 C<io.*> functions handle 64 bit file offsets

The file I/O functions in the standard C<io.*> library handle 64 bit
file offsets. In particular this means it's possible to open files
larger than 2 Gigabytes and to reposition or obtain the current file
position for offsets beyond 2 GB (C<fp:seek()> method).

=head2 C<debug.*> functions identify metamethods

C<debug.getinfo()> and C<lua_getinfo()> also return information about
invoked metamethods. The C<namewhat> field is set to C<"metamethod">
and the C<name> field has the name of the corresponding metamethod
(e.g. C<"__index">).

=head2 Fully Resumable VM

The LuaJIT VM is fully resumable. This means you can yield from a
coroutine even across contexts, where this would not possible with the
standard Lua 5.1 VM: e.g. you can yield across C<pcall()> and
C<xpcall()>, across iterators and across metamethods.

=head2 Extensions from Lua 5.2

LuaJIT supports some language and library extensions from Lua 5.2.
Features that are unlikely to break existing code are unconditionally
enabled:

=over

=item * C<goto> and C<::labels::>.

=item * Hex escapes C<'\x3F'> and C<'\*'> escape in strings.

=item * C<load(string|reader [, chunkname [,mode [,env]]])>.

=item * C<loadstring()> is an alias for C<load()>.

=item * C<loadfile(filename [,mode [,env]])>.

=item * C<math.log(x [,base])>.

=item * C<string.rep(s, n [,sep])>.

=item * C<string.format()>: C<%q> reversible. C<%s> checks
C<__tostring>. C<%a> and C<"%A> added.

=item * String matching pattern C<%g> added.

=item * C<io.read("*L")>.

=item * C<io.lines()> and C<file:lines()> process C<io.read()> options.

=item * C<os.exit(status|true|false [,close])>.

=item * C<package.searchpath(name, path [, sep [, rep]])>.

=item * C<package.loadlib(name, "*")>.

=item * C<debug.getinfo()> returns C<nparams> and C<isvararg> for
option C<"u">.

=item * C<debug.getlocal()> accepts function instead of level.

=item * C<debug.getlocal()> and C<debug.setlocal()> accept negative
indexes for varargs.

=item * C<debug.getupvalue()> and C<debug.setupvalue()> handle C
functions.

=item * C<debug.upvalueid()> and C<debug.upvaluejoin()>.

=item * Lua/C API extensions: C<lua_version()> C<lua_upvalueid()>
C<lua_upvaluejoin()> C<lua_loadx()> C<lua_copy()> C<lua_tonumberx()>
C<lua_tointegerx()> C<luaL_fileresult()> C<luaL_execresult()>
C<luaL_loadfilex()> C<luaL_loadbufferx()> C<luaL_traceback()>
C<luaL_setfuncs()> C<luaL_pushmodule()> C<luaL_newlibtable()>
C<luaL_newlib()> C<luaL_testudata()> C<luaL_setmetatable()>

=item * Command line option C<-E>.

=item * Command line checks C<__tostring> for errors.

=back

Other features are only enabled, if LuaJIT is built with
C<-DLUAJIT_ENABLE_LUA52COMPAT>:

=over

=item * C<goto> is a keyword and not a valid variable name anymore.

=item * C<break> can be placed anywhere. Empty statements (C<;;>) are
allowed.

=item * C<__lt>, C<__le> are invoked for mixed types.

=item * C<__len> for tables. C<rawlen()> library function.

=item * C<pairs()> and C<ipairs()> check for C<__pairs> and
C<__ipairs>.

=item * C<coroutine.running()> returns two results.

=item * C<table.pack()> and C<table.unpack()> (same as C<unpack()>).

=item * C<io.write()> and C<file:write()> return file handle instead of
C<true>.

=item * C<os.execute()> and C<pipe:close()> return detailed exit
status.

=item * C<debug.setmetatable()> returns object.

=item * C<debug.getuservalue()> and C<debug.setuservalue()>.

=item * Remove C<math.mod()>, C<string.gfind()>.

=item * C<package.searchers>.

=item * C<module()> returns the module table.

=back

Note: this provides only partial compatibility with Lua 5.2 at the
language and Lua library level. LuaJIT is API+ABI-compatible with Lua
5.1, which prevents implementing features that would otherwise break
the Lua/C API and ABI (e.g. C<_ENV>).

=head2 Extensions from Lua 5.3

LuaJIT supports some extensions from Lua 5.3:

=over

=item * Unicode escape C<'\u{XX...}'> embeds the UTF-8 encoding in
string literals.

=item * The argument table C<arg> can be read (and modified) by
C<LUA_INIT> and C<-e> chunks.

=item * C<io.read()> and C<file:read()> accept formats with or without
a leading C<*>.

=item * C<table.move(a1, f, e, t [,a2])>.

=item * C<coroutine.isyieldable()>.

=item * Lua/C API extensions: C<lua_isyieldable()>

=back

=head2 C++ Exception Interoperability

LuaJIT has built-in support for interoperating with C++ exceptions. The
available range of features depends on the target platform and the
toolchain used to compile LuaJIT:

Platform

Compiler

Interoperability

POSIX/x64, DWARF2 unwinding

GCC 4.3+, Clang

B<Full>

ARM C<-DLUAJIT_UNWIND_EXTERNAL>

GCC, Clang

B<Full>

Other platforms, DWARF2 unwinding

GCC, Clang

B<Limited>

Windows/x64

MSVC or WinSDK

B<Full>

Windows/x86

Any

B<Full>

Other platforms

Other compilers

B<No>

B<Full interoperability> means:

=over

=item * C++ exceptions can be caught on the Lua side with C<pcall()>,
C<lua_pcall()> etc.

=item * C++ exceptions will be converted to the generic Lua error
C<"C++ exception">, unless you use the C call wrapper feature.

=item * It's safe to throw C++ exceptions across non-protected Lua
frames on the C stack. The contents of the C++ exception object pass
through unmodified.

=item * Lua errors can be caught on the C++ side with C<catch(...)>.
The corresponding Lua error message can be retrieved from the Lua
stack.

=item * Throwing Lua errors across C++ frames is safe. C++ destructors
will be called.

=back

B<Limited interoperability> means:

=over

=item * C++ exceptions can be caught on the Lua side with C<pcall()>,
C<lua_pcall()> etc.

=item * C++ exceptions will be converted to the generic Lua error
C<"C++ exception">, unless you use the C call wrapper feature.

=item * C++ exceptions will be caught by non-protected Lua frames and
are rethrown as a generic Lua error. The C++ exception object will be
destroyed.

=item * Lua errors B<cannot> be caught on the C++ side.

=item * Throwing Lua errors across C++ frames will B<not> call C++
destructors.

=back

B<No interoperability> means:

=over

=item * It's B<not> safe to throw C++ exceptions across Lua frames.

=item * C++ exceptions B<cannot> be caught on the Lua side.

=item * Lua errors B<cannot> be caught on the C++ side.

=item * Throwing Lua errors across C++ frames will B<not> call C++
destructors.

=back

----

Copyright E<copy> 2005-2017 Mike Pall E<middot> Contact

=cut

#Pod::HTML2Pod conversion notes:
#From file extensions.html
# 17733 bytes of input
#Mon May 14 13:19:16 2018 agentzh
# No a_name switch not specified, so will not try to render <a name='...'>
# No a_href switch not specified, so will not try to render <a href='...'>
