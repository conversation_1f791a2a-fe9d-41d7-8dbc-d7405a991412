local _M = {}
-- 左右侧板见光

-- 加载所需模块
local xml_search_in_part = require("lib.xml-search-in-part")
local geometry = require("lib.oop-rule-engine")
local check_rule_result = require("lib.check-rule-result")


-- 定义规则执行入口
function _M.dowork(root, target, context)
	local rule_result = check_rule_result.new("侧板见光检查", check_rule_result.LEVEL.INFO)

	-- 存储结果
	local result = {}
	local logs = {}

	-- 1. 获取所有单元节点
	local engine_object = geometry.current()

	local part_xml = root:search("/Root/Parts")
	local zcbjg_xmls = xml_search_in_part.get_part_xmllist_bykvs_in_part(part_xml, "Parameters/Parameter", {
		{ key = "name",  value = "ZCBJG" },
		{ key = "value", value = "0" }
	})
	local ycbjg_xmls = xml_search_in_part.get_part_xmllist_bykvs_in_part(part_xml, "Parameters/Parameter", {
		{ key = "name",  value = "YCBJG" },
		{ key = "value", value = "0" }
	})

	local zcb_xmls = {}
	if zcbjg_xmls then
		for _, v in ipairs(zcbjg_xmls) do
			local xmls = xml_search_in_part.get_part_xmllist_bykvs_in_part(v, "Parameters/Parameter", {
				{ key = "name",  value = "BJBQ" },
				{ key = "value", value = "1" }
			})
			if xmls then
				for _, part in ipairs(xmls) do
					if part then
						table.insert(zcb_xmls, part)
					end
				end
			end
		end
	end

	local ycb_xmls = {}
	if ycbjg_xmls then
		for _, v in ipairs(ycbjg_xmls) do
			local xmls = xml_search_in_part.get_part_xmllist_bykvs_in_part(v, "Parameters/Parameter", {
				{ key = "name",  value = "BJBQ" },
				{ key = "value", value = "2" }
			})
			if xmls then
				for _, part in ipairs(xmls) do
					if part then
						table.insert(ycb_xmls, part)
					end
				end
			end
		end
	end

	local all_detect_xmls = root:search("/Root/Parts/Part")
	local structure_xml = part_xml:search("/Root/Structures")
	local wall_xmls = structure_xml and structure_xml:search("//Wall") or {}
	local pillar_xmls = structure_xml and structure_xml:search("//Pillar") or {}
	for _, v in ipairs(pillar_xmls) do
		table.insert(all_detect_xmls, v)
	end
	for _, v in ipairs(wall_xmls) do
		table.insert(all_detect_xmls, v)
	end

	if not all_detect_xmls or #all_detect_xmls == 0 then
		return rule_result:pass("没有找到任何墙体或柱体")
	end

	for _, zcb in ipairs(zcb_xmls) do
		local detect_xmls = {}
		for _, x in ipairs(all_detect_xmls) do
			if x.id ~= zcb.parentId then
				table.insert(detect_xmls, x.id)
			end
		end
		local left_cover_result = engine_object:aabb_cover_detect(zcb.id, "left", 10, detect_xmls)
		local flag = true
		if left_cover_result then
			for _, cover_result in ipairs(left_cover_result) do
				if cover_result > 2 then
					flag = false
				end
			end
		end
		local right_cover_result = engine_object:aabb_cover_detect(zcb.id, "right", 10, detect_xmls)
		if right_cover_result then
			for _, cover_result in ipairs(right_cover_result) do
				if cover_result > 2 then
					flag = false
				end
			end
		end
		if flag then
			table.insert(result, {
				prompt = string.format("%s 见光, 请设置见光参数", zcb:get_attribute("name")),
				related_ids = { zcb.id }
			})
		end
	end

	for _, ycb in ipairs(ycb_xmls) do
		local detect_xmls = {}
		for _, x in ipairs(all_detect_xmls) do
			if x.id ~= ycb.parentId then
				table.insert(detect_xmls, x.id)
			end
		end
		local flag = true
		local left_cover_result = engine_object:aabb_cover_detect(ycb.id, "left", 10, detect_xmls)
		if left_cover_result then
			for _, cover_result in ipairs(left_cover_result) do
				if cover_result > 2 then
					flag = false
				end
			end
		end
		local right_cover_result = engine_object:aabb_cover_detect(ycb.id, "right", 10, detect_xmls)
		if right_cover_result then
			for _, cover_result in ipairs(right_cover_result) do
				if cover_result > 2 then
					flag = false
				end
			end
		end
		if flag then
			table.insert(result, {
				prompt = string.format("%s 见光, 请设置见光参数", ycb:get_attribute("name")),
				related_ids = { ycb.id }
			})
		end
	end

	table.insert(logs, string.format("检查完成，发现 %d 个侧板见光", #result))

	return rule_result:error(result, logs)
end

return _M
