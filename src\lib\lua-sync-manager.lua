local generator = require "lib.generator-lua-util"
local cjson = require "cjson"
local function_data = require "lib.function-data"

local _M = {}

-- 文件信息缓存，用于存储文件名和版本信息
local script_info_cache = ngx.shared.script_info_cache


-- 检查脚本是否需要更新
local function should_update_script(script_name, func_info)
    local cached_info = script_info_cache:get(script_name)
    if not cached_info then
        ngx.log(ngx.DEBUG, "脚本缓存不存在: ", script_name)
        return true
    end

    local ok, cached_table = pcall(cjson.decode, cached_info)
    if not ok then
        ngx.log(ngx.ERR, "解析缓存信息失败: ", cached_info)
        return true
    end
    cached_info = cached_table

    -- 比较版本号
    if cached_info.versionCode ~= func_info.versionCode then
        ngx.log(ngx.DEBUG, string.format("脚本版本不匹配[%s]: 缓存=%s, 函数=%s",
            script_name, tostring(cached_info.versionCode), tostring(func_info.versionCode)))
        return true
    end

    ngx.log(ngx.DEBUG, "脚本无需更新: ", script_name)
    return false
end

-- 更新脚本信息缓存
local function update_script_cache(script_name, func_info)
    local cache_info = {
        versionCode = func_info.versionCode
    }
    local ok, err = script_info_cache:set(script_name, cjson.encode(cache_info))
    if not ok then
        ngx.log(ngx.ERR, "更新脚本缓存失败: ", err)
    end

    -- 如果当前package.loaded缓存里 已经有这个缓存 则清空旧的 等待自动加载新的
    -- 当这个执行的时候  会直接清理当前的lua 如果当前运行时 还在调用 就会有问题
    if package.loaded[script_name] then
        package.loaded[script_name] = nil
    end
end

-- 同步所有脚本
function _M.sync_all_scripts()
    -- 获取所有函数
    local functions, err = function_data.get_all_functions()
    if not functions then
        return false, "无法获取函数列表: " .. (err or "unknown error")
    end

    local success_count = 0
    local error_count = 0
    local skip_count = 0
    local details = {}

    -- 遍历所有函数
    for _, func in ipairs(functions) do
        local script_name = func.funcName
        if script_name then
            -- 检查脚本是否需要更新
            if should_update_script(script_name, func) then
                local success, gen_err = generator.generate_lua_file(script_name, func.luaScript)
                if success then
                    -- 更新缓存
                    update_script_cache(script_name, func)
                    success_count = success_count + 1
                    table.insert(details, {
                        name = script_name,
                        status = "updated",
                        message = "更新成功",
                        versionCode = func.versionCode
                    })
                    ngx.log(ngx.INFO, "成功更新脚本: " .. script_name .. ", 版本: " .. func.versionCode)
                else
                    error_count = error_count + 1
                    table.insert(details, {
                        name = script_name,
                        status = "error",
                        message = gen_err,
                        versionCode = func.versionCode
                    })
                    ngx.log(ngx.ERR, "生成脚本失败 [" .. script_name .. "]: ", gen_err)
                end
            else
                skip_count = skip_count + 1
                table.insert(details, {
                    name = script_name,
                    status = "skipped",
                    message = "无需更新",
                    versionCode = func.versionCode
                })
                ngx.log(ngx.INFO, "脚本无需更新: " .. script_name .. ", 版本: " .. func.versionCode)
            end
        end
    end

    return true, {
        summary = string.format("同步完成: 更新=%d, 跳过=%d, 失败=%d",
                              success_count, skip_count, error_count),
        details = details,
        stats = {
            success_count = success_count,
            error_count = error_count,
            skip_count = skip_count
        }
    }
end

return _M
