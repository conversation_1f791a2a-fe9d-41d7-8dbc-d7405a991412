=encoding utf-8


=head1 Name

ngx_rds_csv - Nginx output filter module to convert Resty-DBD-Streams
(RDS) to Comma-Separated Values (CSV)


=head1 Status

This module is considered production ready.


=head1 Synopsis


    location /foo {
        # drizzle_pass/postgres_pass/...
    
        rds_csv on;
        rds_csv_row_terminator "\n"; # default to "\r\n"
    }


=head1 Description

This module implements an efficient output filter that converts
Resty-DBD-Streams (RDS) generated by L<ngx_drizzle|https://github.com/openresty/drizzle-nginx-module>
and L<ngx_postgres|https://github.com/FRiCKLE/ngx_postgres/>
to Comma-Separated Values (CSV) format in a streaming fashion.
By default, the CSV format is in compliance with L<RFC 4180|http://tools.ietf.org/html/rfc4180>:

http://tools.ietf.org/html/rfc4180

SQL NULL values will be converted to empty field value, just like
empty string values.




=head1 Directives




=head2 rds_csv

B<syntax:> I<rds_csv on|off>

B<default:> I<rds_csv off>

B<context:> I<http, server, location, location if>

Enables this output filter when on and disables otherwise.




=head2 rds_csv_row_terminator

B<syntax:> I<rds_csv_row_terminator E<lt>strE<gt>>

B<default:> I<rds_csv_row_terminator "\r\n">

B<context:> I<http, server, location, location if>

Specifies the row terminator used by the CSV format.
Only C<"\r\n"> and C<"\n"> are allowed.

Defaults to C<"\r\n">, i.e., CR LF, according to L<RFC 4180|http://tools.ietf.org/html/rfc4180>.




=head2 rds_csv_field_separator

B<syntax:> I<rds_csv_field_separator E<lt>charE<gt>>

B<default:> I<rds_csv_field_separator ",">

B<context:> I<http, server, location, location if>

Specifies the field seperator used by the CSV format.
Only C<",">, C<";">, and C<"\t"> are allowed.

Defaults to C<","> according to L<RFC 4180|http://tools.ietf.org/html/rfc4180>.




=head2 rds_csv_field_name_header

B<syntax:> I<rds_csv_field_name_header on|off>

B<default:> I<rds_csv_field_name_header off>

B<context:> I<http, server, location, location if>

Emits the first line of field names when this directive is set on,
and none otherwise.




=head2 rds_csv_content_type

B<syntax:> I<rds_csv_content_type E<lt>strE<gt>>

B<default:> I<rds_csv_content_type "text/csv; header=E<lt>present|absenceE<gt>">

B<context:> I<http, server, location, location if>

Specifies the C<Content-Type> response header generated by this module.

Defaults to C<"text/csv; header=present"> or C<"text/csv; header=absence">,
depending on whether L<rds_csv_field_name_header> is on or off.




=head2 rds_csv_buffer_size

B<syntax:> I<rds_csv_buffer_size E<lt>sizeE<gt>>

B<default:> I<rds_csv_buffer_size 4k/8k>

B<context:> I<http, server, location, location if>

The lager this buffer size setting, the less streammy the output
will be.




=head1 Installation

You're recommended to install this module (as well as the Nginx core and many other goodies) via the L<ngx_openresty bundle|http://openresty.org>. See L<the detailed instructions|http://openresty.org/#Installation> for downloading and installing ngx_openresty into your system. This is the easiest and most safe way to set things up.

Alternatively, you can install this module manually with the Nginx source:

Grab the nginx source code from L<nginx.org|http://nginx.org/>, for example,
the version 1.13.6 (see L<nginx compatibility>), and then build the source with this module:


     $ wget 'http://nginx.org/download/nginx-1.13.6.tar.gz'
     $ tar -xzvf nginx-1.13.6.tar.gz
     $ cd nginx-1.13.6/
    
     # Here we assume you would install you nginx under /opt/nginx/.
     $ ./configure --prefix=/opt/nginx \
         --add-module=/path/to/rds-csv-nginx-module
    
     $ make -j2
     $ make install

Download the latest version of the release tarball of this module from L<rds-csv-nginx-module file list|https://github.com/openresty/rds-csv-nginx-module/tags>.

Also, this module is included and enabled by default in the L<ngx_openresty bundle|http://openresty.org>.




=head1 Compatibility

This module is compatible with the following versions of Nginx:


=over


=item *

B<1.13.x> (last tested: 1.13.6)

=item *

B<1.12.x>

=item *

B<1.11.x> (last tested: 1.11.2)

=item *

B<1.10.x>

=item *

B<1.9.x> (last tested: 1.9.7)

=item *

B<1.8.x>

=item *

B<1.7.x> (last tested: 1.7.10)

=item *

B<1.6.x>

=item *

B<1.5.x>

=item *

B<1.4.x> (last tested: 1.4.3)

=item *

B<1.2.x> (last tested: 1.2.9)

=item *

B<1.1.x> (last tested: 1.1.5)

=item *

B<1.0.x> (last tested: 1.0.8)


=back




=head1 Author

Yichun "agentzh" Zhang E<lt><EMAIL><gt>, OpenResty Inc.




=head1 Copyright & License

This module is licenced under the BSD license.

Copyright (C) 2011-2018, Yichun "agentzh" Zhang (章亦春) E<lt><EMAIL><gt>, OpenResty Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:


=over


=item *

Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.

=item *

Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED
TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

L<ngx_drizzle|https://github.com/openresty/drizzle-nginx-module>

=item *

L<ngx_postgres|https://github.com/FRiCKLE/ngx_postgres/>

=item *

L<ngx_rds_json|https://github.com/openresty/rds-json-nginx-module>


=back



