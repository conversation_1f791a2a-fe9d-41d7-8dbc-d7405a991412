=encoding utf-8

=head1 NAME

ngx_http_session_log_module - Module ngx_http_session_log_module




=head1



The C<ngx_http_session_log_module> module enables logging
sessions (that is, aggregates of multiple HTTP requests) instead of
individual HTTP requests.






B<NOTE>

This module is available as part of our
commercial subscription.





=head1 Example Configuration



The following configuration sets up a session log and maps requests to
sessions according to the request client address and C<User-Agent>
request header field:

    
        session_log_zone /path/to/log format=combined
                         zone=one:1m timeout=30s
                         md5=$binary_remote_addr$http_user_agent;
    
        location /media/ {
            session_log one;
        }






=head1 Directives

=head2 session_log


B<syntax:> session_log I<I<C<name>> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Enables the use of the specified session log.
The special value C<off> cancels the effect
of the C<session_log> directives
inherited from the previous configuration level.







=head2 session_log_format


B<syntax:> session_log_format I<
    I<C<name>>
    I<C<string>> ...>


B<default:> I<combined "...">


B<context:> I<http>





Specifies the output format of a log.
The value of the C<$body_bytes_sent> variable is aggregated across
all requests in a session.
The values of all other variables available for logging correspond to the
first request in a session.







=head2 session_log_zone


B<syntax:> session_log_zone I<
    I<C<path>>
    C<zone>=I<C<name>>:I<C<size>>
    [C<format>=I<C<format>>]
    [C<timeout>=I<C<time>>]
    [C<id>=I<C<id>>]
    [C<md5>=I<C<md5>>]
>



B<context:> I<http>





Sets the path to a log file and configures the shared memory zone that is used
to store currently active sessions.





A session is considered active for as long as the time elapsed since
the last request in the session does not exceed the specified
C<timeout> (by default, 30 seconds).
Once a session is no longer active, it is written to the log.





The C<id> parameter identifies the
session to which a request is mapped.
The C<id> parameter is set to the hexadecimal representation
of an MD5 hash (for example, obtained from a cookie using variables).
If this parameter is not specified or does not represent the valid
MD5 hash, nginx computes the MD5 hash from the value of
the C<md5> parameter and creates a new session using this hash.
Both the C<id> and C<md5> parameters
can contain variables.





The C<format> parameter sets the custom session log
format configured by the L</session_log_format> directive.
If C<format> is not specified, the predefined
“C<combined>” format is used.







=head1 Embedded Variables



The C<ngx_http_session_log_module> module supports
two embedded variables:


=over



=item C<$session_log_id>




current session ID;



=item C<$session_log_binary_id>





current session ID in binary form (16 bytes).




=back






