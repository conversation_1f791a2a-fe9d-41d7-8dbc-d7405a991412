=encoding utf-8

=head1 NAME

ngx_mail_ssl_module - Module ngx_mail_ssl_module




=head1



The C<ngx_mail_ssl_module> module provides the necessary
support for a mail proxy server to work with the SSLE<sol>TLS protocol.





This module is not built by default, it should be enabled with the
C<--with-mail_ssl_module>
configuration parameter.

B<NOTE>

This module requires the
L<OpenSSL|http://www.openssl.org> library.





=head1 Example Configuration



To reduce the processor load, it is recommended to

=over




=item *

set the number of
L<worker processes|ngx_core_module>
equal to the number of processors,



=item *

enable the shared session cache,



=item *

disable the built-in session cache,



=item *

and possibly increase the session lifetime
(by default, 5 minutes):



=back




    
    <emphasis>worker_processes auto;</emphasis>
    
    mail {
    
        ...
    
        server {
            listen              993 ssl;
    
            ssl_protocols       TLSv1.2 TLSv1.3;
            ssl_ciphers         AES128-SHA:AES256-SHA:RC4-SHA:DES-CBC3-SHA:RC4-MD5;
            ssl_certificate     /usr/local/nginx/conf/cert.pem;
            ssl_certificate_key /usr/local/nginx/conf/cert.key;
            <emphasis>ssl_session_cache   shared:SSL:10m;</emphasis>
            <emphasis>ssl_session_timeout 10m;</emphasis>
    
            ...
        }






=head1 Directives

=head2 ssl


B<syntax:> ssl I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<mail>


B<context:> I<server>





This directive was made obsolete in version 1.15.0
and was removed in version 1.25.1.
The C<ssl> parameter
of the L<ngx_mail_core_module> directive
should be used instead.







=head2 ssl_certificate


B<syntax:> ssl_certificate I<I<C<file>>>



B<context:> I<mail>


B<context:> I<server>





Specifies a I<C<file>> with the certificate in the PEM format
for the given server.
If intermediate certificates should be specified in addition to a primary
certificate, they should be specified in the same file in the following
order: the primary certificate comes first, then the intermediate certificates.
A secret key in the PEM format may be placed in the same file.





Since version 1.11.0,
this directive can be specified multiple times
to load certificates of different types, for example, RSA and ECDSA:

    
    server {
        listen              993 ssl;
    
        ssl_certificate     example.com.rsa.crt;
        ssl_certificate_key example.com.rsa.key;
    
        ssl_certificate     example.com.ecdsa.crt;
        ssl_certificate_key example.com.ecdsa.key;
    
        ...
    }



B<NOTE>

Only OpenSSL 1.0.2 or higher supports separate
L<certificate
chains|configuring_https_servers>
for different certificates.
With older versions, only one certificate chain can be used.






The value
C<data>:I<C<certificate>>
can be specified instead of the I<C<file>> (1.15.10),
which loads a certificate
without using intermediate files.
Note that inappropriate use of this syntax may have its security implications,
such as writing secret key data to
L<error log|ngx_core_module>.







=head2 ssl_certificate_key


B<syntax:> ssl_certificate_key I<I<C<file>>>



B<context:> I<mail>


B<context:> I<server>





Specifies a I<C<file>> with the secret key in the PEM format
for the given server.





The value
C<engine>:I<C<name>>:I<C<id>>
can be specified instead of the I<C<file>> (1.7.9),
which loads a secret key with a specified I<C<id>>
from the OpenSSL engine I<C<name>>.





The value
C<data>:I<C<key>>
can be specified instead of the I<C<file>> (1.15.10),
which loads a secret key without using intermediate files.
Note that inappropriate use of this syntax may have its security implications,
such as writing secret key data to
L<error log|ngx_core_module>.







=head2 ssl_ciphers


B<syntax:> ssl_ciphers I<I<C<ciphers>>>


B<default:> I<HIGH:!aNULL:!MD5>


B<context:> I<mail>


B<context:> I<server>





Specifies the enabled ciphers.
The ciphers are specified in the format understood by the
OpenSSL library, for example:

    
    ssl_ciphers ALL:!aNULL:!EXPORT56:RC4+RSA:+HIGH:+MEDIUM:+LOW:+SSLv2:+EXP;







The full list can be viewed using the
“C<openssl ciphers>” command.






B<NOTE>

The previous versions of nginx used
L<different|configuring_https_servers>
ciphers by default.








=head2 ssl_client_certificate


B<syntax:> ssl_client_certificate I<I<C<file>>>



B<context:> I<mail>


B<context:> I<server>



This directive appeared in version 1.7.11.





Specifies a I<C<file>> with trusted CA certificates in the PEM format
used to verify client certificates.





The list of certificates will be sent to clients.
If this is not desired, the L</ssl_trusted_certificate>
directive can be used.







=head2 ssl_conf_command


B<syntax:> ssl_conf_command I<I<C<name>> I<C<value>>>



B<context:> I<mail>


B<context:> I<server>



This directive appeared in version 1.19.4.





Sets arbitrary OpenSSL configuration
L<commands|https://www.openssl.org/docs/man1.1.1/man3/SSL_CONF_cmd.html>.

B<NOTE>

The directive is supported when using OpenSSL 1.0.2 or higher.






Several C<ssl_conf_command> directives
can be specified on the same level:

    
    ssl_conf_command Options PrioritizeChaCha;
    ssl_conf_command Ciphersuites TLS_CHACHA20_POLY1305_SHA256;


These directives are inherited from the previous configuration level
if and only if there are no C<ssl_conf_command> directives
defined on the current level.






B<NOTE>

Note that configuring OpenSSL directly
might result in unexpected behavior.








=head2 ssl_crl


B<syntax:> ssl_crl I<I<C<file>>>



B<context:> I<mail>


B<context:> I<server>



This directive appeared in version 1.7.11.





Specifies a I<C<file>> with revoked certificates (CRL)
in the PEM format used to verify
client certificates.







=head2 ssl_dhparam


B<syntax:> ssl_dhparam I<I<C<file>>>



B<context:> I<mail>


B<context:> I<server>



This directive appeared in version 0.7.2.





Specifies a I<C<file>> with DH parameters for DHE ciphers.





By default no parameters are set,
and therefore DHE ciphers will not be used.

B<NOTE>

Prior to version 1.11.0, builtin parameters were used by default.








=head2 ssl_ecdh_curve


B<syntax:> ssl_ecdh_curve I<I<C<curve>>>


B<default:> I<auto>


B<context:> I<mail>


B<context:> I<server>



This directive appeared in version 1.1.0.



This directive appeared in version 1.0.6.





Specifies a I<C<curve>> for ECDHE ciphers.





When using OpenSSL 1.0.2 or higher,
it is possible to specify multiple curves (1.11.0), for example:

    
    ssl_ecdh_curve prime256v1:secp384r1;







The special value C<auto> (1.11.0) instructs nginx to use
a list built into the OpenSSL library when using OpenSSL 1.0.2 or higher,
or C<prime256v1> with older versions.






B<NOTE>

Prior to version 1.11.0,
the C<prime256v1> curve was used by default.







B<NOTE>

When using OpenSSL 1.0.2 or higher,
this directive sets the list of curves supported by the server.
Thus, in order for ECDSA certificates to work,
it is important to include the curves used in the certificates.








=head2 ssl_password_file


B<syntax:> ssl_password_file I<I<C<file>>>



B<context:> I<mail>


B<context:> I<server>



This directive appeared in version 1.7.3.





Specifies a I<C<file>> with passphrases for
secret keys
where each passphrase is specified on a separate line.
Passphrases are tried in turn when loading the key.





Example:

    
    mail {
        ssl_password_file /etc/keys/global.pass;
        ...
    
        server {
            server_name mail1.example.com;
            ssl_certificate_key /etc/keys/first.key;
        }
    
        server {
            server_name mail2.example.com;
    
            # named pipe can also be used instead of a file
            ssl_password_file /etc/keys/fifo;
            ssl_certificate_key /etc/keys/second.key;
        }
    }









=head2 ssl_prefer_server_ciphers


B<syntax:> ssl_prefer_server_ciphers I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<mail>


B<context:> I<server>





Specifies that server ciphers should be preferred over client ciphers
when the SSLv3 and TLS protocols are used.







=head2 ssl_protocols


B<syntax:> ssl_protocols I<
    [C<SSLv2>]
    [C<SSLv3>]
    [C<TLSv1>]
    [C<TLSv1.1>]
    [C<TLSv1.2>]
    [C<TLSv1.3>]>


B<default:> I<TLSv1.2 TLSv1.3>


B<context:> I<mail>


B<context:> I<server>





Enables the specified protocols.

B<NOTE>

The C<TLSv1.1> and C<TLSv1.2> parameters
(1.1.13, 1.0.12) work only when OpenSSL 1.0.1 or higher is used.


B<NOTE>

The C<TLSv1.3> parameter (1.13.0) works only when
OpenSSL 1.1.1 or higher is used.


B<NOTE>

The C<TLSv1.3> parameter is used by default
since 1.23.4.








=head2 ssl_session_cache


B<syntax:> ssl_session_cache I<
    C<off> E<verbar>
    C<none> E<verbar>
    [C<builtin>[:I<C<size>>]]
    [C<shared>:I<C<name>>:I<C<size>>]>


B<default:> I<none>


B<context:> I<mail>


B<context:> I<server>





Sets the types and sizes of caches that store session parameters.
A cache can be of any of the following types:

=over



=item C<off>




the use of a session cache is strictly prohibited:
nginx explicitly tells a client that sessions may not be reused.



=item C<none>




the use of a session cache is gently disallowed:
nginx tells a client that sessions may be reused, but does not
actually store session parameters in the cache.



=item C<builtin>




a cache built in OpenSSL; used by one worker process only.
The cache size is specified in sessions.
If size is not given, it is equal to 20480 sessions.
Use of the built-in cache can cause memory fragmentation.



=item C<shared>




a cache shared between all worker processes.
The cache size is specified in bytes; one megabyte can store
about 4000 sessions.
Each shared cache should have an arbitrary name.
A cache with the same name can be used in several
servers.
It is also used to automatically generate, store, and
periodically rotate TLS session ticket keys (1.23.2)
unless configured explicitly
using the L</ssl_session_ticket_key> directive.




=back







Both cache types can be used simultaneously, for example:

    
    ssl_session_cache builtin:1000 shared:SSL:10m;


but using only shared cache without the built-in cache should
be more efficient.







=head2 ssl_session_ticket_key


B<syntax:> ssl_session_ticket_key I<I<C<file>>>



B<context:> I<mail>


B<context:> I<server>



This directive appeared in version 1.5.7.





Sets a I<C<file>> with the secret key used to encrypt
and decrypt TLS session tickets.
The directive is necessary if the same key has to be shared between
multiple servers.
By default, a randomly generated key is used.





If several keys are specified, only the first key is
used to encrypt TLS session tickets.
This allows configuring key rotation, for example:

    
    ssl_session_ticket_key current.key;
    ssl_session_ticket_key previous.key;







The I<C<file>> must contain 80 or 48 bytes
of random data and can be created using the following command:

    
    openssl rand 80 > ticket.key


Depending on the file size either AES256 (for 80-byte keys, 1.11.8)
or AES128 (for 48-byte keys) is used for encryption.







=head2 ssl_session_tickets


B<syntax:> ssl_session_tickets I<C<on> E<verbar> C<off>>


B<default:> I<on>


B<context:> I<mail>


B<context:> I<server>



This directive appeared in version 1.5.9.





Enables or disables session resumption through
L<TLS session tickets|https://datatracker.ietf.org/doc/html/rfc5077>.







=head2 ssl_session_timeout


B<syntax:> ssl_session_timeout I<I<C<time>>>


B<default:> I<5m>


B<context:> I<mail>


B<context:> I<server>





Specifies a time during which a client may reuse the
session parameters.







=head2 ssl_trusted_certificate


B<syntax:> ssl_trusted_certificate I<I<C<file>>>



B<context:> I<mail>


B<context:> I<server>



This directive appeared in version 1.7.11.





Specifies a I<C<file>> with trusted CA certificates in the PEM format
used to verify client certificates.





In contrast to the certificate set by L</ssl_client_certificate>,
the list of these certificates will not be sent to clients.







=head2 ssl_verify_client


B<syntax:> ssl_verify_client I<
    C<on> E<verbar> C<off> E<verbar>
    C<optional> E<verbar> C<optional_no_ca>>


B<default:> I<off>


B<context:> I<mail>


B<context:> I<server>



This directive appeared in version 1.7.11.





Enables verification of client certificates.
The verification result is passed in the
C<Auth-SSL-Verify> header of the
L<authentication|ngx_mail_auth_http_module>
request.





The C<optional> parameter requests the client
certificate and verifies it if the certificate is present.





The C<optional_no_ca> parameter
requests the client
certificate but does not require it to be signed by a trusted CA certificate.
This is intended for the use in cases when a service that is external to nginx
performs the actual certificate verification.
The contents of the certificate is accessible through requests
L<sent|ngx_mail_auth_http_module>
to the authentication server.







=head2 ssl_verify_depth


B<syntax:> ssl_verify_depth I<I<C<number>>>


B<default:> I<1>


B<context:> I<mail>


B<context:> I<server>



This directive appeared in version 1.7.11.





Sets the verification depth in the client certificates chain.







=head2 starttls


B<syntax:> starttls I<
  C<on> E<verbar>
  C<off> E<verbar>
  C<only>>


B<default:> I<off>


B<context:> I<mail>


B<context:> I<server>






=over



=item C<on>




allow usage of the C<STLS> command for the POP3
and the C<STARTTLS> command for the IMAP and SMTP;



=item C<off>




deny usage of the C<STLS>
and C<STARTTLS> commands;



=item C<only>




require preliminary TLS transition.




=back









