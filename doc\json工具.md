统一使用`cjson`动态库提供的`json`序列化和反序列化功能。参考[cjson-test](../test/cjson-test.lua)

# 不安全模式

如果编解码发生异常会直接抛出 error

```lua

local json = require('cjson')

-- 反序列化（解码）
local user = json.decode(json_str)
assert(user.name == '中文')
assert(user.age == 18)

-- 序列化（编码）
local stringify = json.encode(user)
-- 注意序列化可能顺序反了
assert(stringify == json_str or stringify == '{"age":18,"name":"中文"}')
```

# 安全模式

如果编解码发生异常会返回 nil 和错误信息，类似于`pcall`。一般只用在反序列化操作（解码）

```lua

-- 安全模式的 json 工具，类似于 pcall
local safe_json = require('cjson.safe')

-- 一个错误的 json 字符串
local json_str_fatal = '{"name":"中文","age":18'
local safe_tbl, err = safe_json.decode(json_str_fatal)
assert(safe_tbl == nil)
assert(err ~= nil)
print('字符串反序列化发生异常', err)

--- 序列化（编码） nil 值忽略问题：cjson 在序列化时，对于 nil 值是不做序列化的，除非手动定义成 cjson.null
local role = {
    id = 1,
    name = '管理员',
    age = nil
}

```

# nil 值序列化问题

cjson 在序列化时，对于 nil 值是不做序列化的，除非手动定义成 cjson.null。

```lua

local json = require('cjson')
local role = {
    id = 1,
    name = '管理员',
    age = nil
}

-- 特别注意：使用 cjson.null 这个 userdata 必须手动赋值，不能直接在表里定义
role.type = json.null

local json_with_null = json.encode(role)
print(json_with_null)
-- 断言 json_with_null 里有 "type":null 字样
assert(json_with_null:find('"type":null') ~= nil)
-- 并且没有 "age" 字样
assert(json_with_null:find('"age"') == nil)

```
