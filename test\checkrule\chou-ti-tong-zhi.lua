-- 同空间 抽屉深 花色是否相同检查
-- 1、根据单元数据 划分成不同的房间的单元组
-- 2、遍历每个单元组 收集数据
-- 3、检查是否有不一样值的花色 深度数据 如果有 则该房间校验检测结果为false

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local check_rule_result = require("lib.check-rule-result")

local _M = {}

-- 定义规则执行入口
function _M.dowork(root, target, context)
	local rule_result = check_rule_result.new("同空间下的抽芯花色/厚度同值检查", check_rule_result.LEVEL.INFO)

	-- 存储结果
	local result = {}
	local logs = {}
    
	local xml_parts_p = root:search("/Root/Parts")
	local drawers_xml = xml_search.get_part_by_mkbq(xml_parts_p, 4)

	if not drawers_xml or #drawers_xml == 0 then
		return rule_result:pass("没有找到任何抽屉")
	end

	-- 2. 根据房间过滤到不同的单元组
	local room_unit_map = {}
	for _, drawer_xml in ipairs(drawers_xml) do
		local room_id = drawer_xml:get_attribute("roomId")
		if not room_id then
			-- 如果roomId为空 则获取父级roomId
			room_id = drawer_xml:parent():get_attribute("roomId")
		end
		if not room_unit_map[room_id] then
			room_unit_map[room_id] = {}
		end
		-- 找抽芯
		local drawer_core_xml = xml_search.get_part_by_bjbq(drawer_xml, 11)
		-- 遍历所有抽芯 理论上就一个
		if drawer_core_xml and #drawer_core_xml > 0 then
			for index, drawer_core in ipairs(drawer_core_xml) do
				local drawer_core_D = xml_search.get_value_in_parameter(drawer_core, "CXHD")
				local texture = drawer_core:get_attribute("textureName") or ""

				table.insert(room_unit_map[room_id], {
					id = drawer_xml:get_attribute("id"),
					name = drawer_xml:get_attribute("name"),
					deep = tonumber(drawer_core_D),
					textureName = texture
				})
			end
		end
	end

	-- 3. 遍历每个单元组 收集数据

	-- 4. 检查是否有不一样值的数据 如果有 则该房间校验结果为false
	local drawer_count = 0
	for room_id, data in pairs(room_unit_map) do
		local has_diff_deep = false
		local has_diff_texture = false

		if #data > 1 then
			local first_drawer = data[1]
			local drawer_ids = {}
			for _, drawer_data in ipairs(data) do
				drawer_count = drawer_count + 1
				if drawer_data.drawer_deep ~= first_drawer.drawer_deep then
					has_diff_deep = true
				end
				if drawer_data.textureName ~= first_drawer.textureName then
					has_diff_texture = true
				end

				table.insert(drawer_ids, drawer_data.id)

				table.insert(logs, string.format("房间ID: %s 抽屉: %s (ID: %s) 抽芯厚度: %s mm 花色: %s", 
					room_id, drawer_data.id, drawer_data.name, drawer_data.deep, drawer_data.textureName))
			end

			local message = string.format("房间 %s 的抽芯厚度或花色不一致", room_id)
			if has_diff_deep or has_diff_texture then
				table.insert(result, {
					prompt = message,
					related_ids = drawer_ids
				})
			end
		end
	end

	table.insert(logs, string.format("检查完成，共检查了 %d 个抽屉", drawer_count))

	return rule_result:error(result, logs)
end

return _M
