-- 动态规则执行器  只会执行 lua_scripts/dynamic 目录下的脚本
-- 根据传入的规则名称 从./dynamic/ 目录下 加载对应的lua脚本
-- 执行脚本 传入 环境变量 并返回执行结果

local dynamic_rule_executor = {}

-- 获取当前脚本所在目录的函数
local function get_script_path()
    local info = debug.getinfo(1, "S")
    local script_path = info.source:sub(2)  -- 移除开头的 '@'
    return script_path:match("(.*[/\\])") or "./"
end
-- 获取项目根目录的函数
local function get_root_path()
    local current_path = get_script_path()
    -- 从 src/lib/ 回退到项目根目录
    return current_path:match("(.*[/\\])src[/\\]lib[/\\]") or "../"
end

-- 执行规则
-- @param ruleName string 规则名称
-- @param env table 环境变量
-- @return boolean, string 执行结果和错误信息
function dynamic_rule_executor:execute(ruleName, env)
    -- 打印传入的 ruleName 的类型和值
    
    if not ruleName then
        return false, "规则名称不能为空"
    end

    -- 类型检查
    ruleName = tostring(ruleName)
    if not ruleName or ruleName == "" then
        return false, "规则名称格式异常"
    end

    -- 构建规则文件路径
    local root_path = get_root_path()
    local scriptPath = root_path .. "windows/lua_scripts/dynamic/" .. ruleName .. ".lua"
    -- 如果当前环境是 linux 需要变更执行路径
    -- todo linux环境测试
    if package.config:sub(1, 1) == "/" then
        scriptPath = "/usr/local/openresty/nginx/lua_scripts/dynamic/" .. ruleName .. ".lua"
    end
    
    -- 检查文件是否存在
    local file = io.open(scriptPath, "r")
    if not file then
        return false, string.format("规则文件不存在: %s", scriptPath)
    end
    file:close()

    -- 添加模块搜索路径
    local script_dir = scriptPath:match("(.*[/\\])")
    package.path = script_dir .. "?.lua;" .. package.path

    -- 加载规则脚本
    local chunk, err = loadfile(scriptPath)
    if not chunk then
        return false, string.format("加载规则文件失败: %s", err)
    end

    -- 执行规则，直接传递环境变量作为参数
    local success, ruleSuccess, ruleResult = pcall(chunk, env)
    if not success then
        return false, string.format("执行规则失败: %s", ruleSuccess)
    end

    -- 返回规则执行的结果
    return ruleSuccess, ruleResult
end

return dynamic_rule_executor
