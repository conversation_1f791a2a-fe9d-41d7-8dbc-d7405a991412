local _M = {}

--抽屉
--导轨类型为全拉按弹阻尼导轨时，【DGLX】=93、94、95、96时，安装内空的宽不在【350,1064】范围内时，需提示。

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local geometry = require("lib.oop-rule-engine")
local check_rule_result = require("lib.check-rule-result")

local function get_rootPart_xml(target, root)
    local parent_id = target:get_attribute("parentId")
    if parent_id then
        local parent = root:search("//Part[@id='" .. parent_id .. "']")
        if parent and #parent > 0 then
            return get_rootPart_xml(parent[1], root)
        else
            return target
        end
    end
    return target
end

-- 定义规则执行入口
function _M.dowork(root, target, context)
	local rule_result = check_rule_result.new("抽屉所在内空宽度检查", check_rule_result.LEVEL.ERROR)

	-- 存储结果
	local result = {}
    local logs = {}
    
    -- 1. 获取所有抽屉节点
    local engine_object = geometry.current()

    local drawers_xml= root:search("//Part[@prodCatId='1043']")

    if not drawers_xml or #drawers_xml == 0 then
        return rule_result:pass("未找到任何抽屉节点")
    end

    -- 遍历抽屉节点
    -- table.insert(logs, "找到抽屉节点数量: " .. #drawers_xml)
    for _, drawer in ipairs(drawers_xml) do
        -- 获取 DGLX 参数值
        local parameters = drawer:search("Parameters/Parameter[@name='DGLX']")
        local dglx = parameters and parameters[1] and tonumber(parameters[1]:get_attribute("value"))
        if dglx and dglx >= 93 and dglx <= 96 then
            local drawer_id = drawer:get_attribute("id")
            local drawer_name = drawer:get_attribute("name")
			-- table.insert(logs, "DGLX[93-96]抽屉id: " .. drawer_id)
            -- 获取当前抽屉抽芯的内空
            local core_parts = drawer:search("Part")
            for _, part in ipairs(core_parts) do
                -- 获取 Parameters 节点
                local parameters = part:search("Parameters/Parameter[@name='BJBQ']")
                local bjbq = parameters and parameters[1] and parameters[1]:get_attribute("value")

                if bjbq == "11" then
                    -- 找到抽芯节点，获取抽芯id
                    local part_id = part:get_attribute("id")
                    local internalSpace = engine_object:get_internal_space(part_id)
                    -- table.insert(logs, "抽芯id: " .. part_id)
                    -- table.insert(logs, {
					-- 	internalSpace = internalSpace,
					-- } )
                    local rootPart_xml = get_rootPart_xml(part, root)
                    local rootPart_id = rootPart_xml:get_attribute("id")
                    -- table.insert(logs, "rootPart_id: " .. rootPart_id)
                    local space_xml = rootPart_xml:search("Space")
					local rz = space_xml and space_xml[1] and space_xml[1]:get_attribute("RZ")
                    local numrz = tonumber(rz)
                    if numrz and (math.abs(math.abs(numrz) - 0) < 0.01 or math.abs(math.abs(numrz) - 180) < 0.01) then
                        -- 如果抽芯的旋转角度为0或180，则获取内空的左、右边界
                        internalWidth = internalSpace.right - internalSpace.left
                    elseif numrz and (math.abs(math.abs(numrz) - 90) < 0.01 or math.abs(math.abs(numrz) - 270) < 0.01) then
                        -- 如果抽芯的旋转角度为90或270，则获取内空的前、后边界
                        internalWidth = internalSpace.back - internalSpace.front  
                    end 
                    if internalWidth < 350 or internalWidth > 1064 then
                        -- 抽芯所在内空不满足条件，添加到结果中
                        table.insert(result, {
                            prompt = string.format("%s 当前导轨为全拉推弹阻尼导轨时，内空宽度范围为【350,1064】，请调整", drawer_name),
                            related_ids = {drawer_id}
                        })
                    end
                end
            end
        end
    end

    return rule_result:error(result, logs)
end

return _M
