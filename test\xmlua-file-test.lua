local xmlua = require("xmlua")

local file = io.open('cabinet.xml', 'r')
-- 读取整个文件内容
local xml = file:read('*a')
file:close()
-- 解析成 doc
local doc = xmlua.XML.parse(xml)

-- 查询所有 panel
local panels = doc:search('/Root/Cabinet/Panels/Panel')
print('#panels', #panels)

local panel3 = doc:search('/Root/Cabinet/Panels/Panel[@ID="3"]')
print('#panel3', #panel3)
print('panel3', panel3[1]:get_attribute('Material'))
