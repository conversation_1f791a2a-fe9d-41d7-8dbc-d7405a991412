local _M = {}
-- 门板缝隙 是否需要自动调整
-- 1、获取门板的左右上下的盖法（参数里面的lCoverType是否等于0来判断是否内嵌，0是内嵌。rCoverType是右掩盖方式，uCoverType是上掩盖方式，dCoverType是下掩方式）
-- 2、获取门板的W和H
-- 3、获取左右水平线上的门板总数N1（门之间相距4mm以内，则进行计数）
-- 4、获取上下水平线上的门板总数N2（门（需要门板背面齐平）之间相距4mm以内，则进行计数）
-- 5、如果rCoverType、lCoverType都是0，则代表左右同时入柱、
--    如果rCoverType或lCoverType一个等于0另一个不等于0，则代表左右不同时入柱；
--    如果uCoverType、dCoverType都是0，则代表上下同时入柱、
--    如果uCoverType或dCoverType一个等于0另一个不等于0，则代表上下不同时入柱

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local geometry_engine = require("lib.oop-rule-engine")
local check_rule_result = require("lib.check-rule-result")

-- 定义规则执行入口
function _M.dowork(root, target, context)
	local rule_result = check_rule_result.new("门板缝隙检查", check_rule_result.LEVEL.INFO)

	-- 存储结果
	local result = {}
	local logs = {}
    
	-- 1. 获取所有门板节点
	local engine_object = geometry_engine.current()

	-- 过滤掉真门的情况
	local xml_parts_p = root:search("/Root/Parts/Part")
	local doors_xml = xml_search.get_part_by_mkbq(xml_parts_p, 3)
    
	if not doors_xml or #doors_xml == 0 then
		return rule_result:pass("没有找到任何门板 MKBQ 的Part节点")
	end

	-- 2. 获取所有门铰节点 并收集盖值数据
	local door_info_list = {}
	for _, door_xml in ipairs(doors_xml) do
		local door_w = door_xml.W
		local door_h = door_xml.H
		local door_id = door_xml.id
		local door_name = door_xml:get_attribute("name")

		local l_cover_type = xml_search.get_value_in_parameter(door_xml, "lCoverType")
		local r_cover_type = xml_search.get_value_in_parameter(door_xml, "rCoverType")
		local u_cover_type = xml_search.get_value_in_parameter(door_xml, "uCoverType")
		local d_cover_type = xml_search.get_value_in_parameter(door_xml, "dCoverType")

		table.insert(door_info_list, {
			id = door_id,
			name = door_name,
			w = door_w,
			h = door_h,
			lCoverType = l_cover_type,
			rCoverType = r_cover_type,
			uCoverType = u_cover_type,
			dCoverType = d_cover_type
		})
	end

	-- 3、获取场景中 所有门板的 连接关系
	local door_aligned_group = engine_object:get_sceen_door_aligned_group()
	local hor_group = door_aligned_group.horGroup
	local ver_group = door_aligned_group.verGroup

	-- 转换水平方向组的数据结构
	local hor_group_map = {}
	if hor_group and #hor_group > 0 then
		for _, group in ipairs(hor_group) do
			local group_length = #group
			for _, door in ipairs(group) do
				hor_group_map[door.uuid] = group_length
			end
		end
	end

	-- 转换垂直方向组的数据结构
	local ver_group_map = {}
	if ver_group and #ver_group > 0 then
		for _, group in ipairs(ver_group) do
			local group_length = #group
			for _, door in ipairs(group) do
				ver_group_map[door.uuid] = group_length
			end
		end
	end

	-- 5、输出结果
	for _, door_info in ipairs(door_info_list) do
		local door_id = door_info.id
		local door_name = door_info.name
		local door_w = door_info.w
		local door_h = door_info.h

		local N1 = 1
		local N2 = 1

		if hor_group_map[door_id] then
			N1 = hor_group_map[door_id]
		end

		if ver_group_map[door_id] then
			N2 = ver_group_map[door_id]
		end 

		local message = string.format("门板 %s 的水平连接数(N1)为 %d，垂直连接数(N2)为 %d", door_name, N1, N2)

		table.insert(result, {
			id = door_id,
			name = door_name,
			w = door_w,
			h = door_h,
			N1 = N1,
			N2 = N2,
			lCoverType = door_info.lCoverType,
			rCoverType = door_info.rCoverType,
			uCoverType = door_info.uCoverType,
			dCoverType = door_info.dCoverType,
			prompt = message,
			related_ids = {door_id}
		})
	end

	table.insert(logs, string.format("门板缝隙检查完成，共有 %d 个门板", #result))

	return rule_result:error(result, logs)
end

return _M
