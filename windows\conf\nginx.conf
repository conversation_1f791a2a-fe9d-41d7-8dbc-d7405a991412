#user  nobody;
worker_processes  1;
daemon off;
master_process off;
#error_log  logs/error.log;
#error_log  logs/error.log  notice;
error_log  logs/error.log  info;

#pid        logs/nginx.pid;

# 保留环境变量
# env REDIS_MODE;
# env REDIS_HOST;
# env REDIS_PORT;
# env REDIS_PWD;
env REDIS_CLUSTER_NODES;
env REDIS_CLUSTER_PWD;

events {
    worker_connections  2048;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    #缓存区定义
    lua_shared_dict redis_cluster_slot_locks 500m;
    lua_shared_dict script_info_cache 10m;  # 添加脚本信息缓存

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    log_format main '$remote_addr - [$http_x_forwarded_for] - $remote_user [$time_local] '
                    '"$request" $status $body_bytes_sent "$http_referer" "$http_user_agent" '
                    '$request_length $request_time $http_trace_id $host [$proxy_add_x_forwarded_for] '
                    '[-] $upstream_addr $upstream_response_length $upstream_response_time '
                    '$upstream_status [-]';
    access_log  logs/access.log  main;

    client_max_body_size 10m;

    sendfile        on;
    #tcp_nopush     on;

    #Lua默认根路径
    lua_package_path "./lua_scripts/?.lua;./lualib/?.lua;";

    #keepalive_timeout  0;
    keepalive_timeout  65;

    init_worker_by_lua_block {
        ngx.log(ngx.INFO, "开始加载init_worker")
        local init_worker = require "lib.init-worker"
        ngx.log(ngx.INFO, "成功require init_worker模块")
        init_worker.init()
    }

    init_by_lua_block {
        local debug = {
            host = "localhost",
            port = 9966,
            timeout = 30,
            enabled = true
        }

        if debug.enabled then
            local ok = pcall(function()
                ngx.log(ngx.INFO,  "package.cpath"..package.cpath)
                package.cpath = package.cpath .. ";./lualib/emmy_core.dll"
                local dbg = require("emmy_core")
                ngx.log(ngx.INFO, "连接调试器: " .. debug.host .. ":" .. debug.port)
                dbg.tcpConnect(debug.host, debug.port)
                ngx.log(ngx.INFO, "调试器连接成功")
            end)
            if not ok then
                ngx.log(ngx.ERR, "调试器初始化失败")
            end
        end
    }

    #gzip  on;

    server {
        listen       8889;
        server_name  localhost;
        # resolver ************;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location / {
            root   html;
            index  index.html index.htm;
        }

        location /api/v1/rule/expression/calc {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/expression-calc.lua;
        }

        location /api/v1/rule/param/calc {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/param-calc.lua;
        }

        # 规则调试接口
        location /api/v1/rule/debug {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/rule-debugger.lua;
        }

        # 生产检测 单条规则测试
        location /api/v1/rule/process/singleTest {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/single-test.lua;
        }

        # 生产检测
        location /api/v1/rule/process/check {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/process-check.lua;
        }

        # 规则引擎测试
        location /api/v1/rule/engine/test {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/TestRuleEngineGlue.lua;
        }

        # Redis数据获取接口
        location /api/v1/redis/data {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/redis-data.lua;
        }

        # ----------------------------  以下是测试接口 start ----------------------------

        location /redis/save {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/save-redis-mult.lua;
        }

        location /test/script/redis/generate {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/test-generate-redis-script.lua;
        }

        location /test/script/redis/list {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/test-get-redis-list-scripts.lua;
        }

        location /test/script/redis/delete {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/test-delete-redis-script.lua;
        }

        location /actuator/health {
            include common_headers.conf;
            return 200 '{"status":"UP"}';
        }

        location ~ ^/Test(.*) {
            include common_headers.conf;
            content_by_lua_file ./lua_scripts/Test$1.lua;
        }

        # ----------------------------  测试接口 end----------------------------

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

        # proxy the PHP scripts to Apache listening on 127.0.0.1:80
        #
        #location ~ \.php$ {
        #    proxy_pass   http://127.0.0.1;
        #}

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
        #location ~ \.php$ {
        #    root           html;
        #    fastcgi_pass   127.0.0.1:9000;
        #    fastcgi_index  index.php;
        #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
        #    include        fastcgi_params;
        #}

        # deny access to .htaccess files, if Apache's document root
        # concurs with nginx's one
        #
        #location ~ /\.ht {
        #    deny  all;
        #}
    }


    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}


    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

}
