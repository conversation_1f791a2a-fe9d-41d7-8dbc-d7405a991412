local re = require('libRuleEngineCore')

local _M = {
    -- 模块版本
    _VERSION = '1.0.0',
}

local fs = {}
for k, v in pairs(re) do
    table.insert(fs, k)
end

function _M.functions()
    return fs
end

--- 创建一个立方体
---@param a number 长度
---@param b number 宽度
---@param c number 高度
---@return number 立方体id
function _M.make_box(a, b, c)
    return re.makeBox(a, b, c)
end

--- 导出 STEP 文件
---@param filename string STEP 文件路径
---@return nil
function _M.export_step_file(filename)
    local content = re.sceneExport()
    local f = io.open(filename, 'w')
    if not f then
        error('无法打开文件：' .. filename)
    end
    f:write(content)
    f:close()
end

function _M.add_test(a, b)
    return re.addTest(a, b)
end

function _M.core_dump_test()
    return re.coredumpTest()
end

--- 场景初始化
---@param xml string XML 文件内容
---@return table
function _M.scene_init(xml)
    return re.sceneInit(xml)
end

--- 场景清除
---@param t_id table 场景id
---@return table|nil
function _M.scene_clear(t_id)
    return re.sceneClear(t_id)
end

--- 场景所有Shape导出step文件
---@param t_id table 场景id
---@return string
function _M.scene_export(t_id)
    return re.sceneExport(t_id)
end


-- 获取对象之间的距离
-- @param t_id: 场景id
-- @param attrKey1: 对象属性1
-- @param op1: 对象属性1的比较符
-- @param value1: 对象属性1的值
-- @param directions(up, down, left, right, front, back): 方向数组
-- @param distanceOp: 距离的比较符
-- @param distance: 距离
-- @param attrKey2: 对象属性2
-- @return 返回值数组
-- 返回值数组.object: 对象1 uid 
-- 返回值数组.subObject: 对象2 uid
-- 返回值数组.distance: 距离
-- 返回值数组.direction: 方向
function _M.get_objects_distance(t_id, attrKey1, op1, value1, directions, distanceOp, distance, attrKey2, op2, value2)

    -- debug call
    -- local directions = {"up", "down"}
    -- local objects = _M.get_objects_distance("uid", "==", "1", directions, "<", 10, "uid", "==", "2")



    -- debug print
    -- print("get_objects_distance")
    -- print("attrKey1: " .. attrKey1)
    -- print("op1: " .. op1)
    -- print("value1: " .. value1)
    -- for i, direction in ipairs(directions) do
    --     print("direction: " .. direction)
    -- end
    -- print("distanceOp: " .. distanceOp)
    -- print("distance: " .. distance)
    -- print("attrKey2: " .. attrKey2)
    -- print("op2: " .. op2)
    -- print("value2: " .. value2)
    -- local results = re.getObjectsDistance(attrKey1, op1, value1, directions, distanceOp, distance, attrKey2, op2, value2)

    -- for i, object in ipairs(results) do
    --     print("object: " .. object.object)
    --     print("subObject: " .. object.subObject)
    --     print("distance: " .. object.distance)
    --     print("direction: " .. object.direction)
    -- end

    -- return results

    return re.getObjectsDistance(t_id, attrKey1, op1, value1, directions, distanceOp, distance, attrKey2, op2, value2)
end

-- 获取对象之间的距离
-- @param t_id: 场景id
-- @param attrKey1: 对象属性1
-- @param op1: 对象属性1的比较符
-- @param values1: 对象属性1的数组
-- @param directions(up, down, left, right, front, back): 方向数组
-- @param distanceOp: 距离的比较符
-- @param distance: 距离
-- @param attrKey2: 对象属性2
-- @param op2: 对象属性2的比较符
-- @param values2: 对象属性2的数组
-- @return 返回值数组
-- 返回值数组.object: 对象1 uid 
-- 返回值数组.subObject: 对象2 uid
-- 返回值数组.distance: 距离
-- 返回值数组.direction: 方向
function _M.get_objects_distance_value_array(t_id, attrKey1, op1, values1, directions, distanceOp, distance, attrKey2, op2, values2)

    -- debug call
    -- local directions = {"up", "down"}
    -- local objects = _M.get_objects_distance("uid", "==", {"1", "2"}, directions, "<", 10, "uid", "==", {"3", "4"})



    -- debug print
    -- print("get_objects_distance")
    -- print("attrKey1: " .. attrKey1)
    -- print("op1: " .. op1)
    -- for i, value in ipairs(values1) do
    --     print("value: " .. value)
    -- end
    -- for i, direction in ipairs(directions) do
    --     print("direction: " .. direction)
    -- end
    -- print("distanceOp: " .. distanceOp)
    -- print("distance: " .. distance)
    -- print("attrKey2: " .. attrKey2)
    -- print("op2: " .. op2)
    -- for i, value in ipairs(values2) do
    --     print("value: " .. value)
    -- end
    -- local results = re.getObjectsDistance(attrKey1, op1, value1, directions, distanceOp, distance, attrKey2, op2, value2)

    -- for i, object in ipairs(results) do
    --     print("object: " .. object.object)
    --     print("subObject: " .. object.subObject)
    --     print("distance: " .. object.distance)
    --     print("direction: " .. object.direction)
    -- end

    -- return results

    return re.getObjectsDistanceValueArray(t_id, attrKey1, op1, values1, directions, distanceOp, distance, attrKey2, op2, values2)
end

-- 获取对象之间的干涉
-- @param t_id: 场景id
-- @param objects: 对象ID列表，为空是全场景柜子干涉
-- @param ignoreSameRoot: 是否忽略同一个柜子内的板件干涉
-- @return 返回值数组
-- 返回值数组.object: 对象1 uid
-- 返回值数组.subObject: 对象2 uid
function _M.get_objects_overlap(t_id, objects, ignoreSameRoot)


    -- debug call 1
    -- local objects = {
    --     "8a91eaa4-47e9-5df4-aefd-4052bd90295a",
    --     "d129d76f-957a-55ce-a268-3ae94a6de61f"
    -- }
    -- local isSameRoot = true

    -- local results = re.getObjectsOverlap(t_id, objects, isSameRoot)

    -- 全场景检测
    -- debug call 2
    -- local objects = {
    -- }
    -- local isSameRoot = true
    -- local results = re.getObjectsOverlap(t_id, objects, isSameRoot)

    -- -- 遍历结果
    -- for i, result in ipairs(results) do
    --     print("Object:", result.object)
    --     print("Object Name:", result.objectName)
    --     print("SubObject:", result.subObject)
    --     print("SubObject Name:", result.subObjectName)
    -- end

    -- return results

    return re.getObjectsOverlap(t_id, objects, ignoreSameRoot)
end

function _M.add_test_by_id(t_id, a, b)
    return re.addTestById(t_id, a, b)
end

-- 获取两个对象之间的夹角
-- @param t_id: 场景id
-- @param angleObjects: 夹角对象数组
-- @angleObjects.id1: 对象1 uid
-- @angleObjects.id2: 对象2 uid
-- @return 返回值数组
-- 返回值数组.id1: 对象1 uid
-- 返回值数组.id2: 对象2 uid
-- 返回值数组.angle: 夹角
function _M.getObjectsAngle(t_id, angleObjects)

    -- local partId1 = "8a91eaa4-47e9-5df4-aefd-4052bd90295a"
    -- local partId2 = "d129d76f-957a-55ce-a268-3ae94a6de61f"

    -- local angleObjects = {
    --     {
    --         id1 = partId1,
    --         id2 = partId2,
    --     }
    -- }

    -- local result = re.getObjectsAngle(t_id, angleObjects)
    
    return re.getObjectsAngle(t_id, angleObjects)
end


-- 根据门铰的坐标定位到门板的门铰边
-- @param t_id: 场景id
-- @param uid: 门铰uid
-- @return DoorHingeFace
-- DoorHingeFace.coverType
-- DoorHingeFace.doorName
-- DoorHingeFace.doorUUID
function _M.get_door_hinge_face_by_pos(t_id, uid)
    return re.getDoorHingeFaceByPos(t_id, uid)
end

-- 根据门铰的坐标定位到门板的门铰边
-- @param t_id: 场景id
-- @param uid: 门铰uid
-- @param cover_type: 门铰类型
-- @return DoorHingeCoverInfo
-- DoorHingeCoverInfo.doorName
-- DoorHingeCoverInfo.doorUUID
-- DoorHingeCoverInfo.bCover
function _M.get_door_hinge_cover_info(t_id, uid, cover_type)
    return re.getDoorHingeCoverInfo(t_id, uid, cover_type)
end

function _M.aabb_cover_detect(t_id, id, direction, distance, detectIDs)
    return re.aabbCoverDetect(t_id, id, direction, distance, detectIDs)
end

function _M.get_internal_space(t_id, id)
    return re.getInternalSpace(t_id, id)
end

function _M.get_internal_space_distance(t_id, id, internalSpace)
    return re.getInternalSpaceDistance(t_id, id, internalSpace)
end

function _M.get_door_intersect_inside_board(t_id, uuid, distance)
    return re.getDoorIntersectInsideBoard(t_id, uuid, distance)
end

function _M.get_sceen_door_aligned_group(t_id)
    return re.getSceenDoorAlignedGroup(t_id)
end

-- 自定义 __index 元方法，当在 _M 中找不到某个方法时，尝试从 re 中获取
_M.__index = function(t, k)
    -- 先查找 _M 中是否有该方法
    local method = rawget(_M, k)
    if method then
        return method
    end

    -- 如果 _M 中没有，则查找 re 中是否有该方法
    local re_method = re[k]
    if re_method then
        -- 如果 re 中有该方法，则返回该方法
        return re_method
    end

    -- 如果都没有找到，返回 nil
    return nil
end

setmetatable(_M, _M)

return _M;
