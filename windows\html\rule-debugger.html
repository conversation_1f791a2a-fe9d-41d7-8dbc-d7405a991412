<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>Lua脚本验证工具</title>
  <!-- 添加配置对象 -->
  <script>
    const CONFIG = {
      API_BASE_URL: 'http://localhost:8889'  // 可以通过环境变量或配置文件修改
    };
  </script>
  <!-- 引入CodeMirror的CSS和JS文件 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/lua/lua.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
  <!-- 添加js-beautify库 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.9/beautify.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.9/beautify-css.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.9/beautify-html.min.js"></script>
  <!-- 添加prettier库 -->
  <script src="https://unpkg.com/prettier@2.8.8/standalone.js"></script>
  <script src="https://unpkg.com/prettier@2.8.8/parser-html.js"></script>
  <script src="https://unpkg.com/prettier@2.8.8/parser-babel.js"></script>
  <script src="https://unpkg.com/prettier@2.8.8/parser-postcss.js"></script>
  <script src="https://unpkg.com/prettier@2.8.8/parser-lua.js"></script>
  <style>
    body { 
      font-family: Arial, sans-serif; 
      margin: 0;
      padding: 4px;
      background: #f5f5f5;
      height: 100vh;
      overflow: hidden;
      box-sizing: border-box;
    }
    .container {
      max-width: 1600px;
      margin: 0 auto;
      background: white;
      padding: 8px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      height: calc(100% - 8px);
      box-sizing: border-box;
    }
    .main-content {
      display: flex;
      gap: 8px;
      height: 100%;
    }
    .input-section {
      flex: 1;
      min-width: 0;
      background: #f6f6f6;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
    }
    .result-section {
      width: 400px;
      min-width: 400px;
    }
    h2 { 
      margin-top: 0;
      color: #333;
    }
    .editor-container {
      position: relative;
      display: flex;
      flex-direction: column;
      width: 100%;
    }
    .editor-container:first-child {
      flex: 1;
      margin-bottom: 8px;
      max-height: calc(100vh - 500px);
      min-height: 200px;
    }
    .editor-container:first-child .CodeMirror {
      height: 100% !important;
      max-height: 100%;
    }
    .editor-container:not(:first-child) {
      margin-bottom: 8px;
    }
    .editor-container .CodeMirror {
      height: 100% !important;
      width: 100%;
    }
    .context-lua-container {
      display: flex;
      gap: 8px;
      margin-top: 8px;
      height: 400px;
      min-height: 400px;
    }
    .context-editor, .lua-editor {
      display: flex;
      flex-direction: column;
      width: 100%;
    }
    .context-editor {
      flex: 1;
      min-width: 200px;
    }
    .lua-editor {
      flex: 3;
      min-width: 400px;
    }
    .context-editor .CodeMirror, .lua-editor .CodeMirror {
      height: calc(100% - 28px) !important;
      width: 100%;
    }
    .editor-container label {
      height: 20px;
      line-height: 20px;
      margin-bottom: 4px;
      min-width: 60px;
    }
    .CodeMirror {
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    .CodeMirror-scroll {
      overflow-x: auto;
      overflow-y: auto;
    }
    /* 添加自定义滚动条样式 */
    .CodeMirror-scroll::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    .CodeMirror-scroll::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    .CodeMirror-scroll::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
    }
    .CodeMirror-scroll::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
    .drag-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(33, 150, 243, 0.1);
      border: 2px dashed #2196F3;
      border-radius: 4px;
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 100;
      pointer-events: none;
    }
    .drag-overlay.active {
      display: flex;
    }
    .drag-text {
      background: white;
      padding: 10px 20px;
      border-radius: 4px;
      color: #2196F3;
      font-weight: bold;
    }
    .input-group {
      display: flex;
      gap: 10px;
      align-items: center;
      margin-bottom: 5px;
    }
    .input-group label {
      margin-bottom: 0;
      min-width: 60px;
    }
    input[type="text"] { 
      flex: 1;
      padding: 4px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      height: 32px;
      box-sizing: border-box;
      line-height: 24px;
    }
    button {
      padding: 4px 16px;
      background: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background 0.3s;
      height: 32px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 24px;
    }
    button:hover {
      background: #45a049;
    }
    button.secondary {
      background: #2196F3;
    }
    button.secondary:hover {
      background: #1976D2;
    }
    button.blue {
      background: #2196F3;
    }
    button.blue:hover {
      background: #1976D2;
    }
    .result-container {
      background: #f6f6f6;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
      height: calc(100vh - 40px);
      display: flex;
      flex-direction: column;
    }
    .result-header {
      margin-bottom: 8px;
      background: white;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .button-row {
      display: flex;
      gap: 8px;
      justify-content: flex-start;
    }
    .result-content {
      flex: 1;
      overflow: hidden;
      font-family: monospace;
      background: white;
      padding: 0;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    .result-content .CodeMirror {
      height: 100% !important;
    }
    label { 
      font-weight: bold;
      display: block;
      margin-bottom: 5px;
      height: 24px;
      line-height: 24px;
    }
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 1000;
    }
    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 20px;
      border-radius: 8px;
      width: 80%;
      max-width: 800px;
      max-height: 80vh;
      display: flex;
      flex-direction: column;
    }
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      flex-shrink: 0;
    }
    .modal-title {
      margin: 0;
      font-size: 18px;
    }
    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }
    .preview-content {
      background: #f8f8f8;
      padding: 15px;
      border-radius: 4px;
      font-family: monospace;
      white-space: pre;
      overflow: auto;
      max-height: 60vh;
      max-width: 100%;
    }
    .preview-content::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    .preview-content::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    .preview-content::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
    }
    .preview-content::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="main-content">
      <div class="input-section">
        <div class="editor-container">
          <label>XML内容：</label>
          <div class="drag-overlay">
            <div class="drag-text">拖拽XML文件到此处</div>
          </div>
          <textarea id="xml"><root></root></textarea>
        </div>

        <div class="editor-container">
          <label>XPath：</label>
          <div class="input-group">
            <input type="text" id="xpath" placeholder="输入XPath表达式" />
            <button onclick="previewXPath()" class="secondary">预览</button>
          </div>
        </div>

        <div class="context-lua-container">
          <div class="context-editor">
            <label>上下文：</label>
            <textarea id="context"></textarea>
          </div>

          <div class="lua-editor">
            <label>Lua脚本：</label>
            <textarea id="lua"></textarea>
          </div>
        </div>
      </div>

      <div class="result-section">
        <div class="result-container">
          <div class="result-header">
            <div class="button-row">
              <button onclick="submitData()">执行验证</button>
              <button onclick="clearAll()" class="secondary">清空</button>
            </div>
            <div class="button-row">
              <button onclick="formatJSON()" class="secondary">格式化</button>
              <button onclick="toggleLineWrapping()" class="secondary">切换换行</button>
              <button onclick="getRedisData()" class="blue">获取Redis数据</button>
            </div>
          </div>
          <div class="result-content">
            <textarea id="result"></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- XPath预览弹窗 -->
  <div id="xpathModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">XPath预览结果</h3>
        <button class="close-btn" onclick="closeModal()">&times;</button>
      </div>
      <div class="preview-content" id="xpathPreview"></div>
    </div>
  </div>

  <script>
    // 更新所有CodeMirror实例的配置
    const commonEditorConfig = {
      lineNumbers: true,
      theme: "monokai",
      lineWrapping: true,
      scrollbarStyle: "native"
    };

    const xmlEditor = CodeMirror.fromTextArea(document.getElementById("xml"), {
      ...commonEditorConfig,
      mode: "xml",
      value: "<root></root>",  // 设置默认值
      lineWrapping: false  // 禁用自动换行
    });

    const contextEditor = CodeMirror.fromTextArea(document.getElementById("context"), {
      ...commonEditorConfig,
      mode: {name: "javascript", json: true}
    });

    const luaEditor = CodeMirror.fromTextArea(document.getElementById("lua"), {
      ...commonEditorConfig,
      mode: "lua",
      lineWrapping: false  // 禁用自动换行
    });

    const resultEditor = CodeMirror.fromTextArea(document.getElementById("result"), {
      ...commonEditorConfig,
      mode: {name: "javascript", json: true},
      lineWrapping: false  // 禁用自动换行
    });

    // 拖拽功能
    const dragOverlay = document.querySelector('.drag-overlay');
    const editorContainer = document.querySelector('.editor-container:first-child');

    // 阻止默认拖拽行为
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      editorContainer.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // 高亮拖拽区域
    ['dragenter', 'dragover'].forEach(eventName => {
      editorContainer.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
      editorContainer.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
      dragOverlay.classList.add('active');
    }

    function unhighlight() {
      dragOverlay.classList.remove('active');
    }

    // 处理文件拖放
    editorContainer.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
      const dt = e.dataTransfer;
      const files = dt.files;

      if (files.length > 0) {
        const file = files[0];
        if (file.type === 'text/xml' || file.name.endsWith('.xml')) {
          // 首先以二进制方式读取文件
          const reader = new FileReader();
          reader.onload = function(e) {
            const buffer = e.target.result;
            // 检查文件的BOM标记
            const dataView = new DataView(buffer);
            let encoding = 'UTF-8';
            let offset = 0;
            
            // 检查BOM标记
            if (buffer.byteLength >= 2) {
              // UTF-16 (BE)
              if (dataView.getUint16(0) === 0xFEFF) {
                encoding = 'UTF-16BE';
                offset = 2;
              }
              // UTF-16 (LE)
              else if (dataView.getUint16(0) === 0xFFFE) {
                encoding = 'UTF-16LE';
                offset = 2;
              }
              // UTF-8
              else if (buffer.byteLength >= 3 && dataView.getUint8(0) === 0xEF && dataView.getUint8(1) === 0xBB && dataView.getUint8(2) === 0xBF) {
                encoding = 'UTF-8';
                offset = 3;
              }
            }

            // 将二进制数据转换为文本
            const textDecoder = new TextDecoder(encoding);
            let content = textDecoder.decode(buffer.slice(offset));

            // 如果内容看起来是乱码（检查是否包含特殊字符），尝试其他编码
            if (/[\u0000-\u0008\u000B-\u000C\u000E-\u001F\uFFFD\uFFFE\uFFFF]/.test(content)) {
              // 尝试GBK编码
              const gbkReader = new FileReader();
              gbkReader.onload = function(e) {
                xmlEditor.setValue(e.target.result);
              };
              gbkReader.readAsText(file, 'GBK');
            } else {
              // 检查XML声明中的编码
              const encodingMatch = content.match(/<?xml[^>]+encoding=['"]([^'"]+)['"]/);
              if (encodingMatch && encodingMatch[1].toUpperCase() !== encoding) {
                // 如果XML声明的编码与检测到的不同，使用XML声明的编码重新读取
                const declaredEncoding = encodingMatch[1];
                const finalReader = new FileReader();
                finalReader.onload = function(e) {
                  xmlEditor.setValue(e.target.result);
                };
                finalReader.readAsText(file, declaredEncoding);
              } else {
                xmlEditor.setValue(content);
              }
            }
          };
          reader.readAsArrayBuffer(file);
        } else {
          alert('请拖拽XML文件');
        }
      }
    }

    // 显示弹窗
    function showModal() {
      document.getElementById('xpathModal').style.display = 'block';
    }

    // 关闭弹窗
    function closeModal() {
      document.getElementById('xpathModal').style.display = 'none';
    }

    // 点击弹窗外部关闭
    window.onclick = function(event) {
      const modal = document.getElementById('xpathModal');
      if (event.target == modal) {
        closeModal();
      }
    }

    // 添加XML格式化函数
    function formatXML(xmlText) {
      let formatted = '';
      let indent = '';
      const tab = '    '; // 4个空格作为缩进
      
      // 移除多余的空白字符
      xmlText = xmlText.replace(/>\s+</g, '><');
      
      // 在开始标签后和结束标签前添加换行
      xmlText = xmlText
        .replace(/</g, '~::~<')
        .replace(/>/g, '>~::~');
      
      // 处理每一行
      const lines = xmlText.split('~::~');
      lines.forEach(line => {
        if (line.trim() === '') return;
        
        // 处理结束标签
        if (line.match(/^<\//)) {
          indent = indent.substring(tab.length); // 减少缩进
        }
        
        // 添加当前行（带缩进）
        if (line.trim() !== '') {
          formatted += indent + line + '\n';
        }
        
        // 处理开始标签（非自闭合）
        if (line.match(/^<[^/]/) && !line.match(/[^?]\/>/)) {
          indent += tab; // 增加缩进
        }
      });
      
      return formatted;
    }

    // 修改XPath预览功能
    function previewXPath() {
      const xml = xmlEditor.getValue();
      const xpath = document.getElementById('xpath').value;
      
      if (!xml.trim()) {
        alert('请先输入XML内容');
        return;
      }
      
      if (!xpath.trim()) {
        alert('请先输入XPath表达式');
        return;
      }

      try {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xml, "text/xml");
        
        if (xmlDoc.getElementsByTagName("parsererror").length > 0) {
          alert('XML格式错误，请检查XML内容');
          return;
        }

        const result = document.evaluate(
          xpath,
          xmlDoc,
          null,
          XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
          null
        );

        let previewText = '';
        if (result.snapshotLength === 0) {
          previewText = '未找到匹配的节点';
        } else {
          const serializer = new XMLSerializer();
          for (let i = 0; i < result.snapshotLength; i++) {
            if (i > 0) previewText += '\n'; // 节点之间添加空行
            const node = result.snapshotItem(i);
            const xmlText = serializer.serializeToString(node);
            previewText += formatXML(xmlText);
          }
        }

        document.getElementById('xpathPreview').textContent = previewText;
        showModal();
      } catch (e) {
        alert('XPath执行错误：' + e.message);
      }
    }

    function formatJSON() {
      try {
        // 格式化结果
        const currentValue = resultEditor.getValue();
        if (currentValue.trim()) {
          const formatted = JSON.stringify(JSON.parse(currentValue), null, 2);
          resultEditor.setValue(formatted);
        }

        // 格式化上下文
        const contextValue = contextEditor.getValue();
        if (contextValue.trim()) {
          const contextFormatted = JSON.stringify(JSON.parse(contextValue), null, 2);
          contextEditor.setValue(contextFormatted);
        }

        // 格式化Lua代码
        const luaValue = luaEditor.getValue();
        if (luaValue.trim()) {
          // 使用js-beautify格式化Lua代码
          const formattedLua = js_beautify(luaValue, {
            indent_size: 2,
            indent_char: ' ',
            max_preserve_newlines: 2,
            preserve_newlines: true,
            keep_array_indentation: false,
            break_chained_methods: false,
            indent_scripts: 'normal',
            brace_style: 'collapse',
            space_before_conditional: true,
            unescape_strings: false,
            jslint_happy: false,
            end_with_newline: true,
            wrap_line_length: 0,
            indent_inner_html: false,
            comma_first: false,
            e4x: false,
            indent_empty_lines: false
          });
          luaEditor.setValue(formattedLua);
        }
      } catch (e) {
        alert('格式化错误：' + e.message);
      }
    }

    // 修改 getRedisData 函数中的 URL
    async function getRedisData() {
      const xml = xmlEditor.getValue();
      const lua = luaEditor.getValue();
      resultEditor.setValue('获取Redis数据中...');
      try {
        const baseUrl = window.location.origin;  // 获取当前页面的基础URL
        const redisResponse = await fetch(`${baseUrl}/api/v1/redis/data`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify([
            {
              bizType: "xml",
              redisKey: xml,
              needDecompress: true
            },
            {
              bizType: "rule",
              redisKey: lua,
              needDecompress: false
            }
          ])
        });
        
        const redisData = await redisResponse.json();
        
        // 处理Redis返回的数据
        if (redisData.success) {
          // 更新XML编辑器
          const xmlResult = redisData.successResults.find(item => item.bizType === "xml");
          if (xmlResult && xmlResult.redisValue) {
            xmlEditor.setValue(xmlResult.redisValue);
          }
          
          // 更新Lua编辑器
          const ruleResult = redisData.successResults.find(item => item.bizType === "rule");
          if (ruleResult && ruleResult.redisValue) {
            try {
              // 尝试解析redisValue为JSON
              const ruleData = JSON.parse(ruleResult.redisValue);
              if (ruleData.luaScript) {
                luaEditor.setValue(ruleData.luaScript);
              }
            } catch (e) {
              // 如果解析失败，直接使用redisValue
              luaEditor.setValue(ruleResult.redisValue);
            }
          }
        } else {
          // 处理失败结果
          const xmlResult = redisData.failedResults.find(item => item.bizType === "xml");
          if (xmlResult) {
            if (xmlResult.errorMsg) {
              xmlEditor.setValue(`获取失败: ${xmlResult.errorMsg}`);
            } else if (xmlResult.redisValue) {
              xmlEditor.setValue(xmlResult.redisValue);
            }
          }
          
          const ruleResult = redisData.failedResults.find(item => item.bizType === "rule");
          if (ruleResult) {
            if (ruleResult.errorMsg) {
              luaEditor.setValue(`获取失败: ${ruleResult.errorMsg}`);
            } else if (ruleResult.redisValue) {
              try {
                // 尝试解析redisValue为JSON
                const ruleData = JSON.parse(ruleResult.redisValue);
                if (ruleData.luaScript) {
                  luaEditor.setValue(ruleData.luaScript);
                }
              } catch (e) {
                // 如果解析失败，直接使用redisValue
                luaEditor.setValue(ruleResult.redisValue);
              }
            }
          }
        }
        
        // 无论成功失败都显示完整的返回结果
        resultEditor.setValue(JSON.stringify(redisData, null, 2));
      } catch (e) {
        resultEditor.setValue(JSON.stringify({
          success: false,
          message: "Redis数据获取失败",
          error: e.toString()
        }, null, 2));
      }
    }

    // 修改 submitData 函数中的 URL
    async function submitData() {
      const xml = xmlEditor.getValue();
      const xpath = document.getElementById('xpath').value;
      const context = contextEditor.getValue();
      const lua = luaEditor.getValue();
      resultEditor.setValue('执行验证中...');
      try {
        const requestBody = {
          xmlContent: xml,
          luaScript: lua,
          needGeometry: 0,
          context: context ? JSON.parse(context) : {}
        };
        
        if (xpath && xpath.trim()) {
          requestBody.xpath = xpath;
        }

        const baseUrl = window.location.origin;  // 获取当前页面的基础URL
        const res = await fetch(`${baseUrl}/api/v1/rule/debug`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestBody)
        });
        const data = await res.json();
        resultEditor.setValue(JSON.stringify(data, null, 2));
      } catch (e) {
        resultEditor.setValue('请求失败：' + e);
      }
    }

    // 切换所有编辑器的自动换行状态
    function toggleLineWrapping() {
      const isWrapping = !xmlEditor.getOption("lineWrapping");
      
      // 更新所有编辑器的换行设置
      xmlEditor.setOption("lineWrapping", isWrapping);
      contextEditor.setOption("lineWrapping", isWrapping);
      luaEditor.setOption("lineWrapping", isWrapping);
      resultEditor.setOption("lineWrapping", isWrapping);
      
      // 更新按钮文本
      const button = document.querySelector('button[onclick="toggleLineWrapping()"]');
      button.textContent = isWrapping ? "禁用换行" : "启用换行";
    }

    // 添加清空功能
    function clearAll() {
      // 清空XML编辑器
      xmlEditor.setValue('');
      // 清空XPath输入框
      document.getElementById('xpath').value = '';
      // 清空上下文编辑器
      contextEditor.setValue('');
      // 清空Lua编辑器
      luaEditor.setValue('');
      // 清空结果编辑器
      resultEditor.setValue('');
    }
  </script>
</body>
</html>