=encoding utf-8

=head1 NAME

ngx_mail_imap_module - Module ngx_mail_imap_module




=head1 Directives

=head2 imap_auth


B<syntax:> imap_auth I<I<C<method>> ...>


B<default:> I<plain>


B<context:> I<mail>


B<context:> I<server>





Sets permitted methods of authentication for IMAP clients.
Supported methods are:

=over



=item C<plain>




L<LOGIN|https://datatracker.ietf.org/doc/html/rfc3501>,
L<AUTH=PLAIN|https://datatracker.ietf.org/doc/html/rfc4616>



=item C<login>




L<AUTH=LOGIN|https://datatracker.ietf.org/doc/html/draft-murchison-sasl-login-00>



=item C<cram-md5>




L<AUTH=CRAM-MD5|https://datatracker.ietf.org/doc/html/rfc2195>.
In order for this method to work, the password must be stored unencrypted.



=item C<external>




L<AUTH=EXTERNAL|https://datatracker.ietf.org/doc/html/rfc4422> (1.11.6).




=back







Plain text authentication methods
(the C<LOGIN> command, C<AUTH=PLAIN>,
and C<AUTH=LOGIN>) are always enabled,
though if the C<plain> and C<login> methods
are not specified,
C<AUTH=PLAIN> and C<AUTH=LOGIN>
will not be automatically included in L</imap_capabilities>.







=head2 imap_capabilities


B<syntax:> imap_capabilities I<I<C<extension>> ...>


B<default:> I<IMAP4 IMAP4rev1 UIDPLUS>


B<context:> I<mail>


B<context:> I<server>





Sets the
L<IMAP protocol|https://datatracker.ietf.org/doc/html/rfc3501>
extensions list that is passed to the client in response to
the C<CAPABILITY> command.
The authentication methods specified in the L</imap_auth> directive and
L<STARTTLS|https://datatracker.ietf.org/doc/html/rfc2595>
are automatically added to this list depending on the
L<ngx_mail_ssl_module> directive value.





It makes sense to specify the extensions
supported by the IMAP backends
to which the clients are proxied (if these extensions are related to commands
used after the authentication, when nginx transparently proxies a client
connection to the backend).





The current list of standardized extensions is published at
L<www.iana.org|http://www.iana.org/assignments/imap4-capabilities>.







=head2 imap_client_buffer


B<syntax:> imap_client_buffer I<I<C<size>>>


B<default:> I<4kE<verbar>8k>


B<context:> I<mail>


B<context:> I<server>





Sets the I<C<size>> of the buffer used for reading IMAP commands.
By default, the buffer size is equal to one memory page.
This is either 4K or 8K, depending on a platform.







