=encoding utf-8


=head1 Name

drizzle-nginx-module - Upstream module for talking to MySQL and <PERSON><PERSON><PERSON> directly

I<This module is not distributed with the Nginx source.> See L<the installation instructions>.


=head1 Status

This module is already production ready.


=head1 Version

This document describes ngx_drizzle L<v0.1.11|https://github.com/openresty/drizzle-nginx-module/tags> released on 19 April 2018.


=head1 Synopsis


     http {
         ...
    
         upstream cluster {
             # simple round-robin
             drizzle_server 127.0.0.1:3306 dbname=test
                  password=some_pass user=monty protocol=mysql;
             drizzle_server 127.0.0.1:1234 dbname=test2
                  password=pass user=bob protocol=drizzle;
         }
    
         upstream backend {
             drizzle_server 127.0.0.1:3306 dbname=test
                  password=some_pass user=monty protocol=mysql;
         }
    
         server {
             location /mysql {
                 set $my_sql 'select * from cats';
                 drizzle_query $my_sql;
    
                 drizzle_pass backend;
    
                 drizzle_connect_timeout    500ms; # default 60s
                 drizzle_send_query_timeout 2s;    # default 60s
                 drizzle_recv_cols_timeout  1s;    # default 60s
                 drizzle_recv_rows_timeout  1s;    # default 60s
             }
    
             ...
    
             # for connection pool monitoring
             location /mysql-pool-status {
                 allow 127.0.0.1;
                 deny all;
    
                 drizzle_status;
             }
         }
     }




=head1 Description

This is an nginx upstream module integrating L<libdrizzle|https://launchpad.net/drizzle> into Nginx in a non-blocking and streamming way.

Essentially it provides a very efficient and flexible way for nginx internals to access MySQL, Drizzle, as well as other RDBMS's that support the Drizzle or MySQL wired protocol. Also it can serve as a direct REST interface to those RDBMS backends.

This module does not generate human-readable outputs, rather, in a binary format called Resty-DBD-Stream (RDS) designed by ourselves. You usually need other components, like L<rds-json-nginx-module|http://github.com/openresty/rds-json-nginx-module>, L<rds-csv-nginx-module|http://github.com/openresty/rds-csv-nginx-module>, or L<lua-rds-parser|http://github.com/openresty/lua-rds-parser>, to work with this module. See L<Output Format> for details.




=head2 Keepalive connection pool

This module also provides a builtin per-worker connection pool mechanism for MySQL or Drizzle TCP connections.

Here's a sample configuration:


     upstream backend {
         drizzle_server 127.0.0.1:3306 dbname=test
              password=some_pass user=monty protocol=mysql;
         drizzle_keepalive max=100 mode=single overflow=reject;
     }

For now, the connection pool uses a simple LIFO algorithm to assign idle connections in the pool. That is, most recently (successfully) used connections will be reused first the next time. And new idle connections will always replace the oldest idle connections in the pool even if the pool is already full.

See the L<drizzle_keepalive> directive for more details.




=head2 Last Insert ID

If you want to get LAST_INSERT_ID, then ngx_drizzle already returns that automatically for you when you're doing a SQL insert query. Consider the following sample C<nginx.conf> snippet:

     location /test {
         echo_location /mysql "drop table if exists foo";
         echo;
         echo_location /mysql "create table foo (id serial not null, primary key (id), val real);";
         echo;
         echo_location /mysql "insert into foo (val) values (3.1415926);";
         echo;
         echo_location /mysql "select * from foo;";
         echo;
     }
    
     location /mysql {
         drizzle_pass backend;
         drizzle_module_header off;
         drizzle_query $query_string;
         rds_json on;
     }

Then request C<GET /test> gives the following outputs:

     {"errcode":0}
     {"errcode":0}
     {"errcode":0,"insert_id":1,"affected_rows":1}
     [{"id":1,"val":3.1415926}]

You can see the C<insert_id> field (as well as the C<affected_rows> field in the 3rd JSON response.




=head1 Directives




=head2 drizzle_server

B<syntax:> I<drizzle_server E<lt>hostE<gt> user=E<lt>userE<gt> password=E<lt>passE<gt> dbname=E<lt>databaseE<gt>>

B<syntax:> I<drizzle_server E<lt>hostE<gt>:E<lt>portE<gt> user=E<lt>userE<gt> password=E<lt>passE<gt> dbname=E<lt>databaseE<gt> protocol=E<lt>protocolE<gt> charset=E<lt>charsetE<gt>>

B<default:> I<no>

B<context:> I<upstream>

Directive assigns the name and the parameters of server. For the name it is possible to use a domain name, an address, with an optional port (default: 3306). If domain name resolves to several addresses, then all are used.

The following options are supported:

B<user=>C<< <user> >>
MySQL/Drizzle user name C<< <user> >> for login.

B<password=>C<< <pass> >>
Specify mysql password C<< <pass> >>for login. If you have special characters like C<#> or spaces in your password text, then you'll have to quote the whole key-value pair with either single-quotes or double-quotes, as in


     drizzle_server 127.0.0.1:3306 user=monty "password=a b#1"
             dbname=test protocol=mysql;

B<dbname=>C<< <database> >>
Specify default MySQL database C<< <database> >> for the connection. Note that MySQL does allow referencing tables belonging to different databases by qualifying table names with database names in SQL queries.

B<protocol=>C<< <protocol> >>
Specify which wire protocol to use, C<drizzle> or C<mysql>. Default to C<drizzle>.

B<charset=>C<< <charset> >>
Explicitly specify the character set for the MySQL connections. Setting this option to a non-empty value will make this module send out a C<< set names '<charset>' >> query right after the mysql connection is established.
If the default character encoding of the MySQL connection is already what you want, you needn't set this option because it has extra runtime cost.
Here is a small example:

     drizzle_server foo.bar.com:3306 user=monty password=some_pass
                                     dbname=test protocol=mysql
                                     charset=utf8;

Please note that for the mysql server, "utf-8" is not a valid encoding name while C<utf8> is.




=head2 drizzle_keepalive

B<syntax:> I<drizzle_keepalive max=E<lt>sizeE<gt> mode=E<lt>modeE<gt>>

B<default:> I<drizzle_keepalive max=0 mode=single>

B<context:> I<upstream>

Configures the keep-alive connection pool for MySQL/Drizzle connections.

The following options are supported:

B<max=>C<< <num> >>
Specify the capacity of the connection pool for the current upstream block. The E<lt>numE<gt> value I<must> be non-zero. If set to C<0>, it effectively disables the connection pool. This option is default to C<0>.

B<mode=>C<< <mode> >>
This supports two values, C<single> and C<multi>. The C<single> mode means the pool does not distinguish various drizzle servers in the current upstream block while C<multi> means the pool will merely reuse connections which have identical server host names and ports. Note that even under C<multi>, differences between C<dbname> or C<user> parameters will be silently ignored. Default to C<single>.

B<overflow=>C<< <action> >>
This option specifies what to do when the connection pool is already full while new database connection is required. Either C<reject> or C<ignore> can be specified. In case of C<reject>, it will reject the current request, and returns the C<503 Service Unavailable> error page. For C<ignore>, this module will go on creating a new database connection.




=head2 drizzle_query

B<syntax:> I<drizzle_query E<lt>sqlE<gt>>

B<default:> I<no>

B<context:> I<http, server, location, location if>

Specify the SQL queries sent to the Drizzle/MySQL backend.

Nginx variable interpolation is supported, but you must be careful with SQL injection attacks. You can use the L<set_quote_sql_str|http://github.com/openresty/set-misc-nginx-module#set_quote_sql_str> directive, for example, to quote values for SQL interpolation:


     location /cat {
         set_unescape_uri $name $arg_name;
         set_quote_sql_str $quoted_name $name;
    
         drizzle_query "select * from cats where name = $quoted_name";
         drizzle_pass my_backend;
     }




=head2 drizzle_pass

B<syntax:> I<drizzle_pass E<lt>remoteE<gt>>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<content>

This directive specifies the Drizzle or MySQL upstream name to be queried in the current location. The C<< <remote> >> argument can be any upstream name defined with the L<drizzle_server> directive.

Nginx variables can also be interpolated into the C<< <remote> >> argument, so as to do dynamic backend routing, for example:

     upstream moon { drizzle_server ...; }
    
     server {
         location /cat {
             set $backend 'moon';
    
             drizzle_query ...;
             drizzle_pass $backend;
         }
     }




=head2 drizzle_connect_timeout

B<syntax:> I<drizzle_connect_time E<lt>timeE<gt>>

B<default:> I<drizzle_connect_time 60s>

B<context:> I<http, server, location, location if>

Specify the (total) timeout for connecting to a remote Drizzle or MySQL server.

The C<< <time> >> argument can be an integer, with an optional time unit, like C<s> (second), C<ms> (millisecond), C<m> (minute). The default time unit is C<s>, i.e., "second". The default setting is C<60s>.




=head2 drizzle_send_query_timeout

B<syntax:> I<drizzle_send_query_timeout E<lt>timeE<gt>>

B<default:> I<drizzle_send_query_timeout 60s>

B<context:> I<http, server, location, location if>

Specify the (total) timeout for sending a SQL query to a remote Drizzle or MySQL server.

The C<< <time> >> argument can be an integer, with an optional time unit, like C<s> (second), C<ms> (millisecond), C<m> (minute). The default time unit is C<s>, ie, "second". The default setting is C<60s>.




=head2 drizzle_recv_cols_timeout

B<syntax:> I<drizzle_recv_cols_timeout E<lt>timeE<gt>>

B<default:> I<drizzle_recv_cols_timeout 60s>

B<context:> I<http, server, location, location if>

Specify the (total) timeout for receiving the columns metadata of the result-set to a remote Drizzle or MySQL server.

The C<< <time> >> argument can be an integer, with an optional time unit, like C<s> (second), C<ms> (millisecond), C<m> (minute). The default time unit is C<s>, ie, "second". The default setting is C<60s>.




=head2 drizzle_recv_rows_timeout

B<syntax:> I<drizzle_recv_rows_timeout E<lt>timeE<gt>>

B<default:> I<drizzle_recv_rows_timeout 60s>

B<context:> I<http, server, location, location if>

Specify the (total) timeout for receiving the rows data of the result-set (if any) to a remote Drizzle or MySQL server.

The C<< <time> >> argument can be an integer, with an optional time unit, like C<s> (second), C<ms> (millisecond), C<m> (minute). The default time unit is C<s>, ie, "second". The default setting is C<60s>.




=head2 drizzle_buffer_size

B<syntax:> I<drizzle_buffer_size E<lt>sizeE<gt>>

B<default:> I<drizzle_buffer_size 4k/8k>

B<context:> I<http, server, location, location if>

Specify the buffer size for drizzle outputs. Default to the page size (4k/8k). The larger the buffer, the less streammy the outputing process will be.




=head2 drizzle_module_header

B<syntax:> I<drizzle_module_header on|off>

B<default:> I<drizzle_module_header on>

B<context:> I<http, server, location, location if>

Controls whether to output the drizzle header in the response. Default on.

The drizzle module header looks like this:

    X-Resty-DBD-Module: ngx_drizzle 0.1.0




=head2 drizzle_status

B<syntax:> I<drizzle_status>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<content>

When specified, the current Nginx location will output a status report for all the drizzle upstream servers in the virtual server of the current Nginx worker process.

The output looks like this:

    worker process: 15231

    upstream backend
      active connections: 0
      connection pool capacity: 10
      overflow: reject
      cached connection queue: 0
      free'd connection queue: 10
      cached connection successfully used count:
      free'd connection successfully used count: 3 0 0 0 0 0 0 0 0 0
      servers: 1
      peers: 1

    upstream backend2
      active connections: 0
      connection pool capacity: 0
      servers: 1
      peers: 1

Note that, this is I<not> the global statistics if you do have multiple Nginx worker processes configured in your C<nginx.conf>.




=head1 Variables

This module creates the following Nginx variables:




=head2 $drizzle_thread_id

This variable will be assigned a textual number of the underlying MySQL or Drizzle query thread ID when the current SQL query times out. This thread ID can be further used in a SQL kill command to cancel the timed-out query.

Here's an example:

     drizzle_connect_timeout 1s;
     drizzle_send_query_timeout 2s;
     drizzle_recv_cols_timeout 1s;
     drizzle_recv_rows_timeout 1s;
    
     location /query {
         drizzle_query 'select sleep(10)';
         drizzle_pass my_backend;
         rds_json on;
    
         more_set_headers -s 504 'X-Mysql-Tid: $drizzle_thread_id';
     }
    
     location /kill {
         drizzle_query "kill query $arg_tid";
         drizzle_pass my_backend;
         rds_json on;
     }
    
     location /main {
         content_by_lua '
             local res = ngx.location.capture("/query")
             if res.status ~= ngx.HTTP_OK then
                 local tid = res.header["X-Mysql-Tid"]
                 if tid and tid ~= "" then
                     ngx.location.capture("/kill", { args = {tid = tid} })
                 end
                 return ngx.HTTP_INTERNAL_SERVER_ERROR;
             end
             ngx.print(res.body)
         '
     }

where we make use of L<headers-more-nginx-module|http://github.com/openresty/headers-more-nginx-module>, L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>, and L<rds-json-nginx-module|http://github.com/openresty/rds-json-nginx-module> too. When the SQL query timed out, we'll explicitly cancel it immediately. One pitfall here is that you have to add these modules in this order while building Nginx:


=over


=item *

L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>

=item *

L<headers-more-nginx-module|http://github.com/openresty/headers-more-nginx-module>

=item *

L<rds-json-nginx-module|http://github.com/openresty/rds-json-nginx-module>


=back

Such that, their output filters will work in the I<reversed> order, i.e., first convert RDS to JSON, and then add our C<X-Mysql-Tid> custom header, and finally capture the whole (subrequest) response with the Lua module. You're recommended to use the L<OpenResty bundle|http://openresty.org/> though, it ensures the module building order automatically for you.




=head1 Output Format

This module generates binary query results in a format that is shared among the various Nginx database driver modules like L<ngx_postgres|http://github.com/FRiCKLE/ngx_postgres/>. This data format is named C<Resty DBD Stream> (RDS).

If you're a web app developer, you may be more interested in


=over


=item *

using L<rds-json-nginx-module|http://github.com/openresty/rds-json-nginx-module> to obtain JSON output,

=item *

using L<rds-csv-nginx-module|http://github.com/openresty/rds-csv-nginx-module> to obain Comma-Separated-Value (CSV) output,

=item *

or using L<lua-rds-parser|http://github.com/openresty/lua-rds-parser> to parse the RDS data into Lua data structures.


=back

For the HTTP response header part, the C<200 OK> status code should always be returned. The C<Content-Type> header I<must> be set to C<application/x-resty-dbd-stream>. And the driver generating this response also sets a C<X-Resty-DBD> header. For instance, this module adds the following output header:

    X-Resty-DBD-Module: drizzle 0.1.0

where C<0.1.0> is this module's own version number. This C<X-Resty-DBD-Module> header is optional though.

Below is the HTTP response body format (version 0.0.3):




=head2 RDS Header Part

The RDS Header Part consists of the following fields:

B<uint8_t>
endian type (1 means big-endian and little endian otherwise)

B<uint32_t>
format version (v1.2.3 is represented as 1002003 in decimal)

B<uint8_t>
result type (0 means normal SQL result type, fixed for now)

B<uint16_t>
standard error code

B<uint16_t>
driver-specific error code

B<uint16_t>
driver-specific error string length

B<u_char >*
driver-specific error string data

B<uint64_t>
database rows affected

B<uint64_t>
insert id (if none, 0)

B<uint16_t>
column count




=head2 RDS Body Part

When the C<column count> field in the L<RDS Header Part> is zero, then the whole RDS Body Part is omitted.

The RDS Body Part consists of two sections, L<Columns> and L<Rows>.


=head3 Columns


The columns part consists of zero or more column data. The number of columns is determined by C<column count> field in L<RDS Header Part>.

Each column consists of the following fields

B<uint16_t>
non-zero value for standard column type code and for the column list terminator and zero otherwise.

B<uint16_t>
driver-specific column type code

B<uint16_t>
column name length

B<u_char >*
column name data


=head3 Rows


The rows part consists of zero or more row data, terminated by a 8-bit zero.

Each row data consists of a L<Row Flag> and an optional L<Fields Data> part.


=head4 Row Flag


B<uint8_t>
valid row (1 means valid, and 0 means the row list terminator)


=head4 Fields Data


The Fields Data consists zero or more fields of data. The field count is predetermined by the E<lt>codeE<gt>column numberE<lt>/code) specified in L<RDS Header Part>.

B<uint32_t>
field length ((uint32_t) -1 represents NULL)

B<u_char >*
field data in textual representation), is empty (0) if field length == (uint32_t) -1




=head2 RDS buffer Limitations

On the nginx output chain link level, the following components should be put into a single C<ngx_buf_t> struct:


=over


=item *

the header


=back


=over


=item *

each column and the column list terminator


=back


=over


=item *

each row's valid flag byte and row list terminator


=back


=over


=item *

each field in each row (if any) but the field data can span multiple bufs.


=back




=head1 Status Code

If the MySQL error code in MySQL's query result is not OK, then a 500 error page is returned by this module, except for the table non-existent error, which results in the C<410 Gone> error page.




=head1 Caveats


=over


=item *

Other usptream modules like C<upstream_hash> and L<HttpUpstreamKeepaliveModule|http://wiki.nginx.org/HttpUpstreamKeepaliveModule> I<must not> be used with this module in a single upstream block.

=item *

Directives like L<server|http://nginx.org/en/docs/http/ngx_http_upstream_module.html#server> I<must not> be mixed with L<drizzle_server> either.

=item *

Upstream backends that don't use L<drizzle_server> to define server entries I<must not> be used in the L<drizzle_pass> directive.


=back




=head1 Trouble Shooting


=over


=item *

When you see the following error message in C<error.log>:

failed to connect: 15: drizzle_state_handshake_result_read:
old insecure authentication mechanism not supported in upstream, ...


=back

then you may checkout if your MySQL is too old (at least 5.x is required) or your mysql config file explicitly forces the use of old authentication method (you should remove the C<old-passwords> line from your C<my.cnf> and add the line C<secure_auth 1>).

=over


=item *

When you see the following error message in C<error.log>:

failed to connect: 23: Access denied for user 'root'@'ubuntu'
(using password: YES) while connecting to drizzle upstream, ...


=back

You should check if your MySQL account does have got TCP login access on your MySQL server side. A quick check is to use MySQL's official client to connect to your server:

         mysql --protocol=tcp -u user --password=password -h foo.bar.com dbname

Note that the C<--protocol=tcp> option is required here, or your MySQL client may use Unix Domain Socket to connect to your MySQL server.




=head1 Known Issues


=over


=item *

Calling mysql procedures are currently not supported because the underlying libdrizzle library does not support the C<CLIENT_MULTI_RESULTS> flag yet :( But we'll surely work on it.

=item *

Multiple SQL statements in a single query are not supported due to the lack of C<CLIENT_MULTI_STATEMENTS> support in the underlying libdrizzle library.

=item *

This module does not (yet) work with the C<RTSIG> event model.


=back




=head1 Installation

You're recommended to install this module as well as L<rds-json-nginx-module|http://github.com/openresty/rds-json-nginx-module> via the OpenResty bundle:

E<lt>http://openresty.orgE<gt>

The installation steps are usually as simple as C<./configure --with-http_drizzle_module && make && make install> (But you still need to install the libdrizzle library manually, see [E<lt>http://openresty.org/en/drizzle-nginx-module.html]E<gt>(http://openresty.org/en/drizzle-nginx-module.html) for detailed instructions.

Alternatively, you can compile this module with Nginx core's source by hand:


=over


=item *

You should first install libdrizzle 1.0 which is now distributed with the drizzle project and can be obtained from [E<lt>https://launchpad.net/drizzle]E<gt>(https://launchpad.net/drizzle). The latest drizzle7 release does not support building libdrizzle 1.0 separately and requires a lot of external dependencies like Boost and Protobuf which are painful to install. The last version supporting building libdrizzle 1.0 separately is C<2011.07.21>. You can download it from E<lt>http://agentzh.org/misc/nginx/drizzle7-2011.07.21.tar.gzE<gt> . Which this version of drizzle7, installation of libdrizzle 1.0 is usually as simple as

         tar xzvf drizzle7-2011.07.21.tar.gz
         cd drizzle7-2011.07.21/
         ./configure --without-server
         make libdrizzle-1.0
         make install-libdrizzle-1.0

Ensure that you have the C<python> command point to a C<python2> interpreter. It's known that on recent : Arch Linux distribution, C<python> is linked to C<python3> by default, and while running C<make libdrizzle-1.0> will yield the error

         File "config/pandora-plugin", line 185
             print "Dependency loop detected with %s" % plugin['name']
                                                      ^
         SyntaxError: invalid syntax
         make: *** [.plugin.scan] Error 1

You can fix this by pointing C<python> to C<python2>.

=item *

Download the latest version of the release tarball of this module from drizzle-nginx-module L<file list|http://github.com/openresty/drizzle-nginx-module/tags>.

=item *

Grab the nginx source code from L<nginx.org|http://nginx.org/>, for example, the version 1.13.6 (see L<nginx compatibility>), and then build the source with this module:

         wget 'http://nginx.org/download/nginx-1.13.6.tar.gz'
         tar -xzvf nginx-1.13.6.tar.gz
         cd nginx-1.13.6/
    
         # if you have installed libdrizzle to the prefix /opt/drizzle, then
         # specify the following environments:
         # export LIBDRIZZLE_INC=/opt/drizzle/include/libdrizzle-1.0
         # export LIBDRIZZLE_LIB=/opt/drizzle/lib
    
         # Here we assume you would install you nginx under /opt/nginx/.
         ./configure --prefix=/opt/nginx \
                     --add-module=/path/to/drizzle-nginx-module
    
         make -j2
         make install


=back

You usually also need L<rds-json-nginx-module|http://github.com/openresty/rds-json-nginx-module> to obtain JSON output from the binary RDS output generated by this upstream module.




=head1 Compatibility

If you're using MySQL, then MySQL C<5.0 ~ 5.5> is required. We're not sure if MySQL C<5.6+> work; reports welcome!

This module has been tested on Linux and Mac OS X. Reports on other POSIX-compliant systems will be highly appreciated.

The following versions of Nginx should work with this module:


=over


=item *

1.16.x

=item *

1.15.x    (last tested: 1.15.8)

=item *

1.14.x

=item *

1.13.x    (last tested: 1.13.6)

=item *

1.12.x

=item *

1.11.x    (last tested: 1.11.2)

=item *

1.10.x

=item *

1.9.x     (last tested: 1.9.15)

=item *

1.8.x

=item *

1.7.x     (last tested: 1.7.10)

=item *

1.6.x

=item *

1.5.x     (last tested: 1.5.8)

=item *

1.4.x     (last tested: 1.4.4)

=item *

1.3.x     (last tested: 1.3.7)

=item *

1.2.x     (last tested: 1.2.9)

=item *

1.1.x     (last tested: 1.1.5)

=item *

1.0.x     (last tested: 1.0.8)

=item *

0.8.x     (last tested: 0.8.55)

=item *

0.7.x E<gt>= 0.7.44 (last tested version is 0.7.67)


=back

Earlier versions of Nginx like C<0.6.x> and C<0.5.x> will I<not> work.

If you find that any particular version of Nginx above C<0.7.44> does not work with this module, please consider reporting a bug.




=head1 Community




=head2 English Mailing List

The L<openresty-en|https://groups.google.com/group/openresty-en> mailing list is for English speakers.




=head2 Chinese Mailing List

The L<openresty|https://groups.google.com/group/openresty> mailing list is for Chinese speakers.




=head1 Report Bugs

Please submit bug reports, wishlists, or patches by


=over


=item 1.

creating a ticket on the L<issue tracking interface|http://github.com/openresty/drizzle-nginx-module/issues> provided by GitHub,

=item 2.

or sending an email to the L<OpenResty community>.


=back




=head1 Source Repository

Available on github at L<openrestyE<sol>drizzle-nginx-module|http://github.com/openresty/drizzle-nginx-module>.




=head1 Test Suite

This module comes with a Perl-driven test suite. The L<test cases|http://github.com/openresty/drizzle-nginx-module/tree/master/t/> are
L<declarative|http://github.com/openresty/drizzle-nginx-module/blob/master/t/sanity.t> too. Thanks to the L<Test::Nginx|http://search.cpan.org/perldoc?Test::Nginx> module in the Perl world.

To run it on your side:


     $ PATH=/path/to/your/nginx-with-echo-module:$PATH prove -r t

Because a single nginx server (by default, C<localhost:1984>) is used across all the test scripts (C<.t> files), it's meaningless to run the test suite in parallel by specifying C<-jN> when invoking the C<prove> utility.




=head1 TODO


=over


=item *

add the MySQL transaction support.

=item *

add multi-statement MySQL query support.

=item *

implement the "drizzle_max_output_size" directive. When the RDS data is larger then the size specified, the module will try to terminate the output as quickly as possible but will still ensure the resulting response body is still in valid RDS format.

=item *

implement the C<drizzle_upstream_next> mechanism for failover support.

=item *

add support for multiple "drizzle_query" directives in a single location.

=item *

implement I<weighted> round-robin algorithm for the upstream server clusters.

=item *

add the C<max_idle_time> option to the L<drizzle_server> directive, so that the connection pool will automatically release idle connections for the timeout you specify.

=item *

add the C<min> option to the "drizzle_server" directive so that the connection pool will automatically create that number of connections and put them into the pool.

=item *

add Unix domain socket support in the C<drizzle_server> directive.

=item *

make the L<drizzle_query> directive reject variables that have not been processed by a L<drizzle_process> directive. This will pretect us from SQL injections. There will also be an option ("strict=no") to disable such checks.


=back




=head1 Changes

The changes of every release of this module can be obtained from the OpenResty bundle's change logs:

E<lt>http://openresty.org/#ChangesE<gt>




=head1 Authors


=over


=item *

chaoslawful (王晓哲) E<lt>chaoslawful at gmail dot comE<gt>

=item *

Yichun "agentzh" Zhang (章亦春) E<lt>agentzh at gmail dot comE<gt>, OpenResty Inc.

=item *

Piotr Sikora E<lt>piotr.sikora at frickle dot comE<gt>, Google Inc.


=back




=head1 Copyright & License

This module is licenced under the BSD license.

Copyright (C) 2009-2014, by Xiaozhe Wang (chaoslawful) E<lt><EMAIL><gt>.

Copyright (C) 2009-2018, by Yichun "agentzh" Zhang (章亦春) E<lt><EMAIL><gt>, OpenResty Inc.

Copyright (C) 2010-2014, by FRiCKLE Piotr Sikora E<lt><EMAIL><gt>, Google Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:


=over


=item *

Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.


=back


=over


=item *

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

L<rds-json-nginx-module|http://github.com/openresty/rds-json-nginx-module>

=item *

L<rds-csv-nginx-module|http://github.com/openresty/rds-csv-nginx-module>

=item *

L<lua-rds-parser|http://github.com/openresty/lua-rds-parser>

=item *

L<The OpenResty bundle|http://openresty.org>

=item *

L<DrizzleNginxModule bundled by OpenResty|http://openresty.org/#DrizzleNginxModule>

=item *

L<postgres-nginx-module|http://github.com/FRiCKLE/ngx_postgres>

=item *

L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>

=item *

The L<lua-resty-mysql|https://github.com/openresty/lua-resty-mysql> library based on the L<lua-nginx-module|http://github.com/openresty/lua-nginx-module> cosocket API.


=back

