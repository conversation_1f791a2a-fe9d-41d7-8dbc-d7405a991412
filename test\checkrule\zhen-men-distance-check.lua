local _M = {}
-- 检查真门上下左右距离50mm以内是否有其他真门

-- 加载所需模块
local xml_search_in_part = require("lib.xml-search-in-part")
local geometry_engine = require("lib.oop-rule-engine")
local check_rule_result = require("lib.check-rule-result")

-- 定义规则执行入口
function _M.dowork(root, target, context)
	local rule_result = check_rule_result.new("自定义真门距离检查", check_rule_result.LEVEL.ERROR)

	-- 存储结果
	local result = {}
	local logs = {}

	-- 1. 获取所有单元节点
	local engine_object = geometry_engine.current()

	local part_xml = root:search("/Root/Parts")
	local units_xml = xml_search_in_part.get_part_xmllist_bykvs_in_part(part_xml, "Parameters/Parameter", {
		{ key = "name",  value = "MKBQ" },
		{ key = "value", value = "3" }
	})

	if not units_xml or #units_xml == 0 then
		return rule_result:pass("没有找到单元节点")
	end

	local doors_xml = {}
	for _, unit_xml_node in ipairs(units_xml) do
		if not unit_xml_node.parentId then
			table.insert(doors_xml, unit_xml_node)
		end
	end

	if not doors_xml or #doors_xml == 0 then
		return rule_result:pass("没有找到真门节点")
	end

	if #doors_xml == 1 then
		table.insert(logs, "只有一个真门，无需检查距离")
		return rule_result:pass(logs)
	end

	for i = 1, #doors_xml do
		for j = 1, #doors_xml do
			if i == j then
				goto continue_inner_loop
			end
			local door1 = doors_xml[i]
			local door2 = doors_xml[j]
			if door1.id == door2.id then
				goto continue_inner_loop
			end
			local distance_check_results = engine_object:get_objects_distance("id", "==", door1.id,
				{ "up", "down", "left", "right" }, "<", 50, "id", "==", door2.id)
			if distance_check_results and #distance_check_results > 0 then
				for _, item in ipairs(distance_check_results) do

					table.insert(result, {
						prompt = string.format("门 %s 与门 %s %s 距离为 %s mm 需要>=50", 
							item.object, item.subObject, item.direction, item.distance),
						related_ids = {item.object, item.subObject}
					})
				end
			end
			::continue_inner_loop::
		end
	end

	table.insert(logs, string.format("检查完成，发现 %d 对真门距离小于 50mm", #result))

	return rule_result:error(result, logs)
end

return _M
