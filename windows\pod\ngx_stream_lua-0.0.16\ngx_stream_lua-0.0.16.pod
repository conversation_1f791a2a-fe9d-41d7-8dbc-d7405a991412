=encoding utf-8



=head1 Name

ngx_stream_lua_module - Embed the power of Lua into Nginx stream/TCP Servers.

This module is a core component of OpenResty. If you are using this module,
then you are essentially using OpenResty.

I<This module is not distributed with the Nginx source.> See [the installation
instructions](#installation).


=head1 Status

Production ready.


=head1 Version

This document describes ngx_stream_lua
L<v0.0.13|https://github.com/openresty/stream-lua-nginx-module/tags>, which was released
on 21 May, 2023.


=head1 Synopsis


    events {
        worker_connections 1024;
    }
    
    stream {
        # define a TCP server listening on the port 1234:
        server {
            listen 1234;
    
            content_by_lua_block {
                ngx.say("Hello, <PERSON><PERSON>!")
            }
        }
    }

Set up as an SSL TCP server:


    stream {
        server {
            listen 4343 ssl;
    
            ssl_protocols       TLSv1 TLSv1.1 TLSv1.2;
            ssl_ciphers         AES128-SHA:AES256-SHA:RC4-SHA:DES-CBC3-SHA:RC4-MD5;
            ssl_certificate     /path/to/cert.pem;
            ssl_certificate_key /path/to/cert.key;
            ssl_session_cache   shared:SSL:10m;
            ssl_session_timeout 10m;
    
            content_by_lua_block {
                local sock = assert(ngx.req.socket(true))
                local data = sock:receive()  -- read a line from downstream
                if data == "thunder!" then
                    ngx.say("flash!")  -- output data
                else
                    ngx.say("boom!")
                end
                ngx.say("the end...")
            }
        }
    }

Listening on a UNIX domain socket is also supported:


    stream {
        server {
            listen unix:/tmp/nginx.sock;
    
            content_by_lua_block {
                ngx.say("What's up?")
                ngx.flush(true)  -- flush any pending output and wait
                ngx.sleep(3)  -- sleeping for 3 sec
                ngx.say("Bye bye...")
            }
        }
    }




=head1 Description

This is a port of the
L<ngx_http_lua_module|https://github.com/openresty/lua-nginx-module#readme> to
the Nginx "stream" subsystem so as to support generic stream/TCP clients.

The available Lua APIs and Nginx directives remain the same as those of the
ngx_http_lua module.




=head2 Directives

The following directives are ported directly from ngx_http_lua. Please check
the documentation of ngx_http_lua for more details about their usage and
behavior.


=over


=item *

L<lua_load_resty_core|https://github.com/openresty/lua-nginx-module#lua_load_resty_core>

=item *

L<lua_code_cache|https://github.com/openresty/lua-nginx-module#lua_code_cache>

=item *

L<lua_regex_cache_max_entries|https://github.com/openresty/lua-nginx-module#lua_regex_cache_max_entries>

=item *

L<lua_package_path|https://github.com/openresty/lua-nginx-module#lua_package_path>

=item *

L<lua_package_cpath|https://github.com/openresty/lua-nginx-module#lua_package_cpath>

=item *

L<init_by_lua_block|https://github.com/openresty/lua-nginx-module#init_by_lua_block>

=item *

L<init_by_lua_file|https://github.com/openresty/lua-nginx-module#init_by_lua_file>

=item *

L<init_worker_by_lua_block|https://github.com/openresty/lua-nginx-module#init_worker_by_lua_block>

=item *

L<init_worker_by_lua_file|https://github.com/openresty/lua-nginx-module#init_worker_by_lua_file>

=item *

L<preread_by_lua_block>

=item *

L<preread_by_lua_file>

=item *

L<content_by_lua_block|https://github.com/openresty/lua-nginx-module#content_by_lua_block>

=item *

L<content_by_lua_file|https://github.com/openresty/lua-nginx-module#content_by_lua_file>

=item *

L<balancer_by_lua_block|https://github.com/openresty/lua-nginx-module#balancer_by_lua_block>

=item *

L<balancer_by_lua_file|https://github.com/openresty/lua-nginx-module#balancer_by_lua_file>

=item *

L<log_by_lua_block>

=item *

L<log_by_lua_file>

=item *

L<ssl_client_hello_by_lua_block|https://github.com/openresty/lua-nginx-module#ssl_client_hello_by_lua_block>

=item *

L<ssl_client_hello_by_lua_file|https://github.com/openresty/lua-nginx-module#ssl_client_hello_by_lua_file>

=item *

L<ssl_certificate_by_lua_block|https://github.com/openresty/lua-nginx-module#ssl_certificate_by_lua_block>

=item *

L<ssl_certificate_by_lua_file|https://github.com/openresty/lua-nginx-module#ssl_certificate_by_lua_file>

=item *

L<lua_shared_dict|https://github.com/openresty/lua-nginx-module#lua_shared_dict>

=item *

L<lua_socket_connect_timeout|https://github.com/openresty/lua-nginx-module#lua_socket_connect_timeout>

=item *

L<lua_socket_buffer_size|https://github.com/openresty/lua-nginx-module#lua_socket_buffer_size>

=item *

L<lua_socket_pool_size|https://github.com/openresty/lua-nginx-module#lua_socket_pool_size>

=item *

L<lua_socket_keepalive_timeout|https://github.com/openresty/lua-nginx-module#lua_socket_keepalive_timeout>

=item *

L<lua_socket_log_errors|https://github.com/openresty/lua-nginx-module#lua_socket_log_errors>

=item *

L<lua_ssl_ciphers|https://github.com/openresty/lua-nginx-module#lua_ssl_ciphers>

=item *

L<lua_ssl_crl|https://github.com/openresty/lua-nginx-module#lua_ssl_crl>

=item *

L<lua_ssl_protocols|https://github.com/openresty/lua-nginx-module#lua_ssl_protocols>

=item *

L<lua_ssl_certificate|https://github.com/openresty/lua-nginx-module#lua_ssl_certificate>

=item *

L<lua_ssl_certificate_key|https://github.com/openresty/lua-nginx-module#lua_ssl_certificate_key>

=item *

L<lua_ssl_trusted_certificate|https://github.com/openresty/lua-nginx-module#lua_ssl_trusted_certificate>

=item *

L<lua_ssl_verify_depth|https://github.com/openresty/lua-nginx-module#lua_ssl_verify_depth>

=item *

L<lua_ssl_conf_command|https://github.com/openresty/lua-nginx-module#lua_ssl_conf_command>

=item *

L<lua_check_client_abort|https://github.com/openresty/lua-nginx-module#lua_check_client_abort>

=item *

L<lua_max_pending_timers|https://github.com/openresty/lua-nginx-module#lua_max_pending_timers>

=item *

L<lua_max_running_timers|https://github.com/openresty/lua-nginx-module#lua_max_running_timers>

=item *

L<lua_sa_restart|https://github.com/openresty/lua-nginx-module#lua_sa_restart>

=item *

L<lua_add_variable>

=item *

L<lua_capture_error_log|https://github.com/openresty/lua-nginx-module#lua_capture_error_log>

=item *

L<preread_by_lua_no_postpone>


=back

The L<send_timeout|https://nginx.org/r/send_timeout> directive in the Nginx
"http" subsystem is missing in the "stream" subsystem. As such,
ngx_stream_lua_module uses the C<lua_socket_send_timeout> directive for this
purpose instead.

B<Note:> the lingering close directive that used to exist in older version of
C<stream_lua_nginx_module> has been removed and can now be simulated with the
newly added L<tcpsock:shutdown> API if necessary.




=head2 preread_by_lua_block

B<syntax:> I<preread_by_lua_block { lua-script }>

B<context:> I<stream, server>

B<phase:> I<preread>

Acts as a C<preread> phase handler and executes Lua code string specified in C<lua-script> for every connection
(or packet in datagram mode).
The Lua code may make L<API calls> and is executed as a new spawned coroutine in an independent global environment (i.e. a sandbox).

It is possible to acquire the raw request socket using L<ngx.req.socket|https://github.com/openresty/lua-nginx-module#ngxreqsocket>
and receive data from or send data to the client. However, keep in mind that calling the C<receive()> method
of the request socket will consume the data from the buffer and such consumed data will not be seen by handlers
further down the chain.

The C<preread_by_lua_block> code will always run at the end of the C<preread> processing phase unless
L<preread\_by\_lua\_no\_postpone> is turned on.

This directive was first introduced in the C<v0.0.3> release.




=head2 preread_by_lua_file

B<syntax:> I<preread_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<stream, server>

B<phase:> I<preread>

Equivalent to L<preread_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code
or LuaJIT bytecode to be executed.

Nginx variables can be used in the C<< <path-to-lua-script-file> >> string to provide flexibility. This however carries some risks and is not ordinarily recommended.

When a relative path like C<foo/bar.lua> is given, it will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option given when starting the Nginx server.

When the Lua code cache is turned on (by default), the user code is loaded once at the first connection and cached. The Nginx config must be reloaded each time the Lua source file is modified. The Lua code cache can be temporarily disabled during development by switching L<lua_code_cache> C<off> in C<nginx.conf> to avoid having to reload Nginx.

This directive was first introduced in the C<v0.0.3> release.




=head2 log_by_lua_block

B<syntax:> I<log_by_lua_block { lua-script }>

B<context:> I<stream, server>

B<phase:> I<log>

Runs the Lua source code specified as C<< <lua-script> >> during the C<log> request processing phase. This does not replace the current access logs, but runs before.

Yielding APIs such as C<ngx.req.socket>, C<ngx.socket.*>, C<ngx.sleep>, or C<ngx.say> are B<not> available in this phase.

This directive was first introduced in the C<v0.0.3> release.




=head2 log_by_lua_file

B<syntax:> I<log_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<stream, server>

B<phase:> I<log>

Equivalent to L<log_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code
or LuaJIT bytecode to be executed.

Nginx variables can be used in the C<< <path-to-lua-script-file> >> string to provide flexibility. This however carries some risks and is not ordinarily recommended.

When a relative path like C<foo/bar.lua> is given, it will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option given when starting the Nginx server.

When the Lua code cache is turned on (by default), the user code is loaded once at the first connection and cached. The Nginx config must be reloaded each time the Lua source file is modified. The Lua code cache can be temporarily disabled during development by switching L<lua_code_cache> C<off> in C<nginx.conf> to avoid having to reload Nginx.

This directive was first introduced in the C<v0.0.3> release.




=head2 lua_add_variable

B<syntax:> I<lua_add_variable $var>

B<context:> I<stream>

Add the variable C<$var> to the "stream" subsystem and makes it changeable. If C<$var> already exists,
this directive will do nothing.

By default, variables added using this directive are considered "not found" and reading them
using C<ngx.var> will return C<nil>. However, they could be re-assigned via the C<ngx.var.VARIABLE> API at any time.

This directive was first introduced in the C<v0.0.4> release.




=head2 preread_by_lua_no_postpone

B<syntax:> I<preread_by_lua_no_postpone on|off>

B<context:> I<stream>

Controls whether or not to disable postponing L<preread\_by\_lua*> directives
to run at the end of the C<preread> processing phase. By default, this directive is turned off
and the Lua code is postponed to run at the end of the C<preread> phase.

This directive was first introduced in the C<v0.0.4> release.




=head2 Nginx API for Lua

Many Lua API functions are ported from ngx_http_lua. Check out the official
manual of ngx_http_lua for more details on these Lua API functions.


=over


=item *

L<ngx.var.VARIABLE|https://github.com/openresty/lua-nginx-module#ngxvarvariable>


=back

This module fully supports the new variable subsystem inside the Nginx stream core. You may access any
L<built-in variables|https://nginx.org/en/docs/stream/ngx_stream_core_module.html#variables> provided by the stream core or
other stream modules.

=over


=item *

L<Core constants|https://github.com/openresty/lua-nginx-module#core-constants>

C<ngx.OK>, C<ngx.ERROR>, and etc.

=item *

L<Nginx log level constants|https://github.com/openresty/lua-nginx-module#nginx-log-level-constants>

C<ngx.ERR>, C<ngx.WARN>, and etc.

=item *

L<print|https://github.com/openresty/lua-nginx-module#print>

=item *

L<ngx.ctx|https://github.com/openresty/lua-nginx-module#ngxctx>

=item *

L<ngx.balancer|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/balancer.md>


=back


=over


=item *

L<ngx.req.socket|https://github.com/openresty/lua-nginx-module#ngxreqsocket>


=back

Only raw request sockets are supported, for obvious reasons. The C<raw> argument value
is ignored and the raw request socket is always returned. Unlike ngx_http_lua,
you can still call output API functions like C<ngx.say>, C<ngx.print>, and C<ngx.flush>
after acquiring the raw request socket via this function.

When the stream server is in UDP mode, reading from the downstream socket returned by the
C<ngx.req.socket> call will only return the content of a single packet. Therefore
the reading call will never block and will return C<nil, "no more data"> when all the
data from the datagram has been consumed. However, you may choose to send multiple UDP
packets back to the client using the downstream socket.

The raw TCP sockets returned by this module will contain the following extra method:




=head2 reqsock:receiveany

B<syntax:> I<data, err = reqsock:receiveany(max)>

B<context:> I<content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>>

This method is similar to L<tcpsock:receiveany|https://github.com/openresty/lua-nginx-module#tcpsockreceiveany> method

This method was introduced into C<stream-lua-nginx-module> since C<v0.0.8>.




=head2 tcpsock:shutdown

B<syntax:> I<ok, err = tcpsock:shutdown("send")>

B<context:> I<content_by_luaE<42>>

Shuts down the write part of the request socket, prevents all further writing to the client
and sends TCP FIN, while keeping the reading half open.

Currently only the C<"send"> direction is supported. Using any parameters other than "send" will return
an error.

If you called any output functions (like L<ngx.say|https://github.com/openresty/lua-nginx-module#ngxsay>)
before calling this method, consider use C<ngx.flush(true)> to make sure all busy buffers are complely
flushed before shutting down the socket. If any busy buffers were detected, this method will return C<nil>
will error message C<"socket busy writing">.

This feature is particularly useful for protocols that generate a response before actually
finishing consuming all incoming data. Normally, the kernel will send RST to the client when
L<tcpsock:close|https://github.com/openresty/lua-nginx-module#tcpsockclose> is called without
emptying the receiving buffer first. Calling this method will allow you to keep reading from
the receiving buffer and prevents RST from being sent.

You can also use this method to simulate lingering close similar to that
L<provided by the ngx_http_core_module|https://nginx.org/en/docs/http/ngx_http_core_module.html#lingering_close>
for protocols in need of such behavior. Here is an example:


    local LINGERING_TIME = 30 -- 30 seconds
    local LINGERING_TIMEOUT = 5000 -- 5 seconds
    
    local ok, err = sock:shutdown("send")
    if not ok then
        ngx.log(ngx.ERR, "failed to shutdown: ", err)
        return
    end
    
    local deadline = ngx.time() + LINGERING_TIME
    
    sock:settimeouts(nil, nil, LINGERING_TIMEOUT)
    
    repeat
        local data, _, partial = sock:receive(1024)
    until (not data and not partial) or ngx.time() >= deadline




=head2 reqsock:peek

B<syntax:> I<ok, err = reqsock:peek(size)>

B<context:> I<preread_by_luaE<42>>

Peeks into the L<preread|https://nginx.org/en/docs/stream/stream_processing.html#preread_phase>
buffer that contains downstream data sent by the client without consuming them.
That is, data returned by this API will still be forwarded upstream in later phases.

This function takes a single required argument, C<size>, which is the number of bytes to be peeked.
Repeated calls to this function always returns data from the beginning of the preread buffer.

Note that preread phase happens after the TLS handshake. If the stream server was configured with
TLS enabled, the returned data will be in clear text.

If preread buffer does not have the requested amount of data, then the current Lua thread will
be yielded until more data is available, L<`preread_buffer_size`|https://nginx.org/en/docs/stream/ngx_stream_core_module.html#preread_buffer_size>
has been exceeded, or L<`preread_timeout`|https://nginx.org/en/docs/stream/ngx_stream_core_module.html#preread_timeout>
has elapsed. Successful calls always return the requested amounts of data, that is, no partial
data will be returned.

When L<`preread_buffer_size`|https://nginx.org/en/docs/stream/ngx_stream_core_module.html#preread_buffer_size>
has been exceeded, the current stream session will be terminated with the
L<session status code|https://nginx.org/en/docs/stream/ngx_stream_core_module.html#var_status> C<400>
immediately by the stream core module, with error message C<"preread buffer full"> that will be printed to the error log.

When L<`preread_timeout`|https://nginx.org/en/docs/stream/ngx_stream_core_module.html#preread_timeout> has been exceeded,
the current stream session will be terminated with the
L<session status code|https://nginx.org/en/docs/stream/ngx_stream_core_module.html#var_status> C<200> immediately by the stream core module.

In both cases, no further processing on the session is possible (except C<log_by_lua*>). The connection will be closed by the
stream core module automatically.

Note that this API cannot be used if consumption of client data has occurred. For example, after calling
C<reqsock:receive>. If such an attempt was made, the Lua error C<"attempt to peek on a consumed socket"> will
be thrown. Consuming client data after calling this API is allowed and safe.

Here is an example of using this API:


    local sock = assert(ngx.req.socket())
    
    local data = assert(sock:peek(1)) -- peek the first 1 byte that contains the length
    data = string.byte(data)
    
    data = assert(sock:peek(data + 1)) -- peek the length + the size byte
    
    local payload = data:sub(2) -- trim the length byte to get actual payload
    
    ngx.log(ngx.INFO, "payload is: ", payload)

This API was first introduced in the C<v0.0.6> release.




=over


=item *

L<ngx.print|https://github.com/openresty/lua-nginx-module#ngxprint>

=item *

L<ngx.say|https://github.com/openresty/lua-nginx-module#ngxsay>

=item *

L<ngx.log|https://github.com/openresty/lua-nginx-module#ngxlog>

=item *

L<ngx.flush|https://github.com/openresty/lua-nginx-module#ngxflush>

This call currently ignores the C<wait> argument and always wait for all the pending
output to be completely flushed out (to the system socket send buffers).

=item *

L<ngx.exit|https://github.com/openresty/lua-nginx-module#ngxexit>

=item *

L<ngx.eof|https://github.com/openresty/lua-nginx-module#ngxeof>

=item *

L<ngx.sleep|https://github.com/openresty/lua-nginx-module#ngxsleep>

=item *

L<ngx.escape_uri|https://github.com/openresty/lua-nginx-module#ngxescape_uri>

=item *

L<ngx.unescape_uri|https://github.com/openresty/lua-nginx-module#ngxunescape_uri>

=item *

L<ngx.encode_args|https://github.com/openresty/lua-nginx-module#ngxencode_args>

=item *

L<ngx.decode_args|https://github.com/openresty/lua-nginx-module#ngxdecode_args>

=item *

L<ngx.encode_base64|https://github.com/openresty/lua-nginx-module#ngxencode_base64>

=item *

L<ngx.decode_base64|https://github.com/openresty/lua-nginx-module#ngxdecode_base64>

=item *

L<ngx.crc32_short|https://github.com/openresty/lua-nginx-module#ngxcrc32_short>

=item *

L<ngx.crc32_long|https://github.com/openresty/lua-nginx-module#ngxcrc32_long>

=item *

L<ngx.hmac_sha1|https://github.com/openresty/lua-nginx-module#ngxhmac_sha1>

=item *

L<ngx.md5|https://github.com/openresty/lua-nginx-module#ngxmd5>

=item *

L<ngx.md5_bin|https://github.com/openresty/lua-nginx-module#ngxmd5_bin>

=item *

L<ngx.sha1_bin|https://github.com/openresty/lua-nginx-module#ngxsha1_bin>

=item *

L<ngx.quote_sql_str|https://github.com/openresty/lua-nginx-module#ngxquote_sql_str>

=item *

L<ngx.today|https://github.com/openresty/lua-nginx-module#ngxtoday>

=item *

L<ngx.time|https://github.com/openresty/lua-nginx-module#ngxtime>

=item *

L<ngx.now|https://github.com/openresty/lua-nginx-module#ngxnow>

=item *

L<ngx.update_time|https://github.com/openresty/lua-nginx-module#ngxupdate_time>

=item *

L<ngx.localtime|https://github.com/openresty/lua-nginx-module#ngxlocaltime>

=item *

L<ngx.utctime|https://github.com/openresty/lua-nginx-module#ngxutctime>

=item *

L<ngx.re.match|https://github.com/openresty/lua-nginx-module#ngxrematch>

=item *

L<ngx.re.find|https://github.com/openresty/lua-nginx-module#ngxrefind>

=item *

L<ngx.re.gmatch|https://github.com/openresty/lua-nginx-module#ngxregmatch>

=item *

L<ngx.re.sub|https://github.com/openresty/lua-nginx-module#ngxresub>

=item *

L<ngx.re.gsub|https://github.com/openresty/lua-nginx-module#ngxregsub>

=item *

L<ngx.shared.DICT|https://github.com/openresty/lua-nginx-module#ngxshareddict>

=item *

L<ngx.socket.tcp|https://github.com/openresty/lua-nginx-module#ngxsockettcp>

=item *

L<ngx.socket.udp|https://github.com/openresty/lua-nginx-module#ngxsocketudp>

=item *

L<ngx.socket.connect|https://github.com/openresty/lua-nginx-module#ngxsocketconnect>

=item *

L<ngx.get_phase|https://github.com/openresty/lua-nginx-module#ngxget_phase>

=item *

L<ngx.thread.spawn|https://github.com/openresty/lua-nginx-module#ngxthreadspawn>

=item *

L<ngx.thread.wait|https://github.com/openresty/lua-nginx-module#ngxthreadwait>

=item *

L<ngx.thread.kill|https://github.com/openresty/lua-nginx-module#ngxthreadkill>

=item *

L<ngx.on_abort|https://github.com/openresty/lua-nginx-module#ngxon_abort>

=item *

L<ngx.timer.at|https://github.com/openresty/lua-nginx-module#ngxtimerat>

=item *

L<ngx.timer.running_count|https://github.com/openresty/lua-nginx-module#ngxtimerrunning_count>

=item *

L<ngx.timer.pending_count|https://github.com/openresty/lua-nginx-module#ngxtimerpending_count>

=item *

L<ngx.config.debug|https://github.com/openresty/lua-nginx-module#ngxconfigdebug>

=item *

L<ngx.config.subsystem|https://github.com/openresty/lua-nginx-module#ngxconfigsubsystem>

Always takes the Lua string value C<"stream"> in this module.

=item *

L<ngx.config.prefix|https://github.com/openresty/lua-nginx-module#ngxconfigprefix>

=item *

L<ngx.config.nginx_version|https://github.com/openresty/lua-nginx-module#ngxconfignginx_version>

=item *

L<ngx.config.nginx_configure|https://github.com/openresty/lua-nginx-module#ngxconfignginx_configure>

=item *

L<ngx.config.ngx_lua_version|https://github.com/openresty/lua-nginx-module#ngxconfigngx_lua_version>

=item *

L<ngx.worker.exiting|https://github.com/openresty/lua-nginx-module#ngxworkerexiting>

=item *

L<ngx.worker.pid|https://github.com/openresty/lua-nginx-module#ngxworkerpid>

=item *

L<ngx.worker.pids|https://github.com/openresty/lua-nginx-module#ngxworkerpids>

=item *

L<ngx.worker.count|https://github.com/openresty/lua-nginx-module#ngxworkercount>

=item *

L<ngx.worker.id|https://github.com/openresty/lua-nginx-module#ngxworkerid>

=item *

L<coroutine.create|https://github.com/openresty/lua-nginx-module#coroutinecreate>

=item *

L<coroutine.resume|https://github.com/openresty/lua-nginx-module#coroutineresume>

=item *

L<coroutine.yield|https://github.com/openresty/lua-nginx-module#coroutineyield>

=item *

L<coroutine.wrap|https://github.com/openresty/lua-nginx-module#coroutinewrap>

=item *

L<coroutine.running|https://github.com/openresty/lua-nginx-module#coroutinerunning>

=item *

L<coroutine.status|https://github.com/openresty/lua-nginx-module#coroutinestatus>


=back




=head1 TODO


=over


=item *

Add new directives C<access_by_lua_block> and C<access_by_lua_file>.

=item *

Add C<lua_postpone_output> to emulate the L<postpone_output|https://nginx.org/r/postpone_output> directive.


=back




=head1 Nginx Compatibility

The latest version of this module is compatible with the following versions of Nginx:


=over


=item *

1.25.x (last tested: 1.25.1)

=item *

1.21.x (last tested: 1.21.4)

=item *

1.19.x (last tested: 1.19.3)

=item *

1.17.x (last tested: 1.17.8)

=item *

1.15.x (last tested: 1.15.8)

=item *

1.13.x (last tested: 1.13.6)


=back

Nginx cores older than 1.13.6 (exclusive) are I<not> tested and may or may not
work. Use at your own risk!




=head1 Installation

It is I<highly> recommended to use L<OpenResty releases|https://openresty.org>
which bundle Nginx, ngx_http_lua, ngx_stream_lua, (this module), LuaJIT, as
well as other powerful companion Nginx modules and Lua libraries.

It is discouraged to build this module with Nginx yourself since it is tricky
to set up exactly right.

Note that Nginx, LuaJIT, and OpenSSL official releases have various limitations
and long standing bugs that can cause some of this module's features to be
disabled, not work properly, or run slower. Official OpenResty releases are
recommended because they bundle L<OpenResty's optimized LuaJIT 2.1 fork|https://github.com/openresty/luajit2> and
[Nginx/OpenSSL
patches](https://github.com/openresty/openresty/tree/master/patches).

Alternatively, ngx_stream_lua can be manually compiled into Nginx:


=over


=item 1.

LuaJIT can be downloaded from the L<latest release of OpenResty's LuaJIT fork|https://github.com/openresty/luajit2/releases>. The official LuaJIT 2.x releases are also supported, although performance will be significantly lower for reasons elaborated above

=item 2.

Download the latest version of ngx_stream_lua L<HERE|https://github.com/openresty/stream-lua-nginx-module/tags>

=item 3.

Download the latest supported version of Nginx L<HERE|https://nginx.org/> (See L<Nginx Compatibility>)


=back

Build the source with this module:


    wget 'https://nginx.org/download/nginx-1.13.6.tar.gz'
    tar -xzvf nginx-1.13.6.tar.gz
    cd nginx-1.13.6/
    
    # tell nginx's build system where to find LuaJIT 2.1:
    export LUAJIT_LIB=/path/to/luajit/lib
    export LUAJIT_INC=/path/to/luajit/include/luajit-2.1
    
    # Here we assume Nginx is to be installed under /opt/nginx/.
    ./configure --prefix=/opt/nginx \
            --with-ld-opt="-Wl,-rpath,/path/to/luajit-or-lua/lib" \
            --with-stream \
            --with-stream_ssl_module \
            --add-module=/path/to/stream-lua-nginx-module
    
    # Build and install
    make -j4
    make install

You may use C<--without-http> if you do not wish to use this module with the
"http" subsystem. ngx_stream_lua will work perfectly fine without the "http"
subsystem.




=head1 Community




=head2 English Mailing List

The L<openresty-en|https://groups.google.com/group/openresty-en> mailing list is for English speakers.




=head2 Chinese Mailing List

The L<openresty|https://groups.google.com/group/openresty> mailing list is for Chinese speakers.




=head1 Code Repository

The code repository of this project is hosted on GitHub at
L<openrestyE<sol>stream-lua-nginx-module|https://github.com/openresty/stream-lua-nginx-module>.




=head1 Bugs and Patches

Please submit bug reports, wishlists, or patches by


=over


=item 1.

creating a ticket on the L<GitHub Issue Tracker|https://github.com/openresty/stream-lua-nginx-module/issues>,

=item 2.

or posting to the L<OpenResty community>.


=back




=head1 Acknowledgments

We appreciate L<Kong Inc.|https://konghq.com/> for kindly sponsoring L<OpenResty Inc.|https://openresty.com/> on the following
work:

=over


=item *

Compatibility with Nginx core 1.13.3.

=item *

Development of L<meta-lua-nginx-module|https://github.com/openresty/meta-lua-nginx-module>
to make code sharing between this module and L<lua-nginx-module|https://github.com/openresty/lua-nginx-module> possible.

=item *

C<balancer_by_lua_*>, C<preread_by_lua_*>, C<log_by_lua_*> and C<ssl_certby_lua*> phases support.

=item *

L<`reqsock:peek`> API support.


=back




=head1 Copyright and License

This module is licensed under the BSD license.

Copyright (C) 2009-2019, by Yichun "agentzh" Zhang (章亦春) E<lt><EMAIL><gt>, OpenResty Inc.

Copyright (C) 2009-2016, by Xiaozhe Wang (chaoslawful) E<lt><EMAIL><gt>.

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:


=over


=item *

Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.


=back


=over


=item *

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

L<ngx_http_lua_module|https://github.com/openresty/lua-nginx-module>

=item *

L<ngx_stream_echo_module|https://github.com/openresty/stream-echo-nginx-module>

=item *

L<OpenResty|https://openresty.org/>


=back



