=encoding utf-8

=head1 NAME

ngx_stream_ssl_preread_module - Module ngx_stream_ssl_preread_module




=head1



The C<ngx_stream_ssl_preread_module> module (1.11.5) allows
extracting information from the
L<ClientHello|https://datatracker.ietf.org/doc/html/rfc5246#section-7.4.1.2>
message without terminating SSLE<sol>TLS,
for example, the server name requested through
L<SNI|https://datatracker.ietf.org/doc/html/rfc6066#section-3>
or protocols advertised in
L<ALPN|https://datatracker.ietf.org/doc/html/rfc7301>.
This module is not built by default, it should be enabled with the
C<--with-stream_ssl_preread_module>
configuration parameter.




=head1 Example Configuration



Selecting an upstream based on server name:

    
    map $ssl_preread_server_name $name {
        backend.example.com      backend;
        default                  backend2;
    }
    
    upstream backend {
        server 192.168.0.1:12345;
        server 192.168.0.2:12345;
    }
    
    upstream backend2 {
        server 192.168.0.3:12345;
        server 192.168.0.4:12345;
    }
    
    server {
        listen      12346;
        proxy_pass  $name;
        ssl_preread on;
    }







Selecting an upstream based on protocol:

    
    map $ssl_preread_alpn_protocols $proxy {
        ~\bh2\b           127.0.0.1:8001;
        ~\bhttp/1.1\b     127.0.0.1:8002;
        ~\bxmpp-client\b  127.0.0.1:8003;
    }
    
    server {
        listen      9000;
        proxy_pass  $proxy;
        ssl_preread on;
    }







Selecting an upstream based on SSL protocol version:

    
    map $ssl_preread_protocol $upstream {
        ""        ssh.example.com:22;
        "TLSv1.2" new.example.com:443;
        default   tls.example.com:443;
    }
    
    # ssh and https on the same port
    server {
        listen      192.168.0.1:443;
        proxy_pass  $upstream;
        ssl_preread on;
    }






=head1 Directives

=head2 ssl_preread


B<syntax:> ssl_preread I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<stream>


B<context:> I<server>





Enables extracting information from the ClientHello message at
the L<preread|stream_processing> phase.







=head1 Embedded Variables




=over



=item C<$ssl_preread_protocol>




the highest SSL protocol version supported by the client (1.15.2)



=item C<$ssl_preread_server_name>




server name requested through SNI



=item C<$ssl_preread_alpn_protocols>




list of protocols advertised by the client through ALPN (1.13.10).
The values are separated by commas.




=back






