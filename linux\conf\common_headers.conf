#允许跨域访问
add_header 'Access-Control-Allow-Origin' '*';
add_header 'Access-Control-Allow-Credentials' 'true';
add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,syscode,magiccube-app-id';
#使用本地时间
autoindex_localtime on;
client_body_buffer_size 20M;
client_max_body_size  50M;
default_type application/json;
