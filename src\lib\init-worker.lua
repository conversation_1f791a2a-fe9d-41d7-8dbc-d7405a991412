local sync_manager = require "lib.lua-sync-manager"

local _M = {}

-- 定时器回调函数
local function sync_timer_callback(premature)
    if premature then
        ngx.log(ngx.INFO, "定时器被提前终止")
        return
    end

    local ok, result = sync_manager.sync_all_scripts()
    if not ok then
        ngx.log(ngx.ERR, "同步脚本失败: ", result)
    else
        ngx.log(ngx.INFO, result.summary)
    end

    -- 重新创建定时器  暂定只执行一次
    -- local ok, err = ngx.timer.at(3600, sync_timer_callback)  -- 1小时间隔
    -- if not ok then
    --     ngx.log(ngx.ERR, "创建定时器失败: ", err)
    -- end
end

-- 初始化函数
function _M.init()
    ngx.log(ngx.INFO, "init_worker 开始初始化")
    
    -- 立即执行一次同步
    local ok, err = ngx.timer.at(0, sync_timer_callback)
    if not ok then
        ngx.log(ngx.ERR, "创建初始定时器失败: ", err)
    end
end

-- 手动触发同步的API
function _M.manual_sync()
    return sync_manager.sync_all_scripts()
end

return _M 