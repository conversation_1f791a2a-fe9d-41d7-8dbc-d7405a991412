FROM openresty/openresty:1.27.1.2-0-rocky
#FROM openresty/openresty:1.27.1.2-0-centos-rpm
# 指定容器内部的工作目录
WORKDIR /usr/local/openresty

# 复制 zstd 库到容器
COPY linux/libzstd.so.1.5.7 /usr/local/lib/libzstd.so
# 复制 xmlua 相关动态库到容器
COPY linux/lua-utf8.so /usr/local/lib/liblua-utf8.so
#COPY linux/libxml2.so.2.9.1 /usr/local/lib/libxml2.so
#COPY linux/libz.so.1.2.7 /usr/local/lib/libz.so.1
#COPY linux/libc-2.28.so /usr/local/lib/libc.so.6
# 复制规则引擎动态库到容器
COPY linux/libRuleEngineCore.so /usr/local/openresty/lualib/libRuleEngineCore.so
COPY 'linux/libstdc++.so.6' '/usr/local/lib/libstdc++.so.6'
COPY linux/ld-linux-x86-64.so.2 /usr/local/lib/ld-linux-x86-64.so.2
# 复制启动脚本（包括 DNS 解析替换逻辑）到容器
COPY linux/start.sh /start.sh
RUN chmod +x /start.sh
# 复制测试用文件
COPY linux/xml.xml /usr/local/xml.xml
# 复制html页面
COPY windows/html/rule-debugger.html /usr/local/openresty/nginx/html/rule-debugger.html
COPY windows/html/new-page.html /usr/local/openresty/nginx/html/new-page.html

# 复制 nginx 配置文件到容器
COPY linux/conf/common_headers.conf /usr/local/openresty/nginx/conf/common_headers.conf
COPY linux/conf/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf

# xmlua 工具包
COPY commons/lualib /usr/local/openresty/lualib/
COPY commons/resty/ /usr/local/openresty/lualib/resty/

# 创建 lua_scripts/dynamic 目录，这是业务脚本目录（变化频繁）以及动态脚本目录
RUN mkdir -p /usr/local/openresty/nginx/lua_scripts/dynamic && chown -R nobody:nobody /usr/local/openresty/nginx/lua_scripts/dynamic

# 创建 lua_scripts/lib 目录，这是业务脚本依赖的工具库
COPY src/ /usr/local/openresty/lua_scripts/

# 复制 http 接口处理脚本到容器
COPY src/controller/ /usr/local/openresty/nginx/lua_scripts/
# 设置 LD_LIBRARY_PATH 环境变量
ENV LD_LIBRARY_PATH=/usr/local/lib/:\$LD_LIBRARY_PATH

EXPOSE 80
#CMD ["/usr/local/openresty/bin/openresty", "-g", "daemon off;"]
CMD ["/start.sh"]
