local _M = {}

-- 判断字符串是否为 nil
function _M.is_nil(str)
    return str == nil
end

-- 判断字符串是否为空（nil 或空串）
function _M.is_empty(str)
    return str == nil or str == ""
end

-- 判断字符串是否为空白（nil、空串或只包含空白字符）
function _M.is_blank(str)
    if str == nil or str == "" then
        return true
    end
    -- 使用模式匹配检查是否只包含空白字符
    return string.match(str, "^%s*$") ~= nil
end

-- 判断字符串是否不为空
function _M.is_not_empty(str)
    return not _M.is_empty(str)
end

-- 判断字符串是否不为空白
function _M.is_not_blank(str)
    return not _M.is_blank(str)
end

-- 去除字符串两端的空白字符
function _M.trim(str)
    if str == nil then
        return nil
    end
    return string.match(str, "^%s*(.-)%s*$")
end

-- 去除字符串左侧的空白字符
function _M.trim_left(str)
    if str == nil then
        return nil
    end
    return string.match(str, "^%s*(.+)$") or ""
end

-- 去除字符串右侧的空白字符
function _M.trim_right(str)
    if str == nil then
        return nil
    end
    return string.match(str, "^(.-)%s*$") or ""
end

-- 将字符串转换为大写
function _M.to_upper(str)
    if str == nil then
        return nil
    end
    return string.upper(str)
end

-- 将字符串转换为小写
function _M.to_lower(str)
    if str == nil then
        return nil
    end
    return string.lower(str)
end

return _M