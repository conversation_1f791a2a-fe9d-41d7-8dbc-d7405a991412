-- 不见光门板 门档数据检查 异常检测
-- 1、检查平板门（prodCatId="498"）的coverType是否为0，是则继续往下否则停止；
-- 2、获取门往内方向（排除门子级的素材），如果40以内都没有其他板件（任意是Part节点输出的内容prodCatId="713"），
-- 3、有就判断门板所在柜子下是否有一个门档的Part节点，没有则报错。

local _M = {}

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local geometry_engine = require("lib.oop-rule-engine")
local check_rule_result = require("lib.check-rule-result")

-- 定义规则执行入口
function _M.dowork(root, target, context)
	local rule_result = check_rule_result.new("门档检查", check_rule_result.LEVEL.INFO)

	-- 存储结果
	local result = {}
	local logs = {}
    
	-- 1. 获取所有门板节点
	local engine_object = geometry_engine.current()

	-- 过滤掉真门的情况
	local xml_parts_p = root:search("/Root/Parts/Part")
	local doors_xml = xml_search.get_part_by_mkbq(xml_parts_p, 3)
    
	if not doors_xml or #doors_xml == 0 then
		return rule_result:pass("没有找到任何门板")
	end

	-- 2. 获取所有不见光门板
	local un_cover_doors = {}
	for _, door_xml in ipairs(doors_xml) do
		local cover_type = xml_search.get_value_in_parameter(door_xml, "coverType")
		if tonumber(cover_type) == 0 then
			table.insert(un_cover_doors, door_xml)
		end
	end

	if not un_cover_doors or #un_cover_doors == 0 then
		return rule_result:pass("没有找到任何不见光的门板")
	end

	-- 3、获取门往内方向（排除门子级的素材），如果40以内都没有其他板件（任意是Part节点输出的内容prodCatId="713"）
	-- 获取40mm 内是否有其他板件
	local need_men_dang = {}
	for _, door in ipairs(un_cover_doors) do
		local door_id = door.id
		local door_name = door:get_attribute("name")
		local relation = engine_object:get_objects_distance("id", "==", door_id, {"back"}, "<", 40, "prodCatId", "==", "713")
		if not relation or #relation == 0 then
			-- 如果没有 则需要输出门档

			table.insert(need_men_dang, {
				prompt = string.format("四周入柱的 %s 需要有门挡", door_name),
				related_ids = {door_id}
			})
		end
	end
    
	table.insert(logs, string.format("已收集 %d 个内嵌门，其中需要安装门档的有 %d 个", #un_cover_doors, #need_men_dang))

	return rule_result:error(need_men_dang, logs)
end

return _M
