=encoding utf-8

=head1 NAME

ngx_stream_proxy_module - Module ngx_stream_proxy_module




=head1



The C<ngx_stream_proxy_module> module (1.9.0) allows proxying
data streams over TCP, UDP (1.9.13), and UNIX-domain sockets.




=head1 Example Configuration




    
    server {
        listen 127.0.0.1:12345;
        proxy_pass 127.0.0.1:8080;
    }
    
    server {
        listen 12345;
        proxy_connect_timeout 1s;
        proxy_timeout 1m;
        proxy_pass example.com:12345;
    }
    
    server {
        listen 53 udp reuseport;
        proxy_timeout 20s;
        proxy_pass dns.example.com:53;
    }
    
    server {
        listen [::1]:12345;
        proxy_pass unix:/tmp/stream.socket;
    }






=head1 Directives

=head2 proxy_bind


B<syntax:> proxy_bind I<
    I<C<address>>
    [C<transparent>] E<verbar>
    C<off>>



B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.9.2.





Makes outgoing connections to a proxied server originate
from the specified local IP I<C<address>>.
Parameter value can contain variables (1.11.2).
The special value C<off> cancels the effect
of the C<proxy_bind> directive
inherited from the previous configuration level, which allows the
system to auto-assign the local IP address.





The C<transparent> parameter (1.11.0) allows
outgoing connections to a proxied server originate
from a non-local IP address,
for example, from a real IP address of a client:

    
    proxy_bind $remote_addr transparent;


In order for this parameter to work,
it is usually necessary to run nginx worker processes with the
L<superuser|ngx_core_module> privileges.
On Linux it is not required (1.13.8) as if
the C<transparent> parameter is specified, worker processes
inherit the C<CAP_NET_RAW> capability from the master process.
It is also necessary to configure kernel routing table
to intercept network traffic from the proxied server.







=head2 proxy_buffer_size


B<syntax:> proxy_buffer_size I<I<C<size>>>


B<default:> I<16k>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.9.4.





Sets the I<C<size>> of the buffer used for reading data
from the proxied server.
Also sets the I<C<size>> of the buffer used for reading data
from the client.







=head2 proxy_connect_timeout


B<syntax:> proxy_connect_timeout I<I<C<time>>>


B<default:> I<60s>


B<context:> I<stream>


B<context:> I<server>





Defines a timeout for establishing a connection with a proxied server.







=head2 proxy_download_rate


B<syntax:> proxy_download_rate I<I<C<rate>>>


B<default:> I<0>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.9.3.





Limits the speed of reading the data from the proxied server.
The I<C<rate>> is specified in bytes per second.
The zero value disables rate limiting.
The limit is set per a connection, so if nginx simultaneously opens
two connections to the proxied server,
the overall rate will be twice as much as the specified limit.





Parameter value can contain variables (1.17.0).
It may be useful in cases where rate should be limited
depending on a certain condition:

    
    map $slow $rate {
        1     4k;
        2     8k;
    }
    
    proxy_download_rate $rate;









=head2 proxy_half_close


B<syntax:> proxy_half_close I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.21.4.





Enables or disables closing
each direction of a TCP connection independently (“TCP half-close”).
If enabled, proxying over TCP will be kept
until both sides close the connection.







=head2 proxy_next_upstream


B<syntax:> proxy_next_upstream I<C<on> E<verbar> C<off>>


B<default:> I<on>


B<context:> I<stream>


B<context:> I<server>





When a connection to the proxied server cannot be established, determines
whether a client connection will be passed to the next server.





Passing a connection to the next server can be limited by
the number of tries
and by time.







=head2 proxy_next_upstream_timeout


B<syntax:> proxy_next_upstream_timeout I<I<C<time>>>


B<default:> I<0>


B<context:> I<stream>


B<context:> I<server>





Limits the time allowed to pass a connection to the
next server.
The C<0> value turns off this limitation.







=head2 proxy_next_upstream_tries


B<syntax:> proxy_next_upstream_tries I<I<C<number>>>


B<default:> I<0>


B<context:> I<stream>


B<context:> I<server>





Limits the number of possible tries for passing a connection to the
next server.
The C<0> value turns off this limitation.







=head2 proxy_pass


B<syntax:> proxy_pass I<I<C<address>>>



B<context:> I<server>





Sets the address of a proxied server.
The address can be specified as a domain name or IP address,
and a port:

    
    proxy_pass localhost:12345;


or as a UNIX-domain socket path:

    
    proxy_pass unix:/tmp/stream.socket;







If a domain name resolves to several addresses, all of them will be
used in a round-robin fashion.
In addition, an address can be specified as a
L<server group|ngx_stream_upstream_module>.





The address can also be specified using variables (1.11.3):

    
    proxy_pass $upstream;


In this case, the server name is searched among the described
L<server groups|ngx_stream_upstream_module>,
and, if not found, is determined using a
L<ngx_stream_core_module>.







=head2 proxy_protocol


B<syntax:> proxy_protocol I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.9.2.





Enables the
L<PROXY
protocol|http://www.haproxy.org/download/1.8/doc/proxy-protocol.txt> for connections to a proxied server.







=head2 proxy_requests


B<syntax:> proxy_requests I<I<C<number>>>


B<default:> I<0>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.15.7.





Sets the number of client datagrams at which
binding between a client and existing UDP stream session is dropped.
After receiving the specified number of datagrams, next datagram
from the same client starts a new session.
The session terminates when all client datagrams are transmitted
to a proxied server and the expected number of
responses is received,
or when it reaches a timeout.







=head2 proxy_responses


B<syntax:> proxy_responses I<I<C<number>>>



B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.9.13.





Sets the number of datagrams expected from the proxied server
in response to a client datagram
if the L<UDP|ngx_stream_core_module>
protocol is used.
The number serves as a hint for session termination.
By default, the number of datagrams is not limited.





If zero value is specified, no response is expected.
However, if a response is received and the
session is still not finished, the response will be handled.







=head2 proxy_session_drop


B<syntax:> proxy_session_drop I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.15.8.





Enables terminating all sessions to a proxied server
after it was removed from the group or marked as permanently unavailable.
This can occur because of
L<re-resolve|ngx_stream_core_module>
or with the API
L<C<DELETE>|ngx_http_api_module>
command.
A server can be marked as permanently unavailable if it is considered
L<unhealthy|ngx_stream_upstream_hc_module>
or with the API
L<C<PATCH>|ngx_http_api_module>
command.
Each session is terminated when the next
read or write event is processed for the client or proxied server.






B<NOTE>

This directive is available as part of our
commercial subscription.








=head2 proxy_socket_keepalive


B<syntax:> proxy_socket_keepalive I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.15.6.





Configures the “TCP keepalive” behavior
for outgoing connections to a proxied server.
By default, the operating system’s settings are in effect for the socket.
If the directive is set to the value “C<on>”, the
C<SO_KEEPALIVE> socket option is turned on for the socket.







=head2 proxy_ssl


B<syntax:> proxy_ssl I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<stream>


B<context:> I<server>





Enables the SSLE<sol>TLS protocol for connections to a proxied server.







=head2 proxy_ssl_certificate


B<syntax:> proxy_ssl_certificate I<I<C<file>>>



B<context:> I<stream>


B<context:> I<server>





Specifies a I<C<file>> with the certificate in the PEM format
used for authentication to a proxied server.





Since version 1.21.0, variables can be used in the I<C<file>> name.







=head2 proxy_ssl_certificate_cache


B<syntax:> proxy_ssl_certificate_cache I<C<off>>


B<syntax:> proxy_ssl_certificate_cache I<
    C<max>=I<C<N>>
    [C<inactive>=I<C<time>>]
    [C<valid>=I<C<time>>]>


B<default:> I<off>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.27.4.





Defines a cache that stores
SSL certificates and
secret keys
specified with variables.





The directive has the following parameters:

=over



=item 
C<max>





sets the maximum number of elements in the cache;
on cache overflow the least recently used (LRU) elements are removed;



=item 
C<inactive>





defines a time after which an element is removed from the cache
if it has not been accessed during this time;
by default, it is 10 seconds;



=item 
C<valid>





defines a time during which
an element in the cache is considered valid
and can be reused;
by default, it is 60 seconds.
Certificates that exceed this time will be reloaded or revalidated;



=item 
C<off>





disables the cache.




=back







Example:

    
    proxy_ssl_certificate       $proxy_ssl_server_name.crt;
    proxy_ssl_certificate_key   $proxy_ssl_server_name.key;
    proxy_ssl_certificate_cache max=1000 inactive=20s valid=1m;









=head2 proxy_ssl_certificate_key


B<syntax:> proxy_ssl_certificate_key I<I<C<file>>>



B<context:> I<stream>


B<context:> I<server>





Specifies a I<C<file>> with the secret key in the PEM format
used for authentication to a proxied server.





Since version 1.21.0, variables can be used in the I<C<file>> name.







=head2 proxy_ssl_ciphers


B<syntax:> proxy_ssl_ciphers I<I<C<ciphers>>>


B<default:> I<DEFAULT>


B<context:> I<stream>


B<context:> I<server>





Specifies the enabled ciphers for connections to a proxied server.
The ciphers are specified in the format understood by the OpenSSL library.





The full list can be viewed using the
“C<openssl ciphers>” command.







=head2 proxy_ssl_conf_command


B<syntax:> proxy_ssl_conf_command I<I<C<name>> I<C<value>>>



B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.19.4.





Sets arbitrary OpenSSL configuration
L<commands|https://www.openssl.org/docs/man1.1.1/man3/SSL_CONF_cmd.html>
when establishing a connection with the proxied server.

B<NOTE>

The directive is supported when using OpenSSL 1.0.2 or higher.






Several C<proxy_ssl_conf_command> directives
can be specified on the same level.
These directives are inherited from the previous configuration level
if and only if there are
no C<proxy_ssl_conf_command> directives
defined on the current level.






B<NOTE>

Note that configuring OpenSSL directly
might result in unexpected behavior.








=head2 proxy_ssl_crl


B<syntax:> proxy_ssl_crl I<I<C<file>>>



B<context:> I<stream>


B<context:> I<server>





Specifies a I<C<file>> with revoked certificates (CRL)
in the PEM format used to verify
the certificate of the proxied server.







=head2 proxy_ssl_key_log


B<syntax:> proxy_ssl_key_log I<path>



B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.27.2.





Enables logging of proxied server connection SSL keys
and specifies the path to the key log file.
Keys are logged in the
L<SSLKEYLOGFILE|https://datatracker.ietf.org/doc/html/draft-ietf-tls-keylogfile>
format compatible with Wireshark.






B<NOTE>

This directive is available as part of our
commercial subscription.








=head2 proxy_ssl_name


B<syntax:> proxy_ssl_name I<I<C<name>>>


B<default:> I<host from proxy_pass>


B<context:> I<stream>


B<context:> I<server>





Allows overriding the server name used to
verify
the certificate of the proxied server and to be
passed through SNI
when establishing a connection with the proxied server.
The server name can also be specified using variables (1.11.3).





By default, the host part of the L</proxy_pass> address is used.







=head2 proxy_ssl_password_file


B<syntax:> proxy_ssl_password_file I<I<C<file>>>



B<context:> I<stream>


B<context:> I<server>





Specifies a I<C<file>> with passphrases for
secret keys
where each passphrase is specified on a separate line.
Passphrases are tried in turn when loading the key.







=head2 proxy_ssl_protocols


B<syntax:> proxy_ssl_protocols I<
    [C<SSLv2>]
    [C<SSLv3>]
    [C<TLSv1>]
    [C<TLSv1.1>]
    [C<TLSv1.2>]
    [C<TLSv1.3>]>


B<default:> I<TLSv1.2 TLSv1.3>


B<context:> I<stream>


B<context:> I<server>





Enables the specified protocols for connections to a proxied server.






B<NOTE>

The C<TLSv1.3> parameter is used by default
since 1.23.4.








=head2 proxy_ssl_server_name


B<syntax:> proxy_ssl_server_name I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<stream>


B<context:> I<server>





Enables or disables passing of the server name through
L<TLS
Server Name Indication extension|http://en.wikipedia.org/wiki/Server_Name_Indication> (SNI, RFC 6066)
when establishing a connection with the proxied server.







=head2 proxy_ssl_session_reuse


B<syntax:> proxy_ssl_session_reuse I<C<on> E<verbar> C<off>>


B<default:> I<on>


B<context:> I<stream>


B<context:> I<server>





Determines whether SSL sessions can be reused when working with
the proxied server.
If the errors
“C<digest check failed>”
appear in the logs, try disabling session reuse.







=head2 proxy_ssl_trusted_certificate


B<syntax:> proxy_ssl_trusted_certificate I<I<C<file>>>



B<context:> I<stream>


B<context:> I<server>





Specifies a I<C<file>> with trusted CA certificates in the PEM format
used to verify
the certificate of the proxied server.







=head2 proxy_ssl_verify


B<syntax:> proxy_ssl_verify I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<stream>


B<context:> I<server>





Enables or disables verification of the proxied server certificate.







=head2 proxy_ssl_verify_depth


B<syntax:> proxy_ssl_verify_depth I<I<C<number>>>


B<default:> I<1>


B<context:> I<stream>


B<context:> I<server>





Sets the verification depth in the proxied server certificates chain.







=head2 proxy_timeout


B<syntax:> proxy_timeout I<I<C<timeout>>>


B<default:> I<10m>


B<context:> I<stream>


B<context:> I<server>





Sets the I<C<timeout>> between two successive
read or write operations on client or proxied server connections.
If no data is transmitted within this time, the connection is closed.







=head2 proxy_upload_rate


B<syntax:> proxy_upload_rate I<I<C<rate>>>


B<default:> I<0>


B<context:> I<stream>


B<context:> I<server>



This directive appeared in version 1.9.3.





Limits the speed of reading the data from the client.
The I<C<rate>> is specified in bytes per second.
The zero value disables rate limiting.
The limit is set per a connection, so if the client simultaneously opens
two connections,
the overall rate will be twice as much as the specified limit.





Parameter value can contain variables (1.17.0).
It may be useful in cases where rate should be limited
depending on a certain condition:

    
    map $slow $rate {
        1     4k;
        2     8k;
    }
    
    proxy_upload_rate $rate;









