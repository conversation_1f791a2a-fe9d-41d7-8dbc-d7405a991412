local _M = {}

--判断抽屉（prodCatId="1043"）的参数DGLX的取值是不是[773,776]，如果不是，则执行下发判断
--使用内空对比函数，找到模板素材距离后方的距离是不是小于15 是就错误

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local geometry = require("lib.oop-rule-engine")
local check_rule_result = require("lib.check-rule-result")

-- 定义规则执行入口
function _M.dowork(root, target, context)
	local rule_result = check_rule_result.new("抽屉距后值检查", check_rule_result.LEVEL.INFO)

	-- 存储结果
	local result = {}
    local logs = {}
    
    -- 1. 获取所有抽屉节点
    local engine_object = geometry.current()

    local drawers_xml= root:search("//Part[@prodCatId='1043']")

    if not drawers_xml or #drawers_xml == 0 then
        return rule_result:pass("未找到任何抽屉节点")
    end

    -- 遍历抽屉节点
    -- table.insert(logs, "找到抽屉节点数量: " .. #drawers_xml)
    for _, drawer in ipairs(drawers_xml) do
        -- 获取 DGLX 参数值
        local parameters = drawer:search("Parameters/Parameter[@name='DGLX']")
        local dglx = parameters and parameters[1] and tonumber(parameters[1]:get_attribute("value"))
        if dglx and dglx >= 773 and dglx <= 776 then
            local drawer_id = drawer:get_attribute("id")
            local drawer_name = drawer:get_attribute("name")
			-- table.insert(logs, "DGLX[773-776]抽屉id: " .. drawer_id)
            -- 获取当前抽屉抽芯的内空
            local core_parts = drawer:search("Part")
            for _, part in ipairs(core_parts) do
                -- 获取 Parameters 节点
                local parameters = part:search("Parameters/Parameter[@name='BJBQ']")
                local bjbq = parameters and parameters[1] and parameters[1]:get_attribute("value")

                if bjbq == "11" then
                    -- 找到抽芯节点，获取抽芯id
                    local part_id = part:get_attribute("id")
                    local internalSpace = engine_object:get_internal_space(part_id)
                    local internalSpaceDistace = engine_object:get_internal_space_distance(part_id, internalSpace)
                    local back_distance = internalSpaceDistace.backDistance
                    -- table.insert(logs, "抽芯id: " .. part_id)
                    -- table.insert(logs, {
					-- 	internalSpace = internalSpace,	
					-- 	internalSpaceDistace = internalSpaceDistace,
					-- } )
                    if back_distance and back_distance < 15 then
                        -- 如果内空距离小于15，则添加到结果中
                        table.insert(result, {
                            prompt = string.format("%s 当前导轨为百隆全拉推弹阻尼导轨，后留空需≥15，请调整", drawer_name),
                            related_ids = {drawer_id}
                        })
                    end
                end
            end
        end
    end

    return rule_result:error(result, logs)
end

return _M
