# 说明

本项目升级了`openresty`的版本为`********`（原版本为`********`，`2021`年发布）。同时对整个项目结构做了比较大的调整。将`windows`运行环境与`linux`运行环境以及业务代码做了深层次分离。

# 环境变量要求

由于依赖了`redis`服务，所以需要提供`redis`相关的环境变量。

- `REDIS_CLUSTER_NODES`：`redis`集群地址，要求是合法的`JSON`数组字符串，格式为：`[{"ip": "***********", "port": 7001}, {"ip": "***********", "port": 7002}]`。
- `REDIS_CLUSTER_PWD`：`redis`集群密码，如果不需要密码配置则设置为空串。

# 容器部署

首先通过`docker build -t rule-engine:1.0 .`命令构建镜像。然后通过`docker run -d --rm --name re -p 8889:80 rule-engine:1.0`启动。之后就可以直接使用`8889`端口访问服务了。

# windows 本地启动

直接双击`start.bat`就会启动服务（此时会有命令行黑框一闪而过），启动脚本的主要逻辑就是将`src`目录和`commons`目录下的脚本复制到`openresty`里，然后启动服务。

默认监听端口为`8889`，有需要可以修改`windows\conf\nginx.conf`配置文件去调整。

# 测试

启动服务后，可以通过`test`目录下的`http`脚本完成基本功能验证：

# 目录介绍

- `windows`目录：`windows`版本的`openresty`
- `linux`目录：`linux`环境`docker`打包配置文件，主要就一个`nginx.conf`。
- `commons`目录：`lua`通用工具包，目前主要就是`http`相关工具。
- `src`目录：`lua`业务代码。其中`lib`目录表示工具包，`controller`目录表示控制层，其他目录按照业务分层。我们所有业务代码都放到这里面，后续只需要修改这个目录的代码。启动或者打包的时候会自动处理这个目录。
- `test`目录：测试脚本，比如`http`测试脚本等。
- `start.bat`脚本：`windows`启动脚本，双击运行。
- `stop.bat`脚本：`windows`停止脚本，双击运行。

# 开发流程

修改`src`目录代码，然后通过`start.bat`脚本启动服务，然后通过`test`目录下的`http`脚本去验证。（如果不确定之前是否启动了服务，可以先执行`stop.bat`尝试关闭进程）。

另外多次启动`start.bat`正常不会有影响，有问题可以通过如下`cmd`命令自行处理：

```shell

# 查看当前是否启动了 nginx 服务
tasklist /fi "IMAGENAME eq nginx.exe"

# 强杀 nginx 进程
taskkill /F /IM nginx.exe

```

# 扩展工具

请查看[参考文档](doc)
