-- 计算生产参数 - 模块：平板三边抽屉，厂商：平板抽面，参数：加锁备注，C_JSBZ

local _M = {}

-- 辅助函数：获取并转换参数值
local function get_param(obj, param_name)
    local value = tonumber(obj[param_name])
    if not value then
        return nil, param_name .. "参数值不存在或不是有效的数字"
    end
    return value
end

function _M.dowork(root, target, context)
    -- 获取所有需要的参数值
    local jslx, err = get_param(target, "JSLX")
    if not jslx then
        return nil, "获取不到" .. err
    end
    
    local jt, err = get_param(target, "J_T")
    if not jt then
        return nil, "获取不到" .. err
    end
    
    -- 根据JSLX的值确定输出格式
    if jslx == 1 then
        -- JSLX=1的情况
        if jt == 1 then
            return "KCSKF:"
        elseif jt == 2 then
            return "KCSKH:"
        elseif jt == 3 then
            return "KCSKI:"
        end
    elseif jslx == 2 then
        -- JSLX=2的情况
        if jt == 1 then
            return "DZWSK:;QGKH:"
        elseif jt == 2 then
            return "DZWSK:;BGKH:"
        elseif jt == 3 then
            return "DZWSK:;RZKH:"
        end
    end
    
    return ""
end

return _M