-- 几何引擎单例类
-- 用于 避免 几何引擎 重复初始化
-- 外部调用方式：
-- local geometry = require("lib/geometry-engine-ins")
-- 主类初始化
-- local engine_instance = geometry_engine:get_current_instance()
-- 这个才是 当前的 几何引擎实例
-- local engine_object = engine_instance:create_engine_object(xml_data)
-- 执行 几何引擎的 方法
-- local add_result = engine_object:add_test(10,20)
-- engine_object:get_objects_distance()
--  主类清理
-- engine_instance:clear_current_instance()

-- 使用局部变量存储模块依赖
local engine = require("lib/rule-engine")
local GeometryObject = require("lib/geometry-engine-object")

-- 几何引擎类定义
local geometry_engine = {}

-- 创建新的几何引擎实例
function geometry_engine:new()
    local instance = {
        engine = engine,
        object = nil
    }
    setmetatable(instance, { __index = geometry_engine })
    return instance
end

-- 创建几何引擎对象
function geometry_engine:create_engine_object(xmlString)
    local t_id = self.engine.scene_init(xmlString)
    local engine_object = GeometryObject.new(t_id)
    self.object = engine_object
    return engine_object
end

function geometry_engine:is_engine_object_exist()
    return ngx.ctx.geometry_engine ~= nil
end

-- 获取当前请求的几何引擎实例
function geometry_engine:get_current_instance()
    if not ngx.ctx.geometry_engine then
        ngx.ctx.geometry_engine = geometry_engine:new()
    end
    return ngx.ctx.geometry_engine
end

-- 获取当前引擎对象
function geometry_engine:get_current_engine_object()
    if not self.object then
        return nil
    end
    return self.object
end

-- 清理当前请求的几何引擎实例
function geometry_engine:clear_current_instance()
    if ngx.ctx.geometry_engine then
        local engine_object = self:get_current_engine_object()
        if engine_object then
            engine_object:scene_clear()
        end
        ngx.ctx.geometry_engine = nil
    end
end

-- 初始化几何引擎
-- function geometry_engine:init(xmlString)
--     if not self.initialized then
--         -- 初始化几何引擎
--         engine.scene_init(xmlString)
--         self.initialized = true
--     end
-- end

-- function geometry_engine:core_dump_test()
--     engine.core_dump_test()
-- end

-- 获取几何关系
-- function geometry_engine:get_objects_distance(
--     attrKey1, op1, value1, directions, distanceOp, distance, attrKey2, op2, value2)
--     return engine.get_objects_distance(
--         attrKey1, op1, value1, directions, distanceOp, distance, attrKey2, op2, value2
--     )
-- end

-- 返回模块
return geometry_engine
