=encoding utf-8


=head1 Name

ngx_rds_json - an output filter that formats Resty DBD Streams generated by ngx_drizzle and others to JSON


=head1 Status

This module is considered production ready.

We need your help! If you find this module useful and/or interesting, please consider joining the development!
Commit bit can be freely delivered at your request ;)


=head1 Synopsis


    server {
        location /mysql {
            drizzle_query 'select * from cats';
            drizzle_pass my_mysql_upstream;
    
            rds_json on;
        }
    
        location /foo {
            if ($arg_limit !~ '^\d{2}$') {
                rds_json_ret 400 'Bad "limit" argument';
            }
    
            drizzle_query "select * from dogs limit $arg_limit";
            drizzle_pass my_mysql_upstream;
    
            rds_json on;
        }
        ...
    }


=head1 Description

This module provides an output filter that can format the RDS outputs
generated by L<ngx_drizzle|https://github.com/openresty/drizzle-nginx-module>
and L<ngx_postgres|https://github.com/FRiCKLE/ngx_postgres/> modules to JSON.




=head1 Directives




=head2 rds_json

B<syntax:> I<rds_json on|off>

B<default:> I<rds_json off>

B<context:> I<http, server, location, if location>

Enables or disables the output filter of this module.




=head2 rds_json_buffer_size

B<syntax:> I<rds_json_buffer_size   E<lt>bytesE<gt>>

B<default:> I<rds_json_buffer_size E<lt>page-sizeE<gt>>

B<context:> I<http, server, location, if location>

Controls the buffer size used by this module. default to the page size (4k/8k).
The bigger the buffer size, the less streammy the conversion
will be. But usually increasing the buffer size
does help reduce CPU time.




=head2 rds_json_format

B<syntax:> I<rds_json_format  normal|compact>

B<default:> I<rds_json_format normal>

B<context:> I<http, server, location, if location>

Controls the output JSON format. A sample of the default "normal" format
looks like this


     [{"id":1,"name":"marry"},{"id":2,"name":"bob"}]

while it looks like below when in the "compact" format


     [["id","name"],[1,"marry"],[2,"bob"]]

that is, the first row holds the column name list.




=head2 rds_json_root

B<syntax:> I<rds_json_root E<lt>keyE<gt>>

B<default:> I<no>

B<context:> I<http, server, location, if location>

Specifies the "root" key for data rows (if any). For example,


     rds_json on;
     rds_json_root rows;

will return JSON output like this:


     {"rows":[{"id":2,"name":null},{"id":3,"name":"bob"}]}

if C<rds_json_format compact> is also specified, then the
output will look like this:


     {"rows":[["id","name"],[2,null],[3,"bob"]]}

Nginx variables are not supported in the E<lt>keyE<gt> argument of
this directive.

If this directive is not defined, neither are L<rds_json_success_property>,
nor L<rds_json_user_property>, the JSON output for select queries will
just be an array at the top level.

When either L<rds_json_success_property> or L<rds_json_user_property> are specified,
this directive takes a default argument of "data".




=head2 rds_json_success_property

B<syntax:> I<rds_json_success_property E<lt>keyE<gt>>

B<default:> I<no>

B<context:> I<http, server, location, if location>

Specifies the top-level object property name used in the JSON output
for indicating success or false of the query.




=head2 rds_json_user_property

B<syntax:> I<rds_json_user_property E<lt>keyE<gt> E<lt>valueE<gt>>

B<default:> I<no>

B<context:> I<http, server, location, if location>

Specifies additonal user properties for the top-level object
of the JSON output.

Multiple instances of this directives are allowed in a single scope.

Nginx variables are supported in the C<value> argument.

Both of the C<key> and C<value> arguments will be automatically
quoted according to JSON strings' notation.




=head2 rds_json_errcode_key

B<syntax:> I<rds_json_errcode_key E<lt>keyE<gt>>

B<default:> I<rds_json_errcode_key errcode>

B<context:> I<http, server, location, if location>

Specifies the errcode key name used in the JSON output.




=head2 rds_json_errstr_key

B<syntax:> I<rds_json_errstr_key E<lt>keyE<gt>>

B<default:> I<rds_json_errstr_key errstr>

B<context:> I<http, server, location, if location>

Specifies the errstr key name used in the JSON output.




=head2 rds_json_ret

B<syntax:> I<rds_json_ret E<lt>error-codeE<gt> E<lt>descrptionE<gt>>

B<default:> I<no>

B<context:> I<location, if location>

This directive enables a content handler that simply emits
an response body like this:


     {"errcode":<error-code>,"errstr":"<description>"}

while the C<< <description> >> string will be properly quoted as
a JSON string.




=head2 rds_json_content_type

B<syntax:> I<rds_json_content_type E<lt>mime-typeE<gt>>

B<default:> I<rds_json_content_type application/json>

B<context:> I<http, server, location, if location>

Controls the C<Content-Type> header of the response generated by
this module's output filter.




=head1 Installation

You're recommended to install this module (as well as the Nginx core and many other goodies) via the L<OpenResty bundle|http://openresty.org>. See L<the detailed instructions|http://openresty.org/#Installation> for downloading and installing OpenResty into your system. This is the easiest and most safe way to set things up.

Alternatively, you can install this module manually with the Nginx source:

Grab the nginx source code from L<nginx.org|http://nginx.org/>, for example,
the version 1.13.6 (see L<nginx compatibility>), and then build the source with this module:


     $ wget 'http://nginx.org/download/nginx-1.13.6.tar.gz'
     $ tar -xzvf nginx-1.13.6.tar.gz
     $ cd nginx-1.13.6/
    
     # Here we assume you would install you nginx under /opt/nginx/.
     $ ./configure --prefix=/opt/nginx \
         --add-module=/path/to/rds-json-nginx-module
    
     $ make -j2
     $ make install

Download the latest version of the release tarball of this module from L<rds-json-nginx-module file list|https://github.com/openresty/rds-json-nginx-module/tags>.

Starting from NGINX 1.9.11, you can also compile this module as a dynamic module, by using the C<--add-dynamic-module=PATH> option
instead of C<--add-module=PATH> on the C<./configure> command line above.
And then you can explicitly load the module in your C<nginx.conf> via the L<load_module|http://nginx.org/en/docs/ngx_core_module.html#load_module> directive, for example,


    load_module /path/to/modules/ngx_http_rds_json_filter_module.so;

Also, this module is included and enabled by default in the L<OpenResty bundle|http://openresty.org>.




=head1 Compatibility

The following versions of Nginx should work with this module:


=over


=item *

B<1.13.x> (last tested: 1.13.6)

=item *

B<1.12.x>

=item *

B<1.11.x> (last tested: 1.11.2)

=item *

B<1.10.x>

=item *

B<1.9.x> (last tested: 1.9.7)

=item *

B<1.8.x>

=item *

B<1.7.x> (last tested: 1.7.10)

=item *

B<1.6.x>

=item *

B<1.5.x> (last tested: 1.5.12)

=item *

B<1.4.x> (last tested: 1.4.4)

=item *

B<1.2.x> (last tested: 1.2.9)

=item *

B<1.1.x> (last tested: 1.1.5)

=item *

B<1.0.x> (last tested: 1.0.9)

=item *

B<0.9.x> (last tested: 0.9.4)

=item *

B<0.8.x> (last tested: 0.8.55)

=item *

B<0.7.x E<gt>= 0.7.46> (last tested: 0.7.68)


=back

Earlier versions of Nginx like 0.6.x and 0.5.x will I<not> work.

If you find that any particular version of Nginx above 0.7.44 does not
work with this module, please consider reporting a bug.




=head1 Author

Yichun "agentzh" Zhang (章亦春) I<E<lt><EMAIL><gt>>, OpenResty Inc.




=head1 Copyright & License

This module is licenced under the BSD license.

Copyright (C) 2009-2017, Yichun Zhang (agentzh) E<lt><EMAIL><gt>, OpenResty Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:


=over


=item *

Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.

=item *

Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED
TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

L<ngx_drizzle|https://github.com/openresty/drizzle-nginx-module>

=item *

L<ngx_postgres|https://github.com/FRiCKLE/ngx_postgres/>

=item *

L<ngx_rds_csv|https://github.com/openresty/rds-csv-nginx-module>


=back



