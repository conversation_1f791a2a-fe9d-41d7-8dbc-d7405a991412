<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lua函数查看工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .input-area {
            margin: 20px 0;
            text-align: center;
        }
        .file-input-wrapper {
            margin-bottom: 15px;
        }
        .file-input {
            display: none;
        }
        .file-input-label {
            display: inline-block;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .file-input-label:hover {
            background-color: #45a049;
        }
        .file-name {
            margin-top: 10px;
            color: #666;
        }
        .copy-button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
            transition: background-color 0.3s;
        }
        .copy-button:hover {
            background-color: #1976D2;
        }
        .copy-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            font-family: 'Microsoft YaHei', sans-serif;
            margin-top: 15px;
        }
        .result-area {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .result-area pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Lua函数查看工具</h1>
        <div class="input-area">
            <div class="file-input-wrapper">
                <input type="file" id="fileInput" class="file-input" accept=".lua">
                <label for="fileInput" class="file-input-label">选择Lua文件</label>
                <div class="file-name" id="fileName"></div>
            </div>
            <textarea id="inputText" placeholder="文件内容将显示在这里..." readonly></textarea>
        </div>
        <div class="result-area">
            <h3>转义后的结果：</h3>
            <pre id="result"></pre>
            <button id="copyButton" class="copy-button" disabled>复制为JSON格式</button>
        </div>
    </div>

    <script>
        function escapeString(str) {
            return str.replace(/[\\"']/g, '\\$&')
                     .replace(/\u0000/g, '\\0')
                     .replace(/\n/g, '\\n')
                     .replace(/\r/g, '\\r')
                     .replace(/\t/g, '\\t')
                     .replace(/\f/g, '\\f')
                     .replace(/\v/g, '\\v')
                     .replace(/[\u0000-\u0019]+/g, function(match) {
                         return '\\u' + ('0000' + match.charCodeAt(0).toString(16)).slice(-4);
                     });
        }

        const fileInput = document.getElementById('fileInput');
        const inputText = document.getElementById('inputText');
        const result = document.getElementById('result');
        const fileName = document.getElementById('fileName');
        const copyButton = document.getElementById('copyButton');

        function createJsonStructure(content, fileName) {
            const funcName = fileName.replace('.lua', '');
            return JSON.stringify({
                funcName: funcName,
                luaScript: content,
                versionCode: "v1"
            }, null, 2);
        }

        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                fileName.textContent = `已选择文件: ${file.name}`;
                copyButton.disabled = false;
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    const content = e.target.result;
                    inputText.value = content;
                    let escaped = escapeString(content);
                    // 去掉所有换行和回车
                    escaped = escaped.replace(/\\n/g, '').replace(/\\r/g, '');
                    // 如果还想去掉所有空格，可以加上：escaped = escaped.replace(/\s+/g, '');
                    result.textContent = escaped;
                };
                reader.readAsText(file);
            }
        });

        copyButton.addEventListener('click', function() {
            const file = fileInput.files[0];
            if (file) {
                const jsonContent = createJsonStructure(inputText.value, file.name);
                navigator.clipboard.writeText(jsonContent).then(() => {
                    alert('已复制到剪贴板！');
                }).catch(err => {
                    console.error('复制失败:', err);
                    alert('复制失败，请手动复制');
                });
            }
        });
    </script>
</body>
</html> 