=pod

=encoding utf8


=head1 Lua 5.1 Reference Manual

by <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>

Copyright E<copy> 2006E<ndash>2012 Lua.org, PUC-Rio. Freely available
under the terms of the Lua license.


contents E<middot> index E<middot> other versions

=head1 1 - Introduction

Lua is an extension programming language designed to support general
procedural programming with data description facilities. It also offers
good support for object-oriented programming, functional programming,
and data-driven programming. Lua is intended to be used as a powerful,
light-weight scripting language for any program that needs one. Lua is
implemented as a library, written in I<clean> C (that is, in the common
subset of ANSI C and C++).

Being an extension language, <PERSON><PERSON> has no notion of a "main" program: it
only works I<embedded> in a host client, called the I<embedding
program> or simply the I<host>. This host program can invoke functions
to execute a piece of Lua code, can write and read Lua variables, and
can register C functions to be called by Lua code. Through the use of C
functions, Lua can be augmented to cope with a wide range of different
domains, thus creating customized programming languages sharing a
syntactical framework. The Lua distribution includes a sample host
program called C<lua>, which uses the Lua library to offer a complete,
stand-alone Lua interpreter.

Lua is free software, and is provided as usual with no guarantees, as
stated in its license. The implementation described in this manual is
available at Lu<PERSON>'s official web site, C<www.lua.org>.

Like any other reference manual, this document is dry in places. For a
discussion of the decisions behind the design of Lua, see the technical
papers available at Lua's web site. For a detailed introduction to
programming in Lua, see Roberto's book, I<Programming in Lua (Second
Edition)>.

=head1 2 - The Language

This section describes the lexis, the syntax, and the semantics of Lua.
In other words, this section describes which tokens are valid, how they
can be combined, and what their combinations mean.

The language constructs will be explained using the usual extended BNF
notation, in which {I<a>} means 0 or more I<a>'s, and [I<a>] means an
optional I<a>. Non-terminals are shown like non-terminal, keywords are
shown like B<kword>, and other terminal symbols are shown like
`B<=>E<acute>. The complete syntax of Lua can be found in E<sect>8 at
the end of this manual.

=head2 2.1 - Lexical Conventions

I<Names> (also called I<identifiers>) in Lua can be any string of
letters, digits, and underscores, not beginning with a digit. This
coincides with the definition of names in most languages. (The
definition of letter depends on the current locale: any character
considered alphabetic by the current locale can be used in an
identifier.) Identifiers are used to name variables and table fields.

The following I<keywords> are reserved and cannot be used as names:

     and       break     do        else      elseif
     end       false     for       function  if
     in        local     nil       not       or
     repeat    return    then      true      until     while

Lua is a case-sensitive language: C<and> is a reserved word, but C<And>
and C<AND> are two different, valid names. As a convention, names
starting with an underscore followed by uppercase letters (such as
C<_VERSION>) are reserved for internal global variables used by Lua.

The following strings denote other tokens:

     +     -     *     /     %     ^     #
     ==    ~=    <=    >=    <     >     =
     (     )     {     }     [     ]
     ;     :     ,     .     ..    ...

I<Literal strings> can be delimited by matching single or double
quotes, and can contain the following C-like escape sequences: 'C<\a>'
(bell), 'C<\b>' (backspace), 'C<\f>' (form feed), 'C<\n>' (newline),
'C<\r>' (carriage return), 'C<\t>' (horizontal tab), 'C<\v>' (vertical
tab), 'C<\\>' (backslash), 'C<\">' (quotation mark [double quote]), and
'C<\'>' (apostrophe [single quote]). Moreover, a backslash followed by
a real newline results in a newline in the string. A character in a
string can also be specified by its numerical value using the escape
sequence C<\I<ddd>>, where I<ddd> is a sequence of up to three decimal
digits. (Note that if a numerical escape is to be followed by a digit,
it must be expressed using exactly three digits.) Strings in Lua can
contain any 8-bit value, including embedded zeros, which can be
specified as 'C<\0>'.

Literal strings can also be defined using a long format enclosed by
I<long brackets>. We define an I<opening long bracket of level I<n>> as
an opening square bracket followed by I<n> equal signs followed by
another opening square bracket. So, an opening long bracket of level 0
is written as C<[[>, an opening long bracket of level 1 is written as
C<[=[>, and so on. A I<closing long bracket> is defined similarly; for
instance, a closing long bracket of level 4 is written as C<]====]>. A
long string starts with an opening long bracket of any level and ends
at the first closing long bracket of the same level. Literals in this
bracketed form can run for several lines, do not interpret any escape
sequences, and ignore long brackets of any other level. They can
contain anything except a closing bracket of the proper level.

For convenience, when the opening long bracket is immediately followed
by a newline, the newline is not included in the string. As an example,
in a system using ASCII (in which 'C<a>' is coded as 97, newline is
coded as 10, and 'C<1>' is coded as 49), the five literal strings below
denote the same string:

     a = 'alo\n123"'
     a = "alo\n123\""
     a = '\97lo\10\04923"'
     a = [[alo
     123"]]
     a = [==[
     alo
     123"]==]

A I<numerical constant> can be written with an optional decimal part
and an optional decimal exponent. Lua also accepts integer hexadecimal
constants, by prefixing them with C<0x>. Examples of valid numerical
constants are

     3   3.0   3.1416   314.16e-2   0.31416E1   0xff   0x56

A I<comment> starts with a double hyphen (C<-->) anywhere outside a
string. If the text immediately after C<--> is not an opening long
bracket, the comment is a I<short comment>, which runs until the end of
the line. Otherwise, it is a I<long comment>, which runs until the
corresponding closing long bracket. Long comments are frequently used
to disable code temporarily.

=head2 2.2 - Values and Types

Lua is a I<dynamically typed language>. This means that variables do
not have types; only values do. There are no type definitions in the
language. All values carry their own type.

All values in Lua are I<first-class values>. This means that all values
can be stored in variables, passed as arguments to other functions, and
returned as results.

There are eight basic types in Lua: I<nil>, I<boolean>, I<number>,
I<string>, I<function>, I<userdata>, I<thread>, and I<table>. I<Nil> is
the type of the value B<nil>, whose main property is to be different
from any other value; it usually represents the absence of a useful
value. I<Boolean> is the type of the values B<false> and B<true>. Both
B<nil> and B<false> make a condition false; any other value makes it
true. I<Number> represents real (double-precision floating-point)
numbers. (It is easy to build Lua interpreters that use other internal
representations for numbers, such as single-precision float or long
integers; see file C<luaconf.h>.) I<String> represents arrays of
characters. Lua is 8-bit clean: strings can contain any 8-bit
character, including embedded zeros ('C<\0>') (see E<sect>2.1).

Lua can call (and manipulate) functions written in Lua and functions
written in C (see E<sect>2.5.8).

The type I<userdata> is provided to allow arbitrary C data to be stored
in Lua variables. This type corresponds to a block of raw memory and
has no pre-defined operations in Lua, except assignment and identity
test. However, by using I<metatables>, the programmer can define
operations for userdata values (see E<sect>2.8). Userdata values cannot
be created or modified in Lua, only through the C API. This guarantees
the integrity of data owned by the host program.

The type I<thread> represents independent threads of execution and it
is used to implement coroutines (see E<sect>2.11). Do not confuse Lua
threads with operating-system threads. Lua supports coroutines on all
systems, even those that do not support threads.

The type I<table> implements associative arrays, that is, arrays that
can be indexed not only with numbers, but with any value (except
B<nil>). Tables can be I<heterogeneous>; that is, they can contain
values of all types (except B<nil>). Tables are the sole data
structuring mechanism in Lua; they can be used to represent ordinary
arrays, symbol tables, sets, records, graphs, trees, etc. To represent
records, Lua uses the field name as an index. The language supports
this representation by providing C<a.name> as syntactic sugar for
C<a["name"]>. There are several convenient ways to create tables in Lua
(see E<sect>2.5.7).

Like indices, the value of a table field can be of any type (except
B<nil>). In particular, because functions are first-class values, table
fields can contain functions. Thus tables can also carry I<methods>
(see E<sect>2.5.9).

Tables, functions, threads, and (full) userdata values are I<objects>:
variables do not actually I<contain> these values, only I<references>
to them. Assignment, parameter passing, and function returns always
manipulate references to such values; these operations do not imply any
kind of copy.

The library function C<type> returns a string describing the type of a
given value.

=head2 2.2.1 - Coercion

Lua provides automatic conversion between string and number values at
run time. Any arithmetic operation applied to a string tries to convert
this string to a number, following the usual conversion rules.
Conversely, whenever a number is used where a string is expected, the
number is converted to a string, in a reasonable format. For complete
control over how numbers are converted to strings, use the C<format>
function from the string library (see C<string.format>).

=head2 2.3 - Variables

Variables are places that store values. There are three kinds of
variables in Lua: global variables, local variables, and table fields.

A single name can denote a global variable or a local variable (or a
function's formal parameter, which is a particular kind of local
variable):

	var ::= Name

Name denotes identifiers, as defined in E<sect>2.1.

Any variable is assumed to be global unless explicitly declared as a
local (see E<sect>2.4.7). Local variables are I<lexically scoped>:
local variables can be freely accessed by functions defined inside
their scope (see E<sect>2.6).

Before the first assignment to a variable, its value is B<nil>.

Square brackets are used to index a table:

	var ::= prefixexp `[´ exp `]´

The meaning of accesses to global variables and table fields can be
changed via metatables. An access to an indexed variable C<t[i]> is
equivalent to a call C<gettable_event(t,i)>. (See E<sect>2.8 for a
complete description of the C<gettable_event> function. This function
is not defined or callable in Lua. We use it here only for explanatory
purposes.)

The syntax C<var.Name> is just syntactic sugar for C<var["Name"]>:

	var ::= prefixexp `.´ Name

All global variables live as fields in ordinary Lua tables, called
I<environment tables> or simply I<environments> (see E<sect>2.9). Each
function has its own reference to an environment, so that all global
variables in this function will refer to this environment table. When a
function is created, it inherits the environment from the function that
created it. To get the environment table of a Lua function, you call
C<getfenv>. To replace it, you call C<setfenv>. (You can only
manipulate the environment of C functions through the debug library;
(see E<sect>5.9).)

An access to a global variable C<x> is equivalent to C<_env.x>, which
in turn is equivalent to

     gettable_event(_env, "x")

where C<_env> is the environment of the running function. (See
E<sect>2.8 for a complete description of the C<gettable_event>
function. This function is not defined or callable in Lua. Similarly,
the C<_env> variable is not defined in Lua. We use them here only for
explanatory purposes.)

=head2 2.4 - Statements

Lua supports an almost conventional set of statements, similar to those
in Pascal or C. This set includes assignments, control structures,
function calls, and variable declarations.

=head2 2.4.1 - Chunks

The unit of execution of Lua is called a I<chunk>. A chunk is simply a
sequence of statements, which are executed sequentially. Each statement
can be optionally followed by a semicolon:

	chunk ::= {stat [`;´]}

There are no empty statements and thus 'C<;;>' is not legal.

Lua handles a chunk as the body of an anonymous function with a
variable number of arguments (see E<sect>2.5.9). As such, chunks can
define local variables, receive arguments, and return values.

A chunk can be stored in a file or in a string inside the host program.
To execute a chunk, Lua first pre-compiles the chunk into instructions
for a virtual machine, and then it executes the compiled code with an
interpreter for the virtual machine.

Chunks can also be pre-compiled into binary form; see program C<luac>
for details. Programs in source and compiled forms are interchangeable;
Lua automatically detects the file type and acts accordingly.

=head2 2.4.2 - Blocks

A block is a list of statements; syntactically, a block is the same as
a chunk:

	block ::= chunk

A block can be explicitly delimited to produce a single statement:

	stat ::= do block end

Explicit blocks are useful to control the scope of variable
declarations. Explicit blocks are also sometimes used to add a
B<return> or B<break> statement in the middle of another block (see
E<sect>2.4.4).

=head2 2.4.3 - Assignment

Lua allows multiple assignments. Therefore, the syntax for assignment
defines a list of variables on the left side and a list of expressions
on the right side. The elements in both lists are separated by commas:

	stat ::= varlist `=´ explist
	varlist ::= var {`,´ var}
	explist ::= exp {`,´ exp}

Expressions are discussed in E<sect>2.5.

Before the assignment, the list of values is I<adjusted> to the length
of the list of variables. If there are more values than needed, the
excess values are thrown away. If there are fewer values than needed,
the list is extended with as many B<nil>'s as needed. If the list of
expressions ends with a function call, then all values returned by that
call enter the list of values, before the adjustment (except when the
call is enclosed in parentheses; see E<sect>2.5).

The assignment statement first evaluates all its expressions and only
then are the assignments performed. Thus the code

     i = 3
     i, a[i] = i+1, 20

sets C<a[3]> to 20, without affecting C<a[4]> because the C<i> in
C<a[i]> is evaluated (to 3) before it is assigned 4. Similarly, the
line

     x, y = y, x

exchanges the values of C<x> and C<y>, and

     x, y, z = y, z, x

cyclically permutes the values of C<x>, C<y>, and C<z>.

The meaning of assignments to global variables and table fields can be
changed via metatables. An assignment to an indexed variable C<t[i] =
val> is equivalent to C<settable_event(t,i,val)>. (See E<sect>2.8 for a
complete description of the C<settable_event> function. This function
is not defined or callable in Lua. We use it here only for explanatory
purposes.)

An assignment to a global variable C<x = val> is equivalent to the
assignment C<_env.x = val>, which in turn is equivalent to

     settable_event(_env, "x", val)

where C<_env> is the environment of the running function. (The C<_env>
variable is not defined in Lua. We use it here only for explanatory
purposes.)

=head2 2.4.4 - Control Structures

The control structures B<if>, B<while>, and B<repeat> have the usual
meaning and familiar syntax:

	stat ::= while exp do block end
	stat ::= repeat block until exp
	stat ::= if exp then block {elseif exp then block} [else block] end

Lua also has a B<for> statement, in two flavors (see E<sect>2.4.5).

The condition expression of a control structure can return any value.
Both B<false> and B<nil> are considered false. All values different
from B<nil> and B<false> are considered true (in particular, the number
0 and the empty string are also true).

In the B<repeat>E<ndash>B<until> loop, the inner block does not end at
the B<until> keyword, but only after the condition. So, the condition
can refer to local variables declared inside the loop block.

The B<return> statement is used to return values from a function or a
chunk (which is just a function). Functions and chunks can return more
than one value, and so the syntax for the B<return> statement is

	stat ::= return [explist]

The B<break> statement is used to terminate the execution of a
B<while>, B<repeat>, or B<for> loop, skipping to the next statement
after the loop:

	stat ::= break

A B<break> ends the innermost enclosing loop.

The B<return> and B<break> statements can only be written as the
I<last> statement of a block. If it is really necessary to B<return> or
B<break> in the middle of a block, then an explicit inner block can be
used, as in the idioms C<do return end> and C<do break end>, because
now B<return> and B<break> are the last statements in their (inner)
blocks.

=head2 2.4.5 - For Statement

The B<for> statement has two forms: one numeric and one generic.

The numeric B<for> loop repeats a block of code while a control
variable runs through an arithmetic progression. It has the following
syntax:

	stat ::= for Name `=´ exp `,´ exp [`,´ exp] do block end

The I<block> is repeated for I<name> starting at the value of the first
I<exp>, until it passes the second I<exp> by steps of the third I<exp>.
More precisely, a B<for> statement like

     for v = e1, e2, e3 do block end

is equivalent to the code:

     do
       local var, limit, step = tonumber(e1), tonumber(e2), tonumber(e3)
       if not (var and limit and step) then error() end
       while (step > 0 and var <= limit) or (step <= 0 and var >= limit) do
         local v = var
         block
         var = var + step
       end
     end

Note the following:

=over

=item * All three control expressions are evaluated only once, before
the loop starts. They must all result in numbers.

=item * C<I<var>>, C<I<limit>>, and C<I<step>> are invisible variables.
The names shown here are for explanatory purposes only.

=item * If the third expression (the step) is absent, then a step of 1
is used.

=item * You can use B<break> to exit a B<for> loop.

=item * The loop variable C<v> is local to the loop; you cannot use its
value after the B<for> ends or is broken. If you need this value,
assign it to another variable before breaking or exiting the loop.

=back

The generic B<for> statement works over functions, called I<iterators>.
On each iteration, the iterator function is called to produce a new
value, stopping when this new value is B<nil>. The generic B<for> loop
has the following syntax:

	stat ::= for namelist in explist do block end
	namelist ::= Name {`,´ Name}

A B<for> statement like

     for var_1, ···, var_n in explist do block end

is equivalent to the code:

     do
       local f, s, var = explist
       while true do
         local var_1, ···, var_n = f(s, var)
         var = var_1
         if var == nil then break end
         block
       end
     end

Note the following:

=over

=item * C<I<explist>> is evaluated only once. Its results are an
I<iterator> function, a I<state>, and an initial value for the first
I<iterator variable>.

=item * C<I<f>>, C<I<s>>, and C<I<var>> are invisible variables. The
names are here for explanatory purposes only.

=item * You can use B<break> to exit a B<for> loop.

=item * The loop variables C<I<var_i>> are local to the loop; you
cannot use their values after the B<for> ends. If you need these
values, then assign them to other variables before breaking or exiting
the loop.

=back

=head2 2.4.6 - Function Calls as Statements

To allow possible side-effects, function calls can be executed as
statements:

	stat ::= functioncall

In this case, all returned values are thrown away. Function calls are
explained in E<sect>2.5.8.

=head2 2.4.7 - Local Declarations

Local variables can be declared anywhere inside a block. The
declaration can include an initial assignment:

	stat ::= local namelist [`=´ explist]

If present, an initial assignment has the same semantics of a multiple
assignment (see E<sect>2.4.3). Otherwise, all variables are initialized
with B<nil>.

A chunk is also a block (see E<sect>2.4.1), and so local variables can
be declared in a chunk outside any explicit block. The scope of such
local variables extends until the end of the chunk.

The visibility rules for local variables are explained in E<sect>2.6.

=head2 2.5 - Expressions

The basic expressions in Lua are the following:

	exp ::= prefixexp
	exp ::= nil | false | true
	exp ::= Number
	exp ::= String
	exp ::= function
	exp ::= tableconstructor
	exp ::= `...´
	exp ::= exp binop exp
	exp ::= unop exp
	prefixexp ::= var | functioncall | `(´ exp `)´

Numbers and literal strings are explained in E<sect>2.1; variables are
explained in E<sect>2.3; function definitions are explained in
E<sect>2.5.9; function calls are explained in E<sect>2.5.8; table
constructors are explained in E<sect>2.5.7. Vararg expressions, denoted
by three dots ('C<...>'), can only be used when directly inside a
vararg function; they are explained in E<sect>2.5.9.

Binary operators comprise arithmetic operators (see E<sect>2.5.1),
relational operators (see E<sect>2.5.2), logical operators (see
E<sect>2.5.3), and the concatenation operator (see E<sect>2.5.4). Unary
operators comprise the unary minus (see E<sect>2.5.1), the unary B<not>
(see E<sect>2.5.3), and the unary I<length operator> (see
E<sect>2.5.5).

Both function calls and vararg expressions can result in multiple
values. If an expression is used as a statement (only possible for
function calls (see E<sect>2.4.6)), then its return list is adjusted to
zero elements, thus discarding all returned values. If an expression is
used as the last (or the only) element of a list of expressions, then
no adjustment is made (unless the call is enclosed in parentheses). In
all other contexts, Lua adjusts the result list to one element,
discarding all values except the first one.

Here are some examples:

     f()                -- adjusted to 0 results
     g(f(), x)          -- f() is adjusted to 1 result
     g(x, f())          -- g gets x plus all results from f()
     a,b,c = f(), x     -- f() is adjusted to 1 result (c gets nil)
     a,b = ...          -- a gets the first vararg parameter, b gets
                        -- the second (both a and b can get nil if there
                        -- is no corresponding vararg parameter)
     
     a,b,c = x, f()     -- f() is adjusted to 2 results
     a,b,c = f()        -- f() is adjusted to 3 results
     return f()         -- returns all results from f()
     return ...         -- returns all received vararg parameters
     return x,y,f()     -- returns x, y, and all results from f()
     {f()}              -- creates a list with all results from f()
     {...}              -- creates a list with all vararg parameters
     {f(), nil}         -- f() is adjusted to 1 result

Any expression enclosed in parentheses always results in only one
value. Thus, C<(f(x,y,z))> is always a single value, even if C<f>
returns several values. (The value of C<(f(x,y,z))> is the first value
returned by C<f> or B<nil> if C<f> does not return any values.)

=head2 2.5.1 - Arithmetic Operators

Lua supports the usual arithmetic operators: the binary C<+>
(addition), C<-> (subtraction), C<*> (multiplication), C</> (division),
C<%> (modulo), and C<^> (exponentiation); and unary C<-> (negation). If
the operands are numbers, or strings that can be converted to numbers
(see E<sect>2.2.1), then all operations have the usual meaning.
Exponentiation works for any exponent. For instance, C<x^(-0.5)>
computes the inverse of the square root of C<x>. Modulo is defined as

     a % b == a - math.floor(a/b)*b

That is, it is the remainder of a division that rounds the quotient
towards minus infinity.

=head2 2.5.2 - Relational Operators

The relational operators in Lua are

     ==    ~=    <     >     <=    >=

These operators always result in B<false> or B<true>.

Equality (C<==>) first compares the type of its operands. If the types
are different, then the result is B<false>. Otherwise, the values of
the operands are compared. Numbers and strings are compared in the
usual way. Objects (tables, userdata, threads, and functions) are
compared by I<reference>: two objects are considered equal only if they
are the I<same> object. Every time you create a new object (a table,
userdata, thread, or function), this new object is different from any
previously existing object.

You can change the way that Lua compares tables and userdata by using
the "eq" metamethod (see E<sect>2.8).

The conversion rules of E<sect>2.2.1 I<do not> apply to equality
comparisons. Thus, C<"0"==0> evaluates to B<false>, and C<t[0]> and
C<t["0"]> denote different entries in a table.

The operator C<~=> is exactly the negation of equality (C<==>).

The order operators work as follows. If both arguments are numbers,
then they are compared as such. Otherwise, if both arguments are
strings, then their values are compared according to the current
locale. Otherwise, Lua tries to call the "lt" or the "le" metamethod
(see E<sect>2.8). A comparison C<a E<gt> b> is translated to C<b E<lt>
a> and C<a E<gt>= b> is translated to C<b E<lt>= a>.

=head2 2.5.3 - Logical Operators

The logical operators in Lua are B<and>, B<or>, and B<not>. Like the
control structures (see E<sect>2.4.4), all logical operators consider
both B<false> and B<nil> as false and anything else as true.

The negation operator B<not> always returns B<false> or B<true>. The
conjunction operator B<and> returns its first argument if this value is
B<false> or B<nil>; otherwise, B<and> returns its second argument. The
disjunction operator B<or> returns its first argument if this value is
different from B<nil> and B<false>; otherwise, B<or> returns its second
argument. Both B<and> and B<or> use short-cut evaluation; that is, the
second operand is evaluated only if necessary. Here are some examples:

     10 or 20            --> 10
     10 or error()       --> 10
     nil or "a"          --> "a"
     nil and 10          --> nil
     false and error()   --> false
     false and nil       --> false
     false or nil        --> nil
     10 and 20           --> 20

(In this manual, C<--E<gt>> indicates the result of the preceding
expression.)

=head2 2.5.4 - Concatenation

The string concatenation operator in Lua is denoted by two dots
('C<..>'). If both operands are strings or numbers, then they are
converted to strings according to the rules mentioned in E<sect>2.2.1.
Otherwise, the "concat" metamethod is called (see E<sect>2.8).

=head2 2.5.5 - The Length Operator

The length operator is denoted by the unary operator C<#>. The length
of a string is its number of bytes (that is, the usual meaning of
string length when each character is one byte).

The length of a table C<t> is defined to be any integer index C<n> such
that C<t[n]> is not B<nil> and C<t[n+1]> is B<nil>; moreover, if
C<t[1]> is B<nil>, C<n> can be zero. For a regular array, with non-nil
values from 1 to a given C<n>, its length is exactly that C<n>, the
index of its last value. If the array has "holes" (that is, B<nil>
values between other non-nil values), then C<#t> can be any of the
indices that directly precedes a B<nil> value (that is, it may consider
any such B<nil> value as the end of the array).

=head2 2.5.6 - Precedence

Operator precedence in Lua follows the table below, from lower to
higher priority:

     or
     and
     <     >     <=    >=    ~=    ==
     ..
     +     -
     *     /     %
     not   #     - (unary)
     ^

As usual, you can use parentheses to change the precedences of an
expression. The concatenation ('C<..>') and exponentiation ('C<^>')
operators are right associative. All other binary operators are left
associative.

=head2 2.5.7 - Table Constructors

Table constructors are expressions that create tables. Every time a
constructor is evaluated, a new table is created. A constructor can be
used to create an empty table or to create a table and initialize some
of its fields. The general syntax for constructors is

	tableconstructor ::= `{´ [fieldlist] `}´
	fieldlist ::= field {fieldsep field} [fieldsep]
	field ::= `[´ exp `]´ `=´ exp | Name `=´ exp | exp
	fieldsep ::= `,´ | `;´

Each field of the form C<[exp1] = exp2> adds to the new table an entry
with key C<exp1> and value C<exp2>. A field of the form C<name = exp>
is equivalent to C<["name"] = exp>. Finally, fields of the form C<exp>
are equivalent to C<[i] = exp>, where C<i> are consecutive numerical
integers, starting with 1. Fields in the other formats do not affect
this counting. For example,

     a = { [f(1)] = g; "x", "y"; x = 1, f(x), [30] = 23; 45 }

is equivalent to

     do
       local t = {}
       t[f(1)] = g
       t[1] = "x"         -- 1st exp
       t[2] = "y"         -- 2nd exp
       t.x = 1            -- t["x"] = 1
       t[3] = f(x)        -- 3rd exp
       t[30] = 23
       t[4] = 45          -- 4th exp
       a = t
     end

If the last field in the list has the form C<exp> and the expression is
a function call or a vararg expression, then all values returned by
this expression enter the list consecutively (see E<sect>2.5.8). To
avoid this, enclose the function call or the vararg expression in
parentheses (see E<sect>2.5).

The field list can have an optional trailing separator, as a
convenience for machine-generated code.

=head2 2.5.8 - Function Calls

A function call in Lua has the following syntax:

	functioncall ::= prefixexp args

In a function call, first prefixexp and args are evaluated. If the
value of prefixexp has type I<function>, then this function is called
with the given arguments. Otherwise, the prefixexp "call" metamethod is
called, having as first parameter the value of prefixexp, followed by
the original call arguments (see E<sect>2.8).

The form

	functioncall ::= prefixexp `:´ Name args

can be used to call "methods". A call C<v:name(I<args>)> is syntactic
sugar for C<v.name(v,I<args>)>, except that C<v> is evaluated only
once.

Arguments have the following syntax:

	args ::= `(´ [explist] `)´
	args ::= tableconstructor
	args ::= String

All argument expressions are evaluated before the call. A call of the
form C<f{I<fields>}> is syntactic sugar for C<f({I<fields>})>; that is,
the argument list is a single new table. A call of the form
C<f'I<string>'> (or C<f"I<string>"> or C<f[[I<string>]]>) is syntactic
sugar for C<f('I<string>')>; that is, the argument list is a single
literal string.

As an exception to the free-format syntax of Lua, you cannot put a line
break before the 'C<(>' in a function call. This restriction avoids
some ambiguities in the language. If you write

     a = f
     (g).x(a)

Lua would see that as a single statement, C<a = f(g).x(a)>. So, if you
want two statements, you must add a semi-colon between them. If you
actually want to call C<f>, you must remove the line break before
C<(g)>.

A call of the form C<return> I<functioncall> is called a I<tail call>.
Lua implements I<proper tail calls> (or I<proper tail recursion>): in a
tail call, the called function reuses the stack entry of the calling
function. Therefore, there is no limit on the number of nested tail
calls that a program can execute. However, a tail call erases any debug
information about the calling function. Note that a tail call only
happens with a particular syntax, where the B<return> has one single
function call as argument; this syntax makes the calling function
return exactly the returns of the called function. So, none of the
following examples are tail calls:

     return (f(x))        -- results adjusted to 1
     return 2 * f(x)
     return x, f(x)       -- additional results
     f(x); return         -- results discarded
     return x or f(x)     -- results adjusted to 1

=head2 2.5.9 - Function Definitions

The syntax for function definition is

	function ::= function funcbody
	funcbody ::= `(´ [parlist] `)´ block end

The following syntactic sugar simplifies function definitions:

	stat ::= function funcname funcbody
	stat ::= local function Name funcbody
	funcname ::= Name {`.´ Name} [`:´ Name]

The statement

     function f () body end

translates to

     f = function () body end

The statement

     function t.a.b.c.f () body end

translates to

     t.a.b.c.f = function () body end

The statement

     local function f () body end

translates to

     local f; f = function () body end

I<not> to

     local f = function () body end

(This only makes a difference when the body of the function contains
references to C<f>.)

A function definition is an executable expression, whose value has type
I<function>. When Lua pre-compiles a chunk, all its function bodies are
pre-compiled too. Then, whenever Lua executes the function definition,
the function is I<instantiated> (or I<closed>). This function instance
(or I<closure>) is the final value of the expression. Different
instances of the same function can refer to different external local
variables and can have different environment tables.

Parameters act as local variables that are initialized with the
argument values:

	parlist ::= namelist [`,´ `...´] | `...´

When a function is called, the list of arguments is adjusted to the
length of the list of parameters, unless the function is a variadic or
I<vararg function>, which is indicated by three dots ('C<...>') at the
end of its parameter list. A vararg function does not adjust its
argument list; instead, it collects all extra arguments and supplies
them to the function through a I<vararg expression>, which is also
written as three dots. The value of this expression is a list of all
actual extra arguments, similar to a function with multiple results. If
a vararg expression is used inside another expression or in the middle
of a list of expressions, then its return list is adjusted to one
element. If the expression is used as the last element of a list of
expressions, then no adjustment is made (unless that last expression is
enclosed in parentheses).

As an example, consider the following definitions:

     function f(a, b) end
     function g(a, b, ...) end
     function r() return 1,2,3 end

Then, we have the following mapping from arguments to parameters and to
the vararg expression:

     CALL            PARAMETERS
     
     f(3)             a=3, b=nil
     f(3, 4)          a=3, b=4
     f(3, 4, 5)       a=3, b=4
     f(r(), 10)       a=1, b=10
     f(r())           a=1, b=2
     
     g(3)             a=3, b=nil, ... -->  (nothing)
     g(3, 4)          a=3, b=4,   ... -->  (nothing)
     g(3, 4, 5, 8)    a=3, b=4,   ... -->  5  8
     g(5, r())        a=5, b=1,   ... -->  2  3

Results are returned using the B<return> statement (see E<sect>2.4.4).
If control reaches the end of a function without encountering a
B<return> statement, then the function returns with no results.

The I<colon> syntax is used for defining I<methods>, that is, functions
that have an implicit extra parameter C<self>. Thus, the statement

     function t.a.b.c:f (params) body end

is syntactic sugar for

     t.a.b.c.f = function (self, params) body end

=head2 2.6 - Visibility Rules

Lua is a lexically scoped language. The scope of variables begins at
the first statement I<after> their declaration and lasts until the end
of the innermost block that includes the declaration. Consider the
following example:

     x = 10                -- global variable
     do                    -- new block
       local x = x         -- new 'x', with value 10
       print(x)            --> 10
       x = x+1
       do                  -- another block
         local x = x+1     -- another 'x'
         print(x)          --> 12
       end
       print(x)            --> 11
     end
     print(x)              --> 10  (the global one)

Notice that, in a declaration like C<local x = x>, the new C<x> being
declared is not in scope yet, and so the second C<x> refers to the
outside variable.

Because of the lexical scoping rules, local variables can be freely
accessed by functions defined inside their scope. A local variable used
by an inner function is called an I<upvalue>, or I<external local
variable>, inside the inner function.

Notice that each execution of a B<local> statement defines new local
variables. Consider the following example:

     a = {}
     local x = 20
     for i=1,10 do
       local y = 0
       a[i] = function () y=y+1; return x+y end
     end

The loop creates ten closures (that is, ten instances of the anonymous
function). Each of these closures uses a different C<y> variable, while
all of them share the same C<x>.

=head2 2.7 - Error Handling

Because Lua is an embedded extension language, all Lua actions start
from C code in the host program calling a function from the Lua library
(see C<lua_pcall>). Whenever an error occurs during Lua compilation or
execution, control returns to C, which can take appropriate measures
(such as printing an error message).

Lua code can explicitly generate an error by calling the C<error>
function. If you need to catch errors in Lua, you can use the C<pcall>
function.

=head2 2.8 - Metatables

Every value in Lua can have a I<metatable>. This I<metatable> is an
ordinary Lua table that defines the behavior of the original value
under certain special operations. You can change several aspects of the
behavior of operations over a value by setting specific fields in its
metatable. For instance, when a non-numeric value is the operand of an
addition, Lua checks for a function in the field C<"__add"> in its
metatable. If it finds one, Lua calls this function to perform the
addition.

We call the keys in a metatable I<events> and the values
I<metamethods>. In the previous example, the event is C<"add"> and the
metamethod is the function that performs the addition.

You can query the metatable of any value through the C<getmetatable>
function.

You can replace the metatable of tables through the C<setmetatable>
function. You cannot change the metatable of other types from Lua
(except by using the debug library); you must use the C API for that.

Tables and full userdata have individual metatables (although multiple
tables and userdata can share their metatables). Values of all other
types share one single metatable per type; that is, there is one single
metatable for all numbers, one for all strings, etc.

A metatable controls how an object behaves in arithmetic operations,
order comparisons, concatenation, length operation, and indexing. A
metatable also can define a function to be called when a userdata is
garbage collected. For each of these operations Lua associates a
specific key called an I<event>. When Lua performs one of these
operations over a value, it checks whether this value has a metatable
with the corresponding event. If so, the value associated with that key
(the metamethod) controls how Lua will perform the operation.

Metatables control the operations listed next. Each operation is
identified by its corresponding name. The key for each operation is a
string with its name prefixed by two underscores, 'C<__>'; for
instance, the key for operation "add" is the string C<"__add">. The
semantics of these operations is better explained by a Lua function
describing how the interpreter executes the operation.

The code shown here in Lua is only illustrative; the real behavior is
hard coded in the interpreter and it is much more efficient than this
simulation. All functions used in these descriptions (C<rawget>,
C<tonumber>, etc.) are described in E<sect>5.1. In particular, to
retrieve the metamethod of a given object, we use the expression

     metatable(obj)[event]

This should be read as

     rawget(getmetatable(obj) or {}, event)

That is, the access to a metamethod does not invoke other metamethods,
and the access to objects with no metatables does not fail (it simply
results in B<nil>).

=over

=item * B<"add":> the C<+> operation.

The function C<getbinhandler> below defines how Lua chooses a handler
for a binary operation. First, Lua tries the first operand. If its type
does not define a handler for the operation, then Lua tries the second
operand.

     function getbinhandler (op1, op2, event)
       return metatable(op1)[event] or metatable(op2)[event]
     end

By using this function, the behavior of the C<op1 + op2> is

     function add_event (op1, op2)
       local o1, o2 = tonumber(op1), tonumber(op2)
       if o1 and o2 then  -- both operands are numeric?
         return o1 + o2   -- '+' here is the primitive 'add'
       else  -- at least one of the operands is not numeric
         local h = getbinhandler(op1, op2, "__add")
         if h then
           -- call the handler with both operands
           return (h(op1, op2))
         else  -- no handler available: default behavior
           error(···)
         end
       end
     end

=item * B<"sub":> the C<-> operation. Behavior similar to the "add"
operation.

=item * B<"mul":> the C<*> operation. Behavior similar to the "add"
operation.

=item * B<"div":> the C</> operation. Behavior similar to the "add"
operation.

=item * B<"mod":> the C<%> operation. Behavior similar to the "add"
operation, with the operation C<o1 - floor(o1/o2)*o2> as the primitive
operation.

=item * B<"pow":> the C<^> (exponentiation) operation. Behavior similar
to the "add" operation, with the function C<pow> (from the C math
library) as the primitive operation.

=item * B<"unm":> the unary C<-> operation.

     function unm_event (op)
       local o = tonumber(op)
       if o then  -- operand is numeric?
         return -o  -- '-' here is the primitive 'unm'
       else  -- the operand is not numeric.
         -- Try to get a handler from the operand
         local h = metatable(op).__unm
         if h then
           -- call the handler with the operand
           return (h(op))
         else  -- no handler available: default behavior
           error(···)
         end
       end
     end

=item * B<"concat":> the C<..> (concatenation) operation.

     function concat_event (op1, op2)
       if (type(op1) == "string" or type(op1) == "number") and
          (type(op2) == "string" or type(op2) == "number") then
         return op1 .. op2  -- primitive string concatenation
       else
         local h = getbinhandler(op1, op2, "__concat")
         if h then
           return (h(op1, op2))
         else
           error(···)
         end
       end
     end

=item * B<"len":> the C<#> operation.

     function len_event (op)
       if type(op) == "string" then
         return strlen(op)         -- primitive string length
       elseif type(op) == "table" then
         return #op                -- primitive table length
       else
         local h = metatable(op).__len
         if h then
           -- call the handler with the operand
           return (h(op))
         else  -- no handler available: default behavior
           error(···)
         end
       end
     end

See E<sect>2.5.5 for a description of the length of a table.

=item * B<"eq":> the C<==> operation. The function C<getcomphandler>
defines how Lua chooses a metamethod for comparison operators. A
metamethod only is selected when both objects being compared have the
same type and the same metamethod for the selected operation.

     function getcomphandler (op1, op2, event)
       if type(op1) ~= type(op2) then return nil end
       local mm1 = metatable(op1)[event]
       local mm2 = metatable(op2)[event]
       if mm1 == mm2 then return mm1 else return nil end
     end

The "eq" event is defined as follows:

     function eq_event (op1, op2)
       if type(op1) ~= type(op2) then  -- different types?
         return false   -- different objects
       end
       if op1 == op2 then   -- primitive equal?
         return true   -- objects are equal
       end
       -- try metamethod
       local h = getcomphandler(op1, op2, "__eq")
       if h then
         return (h(op1, op2))
       else
         return false
       end
     end

C<a ~= b> is equivalent to C<not (a == b)>.

=item * B<"lt":> the C<E<lt>> operation.

     function lt_event (op1, op2)
       if type(op1) == "number" and type(op2) == "number" then
         return op1 < op2   -- numeric comparison
       elseif type(op1) == "string" and type(op2) == "string" then
         return op1 < op2   -- lexicographic comparison
       else
         local h = getcomphandler(op1, op2, "__lt")
         if h then
           return (h(op1, op2))
         else
           error(···)
         end
       end
     end

C<a E<gt> b> is equivalent to C<b E<lt> a>.

=item * B<"le":> the C<E<lt>=> operation.

     function le_event (op1, op2)
       if type(op1) == "number" and type(op2) == "number" then
         return op1 <= op2   -- numeric comparison
       elseif type(op1) == "string" and type(op2) == "string" then
         return op1 <= op2   -- lexicographic comparison
       else
         local h = getcomphandler(op1, op2, "__le")
         if h then
           return (h(op1, op2))
         else
           h = getcomphandler(op1, op2, "__lt")
           if h then
             return not h(op2, op1)
           else
             error(···)
           end
         end
       end
     end

C<a E<gt>= b> is equivalent to C<b E<lt>= a>. Note that, in the absence
of a "le" metamethod, Lua tries the "lt", assuming that C<a E<lt>= b>
is equivalent to C<not (b E<lt> a)>.

=item * B<"index":> The indexing access C<table[key]>.

     function gettable_event (table, key)
       local h
       if type(table) == "table" then
         local v = rawget(table, key)
         if v ~= nil then return v end
         h = metatable(table).__index
         if h == nil then return nil end
       else
         h = metatable(table).__index
         if h == nil then
           error(···)
         end
       end
       if type(h) == "function" then
         return (h(table, key))     -- call the handler
       else return h[key]           -- or repeat operation on it
       end
     end

=item * B<"newindex":> The indexing assignment C<table[key] = value>.

     function settable_event (table, key, value)
       local h
       if type(table) == "table" then
         local v = rawget(table, key)
         if v ~= nil then rawset(table, key, value); return end
         h = metatable(table).__newindex
         if h == nil then rawset(table, key, value); return end
       else
         h = metatable(table).__newindex
         if h == nil then
           error(···)
         end
       end
       if type(h) == "function" then
         h(table, key,value)           -- call the handler
       else h[key] = value             -- or repeat operation on it
       end
     end

=item * B<"call":> called when Lua calls a value.

     function function_event (func, ...)
       if type(func) == "function" then
         return func(...)   -- primitive call
       else
         local h = metatable(func).__call
         if h then
           return h(func, ...)
         else
           error(···)
         end
       end
     end

=back

=head2 2.9 - Environments

Besides metatables, objects of types thread, function, and userdata
have another table associated with them, called their I<environment>.
Like metatables, environments are regular tables and multiple objects
can share the same environment.

Threads are created sharing the environment of the creating thread.
Userdata and C functions are created sharing the environment of the
creating C function. Non-nested Lua functions (created by C<loadfile>,
C<loadstring> or C<load>) are created sharing the environment of the
creating thread. Nested Lua functions are created sharing the
environment of the creating Lua function.

Environments associated with userdata have no meaning for Lua. It is
only a convenience feature for programmers to associate a table to a
userdata.

Environments associated with threads are called I<global environments>.
They are used as the default environment for threads and non-nested Lua
functions created by the thread and can be directly accessed by C code
(see E<sect>3.3).

The environment associated with a C function can be directly accessed
by C code (see E<sect>3.3). It is used as the default environment for
other C functions and userdata created by the function.

Environments associated with Lua functions are used to resolve all
accesses to global variables within the function (see E<sect>2.3). They
are used as the default environment for nested Lua functions created by
the function.

You can change the environment of a Lua function or the running thread
by calling C<setfenv>. You can get the environment of a Lua function or
the running thread by calling C<getfenv>. To manipulate the environment
of other objects (userdata, C functions, other threads) you must use
the C API.

=head2 2.10 - Garbage Collection

Lua performs automatic memory management. This means that you have to
worry neither about allocating memory for new objects nor about freeing
it when the objects are no longer needed. Lua manages memory
automatically by running a I<garbage collector> from time to time to
collect all I<dead objects> (that is, objects that are no longer
accessible from Lua). All memory used by Lua is subject to automatic
management: tables, userdata, functions, threads, strings, etc.

Lua implements an incremental mark-and-sweep collector. It uses two
numbers to control its garbage-collection cycles: the
I<garbage-collector pause> and the I<garbage-collector step
multiplier>. Both use percentage points as units (so that a value of
100 means an internal value of 1).

The garbage-collector pause controls how long the collector waits
before starting a new cycle. Larger values make the collector less
aggressive. Values smaller than 100 mean the collector will not wait to
start a new cycle. A value of 200 means that the collector waits for
the total memory in use to double before starting a new cycle.

The step multiplier controls the relative speed of the collector
relative to memory allocation. Larger values make the collector more
aggressive but also increase the size of each incremental step. Values
smaller than 100 make the collector too slow and can result in the
collector never finishing a cycle. The default, 200, means that the
collector runs at "twice" the speed of memory allocation.

You can change these numbers by calling C<lua_gc> in C or
C<collectgarbage> in Lua. With these functions you can also control the
collector directly (e.g., stop and restart it).

=head2 2.10.1 - Garbage-Collection Metamethods

Using the C API, you can set garbage-collector metamethods for userdata
(see E<sect>2.8). These metamethods are also called I<finalizers>.
Finalizers allow you to coordinate Lua's garbage collection with
external resource management (such as closing files, network or
database connections, or freeing your own memory).

Garbage userdata with a field C<__gc> in their metatables are not
collected immediately by the garbage collector. Instead, Lua puts them
in a list. After the collection, Lua does the equivalent of the
following function for each userdata in that list:

     function gc_event (udata)
       local h = metatable(udata).__gc
       if h then
         h(udata)
       end
     end

At the end of each garbage-collection cycle, the finalizers for
userdata are called in I<reverse> order of their creation, among those
collected in that cycle. That is, the first finalizer to be called is
the one associated with the userdata created last in the program. The
userdata itself is freed only in the next garbage-collection cycle.

=head2 2.10.2 - Weak Tables

A I<weak table> is a table whose elements are I<weak references>. A
weak reference is ignored by the garbage collector. In other words, if
the only references to an object are weak references, then the garbage
collector will collect this object.

A weak table can have weak keys, weak values, or both. A table with
weak keys allows the collection of its keys, but prevents the
collection of its values. A table with both weak keys and weak values
allows the collection of both keys and values. In any case, if either
the key or the value is collected, the whole pair is removed from the
table. The weakness of a table is controlled by the C<__mode> field of
its metatable. If the C<__mode> field is a string containing the
character 'C<k>', the keys in the table are weak. If C<__mode> contains
'C<v>', the values in the table are weak.

After you use a table as a metatable, you should not change the value
of its C<__mode> field. Otherwise, the weak behavior of the tables
controlled by this metatable is undefined.

=head2 2.11 - Coroutines

Lua supports coroutines, also called I<collaborative multithreading>. A
coroutine in Lua represents an independent thread of execution. Unlike
threads in multithread systems, however, a coroutine only suspends its
execution by explicitly calling a yield function.

You create a coroutine with a call to C<coroutine.create>. Its sole
argument is a function that is the main function of the coroutine. The
C<create> function only creates a new coroutine and returns a handle to
it (an object of type I<thread>); it does not start the coroutine
execution.

When you first call C<coroutine.resume>, passing as its first argument
a thread returned by C<coroutine.create>, the coroutine starts its
execution, at the first line of its main function. Extra arguments
passed to C<coroutine.resume> are passed on to the coroutine main
function. After the coroutine starts running, it runs until it
terminates or I<yields>.

A coroutine can terminate its execution in two ways: normally, when its
main function returns (explicitly or implicitly, after the last
instruction); and abnormally, if there is an unprotected error. In the
first case, C<coroutine.resume> returns B<true>, plus any values
returned by the coroutine main function. In case of errors,
C<coroutine.resume> returns B<false> plus an error message.

A coroutine yields by calling C<coroutine.yield>. When a coroutine
yields, the corresponding C<coroutine.resume> returns immediately, even
if the yield happens inside nested function calls (that is, not in the
main function, but in a function directly or indirectly called by the
main function). In the case of a yield, C<coroutine.resume> also
returns B<true>, plus any values passed to C<coroutine.yield>. The next
time you resume the same coroutine, it continues its execution from the
point where it yielded, with the call to C<coroutine.yield> returning
any extra arguments passed to C<coroutine.resume>.

Like C<coroutine.create>, the C<coroutine.wrap> function also creates a
coroutine, but instead of returning the coroutine itself, it returns a
function that, when called, resumes the coroutine. Any arguments passed
to this function go as extra arguments to C<coroutine.resume>.
C<coroutine.wrap> returns all the values returned by
C<coroutine.resume>, except the first one (the boolean error code).
Unlike C<coroutine.resume>, C<coroutine.wrap> does not catch errors;
any error is propagated to the caller.

As an example, consider the following code:

     function foo (a)
       print("foo", a)
       return coroutine.yield(2*a)
     end
     
     co = coroutine.create(function (a,b)
           print("co-body", a, b)
           local r = foo(a+1)
           print("co-body", r)
           local r, s = coroutine.yield(a+b, a-b)
           print("co-body", r, s)
           return b, "end"
     end)
            
     print("main", coroutine.resume(co, 1, 10))
     print("main", coroutine.resume(co, "r"))
     print("main", coroutine.resume(co, "x", "y"))
     print("main", coroutine.resume(co, "x", "y"))

When you run it, it produces the following output:

     co-body 1       10
     foo     2
     
     main    true    4
     co-body r
     main    true    11      -9
     co-body x       y
     main    true    10      end
     main    false   cannot resume dead coroutine

=head1 3 - The Application Program Interface

This section describes the C API for Lua, that is, the set of C
functions available to the host program to communicate with Lua. All
API functions and related types and constants are declared in the
header file C<lua.h>.

Even when we use the term "function", any facility in the API may be
provided as a macro instead. All such macros use each of their
arguments exactly once (except for the first argument, which is always
a Lua state), and so do not generate any hidden side-effects.

As in most C libraries, the Lua API functions do not check their
arguments for validity or consistency. However, you can change this
behavior by compiling Lua with a proper definition for the macro
C<luai_apicheck>, in file C<luaconf.h>.

=head2 3.1 - The Stack

Lua uses a I<virtual stack> to pass values to and from C. Each element
in this stack represents a Lua value (B<nil>, number, string, etc.).

Whenever Lua calls C, the called function gets a new stack, which is
independent of previous stacks and of stacks of C functions that are
still active. This stack initially contains any arguments to the C
function and it is where the C function pushes its results to be
returned to the caller (see C<lua_CFunction>).

For convenience, most query operations in the API do not follow a
strict stack discipline. Instead, they can refer to any element in the
stack by using an I<index>: A positive index represents an I<absolute>
stack position (starting at 1); a negative index represents an
I<offset> relative to the top of the stack. More specifically, if the
stack has I<n> elements, then index 1 represents the first element
(that is, the element that was pushed onto the stack first) and index
I<n> represents the last element; index -1 also represents the last
element (that is, the element at the top) and index I<-n> represents
the first element. We say that an index is I<valid> if it lies between
1 and the stack top (that is, if C<1 E<le> abs(index) E<le> top>).

=head2 3.2 - Stack Size

When you interact with Lua API, you are responsible for ensuring
consistency. In particular, I<you are responsible for controlling stack
overflow>. You can use the function C<lua_checkstack> to grow the stack
size.

Whenever Lua calls C, it ensures that at least C<LUA_MINSTACK> stack
positions are available. C<LUA_MINSTACK> is defined as 20, so that
usually you do not have to worry about stack space unless your code has
loops pushing elements onto the stack.

Most query functions accept as indices any value inside the available
stack space, that is, indices up to the maximum stack size you have set
through C<lua_checkstack>. Such indices are called I<acceptable
indices>. More formally, we define an I<acceptable index> as follows:

     (index < 0 && abs(index) <= top) ||
     (index > 0 && index <= stackspace)

Note that 0 is never an acceptable index.

=head2 3.3 - Pseudo-Indices

Unless otherwise noted, any function that accepts valid indices can
also be called with I<pseudo-indices>, which represent some Lua values
that are accessible to C code but which are not in the stack.
Pseudo-indices are used to access the thread environment, the function
environment, the registry, and the upvalues of a C function (see
E<sect>3.4).

The thread environment (where global variables live) is always at
pseudo-index C<LUA_GLOBALSINDEX>. The environment of the running C
function is always at pseudo-index C<LUA_ENVIRONINDEX>.

To access and change the value of global variables, you can use regular
table operations over an environment table. For instance, to access the
value of a global variable, do

     lua_getfield(L, LUA_GLOBALSINDEX, varname);

=head2 3.4 - C Closures

When a C function is created, it is possible to associate some values
with it, thus creating a I<C closure>; these values are called
I<upvalues> and are accessible to the function whenever it is called
(see C<lua_pushcclosure>).

Whenever a C function is called, its upvalues are located at specific
pseudo-indices. These pseudo-indices are produced by the macro
C<lua_upvalueindex>. The first value associated with a function is at
position C<lua_upvalueindex(1)>, and so on. Any access to
C<lua_upvalueindex(I<n>)>, where I<n> is greater than the number of
upvalues of the current function (but not greater than 256), produces
an acceptable (but invalid) index.

=head2 3.5 - Registry

Lua provides a I<registry>, a pre-defined table that can be used by any
C code to store whatever Lua value it needs to store. This table is
always located at pseudo-index C<LUA_REGISTRYINDEX>. Any C library can
store data into this table, but it should take care to choose keys
different from those used by other libraries, to avoid collisions.
Typically, you should use as key a string containing your library name
or a light userdata with the address of a C object in your code.

The integer keys in the registry are used by the reference mechanism,
implemented by the auxiliary library, and therefore should not be used
for other purposes.

=head2 3.6 - Error Handling in C

Internally, Lua uses the C C<longjmp> facility to handle errors. (You
can also choose to use exceptions if you use C++; see file
C<luaconf.h>.) When Lua faces any error (such as memory allocation
errors, type errors, syntax errors, and runtime errors) it I<raises> an
error; that is, it does a long jump. A I<protected environment> uses
C<setjmp> to set a recover point; any error jumps to the most recent
active recover point.

Most functions in the API can throw an error, for instance due to a
memory allocation error. The documentation for each function indicates
whether it can throw errors.

Inside a C function you can throw an error by calling C<lua_error>.

=head2 3.7 - Functions and Types

Here we list all functions and types from the C API in alphabetical
order. Each function has an indicator like this: [-o, +p, I<x>]

The first field, C<o>, is how many elements the function pops from the
stack. The second field, C<p>, is how many elements the function pushes
onto the stack. (Any function always pushes its results after popping
its arguments.) A field in the form C<x|y> means the function can push
(or pop) C<x> or C<y> elements, depending on the situation; an
interrogation mark 'C<?>' means that we cannot know how many elements
the function pops/pushes by looking only at its arguments (e.g., they
may depend on what is on the stack). The third field, C<x>, tells
whether the function may throw errors: 'C<->' means the function never
throws any error; 'C<m>' means the function may throw an error only due
to not enough memory; 'C<e>' means the function may throw other kinds
of errors; 'C<v>' means the function may throw an error on purpose.


=head2 C<lua_Alloc>

 typedef void * (*lua_Alloc) (void *ud,
                              void *ptr,
                              size_t osize,
                              size_t nsize);

The type of the memory-allocation function used by Lua states. The
allocator function must provide a functionality similar to C<realloc>,
but not exactly the same. Its arguments are C<ud>, an opaque pointer
passed to C<lua_newstate>; C<ptr>, a pointer to the block being
allocated/reallocated/freed; C<osize>, the original size of the block;
C<nsize>, the new size of the block. C<ptr> is C<NULL> if and only if
C<osize> is zero. When C<nsize> is zero, the allocator must return
C<NULL>; if C<osize> is not zero, it should free the block pointed to
by C<ptr>. When C<nsize> is not zero, the allocator returns C<NULL> if
and only if it cannot fill the request. When C<nsize> is not zero and
C<osize> is zero, the allocator should behave like C<malloc>. When
C<nsize> and C<osize> are not zero, the allocator behaves like
C<realloc>. Lua assumes that the allocator never fails when C<osize
E<gt>= nsize>.

Here is a simple implementation for the allocator function. It is used
in the auxiliary library by C<luaL_newstate>.

     static void *l_alloc (void *ud, void *ptr, size_t osize,
                                                size_t nsize) {
       (void)ud;  (void)osize;  /* not used */
       if (nsize == 0) {
         free(ptr);
         return NULL;
       }
       else
         return realloc(ptr, nsize);
     }

This code assumes that C<free(NULL)> has no effect and that
C<realloc(NULL, size)> is equivalent to C<malloc(size)>. ANSI C ensures
both behaviors.


=head2 C<lua_atpanic>

[-0, +0, I<->]

 lua_CFunction lua_atpanic (lua_State *L, lua_CFunction panicf);

Sets a new panic function and returns the old one.

If an error happens outside any protected environment, Lua calls a
I<panic function> and then calls C<exit(EXIT_FAILURE)>, thus exiting
the host application. Your panic function can avoid this exit by never
returning (e.g., doing a long jump).

The panic function can access the error message at the top of the
stack.


=head2 C<lua_call>

[-(nargs + 1), +nresults, I<e>]

 void lua_call (lua_State *L, int nargs, int nresults);

Calls a function.

To call a function you must use the following protocol: first, the
function to be called is pushed onto the stack; then, the arguments to
the function are pushed in direct order; that is, the first argument is
pushed first. Finally you call C<lua_call>; C<nargs> is the number of
arguments that you pushed onto the stack. All arguments and the
function value are popped from the stack when the function is called.
The function results are pushed onto the stack when the function
returns. The number of results is adjusted to C<nresults>, unless
C<nresults> is C<LUA_MULTRET>. In this case, I<all> results from the
function are pushed. Lua takes care that the returned values fit into
the stack space. The function results are pushed onto the stack in
direct order (the first result is pushed first), so that after the call
the last result is on the top of the stack.

Any error inside the called function is propagated upwards (with a
C<longjmp>).

The following example shows how the host program can do the equivalent
to this Lua code:

     a = f("how", t.x, 14)

Here it is in C:

     lua_getfield(L, LUA_GLOBALSINDEX, "f"); /* function to be called */
     lua_pushstring(L, "how");                        /* 1st argument */
     lua_getfield(L, LUA_GLOBALSINDEX, "t");   /* table to be indexed */
     lua_getfield(L, -1, "x");        /* push result of t.x (2nd arg) */
     lua_remove(L, -2);                  /* remove 't' from the stack */
     lua_pushinteger(L, 14);                          /* 3rd argument */
     lua_call(L, 3, 1);     /* call 'f' with 3 arguments and 1 result */
     lua_setfield(L, LUA_GLOBALSINDEX, "a");        /* set global 'a' */

Note that the code above is "balanced": at its end, the stack is back
to its original configuration. This is considered good programming
practice.


=head2 C<lua_CFunction>

 typedef int (*lua_CFunction) (lua_State *L);

Type for C functions.

In order to communicate properly with Lua, a C function must use the
following protocol, which defines the way parameters and results are
passed: a C function receives its arguments from Lua in its stack in
direct order (the first argument is pushed first). So, when the
function starts, C<lua_gettop(L)> returns the number of arguments
received by the function. The first argument (if any) is at index 1 and
its last argument is at index C<lua_gettop(L)>. To return values to
Lua, a C function just pushes them onto the stack, in direct order (the
first result is pushed first), and returns the number of results. Any
other value in the stack below the results will be properly discarded
by Lua. Like a Lua function, a C function called by Lua can also return
many results.

As an example, the following function receives a variable number of
numerical arguments and returns their average and sum:

     static int foo (lua_State *L) {
       int n = lua_gettop(L);    /* number of arguments */
       lua_Number sum = 0;
       int i;
       for (i = 1; i <= n; i++) {
         if (!lua_isnumber(L, i)) {
           lua_pushstring(L, "incorrect argument");
           lua_error(L);
         }
         sum += lua_tonumber(L, i);
       }
       lua_pushnumber(L, sum/n);        /* first result */
       lua_pushnumber(L, sum);         /* second result */
       return 2;                   /* number of results */
     }


=head2 C<lua_checkstack>

[-0, +0, I<m>]

 int lua_checkstack (lua_State *L, int extra);

Ensures that there are at least C<extra> free stack slots in the stack.
It returns false if it cannot grow the stack to that size. This
function never shrinks the stack; if the stack is already larger than
the new size, it is left unchanged.


=head2 C<lua_close>

[-0, +0, I<->]

 void lua_close (lua_State *L);

Destroys all objects in the given Lua state (calling the corresponding
garbage-collection metamethods, if any) and frees all dynamic memory
used by this state. On several platforms, you may not need to call this
function, because all resources are naturally released when the host
program ends. On the other hand, long-running programs, such as a
daemon or a web server, might need to release states as soon as they
are not needed, to avoid growing too large.


=head2 C<lua_concat>

[-n, +1, I<e>]

 void lua_concat (lua_State *L, int n);

Concatenates the C<n> values at the top of the stack, pops them, and
leaves the result at the top. If C<n> is 1, the result is the single
value on the stack (that is, the function does nothing); if C<n> is 0,
the result is the empty string. Concatenation is performed following
the usual semantics of Lua (see E<sect>2.5.4).


=head2 C<lua_cpcall>

[-0, +(0|1), I<->]

 int lua_cpcall (lua_State *L, lua_CFunction func, void *ud);

Calls the C function C<func> in protected mode. C<func> starts with
only one element in its stack, a light userdata containing C<ud>. In
case of errors, C<lua_cpcall> returns the same error codes as
C<lua_pcall>, plus the error object on the top of the stack; otherwise,
it returns zero, and does not change the stack. All values returned by
C<func> are discarded.


=head2 C<lua_createtable>

[-0, +1, I<m>]

 void lua_createtable (lua_State *L, int narr, int nrec);

Creates a new empty table and pushes it onto the stack. The new table
has space pre-allocated for C<narr> array elements and C<nrec>
non-array elements. This pre-allocation is useful when you know exactly
how many elements the table will have. Otherwise you can use the
function C<lua_newtable>.


=head2 C<lua_dump>

[-0, +0, I<m>]

 int lua_dump (lua_State *L, lua_Writer writer, void *data);

Dumps a function as a binary chunk. Receives a Lua function on the top
of the stack and produces a binary chunk that, if loaded again, results
in a function equivalent to the one dumped. As it produces parts of the
chunk, C<lua_dump> calls function C<writer> (see C<lua_Writer>) with
the given C<data> to write them.

The value returned is the error code returned by the last call to the
writer; 0 means no errors.

This function does not pop the Lua function from the stack.


=head2 C<lua_equal>

[-0, +0, I<e>]

 int lua_equal (lua_State *L, int index1, int index2);

Returns 1 if the two values in acceptable indices C<index1> and
C<index2> are equal, following the semantics of the Lua C<==> operator
(that is, may call metamethods). Otherwise returns 0. Also returns 0 if
any of the indices is non valid.


=head2 C<lua_error>

[-1, +0, I<v>]

 int lua_error (lua_State *L);

Generates a Lua error. The error message (which can actually be a Lua
value of any type) must be on the stack top. This function does a long
jump, and therefore never returns. (see C<luaL_error>).


=head2 C<lua_gc>

[-0, +0, I<e>]

 int lua_gc (lua_State *L, int what, int data);

Controls the garbage collector.

This function performs several tasks, according to the value of the
parameter C<what>:

=over

=item * B<C<LUA_GCSTOP>:> stops the garbage collector.

=item * B<C<LUA_GCRESTART>:> restarts the garbage collector.

=item * B<C<LUA_GCCOLLECT>:> performs a full garbage-collection cycle.

=item * B<C<LUA_GCCOUNT>:> returns the current amount of memory (in
Kbytes) in use by Lua.

=item * B<C<LUA_GCCOUNTB>:> returns the remainder of dividing the
current amount of bytes of memory in use by Lua by 1024.

=item * B<C<LUA_GCSTEP>:> performs an incremental step of garbage
collection. The step "size" is controlled by C<data> (larger values
mean more steps) in a non-specified way. If you want to control the
step size you must experimentally tune the value of C<data>. The
function returns 1 if the step finished a garbage-collection cycle.

=item * B<C<LUA_GCSETPAUSE>:> sets C<data> as the new value for the
I<pause> of the collector (see E<sect>2.10). The function returns the
previous value of the pause.

=item * B<C<LUA_GCSETSTEPMUL>:> sets C<data> as the new value for the
I<step multiplier> of the collector (see E<sect>2.10). The function
returns the previous value of the step multiplier.

=back


=head2 C<lua_getallocf>

[-0, +0, I<->]

 lua_Alloc lua_getallocf (lua_State *L, void **ud);

Returns the memory-allocation function of a given state. If C<ud> is
not C<NULL>, Lua stores in C<*ud> the opaque pointer passed to
C<lua_newstate>.


=head2 C<lua_getfenv>

[-0, +1, I<->]

 void lua_getfenv (lua_State *L, int index);

Pushes onto the stack the environment table of the value at the given
index.


=head2 C<lua_getfield>

[-0, +1, I<e>]

 void lua_getfield (lua_State *L, int index, const char *k);

Pushes onto the stack the value C<t[k]>, where C<t> is the value at the
given valid index. As in Lua, this function may trigger a metamethod
for the "index" event (see E<sect>2.8).


=head2 C<lua_getglobal>

[-0, +1, I<e>]

 void lua_getglobal (lua_State *L, const char *name);

Pushes onto the stack the value of the global C<name>. It is defined as
a macro:

     #define lua_getglobal(L,s)  lua_getfield(L, LUA_GLOBALSINDEX, s)


=head2 C<lua_getmetatable>

[-0, +(0|1), I<->]

 int lua_getmetatable (lua_State *L, int index);

Pushes onto the stack the metatable of the value at the given
acceptable index. If the index is not valid, or if the value does not
have a metatable, the function returns 0 and pushes nothing on the
stack.


=head2 C<lua_gettable>

[-1, +1, I<e>]

 void lua_gettable (lua_State *L, int index);

Pushes onto the stack the value C<t[k]>, where C<t> is the value at the
given valid index and C<k> is the value at the top of the stack.

This function pops the key from the stack (putting the resulting value
in its place). As in Lua, this function may trigger a metamethod for
the "index" event (see E<sect>2.8).


=head2 C<lua_gettop>

[-0, +0, I<->]

 int lua_gettop (lua_State *L);

Returns the index of the top element in the stack. Because indices
start at 1, this result is equal to the number of elements in the stack
(and so 0 means an empty stack).


=head2 C<lua_insert>

[-1, +1, I<->]

 void lua_insert (lua_State *L, int index);

Moves the top element into the given valid index, shifting up the
elements above this index to open space. Cannot be called with a
pseudo-index, because a pseudo-index is not an actual stack position.


=head2 C<lua_Integer>

 typedef ptrdiff_t lua_Integer;

The type used by the Lua API to represent integral values.

By default it is a C<ptrdiff_t>, which is usually the largest signed
integral type the machine handles "comfortably".


=head2 C<lua_isboolean>

[-0, +0, I<->]

 int lua_isboolean (lua_State *L, int index);

Returns 1 if the value at the given acceptable index has type boolean,
and 0 otherwise.


=head2 C<lua_iscfunction>

[-0, +0, I<->]

 int lua_iscfunction (lua_State *L, int index);

Returns 1 if the value at the given acceptable index is a C function,
and 0 otherwise.


=head2 C<lua_isfunction>

[-0, +0, I<->]

 int lua_isfunction (lua_State *L, int index);

Returns 1 if the value at the given acceptable index is a function
(either C or Lua), and 0 otherwise.


=head2 C<lua_islightuserdata>

[-0, +0, I<->]

 int lua_islightuserdata (lua_State *L, int index);

Returns 1 if the value at the given acceptable index is a light
userdata, and 0 otherwise.


=head2 C<lua_isnil>

[-0, +0, I<->]

 int lua_isnil (lua_State *L, int index);

Returns 1 if the value at the given acceptable index is B<nil>, and 0
otherwise.


=head2 C<lua_isnone>

[-0, +0, I<->]

 int lua_isnone (lua_State *L, int index);

Returns 1 if the given acceptable index is not valid (that is, it
refers to an element outside the current stack), and 0 otherwise.


=head2 C<lua_isnoneornil>

[-0, +0, I<->]

 int lua_isnoneornil (lua_State *L, int index);

Returns 1 if the given acceptable index is not valid (that is, it
refers to an element outside the current stack) or if the value at this
index is B<nil>, and 0 otherwise.


=head2 C<lua_isnumber>

[-0, +0, I<->]

 int lua_isnumber (lua_State *L, int index);

Returns 1 if the value at the given acceptable index is a number or a
string convertible to a number, and 0 otherwise.


=head2 C<lua_isstring>

[-0, +0, I<->]

 int lua_isstring (lua_State *L, int index);

Returns 1 if the value at the given acceptable index is a string or a
number (which is always convertible to a string), and 0 otherwise.


=head2 C<lua_istable>

[-0, +0, I<->]

 int lua_istable (lua_State *L, int index);

Returns 1 if the value at the given acceptable index is a table, and 0
otherwise.


=head2 C<lua_isthread>

[-0, +0, I<->]

 int lua_isthread (lua_State *L, int index);

Returns 1 if the value at the given acceptable index is a thread, and 0
otherwise.


=head2 C<lua_isuserdata>

[-0, +0, I<->]

 int lua_isuserdata (lua_State *L, int index);

Returns 1 if the value at the given acceptable index is a userdata
(either full or light), and 0 otherwise.


=head2 C<lua_lessthan>

[-0, +0, I<e>]

 int lua_lessthan (lua_State *L, int index1, int index2);

Returns 1 if the value at acceptable index C<index1> is smaller than
the value at acceptable index C<index2>, following the semantics of the
Lua C<E<lt>> operator (that is, may call metamethods). Otherwise
returns 0. Also returns 0 if any of the indices is non valid.


=head2 C<lua_load>

[-0, +1, I<->]

 int lua_load (lua_State *L,
               lua_Reader reader,
               void *data,
               const char *chunkname);

Loads a Lua chunk. If there are no errors, C<lua_load> pushes the
compiled chunk as a Lua function on top of the stack. Otherwise, it
pushes an error message. The return values of C<lua_load> are:

=over

=item * B<0:> no errors;

=item * B<C<LUA_ERRSYNTAX>:> syntax error during pre-compilation;

=item * B<C<LUA_ERRMEM>:> memory allocation error.

=back

This function only loads a chunk; it does not run it.

C<lua_load> automatically detects whether the chunk is text or binary,
and loads it accordingly (see program C<luac>).

The C<lua_load> function uses a user-supplied C<reader> function to
read the chunk (see C<lua_Reader>). The C<data> argument is an opaque
value passed to the reader function.

The C<chunkname> argument gives a name to the chunk, which is used for
error messages and in debug information (see E<sect>3.8).


=head2 C<lua_newstate>

[-0, +0, I<->]

 lua_State *lua_newstate (lua_Alloc f, void *ud);

Creates a new, independent state. Returns C<NULL> if cannot create the
state (due to lack of memory). The argument C<f> is the allocator
function; Lua does all memory allocation for this state through this
function. The second argument, C<ud>, is an opaque pointer that Lua
simply passes to the allocator in every call.


=head2 C<lua_newtable>

[-0, +1, I<m>]

 void lua_newtable (lua_State *L);

Creates a new empty table and pushes it onto the stack. It is
equivalent to C<lua_createtable(L, 0, 0)>.


=head2 C<lua_newthread>

[-0, +1, I<m>]

 lua_State *lua_newthread (lua_State *L);

Creates a new thread, pushes it on the stack, and returns a pointer to
a C<lua_State> that represents this new thread. The new state returned
by this function shares with the original state all global objects
(such as tables), but has an independent execution stack.

There is no explicit function to close or to destroy a thread. Threads
are subject to garbage collection, like any Lua object.


=head2 C<lua_newuserdata>

[-0, +1, I<m>]

 void *lua_newuserdata (lua_State *L, size_t size);

This function allocates a new block of memory with the given size,
pushes onto the stack a new full userdata with the block address, and
returns this address.

Userdata represent C values in Lua. A I<full userdata> represents a
block of memory. It is an object (like a table): you must create it, it
can have its own metatable, and you can detect when it is being
collected. A full userdata is only equal to itself (under raw
equality).

When Lua collects a full userdata with a C<gc> metamethod, Lua calls
the metamethod and marks the userdata as finalized. When this userdata
is collected again then Lua frees its corresponding memory.


=head2 C<lua_next>

[-1, +(2|0), I<e>]

 int lua_next (lua_State *L, int index);

Pops a key from the stack, and pushes a key-value pair from the table
at the given index (the "next" pair after the given key). If there are
no more elements in the table, then C<lua_next> returns 0 (and pushes
nothing).

A typical traversal looks like this:

     /* table is in the stack at index 't' */
     lua_pushnil(L);  /* first key */
     while (lua_next(L, t) != 0) {
       /* uses 'key' (at index -2) and 'value' (at index -1) */
       printf("%s - %s\n",
              lua_typename(L, lua_type(L, -2)),
              lua_typename(L, lua_type(L, -1)));
       /* removes 'value'; keeps 'key' for next iteration */
       lua_pop(L, 1);
     }

While traversing a table, do not call C<lua_tolstring> directly on a
key, unless you know that the key is actually a string. Recall that
C<lua_tolstring> I<changes> the value at the given index; this confuses
the next call to C<lua_next>.


=head2 C<lua_Number>

 typedef double lua_Number;

The type of numbers in Lua. By default, it is double, but that can be
changed in C<luaconf.h>.

Through the configuration file you can change Lua to operate with
another type for numbers (e.g., float or long).


=head2 C<lua_objlen>

[-0, +0, I<->]

 size_t lua_objlen (lua_State *L, int index);

Returns the "length" of the value at the given acceptable index: for
strings, this is the string length; for tables, this is the result of
the length operator ('C<#>'); for userdata, this is the size of the
block of memory allocated for the userdata; for other values, it is 0.


=head2 C<lua_pcall>

[-(nargs + 1), +(nresults|1), I<->]

 int lua_pcall (lua_State *L, int nargs, int nresults, int errfunc);

Calls a function in protected mode.

Both C<nargs> and C<nresults> have the same meaning as in C<lua_call>.
If there are no errors during the call, C<lua_pcall> behaves exactly
like C<lua_call>. However, if there is any error, C<lua_pcall> catches
it, pushes a single value on the stack (the error message), and returns
an error code. Like C<lua_call>, C<lua_pcall> always removes the
function and its arguments from the stack.

If C<errfunc> is 0, then the error message returned on the stack is
exactly the original error message. Otherwise, C<errfunc> is the stack
index of an I<error handler function>. (In the current implementation,
this index cannot be a pseudo-index.) In case of runtime errors, this
function will be called with the error message and its return value
will be the message returned on the stack by C<lua_pcall>.

Typically, the error handler function is used to add more debug
information to the error message, such as a stack traceback. Such
information cannot be gathered after the return of C<lua_pcall>, since
by then the stack has unwound.

The C<lua_pcall> function returns 0 in case of success or one of the
following error codes (defined in C<lua.h>):

=over

=item * B<C<LUA_ERRRUN>:> a runtime error.

=item * B<C<LUA_ERRMEM>:> memory allocation error. For such errors, Lua
does not call the error handler function.

=item * B<C<LUA_ERRERR>:> error while running the error handler
function.

=back


=head2 C<lua_pop>

[-n, +0, I<->]

 void lua_pop (lua_State *L, int n);

Pops C<n> elements from the stack.


=head2 C<lua_pushboolean>

[-0, +1, I<->]

 void lua_pushboolean (lua_State *L, int b);

Pushes a boolean value with value C<b> onto the stack.


=head2 C<lua_pushcclosure>

[-n, +1, I<m>]

 void lua_pushcclosure (lua_State *L, lua_CFunction fn, int n);

Pushes a new C closure onto the stack.

When a C function is created, it is possible to associate some values
with it, thus creating a C closure (see E<sect>3.4); these values are
then accessible to the function whenever it is called. To associate
values with a C function, first these values should be pushed onto the
stack (when there are multiple values, the first value is pushed
first). Then C<lua_pushcclosure> is called to create and push the C
function onto the stack, with the argument C<n> telling how many values
should be associated with the function. C<lua_pushcclosure> also pops
these values from the stack.

The maximum value for C<n> is 255.


=head2 C<lua_pushcfunction>

[-0, +1, I<m>]

 void lua_pushcfunction (lua_State *L, lua_CFunction f);

Pushes a C function onto the stack. This function receives a pointer to
a C function and pushes onto the stack a Lua value of type C<function>
that, when called, invokes the corresponding C function.

Any function to be registered in Lua must follow the correct protocol
to receive its parameters and return its results (see
C<lua_CFunction>).

C<lua_pushcfunction> is defined as a macro:

     #define lua_pushcfunction(L,f)  lua_pushcclosure(L,f,0)


=head2 C<lua_pushfstring>

[-0, +1, I<m>]

 const char *lua_pushfstring (lua_State *L, const char *fmt, ...);

Pushes onto the stack a formatted string and returns a pointer to this
string. It is similar to the C function C<sprintf>, but has some
important differences:

=over

=item * You do not have to allocate space for the result: the result is
a Lua string and Lua takes care of memory allocation (and deallocation,
through garbage collection).

=item * The conversion specifiers are quite restricted. There are no
flags, widths, or precisions. The conversion specifiers can only be
'C<%%>' (inserts a 'C<%>' in the string), 'C<%s>' (inserts a
zero-terminated string, with no size restrictions), 'C<%f>' (inserts a
C<lua_Number>), 'C<%p>' (inserts a pointer as a hexadecimal numeral),
'C<%d>' (inserts an C<int>), and 'C<%c>' (inserts an C<int> as a
character).

=back


=head2 C<lua_pushinteger>

[-0, +1, I<->]

 void lua_pushinteger (lua_State *L, lua_Integer n);

Pushes a number with value C<n> onto the stack.


=head2 C<lua_pushlightuserdata>

[-0, +1, I<->]

 void lua_pushlightuserdata (lua_State *L, void *p);

Pushes a light userdata onto the stack.

Userdata represent C values in Lua. A I<light userdata> represents a
pointer. It is a value (like a number): you do not create it, it has no
individual metatable, and it is not collected (as it was never
created). A light userdata is equal to "any" light userdata with the
same C address.


=head2 C<lua_pushliteral>

[-0, +1, I<m>]

 void lua_pushliteral (lua_State *L, const char *s);

This macro is equivalent to C<lua_pushlstring>, but can be used only
when C<s> is a literal string. In these cases, it automatically
provides the string length.


=head2 C<lua_pushlstring>

[-0, +1, I<m>]

 void lua_pushlstring (lua_State *L, const char *s, size_t len);

Pushes the string pointed to by C<s> with size C<len> onto the stack.
Lua makes (or reuses) an internal copy of the given string, so the
memory at C<s> can be freed or reused immediately after the function
returns. The string can contain embedded zeros.


=head2 C<lua_pushnil>

[-0, +1, I<->]

 void lua_pushnil (lua_State *L);

Pushes a nil value onto the stack.


=head2 C<lua_pushnumber>

[-0, +1, I<->]

 void lua_pushnumber (lua_State *L, lua_Number n);

Pushes a number with value C<n> onto the stack.


=head2 C<lua_pushstring>

[-0, +1, I<m>]

 void lua_pushstring (lua_State *L, const char *s);

Pushes the zero-terminated string pointed to by C<s> onto the stack.
Lua makes (or reuses) an internal copy of the given string, so the
memory at C<s> can be freed or reused immediately after the function
returns. The string cannot contain embedded zeros; it is assumed to end
at the first zero.


=head2 C<lua_pushthread>

[-0, +1, I<->]

 int lua_pushthread (lua_State *L);

Pushes the thread represented by C<L> onto the stack. Returns 1 if this
thread is the main thread of its state.


=head2 C<lua_pushvalue>

[-0, +1, I<->]

 void lua_pushvalue (lua_State *L, int index);

Pushes a copy of the element at the given valid index onto the stack.


=head2 C<lua_pushvfstring>

[-0, +1, I<m>]

 const char *lua_pushvfstring (lua_State *L,
                               const char *fmt,
                               va_list argp);

Equivalent to C<lua_pushfstring>, except that it receives a C<va_list>
instead of a variable number of arguments.


=head2 C<lua_rawequal>

[-0, +0, I<->]

 int lua_rawequal (lua_State *L, int index1, int index2);

Returns 1 if the two values in acceptable indices C<index1> and
C<index2> are primitively equal (that is, without calling metamethods).
Otherwise returns 0. Also returns 0 if any of the indices are non
valid.


=head2 C<lua_rawget>

[-1, +1, I<->]

 void lua_rawget (lua_State *L, int index);

Similar to C<lua_gettable>, but does a raw access (i.e., without
metamethods).


=head2 C<lua_rawgeti>

[-0, +1, I<->]

 void lua_rawgeti (lua_State *L, int index, int n);

Pushes onto the stack the value C<t[n]>, where C<t> is the value at the
given valid index. The access is raw; that is, it does not invoke
metamethods.


=head2 C<lua_rawset>

[-2, +0, I<m>]

 void lua_rawset (lua_State *L, int index);

Similar to C<lua_settable>, but does a raw assignment (i.e., without
metamethods).


=head2 C<lua_rawseti>

[-1, +0, I<m>]

 void lua_rawseti (lua_State *L, int index, int n);

Does the equivalent of C<t[n] = v>, where C<t> is the value at the
given valid index and C<v> is the value at the top of the stack.

This function pops the value from the stack. The assignment is raw;
that is, it does not invoke metamethods.


=head2 C<lua_Reader>

 typedef const char * (*lua_Reader) (lua_State *L,
                                     void *data,
                                     size_t *size);

The reader function used by C<lua_load>. Every time it needs another
piece of the chunk, C<lua_load> calls the reader, passing along its
C<data> parameter. The reader must return a pointer to a block of
memory with a new piece of the chunk and set C<size> to the block size.
The block must exist until the reader function is called again. To
signal the end of the chunk, the reader must return C<NULL> or set
C<size> to zero. The reader function may return pieces of any size
greater than zero.


=head2 C<lua_register>

[-0, +0, I<e>]

 void lua_register (lua_State *L,
                    const char *name,
                    lua_CFunction f);

Sets the C function C<f> as the new value of global C<name>. It is
defined as a macro:

     #define lua_register(L,n,f) \
            (lua_pushcfunction(L, f), lua_setglobal(L, n))


=head2 C<lua_remove>

[-1, +0, I<->]

 void lua_remove (lua_State *L, int index);

Removes the element at the given valid index, shifting down the
elements above this index to fill the gap. Cannot be called with a
pseudo-index, because a pseudo-index is not an actual stack position.


=head2 C<lua_replace>

[-1, +0, I<->]

 void lua_replace (lua_State *L, int index);

Moves the top element into the given position (and pops it), without
shifting any element (therefore replacing the value at the given
position).


=head2 C<lua_resume>

[-?, +?, I<->]

 int lua_resume (lua_State *L, int narg);

Starts and resumes a coroutine in a given thread.

To start a coroutine, you first create a new thread (see
C<lua_newthread>); then you push onto its stack the main function plus
any arguments; then you call C<lua_resume>, with C<narg> being the
number of arguments. This call returns when the coroutine suspends or
finishes its execution. When it returns, the stack contains all values
passed to C<lua_yield>, or all values returned by the body function.
C<lua_resume> returns C<LUA_YIELD> if the coroutine yields, 0 if the
coroutine finishes its execution without errors, or an error code in
case of errors (see C<lua_pcall>). In case of errors, the stack is not
unwound, so you can use the debug API over it. The error message is on
the top of the stack. To restart a coroutine, you put on its stack only
the values to be passed as results from C<yield>, and then call
C<lua_resume>.


=head2 C<lua_setallocf>

[-0, +0, I<->]

 void lua_setallocf (lua_State *L, lua_Alloc f, void *ud);

Changes the allocator function of a given state to C<f> with user data
C<ud>.


=head2 C<lua_setfenv>

[-1, +0, I<->]

 int lua_setfenv (lua_State *L, int index);

Pops a table from the stack and sets it as the new environment for the
value at the given index. If the value at the given index is neither a
function nor a thread nor a userdata, C<lua_setfenv> returns 0.
Otherwise it returns 1.


=head2 C<lua_setfield>

[-1, +0, I<e>]

 void lua_setfield (lua_State *L, int index, const char *k);

Does the equivalent to C<t[k] = v>, where C<t> is the value at the
given valid index and C<v> is the value at the top of the stack.

This function pops the value from the stack. As in Lua, this function
may trigger a metamethod for the "newindex" event (see E<sect>2.8).


=head2 C<lua_setglobal>

[-1, +0, I<e>]

 void lua_setglobal (lua_State *L, const char *name);

Pops a value from the stack and sets it as the new value of global
C<name>. It is defined as a macro:

     #define lua_setglobal(L,s)   lua_setfield(L, LUA_GLOBALSINDEX, s)


=head2 C<lua_setmetatable>

[-1, +0, I<->]

 int lua_setmetatable (lua_State *L, int index);

Pops a table from the stack and sets it as the new metatable for the
value at the given acceptable index.


=head2 C<lua_settable>

[-2, +0, I<e>]

 void lua_settable (lua_State *L, int index);

Does the equivalent to C<t[k] = v>, where C<t> is the value at the
given valid index, C<v> is the value at the top of the stack, and C<k>
is the value just below the top.

This function pops both the key and the value from the stack. As in
Lua, this function may trigger a metamethod for the "newindex" event
(see E<sect>2.8).


=head2 C<lua_settop>

[-?, +?, I<->]

 void lua_settop (lua_State *L, int index);

Accepts any acceptable index, or 0, and sets the stack top to this
index. If the new top is larger than the old one, then the new elements
are filled with B<nil>. If C<index> is 0, then all stack elements are
removed.


=head2 C<lua_State>

 typedef struct lua_State lua_State;

Opaque structure that keeps the whole state of a Lua interpreter. The
Lua library is fully reentrant: it has no global variables. All
information about a state is kept in this structure.

A pointer to this state must be passed as the first argument to every
function in the library, except to C<lua_newstate>, which creates a Lua
state from scratch.


=head2 C<lua_status>

[-0, +0, I<->]

 int lua_status (lua_State *L);

Returns the status of the thread C<L>.

The status can be 0 for a normal thread, an error code if the thread
finished its execution with an error, or C<LUA_YIELD> if the thread is
suspended.


=head2 C<lua_toboolean>

[-0, +0, I<->]

 int lua_toboolean (lua_State *L, int index);

Converts the Lua value at the given acceptable index to a C boolean
value (0 or 1). Like all tests in Lua, C<lua_toboolean> returns 1 for
any Lua value different from B<false> and B<nil>; otherwise it returns
0. It also returns 0 when called with a non-valid index. (If you want
to accept only actual boolean values, use C<lua_isboolean> to test the
value's type.)


=head2 C<lua_tocfunction>

[-0, +0, I<->]

 lua_CFunction lua_tocfunction (lua_State *L, int index);

Converts a value at the given acceptable index to a C function. That
value must be a C function; otherwise, returns C<NULL>.


=head2 C<lua_tointeger>

[-0, +0, I<->]

 lua_Integer lua_tointeger (lua_State *L, int index);

Converts the Lua value at the given acceptable index to the signed
integral type C<lua_Integer>. The Lua value must be a number or a
string convertible to a number (see E<sect>2.2.1); otherwise,
C<lua_tointeger> returns 0.

If the number is not an integer, it is truncated in some non-specified
way.


=head2 C<lua_tolstring>

[-0, +0, I<m>]

 const char *lua_tolstring (lua_State *L, int index, size_t *len);

Converts the Lua value at the given acceptable index to a C string. If
C<len> is not C<NULL>, it also sets C<*len> with the string length. The
Lua value must be a string or a number; otherwise, the function returns
C<NULL>. If the value is a number, then C<lua_tolstring> also I<changes
the actual value in the stack to a string>. (This change confuses
C<lua_next> when C<lua_tolstring> is applied to keys during a table
traversal.)

C<lua_tolstring> returns a fully aligned pointer to a string inside the
Lua state. This string always has a zero ('C<\0>') after its last
character (as in C), but can contain other zeros in its body. Because
Lua has garbage collection, there is no guarantee that the pointer
returned by C<lua_tolstring> will be valid after the corresponding
value is removed from the stack.


=head2 C<lua_tonumber>

[-0, +0, I<->]

 lua_Number lua_tonumber (lua_State *L, int index);

Converts the Lua value at the given acceptable index to the C type
C<lua_Number> (see C<lua_Number>). The Lua value must be a number or a
string convertible to a number (see E<sect>2.2.1); otherwise,
C<lua_tonumber> returns 0.


=head2 C<lua_topointer>

[-0, +0, I<->]

 const void *lua_topointer (lua_State *L, int index);

Converts the value at the given acceptable index to a generic C pointer
(C<void*>). The value can be a userdata, a table, a thread, or a
function; otherwise, C<lua_topointer> returns C<NULL>. Different
objects will give different pointers. There is no way to convert the
pointer back to its original value.

Typically this function is used only for debug information.


=head2 C<lua_tostring>

[-0, +0, I<m>]

 const char *lua_tostring (lua_State *L, int index);

Equivalent to C<lua_tolstring> with C<len> equal to C<NULL>.


=head2 C<lua_tothread>

[-0, +0, I<->]

 lua_State *lua_tothread (lua_State *L, int index);

Converts the value at the given acceptable index to a Lua thread
(represented as C<lua_State*>). This value must be a thread; otherwise,
the function returns C<NULL>.


=head2 C<lua_touserdata>

[-0, +0, I<->]

 void *lua_touserdata (lua_State *L, int index);

If the value at the given acceptable index is a full userdata, returns
its block address. If the value is a light userdata, returns its
pointer. Otherwise, returns C<NULL>.


=head2 C<lua_type>

[-0, +0, I<->]

 int lua_type (lua_State *L, int index);

Returns the type of the value in the given acceptable index, or
C<LUA_TNONE> for a non-valid index (that is, an index to an "empty"
stack position). The types returned by C<lua_type> are coded by the
following constants defined in C<lua.h>: C<LUA_TNIL>, C<LUA_TNUMBER>,
C<LUA_TBOOLEAN>, C<LUA_TSTRING>, C<LUA_TTABLE>, C<LUA_TFUNCTION>,
C<LUA_TUSERDATA>, C<LUA_TTHREAD>, and C<LUA_TLIGHTUSERDATA>.


=head2 C<lua_typename>

[-0, +0, I<->]

 const char *lua_typename  (lua_State *L, int tp);

Returns the name of the type encoded by the value C<tp>, which must be
one the values returned by C<lua_type>.


=head2 C<lua_Writer>

 typedef int (*lua_Writer) (lua_State *L,
                            const void* p,
                            size_t sz,
                            void* ud);

The type of the writer function used by C<lua_dump>. Every time it
produces another piece of chunk, C<lua_dump> calls the writer, passing
along the buffer to be written (C<p>), its size (C<sz>), and the
C<data> parameter supplied to C<lua_dump>.

The writer returns an error code: 0 means no errors; any other value
means an error and stops C<lua_dump> from calling the writer again.


=head2 C<lua_xmove>

[-?, +?, I<->]

 void lua_xmove (lua_State *from, lua_State *to, int n);

Exchange values between different threads of the I<same> global state.

This function pops C<n> values from the stack C<from>, and pushes them
onto the stack C<to>.


=head2 C<lua_yield>

[-?, +?, I<->]

 int lua_yield  (lua_State *L, int nresults);

Yields a coroutine.

This function should only be called as the return expression of a C
function, as follows:

     return lua_yield (L, nresults);

When a C function calls C<lua_yield> in that way, the running coroutine
suspends its execution, and the call to C<lua_resume> that started this
coroutine returns. The parameter C<nresults> is the number of values
from the stack that are passed as results to C<lua_resume>.

=head2 3.8 - The Debug Interface

Lua has no built-in debugging facilities. Instead, it offers a special
interface by means of functions and I<hooks>. This interface allows the
construction of different kinds of debuggers, profilers, and other
tools that need "inside information" from the interpreter.


=head2 C<lua_Debug>

 typedef struct lua_Debug {
   int event;
   const char *name;           /* (n) */
   const char *namewhat;       /* (n) */
   const char *what;           /* (S) */
   const char *source;         /* (S) */
   int currentline;            /* (l) */
   int nups;                   /* (u) number of upvalues */
   int linedefined;            /* (S) */
   int lastlinedefined;        /* (S) */
   char short_src[LUA_IDSIZE]; /* (S) */
   /* private part */
   other fields
 } lua_Debug;

A structure used to carry different pieces of information about an
active function. C<lua_getstack> fills only the private part of this
structure, for later use. To fill the other fields of C<lua_Debug> with
useful information, call C<lua_getinfo>.

The fields of C<lua_Debug> have the following meaning:

=over

=item * B<C<source>:> If the function was defined in a string, then
C<source> is that string. If the function was defined in a file, then
C<source> starts with a 'C<@>' followed by the file name.

=item * B<C<short_src>:> a "printable" version of C<source>, to be used
in error messages.

=item * B<C<linedefined>:> the line number where the definition of the
function starts.

=item * B<C<lastlinedefined>:> the line number where the definition of
the function ends.

=item * B<C<what>:> the string C<"Lua"> if the function is a Lua
function, C<"C"> if it is a C function, C<"main"> if it is the main
part of a chunk, and C<"tail"> if it was a function that did a tail
call. In the latter case, Lua has no other information about the
function.

=item * B<C<currentline>:> the current line where the given function is
executing. When no line information is available, C<currentline> is set
to -1.

=item * B<C<name>:> a reasonable name for the given function. Because
functions in Lua are first-class values, they do not have a fixed name:
some functions can be the value of multiple global variables, while
others can be stored only in a table field. The C<lua_getinfo> function
checks how the function was called to find a suitable name. If it
cannot find a name, then C<name> is set to C<NULL>.

=item * B<C<namewhat>:> explains the C<name> field. The value of
C<namewhat> can be C<"global">, C<"local">, C<"method">, C<"field">,
C<"upvalue">, or C<""> (the empty string), according to how the
function was called. (Lua uses the empty string when no other option
seems to apply.)

=item * B<C<nups>:> the number of upvalues of the function.

=back


=head2 C<lua_gethook>

[-0, +0, I<->]

 lua_Hook lua_gethook (lua_State *L);

Returns the current hook function.


=head2 C<lua_gethookcount>

[-0, +0, I<->]

 int lua_gethookcount (lua_State *L);

Returns the current hook count.


=head2 C<lua_gethookmask>

[-0, +0, I<->]

 int lua_gethookmask (lua_State *L);

Returns the current hook mask.


=head2 C<lua_getinfo>

[-(0|1), +(0|1|2), I<m>]

 int lua_getinfo (lua_State *L, const char *what, lua_Debug *ar);

Returns information about a specific function or function invocation.

To get information about a function invocation, the parameter C<ar>
must be a valid activation record that was filled by a previous call to
C<lua_getstack> or given as argument to a hook (see C<lua_Hook>).

To get information about a function you push it onto the stack and
start the C<what> string with the character 'C<E<gt>>'. (In that case,
C<lua_getinfo> pops the function in the top of the stack.) For
instance, to know in which line a function C<f> was defined, you can
write the following code:

     lua_Debug ar;
     lua_getfield(L, LUA_GLOBALSINDEX, "f");  /* get global 'f' */
     lua_getinfo(L, ">S", &ar);
     printf("%d\n", ar.linedefined);

Each character in the string C<what> selects some fields of the
structure C<ar> to be filled or a value to be pushed on the stack:

=over

=item * B<'C<n>':> fills in the field C<name> and C<namewhat>;

=item * B<'C<S>':> fills in the fields C<source>, C<short_src>,
C<linedefined>, C<lastlinedefined>, and C<what>;

=item * B<'C<l>':> fills in the field C<currentline>;

=item * B<'C<u>':> fills in the field C<nups>;

=item * B<'C<f>':> pushes onto the stack the function that is running
at the given level;

=item * B<'C<L>':> pushes onto the stack a table whose indices are the
numbers of the lines that are valid on the function. (A I<valid line>
is a line with some associated code, that is, a line where you can put
a break point. Non-valid lines include empty lines and comments.)

=back

This function returns 0 on error (for instance, an invalid option in
C<what>).


=head2 C<lua_getlocal>

[-0, +(0|1), I<->]

 const char *lua_getlocal (lua_State *L, lua_Debug *ar, int n);

Gets information about a local variable of a given activation record.
The parameter C<ar> must be a valid activation record that was filled
by a previous call to C<lua_getstack> or given as argument to a hook
(see C<lua_Hook>). The index C<n> selects which local variable to
inspect (1 is the first parameter or active local variable, and so on,
until the last active local variable). C<lua_getlocal> pushes the
variable's value onto the stack and returns its name.

Variable names starting with 'C<(>' (open parentheses) represent
internal variables (loop control variables, temporaries, and C function
locals).

Returns C<NULL> (and pushes nothing) when the index is greater than the
number of active local variables.


=head2 C<lua_getstack>

[-0, +0, I<->]

 int lua_getstack (lua_State *L, int level, lua_Debug *ar);

Get information about the interpreter runtime stack.

This function fills parts of a C<lua_Debug> structure with an
identification of the I<activation record> of the function executing at
a given level. Level 0 is the current running function, whereas level
I<n+1> is the function that has called level I<n>. When there are no
errors, C<lua_getstack> returns 1; when called with a level greater
than the stack depth, it returns 0.


=head2 C<lua_getupvalue>

[-0, +(0|1), I<->]

 const char *lua_getupvalue (lua_State *L, int funcindex, int n);

Gets information about a closure's upvalue. (For Lua functions,
upvalues are the external local variables that the function uses, and
that are consequently included in its closure.) C<lua_getupvalue> gets
the index C<n> of an upvalue, pushes the upvalue's value onto the
stack, and returns its name. C<funcindex> points to the closure in the
stack. (Upvalues have no particular order, as they are active through
the whole function. So, they are numbered in an arbitrary order.)

Returns C<NULL> (and pushes nothing) when the index is greater than the
number of upvalues. For C functions, this function uses the empty
string C<""> as a name for all upvalues.


=head2 C<lua_Hook>

 typedef void (*lua_Hook) (lua_State *L, lua_Debug *ar);

Type for debugging hook functions.

Whenever a hook is called, its C<ar> argument has its field C<event>
set to the specific event that triggered the hook. Lua identifies these
events with the following constants: C<LUA_HOOKCALL>, C<LUA_HOOKRET>,
C<LUA_HOOKTAILRET>, C<LUA_HOOKLINE>, and C<LUA_HOOKCOUNT>. Moreover,
for line events, the field C<currentline> is also set. To get the value
of any other field in C<ar>, the hook must call C<lua_getinfo>. For
return events, C<event> can be C<LUA_HOOKRET>, the normal value, or
C<LUA_HOOKTAILRET>. In the latter case, Lua is simulating a return from
a function that did a tail call; in this case, it is useless to call
C<lua_getinfo>.

While Lua is running a hook, it disables other calls to hooks.
Therefore, if a hook calls back Lua to execute a function or a chunk,
this execution occurs without any calls to hooks.


=head2 C<lua_sethook>

[-0, +0, I<->]

 int lua_sethook (lua_State *L, lua_Hook f, int mask, int count);

Sets the debugging hook function.

Argument C<f> is the hook function. C<mask> specifies on which events
the hook will be called: it is formed by a bitwise or of the constants
C<LUA_MASKCALL>, C<LUA_MASKRET>, C<LUA_MASKLINE>, and C<LUA_MASKCOUNT>.
The C<count> argument is only meaningful when the mask includes
C<LUA_MASKCOUNT>. For each event, the hook is called as explained
below:

=over

=item * B<The call hook:> is called when the interpreter calls a
function. The hook is called just after Lua enters the new function,
before the function gets its arguments.

=item * B<The return hook:> is called when the interpreter returns from
a function. The hook is called just before Lua leaves the function. You
have no access to the values to be returned by the function.

=item * B<The line hook:> is called when the interpreter is about to
start the execution of a new line of code, or when it jumps back in the
code (even to the same line). (This event only happens while Lua is
executing a Lua function.)

=item * B<The count hook:> is called after the interpreter executes
every C<count> instructions. (This event only happens while Lua is
executing a Lua function.)

=back

A hook is disabled by setting C<mask> to zero.


=head2 C<lua_setlocal>

[-(0|1), +0, I<->]

 const char *lua_setlocal (lua_State *L, lua_Debug *ar, int n);

Sets the value of a local variable of a given activation record.
Parameters C<ar> and C<n> are as in C<lua_getlocal> (see
C<lua_getlocal>). C<lua_setlocal> assigns the value at the top of the
stack to the variable and returns its name. It also pops the value from
the stack.

Returns C<NULL> (and pops nothing) when the index is greater than the
number of active local variables.


=head2 C<lua_setupvalue>

[-(0|1), +0, I<->]

 const char *lua_setupvalue (lua_State *L, int funcindex, int n);

Sets the value of a closure's upvalue. It assigns the value at the top
of the stack to the upvalue and returns its name. It also pops the
value from the stack. Parameters C<funcindex> and C<n> are as in the
C<lua_getupvalue> (see C<lua_getupvalue>).

Returns C<NULL> (and pops nothing) when the index is greater than the
number of upvalues.

=head1 4 - The Auxiliary Library

The I<auxiliary library> provides several convenient functions to
interface C with Lua. While the basic API provides the primitive
functions for all interactions between C and Lua, the auxiliary library
provides higher-level functions for some common tasks.

All functions from the auxiliary library are defined in header file
C<lauxlib.h> and have a prefix C<luaL_>.

All functions in the auxiliary library are built on top of the basic
API, and so they provide nothing that cannot be done with this API.

Several functions in the auxiliary library are used to check C function
arguments. Their names are always C<luaL_check*> or C<luaL_opt*>. All
of these functions throw an error if the check is not satisfied.
Because the error message is formatted for arguments (e.g., "C<bad
argument #1>"), you should not use these functions for other stack
values.

=head2 4.1 - Functions and Types

Here we list all functions and types from the auxiliary library in
alphabetical order.


=head2 C<luaL_addchar>

[-0, +0, I<m>]

 void luaL_addchar (luaL_Buffer *B, char c);

Adds the character C<c> to the buffer C<B> (see C<luaL_Buffer>).


=head2 C<luaL_addlstring>

[-0, +0, I<m>]

 void luaL_addlstring (luaL_Buffer *B, const char *s, size_t l);

Adds the string pointed to by C<s> with length C<l> to the buffer C<B>
(see C<luaL_Buffer>). The string may contain embedded zeros.


=head2 C<luaL_addsize>

[-0, +0, I<m>]

 void luaL_addsize (luaL_Buffer *B, size_t n);

Adds to the buffer C<B> (see C<luaL_Buffer>) a string of length C<n>
previously copied to the buffer area (see C<luaL_prepbuffer>).


=head2 C<luaL_addstring>

[-0, +0, I<m>]

 void luaL_addstring (luaL_Buffer *B, const char *s);

Adds the zero-terminated string pointed to by C<s> to the buffer C<B>
(see C<luaL_Buffer>). The string may not contain embedded zeros.


=head2 C<luaL_addvalue>

[-1, +0, I<m>]

 void luaL_addvalue (luaL_Buffer *B);

Adds the value at the top of the stack to the buffer C<B> (see
C<luaL_Buffer>). Pops the value.

This is the only function on string buffers that can (and must) be
called with an extra element on the stack, which is the value to be
added to the buffer.


=head2 C<luaL_argcheck>

[-0, +0, I<v>]

 void luaL_argcheck (lua_State *L,
                     int cond,
                     int narg,
                     const char *extramsg);

Checks whether C<cond> is true. If not, raises an error with the
following message, where C<func> is retrieved from the call stack:

     bad argument #<narg> to <func> (<extramsg>)


=head2 C<luaL_argerror>

[-0, +0, I<v>]

 int luaL_argerror (lua_State *L, int narg, const char *extramsg);

Raises an error with the following message, where C<func> is retrieved
from the call stack:

     bad argument #<narg> to <func> (<extramsg>)

This function never returns, but it is an idiom to use it in C
functions as C<return luaL_argerror(I<args>)>.


=head2 C<luaL_Buffer>

 typedef struct luaL_Buffer luaL_Buffer;

Type for a I<string buffer>.

A string buffer allows C code to build Lua strings piecemeal. Its
pattern of use is as follows:

=over

=item * First you declare a variable C<b> of type C<luaL_Buffer>.

=item * Then you initialize it with a call C<luaL_buffinit(L, &b)>.

=item * Then you add string pieces to the buffer calling any of the
C<luaL_add*> functions.

=item * You finish by calling C<luaL_pushresult(&b)>. This call leaves
the final string on the top of the stack.

=back

During its normal operation, a string buffer uses a variable number of
stack slots. So, while using a buffer, you cannot assume that you know
where the top of the stack is. You can use the stack between successive
calls to buffer operations as long as that use is balanced; that is,
when you call a buffer operation, the stack is at the same level it was
immediately after the previous buffer operation. (The only exception to
this rule is C<luaL_addvalue>.) After calling C<luaL_pushresult> the
stack is back to its level when the buffer was initialized, plus the
final string on its top.


=head2 C<luaL_buffinit>

[-0, +0, I<->]

 void luaL_buffinit (lua_State *L, luaL_Buffer *B);

Initializes a buffer C<B>. This function does not allocate any space;
the buffer must be declared as a variable (see C<luaL_Buffer>).


=head2 C<luaL_callmeta>

[-0, +(0|1), I<e>]

 int luaL_callmeta (lua_State *L, int obj, const char *e);

Calls a metamethod.

If the object at index C<obj> has a metatable and this metatable has a
field C<e>, this function calls this field and passes the object as its
only argument. In this case this function returns 1 and pushes onto the
stack the value returned by the call. If there is no metatable or no
metamethod, this function returns 0 (without pushing any value on the
stack).


=head2 C<luaL_checkany>

[-0, +0, I<v>]

 void luaL_checkany (lua_State *L, int narg);

Checks whether the function has an argument of any type (including
B<nil>) at position C<narg>.


=head2 C<luaL_checkint>

[-0, +0, I<v>]

 int luaL_checkint (lua_State *L, int narg);

Checks whether the function argument C<narg> is a number and returns
this number cast to an C<int>.


=head2 C<luaL_checkinteger>

[-0, +0, I<v>]

 lua_Integer luaL_checkinteger (lua_State *L, int narg);

Checks whether the function argument C<narg> is a number and returns
this number cast to a C<lua_Integer>.


=head2 C<luaL_checklong>

[-0, +0, I<v>]

 long luaL_checklong (lua_State *L, int narg);

Checks whether the function argument C<narg> is a number and returns
this number cast to a C<long>.


=head2 C<luaL_checklstring>

[-0, +0, I<v>]

 const char *luaL_checklstring (lua_State *L, int narg, size_t *l);

Checks whether the function argument C<narg> is a string and returns
this string; if C<l> is not C<NULL> fills C<*l> with the string's
length.

This function uses C<lua_tolstring> to get its result, so all
conversions and caveats of that function apply here.


=head2 C<luaL_checknumber>

[-0, +0, I<v>]

 lua_Number luaL_checknumber (lua_State *L, int narg);

Checks whether the function argument C<narg> is a number and returns
this number.


=head2 C<luaL_checkoption>

[-0, +0, I<v>]

 int luaL_checkoption (lua_State *L,
                       int narg,
                       const char *def,
                       const char *const lst[]);

Checks whether the function argument C<narg> is a string and searches
for this string in the array C<lst> (which must be NULL-terminated).
Returns the index in the array where the string was found. Raises an
error if the argument is not a string or if the string cannot be found.

If C<def> is not C<NULL>, the function uses C<def> as a default value
when there is no argument C<narg> or if this argument is B<nil>.

This is a useful function for mapping strings to C enums. (The usual
convention in Lua libraries is to use strings instead of numbers to
select options.)


=head2 C<luaL_checkstack>

[-0, +0, I<v>]

 void luaL_checkstack (lua_State *L, int sz, const char *msg);

Grows the stack size to C<top + sz> elements, raising an error if the
stack cannot grow to that size. C<msg> is an additional text to go into
the error message.


=head2 C<luaL_checkstring>

[-0, +0, I<v>]

 const char *luaL_checkstring (lua_State *L, int narg);

Checks whether the function argument C<narg> is a string and returns
this string.

This function uses C<lua_tolstring> to get its result, so all
conversions and caveats of that function apply here.


=head2 C<luaL_checktype>

[-0, +0, I<v>]

 void luaL_checktype (lua_State *L, int narg, int t);

Checks whether the function argument C<narg> has type C<t>. See
C<lua_type> for the encoding of types for C<t>.


=head2 C<luaL_checkudata>

[-0, +0, I<v>]

 void *luaL_checkudata (lua_State *L, int narg, const char *tname);

Checks whether the function argument C<narg> is a userdata of the type
C<tname> (see C<luaL_newmetatable>).


=head2 C<luaL_dofile>

[-0, +?, I<m>]

 int luaL_dofile (lua_State *L, const char *filename);

Loads and runs the given file. It is defined as the following macro:

     (luaL_loadfile(L, filename) || lua_pcall(L, 0, LUA_MULTRET, 0))

It returns 0 if there are no errors or 1 in case of errors.


=head2 C<luaL_dostring>

[-0, +?, I<m>]

 int luaL_dostring (lua_State *L, const char *str);

Loads and runs the given string. It is defined as the following macro:

     (luaL_loadstring(L, str) || lua_pcall(L, 0, LUA_MULTRET, 0))

It returns 0 if there are no errors or 1 in case of errors.


=head2 C<luaL_error>

[-0, +0, I<v>]

 int luaL_error (lua_State *L, const char *fmt, ...);

Raises an error. The error message format is given by C<fmt> plus any
extra arguments, following the same rules of C<lua_pushfstring>. It
also adds at the beginning of the message the file name and the line
number where the error occurred, if this information is available.

This function never returns, but it is an idiom to use it in C
functions as C<return luaL_error(I<args>)>.


=head2 C<luaL_getmetafield>

[-0, +(0|1), I<m>]

 int luaL_getmetafield (lua_State *L, int obj, const char *e);

Pushes onto the stack the field C<e> from the metatable of the object
at index C<obj>. If the object does not have a metatable, or if the
metatable does not have this field, returns 0 and pushes nothing.


=head2 C<luaL_getmetatable>

[-0, +1, I<->]

 void luaL_getmetatable (lua_State *L, const char *tname);

Pushes onto the stack the metatable associated with name C<tname> in
the registry (see C<luaL_newmetatable>).


=head2 C<luaL_gsub>

[-0, +1, I<m>]

 const char *luaL_gsub (lua_State *L,
                        const char *s,
                        const char *p,
                        const char *r);

Creates a copy of string C<s> by replacing any occurrence of the string
C<p> with the string C<r>. Pushes the resulting string on the stack and
returns it.


=head2 C<luaL_loadbuffer>

[-0, +1, I<m>]

 int luaL_loadbuffer (lua_State *L,
                      const char *buff,
                      size_t sz,
                      const char *name);

Loads a buffer as a Lua chunk. This function uses C<lua_load> to load
the chunk in the buffer pointed to by C<buff> with size C<sz>.

This function returns the same results as C<lua_load>. C<name> is the
chunk name, used for debug information and error messages.


=head2 C<luaL_loadfile>

[-0, +1, I<m>]

 int luaL_loadfile (lua_State *L, const char *filename);

Loads a file as a Lua chunk. This function uses C<lua_load> to load the
chunk in the file named C<filename>. If C<filename> is C<NULL>, then it
loads from the standard input. The first line in the file is ignored if
it starts with a C<#>.

This function returns the same results as C<lua_load>, but it has an
extra error code C<LUA_ERRFILE> if it cannot open/read the file.

As C<lua_load>, this function only loads the chunk; it does not run it.


=head2 C<luaL_loadstring>

[-0, +1, I<m>]

 int luaL_loadstring (lua_State *L, const char *s);

Loads a string as a Lua chunk. This function uses C<lua_load> to load
the chunk in the zero-terminated string C<s>.

This function returns the same results as C<lua_load>.

Also as C<lua_load>, this function only loads the chunk; it does not
run it.


=head2 C<luaL_newmetatable>

[-0, +1, I<m>]

 int luaL_newmetatable (lua_State *L, const char *tname);

If the registry already has the key C<tname>, returns 0. Otherwise,
creates a new table to be used as a metatable for userdata, adds it to
the registry with key C<tname>, and returns 1.

In both cases pushes onto the stack the final value associated with
C<tname> in the registry.


=head2 C<luaL_newstate>

[-0, +0, I<->]

 lua_State *luaL_newstate (void);

Creates a new Lua state. It calls C<lua_newstate> with an allocator
based on the standard C C<realloc> function and then sets a panic
function (see C<lua_atpanic>) that prints an error message to the
standard error output in case of fatal errors.

Returns the new state, or C<NULL> if there is a memory allocation
error.


=head2 C<luaL_openlibs>

[-0, +0, I<m>]

 void luaL_openlibs (lua_State *L);

Opens all standard Lua libraries into the given state.


=head2 C<luaL_optint>

[-0, +0, I<v>]

 int luaL_optint (lua_State *L, int narg, int d);

If the function argument C<narg> is a number, returns this number cast
to an C<int>. If this argument is absent or is B<nil>, returns C<d>.
Otherwise, raises an error.


=head2 C<luaL_optinteger>

[-0, +0, I<v>]

 lua_Integer luaL_optinteger (lua_State *L,
                              int narg,
                              lua_Integer d);

If the function argument C<narg> is a number, returns this number cast
to a C<lua_Integer>. If this argument is absent or is B<nil>, returns
C<d>. Otherwise, raises an error.


=head2 C<luaL_optlong>

[-0, +0, I<v>]

 long luaL_optlong (lua_State *L, int narg, long d);

If the function argument C<narg> is a number, returns this number cast
to a C<long>. If this argument is absent or is B<nil>, returns C<d>.
Otherwise, raises an error.


=head2 C<luaL_optlstring>

[-0, +0, I<v>]

 const char *luaL_optlstring (lua_State *L,
                              int narg,
                              const char *d,
                              size_t *l);

If the function argument C<narg> is a string, returns this string. If
this argument is absent or is B<nil>, returns C<d>. Otherwise, raises
an error.

If C<l> is not C<NULL>, fills the position C<*l> with the results's
length.


=head2 C<luaL_optnumber>

[-0, +0, I<v>]

 lua_Number luaL_optnumber (lua_State *L, int narg, lua_Number d);

If the function argument C<narg> is a number, returns this number. If
this argument is absent or is B<nil>, returns C<d>. Otherwise, raises
an error.


=head2 C<luaL_optstring>

[-0, +0, I<v>]

 const char *luaL_optstring (lua_State *L,
                             int narg,
                             const char *d);

If the function argument C<narg> is a string, returns this string. If
this argument is absent or is B<nil>, returns C<d>. Otherwise, raises
an error.


=head2 C<luaL_prepbuffer>

[-0, +0, I<->]

 char *luaL_prepbuffer (luaL_Buffer *B);

Returns an address to a space of size C<LUAL_BUFFERSIZE> where you can
copy a string to be added to buffer C<B> (see C<luaL_Buffer>). After
copying the string into this space you must call C<luaL_addsize> with
the size of the string to actually add it to the buffer.


=head2 C<luaL_pushresult>

[-?, +1, I<m>]

 void luaL_pushresult (luaL_Buffer *B);

Finishes the use of buffer C<B> leaving the final string on the top of
the stack.


=head2 C<luaL_ref>

[-1, +0, I<m>]

 int luaL_ref (lua_State *L, int t);

Creates and returns a I<reference>, in the table at index C<t>, for the
object at the top of the stack (and pops the object).

A reference is a unique integer key. As long as you do not manually add
integer keys into table C<t>, C<luaL_ref> ensures the uniqueness of the
key it returns. You can retrieve an object referred by reference C<r>
by calling C<lua_rawgeti(L, t, r)>. Function C<luaL_unref> frees a
reference and its associated object.

If the object at the top of the stack is B<nil>, C<luaL_ref> returns
the constant C<LUA_REFNIL>. The constant C<LUA_NOREF> is guaranteed to
be different from any reference returned by C<luaL_ref>.


=head2 C<luaL_Reg>

 typedef struct luaL_Reg {
   const char *name;
   lua_CFunction func;
 } luaL_Reg;

Type for arrays of functions to be registered by C<luaL_register>.
C<name> is the function name and C<func> is a pointer to the function.
Any array of C<luaL_Reg> must end with an sentinel entry in which both
C<name> and C<func> are C<NULL>.


=head2 C<luaL_register>

[-(0|1), +1, I<m>]

 void luaL_register (lua_State *L,
                     const char *libname,
                     const luaL_Reg *l);

Opens a library.

When called with C<libname> equal to C<NULL>, it simply registers all
functions in the list C<l> (see C<luaL_Reg>) into the table on the top
of the stack.

When called with a non-null C<libname>, C<luaL_register> creates a new
table C<t>, sets it as the value of the global variable C<libname>,
sets it as the value of C<package.loaded[libname]>, and registers on it
all functions in the list C<l>. If there is a table in
C<package.loaded[libname]> or in variable C<libname>, reuses this table
instead of creating a new one.

In any case the function leaves the table on the top of the stack.


=head2 C<luaL_typename>

[-0, +0, I<->]

 const char *luaL_typename (lua_State *L, int index);

Returns the name of the type of the value at the given index.


=head2 C<luaL_typerror>

[-0, +0, I<v>]

 int luaL_typerror (lua_State *L, int narg, const char *tname);

Generates an error with a message like the following:

     location: bad argument narg to 'func' (tname expected, got rt)

where C<I<location>> is produced by C<luaL_where>, C<I<func>> is the
name of the current function, and C<I<rt>> is the type name of the
actual argument.


=head2 C<luaL_unref>

[-0, +0, I<->]

 void luaL_unref (lua_State *L, int t, int ref);

Releases reference C<ref> from the table at index C<t> (see
C<luaL_ref>). The entry is removed from the table, so that the referred
object can be collected. The reference C<ref> is also freed to be used
again.

If C<ref> is C<LUA_NOREF> or C<LUA_REFNIL>, C<luaL_unref> does nothing.


=head2 C<luaL_where>

[-0, +1, I<m>]

 void luaL_where (lua_State *L, int lvl);

Pushes onto the stack a string identifying the current position of the
control at level C<lvl> in the call stack. Typically this string has
the following format:

     chunkname:currentline:

Level 0 is the running function, level 1 is the function that called
the running function, etc.

This function is used to build a prefix for error messages.

=head1 5 - Standard Libraries

The standard Lua libraries provide useful functions that are
implemented directly through the C API. Some of these functions provide
essential services to the language (e.g., C<type> and C<getmetatable>);
others provide access to "outside" services (e.g., I/O); and others
could be implemented in Lua itself, but are quite useful or have
critical performance requirements that deserve an implementation in C
(e.g., C<table.sort>).

All libraries are implemented through the official C API and are
provided as separate C modules. Currently, Lua has the following
standard libraries:

=over

=item * basic library, which includes the coroutine sub-library;

=item * package library;

=item * string manipulation;

=item * table manipulation;

=item * mathematical functions (sin, log, etc.);

=item * input and output;

=item * operating system facilities;

=item * debug facilities.

=back

Except for the basic and package libraries, each library provides all
its functions as fields of a global table or as methods of its objects.

To have access to these libraries, the C host program should call the
C<luaL_openlibs> function, which opens all standard libraries.
Alternatively, it can open them individually by calling C<luaopen_base>
(for the basic library), C<luaopen_package> (for the package library),
C<luaopen_string> (for the string library), C<luaopen_table> (for the
table library), C<luaopen_math> (for the mathematical library),
C<luaopen_io> (for the I/O library), C<luaopen_os> (for the Operating
System library), and C<luaopen_debug> (for the debug library). These
functions are declared in C<lualib.h> and should not be called
directly: you must call them like any other Lua C function, e.g., by
using C<lua_call>.

=head2 5.1 - Basic Functions

The basic library provides some core functions to Lua. If you do not
include this library in your application, you should check carefully
whether you need to provide implementations for some of its facilities.


=head2 C<assert (v [, message])>

Issues an error when the value of its argument C<v> is false (i.e.,
B<nil> or B<false>); otherwise, returns all its arguments. C<message>
is an error message; when absent, it defaults to "assertion failed!"


=head2 C<collectgarbage ([opt [, arg]])>

This function is a generic interface to the garbage collector. It
performs different functions according to its first argument, C<opt>:

=over

=item * B<"collect":> performs a full garbage-collection cycle. This is
the default option.

=item * B<"stop":> stops the garbage collector.

=item * B<"restart":> restarts the garbage collector.

=item * B<"count":> returns the total memory in use by Lua (in Kbytes).

=item * B<"step":> performs a garbage-collection step. The step "size"
is controlled by C<arg> (larger values mean more steps) in a
non-specified way. If you want to control the step size you must
experimentally tune the value of C<arg>. Returns B<true> if the step
finished a collection cycle.

=item * B<"setpause":> sets C<arg> as the new value for the I<pause> of
the collector (see E<sect>2.10). Returns the previous value for
I<pause>.

=item * B<"setstepmul":> sets C<arg> as the new value for the I<step
multiplier> of the collector (see E<sect>2.10). Returns the previous
value for I<step>.

=back


=head2 C<dofile ([filename])>

Opens the named file and executes its contents as a Lua chunk. When
called without arguments, C<dofile> executes the contents of the
standard input (C<stdin>). Returns all values returned by the chunk. In
case of errors, C<dofile> propagates the error to its caller (that is,
C<dofile> does not run in protected mode).


=head2 C<error (message [, level])>

Terminates the last protected function called and returns C<message> as
the error message. Function C<error> never returns.

Usually, C<error> adds some information about the error position at the
beginning of the message. The C<level> argument specifies how to get
the error position. With level 1 (the default), the error position is
where the C<error> function was called. Level 2 points the error to
where the function that called C<error> was called; and so on. Passing
a level 0 avoids the addition of error position information to the
message.


=head2 C<_G>

A global variable (not a function) that holds the global environment
(that is, C<_G._G = _G>). Lua itself does not use this variable;
changing its value does not affect any environment, nor vice-versa.
(Use C<setfenv> to change environments.)


=head2 C<getfenv ([f])>

Returns the current environment in use by the function. C<f> can be a
Lua function or a number that specifies the function at that stack
level: Level 1 is the function calling C<getfenv>. If the given
function is not a Lua function, or if C<f> is 0, C<getfenv> returns the
global environment. The default for C<f> is 1.


=head2 C<getmetatable (object)>

If C<object> does not have a metatable, returns B<nil>. Otherwise, if
the object's metatable has a C<"__metatable"> field, returns the
associated value. Otherwise, returns the metatable of the given object.


=head2 C<ipairs (t)>

Returns three values: an iterator function, the table C<t>, and 0, so
that the construction

     for i,v in ipairs(t) do body end

will iterate over the pairs (C<1,t[1]>), (C<2,t[2]>),
E<middot>E<middot>E<middot>, up to the first integer key absent from
the table.


=head2 C<load (func [, chunkname])>

Loads a chunk using function C<func> to get its pieces. Each call to
C<func> must return a string that concatenates with previous results. A
return of an empty string, B<nil>, or no value signals the end of the
chunk.

If there are no errors, returns the compiled chunk as a function;
otherwise, returns B<nil> plus the error message. The environment of
the returned function is the global environment.

C<chunkname> is used as the chunk name for error messages and debug
information. When absent, it defaults to "C<=(load)>".


=head2 C<loadfile ([filename])>

Similar to C<load>, but gets the chunk from file C<filename> or from
the standard input, if no file name is given.


=head2 C<loadstring (string [, chunkname])>

Similar to C<load>, but gets the chunk from the given string.

To load and run a given string, use the idiom

     assert(loadstring(s))()

When absent, C<chunkname> defaults to the given string.


=head2 C<next (table [, index])>

Allows a program to traverse all fields of a table. Its first argument
is a table and its second argument is an index in this table. C<next>
returns the next index of the table and its associated value. When
called with B<nil> as its second argument, C<next> returns an initial
index and its associated value. When called with the last index, or
with B<nil> in an empty table, C<next> returns B<nil>. If the second
argument is absent, then it is interpreted as B<nil>. In particular,
you can use C<next(t)> to check whether a table is empty.

The order in which the indices are enumerated is not specified, I<even
for numeric indices>. (To traverse a table in numeric order, use a
numerical B<for> or the C<ipairs> function.)

The behavior of C<next> is I<undefined> if, during the traversal, you
assign any value to a non-existent field in the table. You may however
modify existing fields. In particular, you may clear existing fields.


=head2 C<pairs (t)>

Returns three values: the C<next> function, the table C<t>, and B<nil>,
so that the construction

     for k,v in pairs(t) do body end

will iterate over all keyE<ndash>value pairs of table C<t>.

See function C<next> for the caveats of modifying the table during its
traversal.


=head2 C<pcall (f, arg1, E<middot>E<middot>E<middot>)>

Calls function C<f> with the given arguments in I<protected mode>. This
means that any error inside C<f> is not propagated; instead, C<pcall>
catches the error and returns a status code. Its first result is the
status code (a boolean), which is true if the call succeeds without
errors. In such case, C<pcall> also returns all results from the call,
after this first result. In case of any error, C<pcall> returns
B<false> plus the error message.


=head2 C<print (E<middot>E<middot>E<middot>)>

Receives any number of arguments, and prints their values to C<stdout>,
using the C<tostring> function to convert them to strings. C<print> is
not intended for formatted output, but only as a quick way to show a
value, typically for debugging. For formatted output, use
C<string.format>.


=head2 C<rawequal (v1, v2)>

Checks whether C<v1> is equal to C<v2>, without invoking any
metamethod. Returns a boolean.


=head2 C<rawget (table, index)>

Gets the real value of C<table[index]>, without invoking any
metamethod. C<table> must be a table; C<index> may be any value.


=head2 C<rawset (table, index, value)>

Sets the real value of C<table[index]> to C<value>, without invoking
any metamethod. C<table> must be a table, C<index> any value different
from B<nil>, and C<value> any Lua value.

This function returns C<table>.


=head2 C<select (index, E<middot>E<middot>E<middot>)>

If C<index> is a number, returns all arguments after argument number
C<index>. Otherwise, C<index> must be the string C<"#">, and C<select>
returns the total number of extra arguments it received.


=head2 C<setfenv (f, table)>

Sets the environment to be used by the given function. C<f> can be a
Lua function or a number that specifies the function at that stack
level: Level 1 is the function calling C<setfenv>. C<setfenv> returns
the given function.

As a special case, when C<f> is 0 C<setfenv> changes the environment of
the running thread. In this case, C<setfenv> returns no values.


=head2 C<setmetatable (table, metatable)>

Sets the metatable for the given table. (You cannot change the
metatable of other types from Lua, only from C.) If C<metatable> is
B<nil>, removes the metatable of the given table. If the original
metatable has a C<"__metatable"> field, raises an error.

This function returns C<table>.


=head2 C<tonumber (e [, base])>

Tries to convert its argument to a number. If the argument is already a
number or a string convertible to a number, then C<tonumber> returns
this number; otherwise, it returns B<nil>.

An optional argument specifies the base to interpret the numeral. The
base may be any integer between 2 and 36, inclusive. In bases above 10,
the letter 'C<A>' (in either upper or lower case) represents 10, 'C<B>'
represents 11, and so forth, with 'C<Z>' representing 35. In base 10
(the default), the number can have a decimal part, as well as an
optional exponent part (see E<sect>2.1). In other bases, only unsigned
integers are accepted.


=head2 C<tostring (e)>

Receives an argument of any type and converts it to a string in a
reasonable format. For complete control of how numbers are converted,
use C<string.format>.

If the metatable of C<e> has a C<"__tostring"> field, then C<tostring>
calls the corresponding value with C<e> as argument, and uses the
result of the call as its result.


=head2 C<type (v)>

Returns the type of its only argument, coded as a string. The possible
results of this function are "C<nil>" (a string, not the value B<nil>),
"C<number>", "C<string>", "C<boolean>", "C<table>", "C<function>",
"C<thread>", and "C<userdata>".


=head2 C<unpack (list [, i [, j]])>

Returns the elements from the given table. This function is equivalent
to

     return list[i], list[i+1], ···, list[j]

except that the above code can be written only for a fixed number of
elements. By default, C<i> is 1 and C<j> is the length of the list, as
defined by the length operator (see E<sect>2.5.5).


=head2 C<_VERSION>

A global variable (not a function) that holds a string containing the
current interpreter version. The current contents of this variable is
"C<Lua 5.1>".


=head2 C<xpcall (f, err)>

This function is similar to C<pcall>, except that you can set a new
error handler.

C<xpcall> calls function C<f> in protected mode, using C<err> as the
error handler. Any error inside C<f> is not propagated; instead,
C<xpcall> catches the error, calls the C<err> function with the
original error object, and returns a status code. Its first result is
the status code (a boolean), which is true if the call succeeds without
errors. In this case, C<xpcall> also returns all results from the call,
after this first result. In case of any error, C<xpcall> returns
B<false> plus the result from C<err>.

=head2 5.2 - Coroutine Manipulation

The operations related to coroutines comprise a sub-library of the
basic library and come inside the table C<coroutine>. See E<sect>2.11
for a general description of coroutines.


=head2 C<coroutine.create (f)>

Creates a new coroutine, with body C<f>. C<f> must be a Lua function.
Returns this new coroutine, an object with type C<"thread">.


=head2 C<coroutine.resume (co [, val1, E<middot>E<middot>E<middot>])>

Starts or continues the execution of coroutine C<co>. The first time
you resume a coroutine, it starts running its body. The values C<val1>,
E<middot>E<middot>E<middot> are passed as the arguments to the body
function. If the coroutine has yielded, C<resume> restarts it; the
values C<val1>, E<middot>E<middot>E<middot> are passed as the results
from the yield.

If the coroutine runs without any errors, C<resume> returns B<true>
plus any values passed to C<yield> (if the coroutine yields) or any
values returned by the body function (if the coroutine terminates). If
there is any error, C<resume> returns B<false> plus the error message.


=head2 C<coroutine.running ()>

Returns the running coroutine, or B<nil> when called by the main
thread.


=head2 C<coroutine.status (co)>

Returns the status of coroutine C<co>, as a string: C<"running">, if
the coroutine is running (that is, it called C<status>);
C<"suspended">, if the coroutine is suspended in a call to C<yield>, or
if it has not started running yet; C<"normal"> if the coroutine is
active but not running (that is, it has resumed another coroutine); and
C<"dead"> if the coroutine has finished its body function, or if it has
stopped with an error.


=head2 C<coroutine.wrap (f)>

Creates a new coroutine, with body C<f>. C<f> must be a Lua function.
Returns a function that resumes the coroutine each time it is called.
Any arguments passed to the function behave as the extra arguments to
C<resume>. Returns the same values returned by C<resume>, except the
first boolean. In case of error, propagates the error.


=head2 C<coroutine.yield (E<middot>E<middot>E<middot>)>

Suspends the execution of the calling coroutine. The coroutine cannot
be running a C function, a metamethod, or an iterator. Any arguments to
C<yield> are passed as extra results to C<resume>.

=head2 5.3 - Modules

The package library provides basic facilities for loading and building
modules in Lua. It exports two of its functions directly in the global
environment: C<require> and C<module>. Everything else is exported in a
table C<package>.


=head2 C<module (name [, E<middot>E<middot>E<middot>])>

Creates a module. If there is a table in C<package.loaded[name]>, this
table is the module. Otherwise, if there is a global table C<t> with
the given name, this table is the module. Otherwise creates a new table
C<t> and sets it as the value of the global C<name> and the value of
C<package.loaded[name]>. This function also initializes C<t._NAME> with
the given name, C<t._M> with the module (C<t> itself), and
C<t._PACKAGE> with the package name (the full module name minus last
component; see below). Finally, C<module> sets C<t> as the new
environment of the current function and the new value of
C<package.loaded[name]>, so that C<require> returns C<t>.

If C<name> is a compound name (that is, one with components separated
by dots), C<module> creates (or reuses, if they already exist) tables
for each component. For instance, if C<name> is C<a.b.c>, then
C<module> stores the module table in field C<c> of field C<b> of global
C<a>.

This function can receive optional I<options> after the module name,
where each option is a function to be applied over the module.


=head2 C<require (modname)>

Loads the given module. The function starts by looking into the
C<package.loaded> table to determine whether C<modname> is already
loaded. If it is, then C<require> returns the value stored at
C<package.loaded[modname]>. Otherwise, it tries to find a I<loader> for
the module.

To find a loader, C<require> is guided by the C<package.loaders> array.
By changing this array, we can change how C<require> looks for a
module. The following explanation is based on the default configuration
for C<package.loaders>.

First C<require> queries C<package.preload[modname]>. If it has a
value, this value (which should be a function) is the loader. Otherwise
C<require> searches for a Lua loader using the path stored in
C<package.path>. If that also fails, it searches for a C loader using
the path stored in C<package.cpath>. If that also fails, it tries an
I<all-in-one> loader (see C<package.loaders>).

Once a loader is found, C<require> calls the loader with a single
argument, C<modname>. If the loader returns any value, C<require>
assigns the returned value to C<package.loaded[modname]>. If the loader
returns no value and has not assigned any value to
C<package.loaded[modname]>, then C<require> assigns B<true> to this
entry. In any case, C<require> returns the final value of
C<package.loaded[modname]>.

If there is any error loading or running the module, or if it cannot
find any loader for the module, then C<require> signals an error.


=head2 C<package.cpath>

The path used by C<require> to search for a C loader.

Lua initializes the C path C<package.cpath> in the same way it
initializes the Lua path C<package.path>, using the environment
variable C<LUA_CPATH> or a default path defined in C<luaconf.h>.


=head2 C<package.loaded>

A table used by C<require> to control which modules are already loaded.
When you require a module C<modname> and C<package.loaded[modname]> is
not false, C<require> simply returns the value stored there.


=head2 C<package.loaders>

A table used by C<require> to control how to load modules.

Each entry in this table is a I<searcher function>. When looking for a
module, C<require> calls each of these searchers in ascending order,
with the module name (the argument given to C<require>) as its sole
parameter. The function can return another function (the module
I<loader>) or a string explaining why it did not find that module (or
B<nil> if it has nothing to say). Lua initializes this table with four
functions.

The first searcher simply looks for a loader in the C<package.preload>
table.

The second searcher looks for a loader as a Lua library, using the path
stored at C<package.path>. A path is a sequence of I<templates>
separated by semicolons. For each template, the searcher will change
each interrogation mark in the template by C<filename>, which is the
module name with each dot replaced by a "directory separator" (such as
"C</>" in Unix); then it will try to open the resulting file name. So,
for instance, if the Lua path is the string

     "./?.lua;./?.lc;/usr/local/?/init.lua"

the search for a Lua file for module C<foo> will try to open the files
C<./foo.lua>, C<./foo.lc>, and C</usr/local/foo/init.lua>, in that
order.

The third searcher looks for a loader as a C library, using the path
given by the variable C<package.cpath>. For instance, if the C path is
the string

     "./?.so;./?.dll;/usr/local/?/init.so"

the searcher for module C<foo> will try to open the files C<./foo.so>,
C<./foo.dll>, and C</usr/local/foo/init.so>, in that order. Once it
finds a C library, this searcher first uses a dynamic link facility to
link the application with the library. Then it tries to find a C
function inside the library to be used as the loader. The name of this
C function is the string "C<luaopen_>" concatenated with a copy of the
module name where each dot is replaced by an underscore. Moreover, if
the module name has a hyphen, its prefix up to (and including) the
first hyphen is removed. For instance, if the module name is
C<a.v1-b.c>, the function name will be C<luaopen_b_c>.

The fourth searcher tries an I<all-in-one loader>. It searches the C
path for a library for the root name of the given module. For instance,
when requiring C<a.b.c>, it will search for a C library for C<a>. If
found, it looks into it for an open function for the submodule; in our
example, that would be C<luaopen_a_b_c>. With this facility, a package
can pack several C submodules into one single library, with each
submodule keeping its original open function.


=head2 C<package.loadlib (libname, funcname)>

Dynamically links the host program with the C library C<libname>.
Inside this library, looks for a function C<funcname> and returns this
function as a C function. (So, C<funcname> must follow the protocol
(see C<lua_CFunction>)).

This is a low-level function. It completely bypasses the package and
module system. Unlike C<require>, it does not perform any path
searching and does not automatically adds extensions. C<libname> must
be the complete file name of the C library, including if necessary a
path and extension. C<funcname> must be the exact name exported by the
C library (which may depend on the C compiler and linker used).

This function is not supported by ANSI C. As such, it is only available
on some platforms (Windows, Linux, Mac OS X, Solaris, BSD, plus other
Unix systems that support the C<dlfcn> standard).


=head2 C<package.path>

The path used by C<require> to search for a Lua loader.

At start-up, Lua initializes this variable with the value of the
environment variable C<LUA_PATH> or with a default path defined in
C<luaconf.h>, if the environment variable is not defined. Any "C<;;>"
in the value of the environment variable is replaced by the default
path.


=head2 C<package.preload>

A table to store loaders for specific modules (see C<require>).


=head2 C<package.seeall (module)>

Sets a metatable for C<module> with its C<__index> field referring to
the global environment, so that this module inherits values from the
global environment. To be used as an option to function C<module>.

=head2 5.4 - String Manipulation

This library provides generic functions for string manipulation, such
as finding and extracting substrings, and pattern matching. When
indexing a string in Lua, the first character is at position 1 (not at
0, as in C). Indices are allowed to be negative and are interpreted as
indexing backwards, from the end of the string. Thus, the last
character is at position -1, and so on.

The string library provides all its functions inside the table
C<string>. It also sets a metatable for strings where the C<__index>
field points to the C<string> table. Therefore, you can use the string
functions in object-oriented style. For instance, C<string.byte(s, i)>
can be written as C<s:byte(i)>.

The string library assumes one-byte character encodings.


=head2 C<string.byte (s [, i [, j]])>

Returns the internal numerical codes of the characters C<s[i]>,
C<s[i+1]>, E<middot>E<middot>E<middot>, C<s[j]>. The default value for
C<i> is 1; the default value for C<j> is C<i>.

Note that numerical codes are not necessarily portable across
platforms.


=head2 C<string.char (E<middot>E<middot>E<middot>)>

Receives zero or more integers. Returns a string with length equal to
the number of arguments, in which each character has the internal
numerical code equal to its corresponding argument.

Note that numerical codes are not necessarily portable across
platforms.


=head2 C<string.dump (function)>

Returns a string containing a binary representation of the given
function, so that a later C<loadstring> on this string returns a copy
of the function. C<function> must be a Lua function without upvalues.


=head2 C<string.find (s, pattern [, init [, plain]])>

Looks for the first match of C<pattern> in the string C<s>. If it finds
a match, then C<find> returns the indices of C<s> where this occurrence
starts and ends; otherwise, it returns B<nil>. A third, optional
numerical argument C<init> specifies where to start the search; its
default value is 1 and can be negative. A value of B<true> as a fourth,
optional argument C<plain> turns off the pattern matching facilities,
so the function does a plain "find substring" operation, with no
characters in C<pattern> being considered "magic". Note that if
C<plain> is given, then C<init> must be given as well.

If the pattern has captures, then in a successful match the captured
values are also returned, after the two indices.


=head2 C<string.format (formatstring, E<middot>E<middot>E<middot>)>

Returns a formatted version of its variable number of arguments
following the description given in its first argument (which must be a
string). The format string follows the same rules as the C<printf>
family of standard C functions. The only differences are that the
options/modifiers C<*>, C<l>, C<L>, C<n>, C<p>, and C<h> are not
supported and that there is an extra option, C<q>. The C<q> option
formats a string in a form suitable to be safely read back by the Lua
interpreter: the string is written between double quotes, and all
double quotes, newlines, embedded zeros, and backslashes in the string
are correctly escaped when written. For instance, the call

     string.format('%q', 'a string with "quotes" and \n new line')

will produce the string:

     "a string with \"quotes\" and \
      new line"

The options C<c>, C<d>, C<E>, C<e>, C<f>, C<g>, C<G>, C<i>, C<o>, C<u>,
C<X>, and C<x> all expect a number as argument, whereas C<q> and C<s>
expect a string.

This function does not accept string values containing embedded zeros,
except as arguments to the C<q> option.


=head2 C<string.gmatch (s, pattern)>

Returns an iterator function that, each time it is called, returns the
next captures from C<pattern> over string C<s>. If C<pattern> specifies
no captures, then the whole match is produced in each call.

As an example, the following loop

     s = "hello world from Lua"
     for w in string.gmatch(s, "%a+") do
       print(w)
     end

will iterate over all the words from string C<s>, printing one per
line. The next example collects all pairs C<key=value> from the given
string into a table:

     t = {}
     s = "from=world, to=Lua"
     for k, v in string.gmatch(s, "(%w+)=(%w+)") do
       t[k] = v
     end

For this function, a 'C<^>' at the start of a pattern does not work as
an anchor, as this would prevent the iteration.


=head2 C<string.gsub (s, pattern, repl [, n])>

Returns a copy of C<s> in which all (or the first C<n>, if given)
occurrences of the C<pattern> have been replaced by a replacement
string specified by C<repl>, which can be a string, a table, or a
function. C<gsub> also returns, as its second value, the total number
of matches that occurred.

If C<repl> is a string, then its value is used for replacement. The
character C<%> works as an escape character: any sequence in C<repl> of
the form C<%I<n>>, with I<n> between 1 and 9, stands for the value of
the I<n>-th captured substring (see below). The sequence C<%0> stands
for the whole match. The sequence C<%%> stands for a single C<%>.

If C<repl> is a table, then the table is queried for every match, using
the first capture as the key; if the pattern specifies no captures,
then the whole match is used as the key.

If C<repl> is a function, then this function is called every time a
match occurs, with all captured substrings passed as arguments, in
order; if the pattern specifies no captures, then the whole match is
passed as a sole argument.

If the value returned by the table query or by the function call is a
string or a number, then it is used as the replacement string;
otherwise, if it is B<false> or B<nil>, then there is no replacement
(that is, the original match is kept in the string).

Here are some examples:

     x = string.gsub("hello world", "(%w+)", "%1 %1")
     --> x="hello hello world world"
     
     x = string.gsub("hello world", "%w+", "%0 %0", 1)
     --> x="hello hello world"
     
     x = string.gsub("hello world from Lua", "(%w+)%s*(%w+)", "%2 %1")
     --> x="world hello Lua from"
     
     x = string.gsub("home = $HOME, user = $USER", "%$(%w+)", os.getenv)
     --> x="home = /home/<USER>"
     
     x = string.gsub("4+5 = $return 4+5$", "%$(.-)%$", function (s)
           return loadstring(s)()
         end)
     --> x="4+5 = 9"
     
     local t = {name="lua", version="5.1"}
     x = string.gsub("$name-$version.tar.gz", "%$(%w+)", t)
     --> x="lua-5.1.tar.gz"


=head2 C<string.len (s)>

Receives a string and returns its length. The empty string C<""> has
length 0. Embedded zeros are counted, so C<"a\000bc\000"> has length 5.


=head2 C<string.lower (s)>

Receives a string and returns a copy of this string with all uppercase
letters changed to lowercase. All other characters are left unchanged.
The definition of what an uppercase letter is depends on the current
locale.


=head2 C<string.match (s, pattern [, init])>

Looks for the first I<match> of C<pattern> in the string C<s>. If it
finds one, then C<match> returns the captures from the pattern;
otherwise it returns B<nil>. If C<pattern> specifies no captures, then
the whole match is returned. A third, optional numerical argument
C<init> specifies where to start the search; its default value is 1 and
can be negative.


=head2 C<string.rep (s, n)>

Returns a string that is the concatenation of C<n> copies of the string
C<s>.


=head2 C<string.reverse (s)>

Returns a string that is the string C<s> reversed.


=head2 C<string.sub (s, i [, j])>

Returns the substring of C<s> that starts at C<i> and continues until
C<j>; C<i> and C<j> can be negative. If C<j> is absent, then it is
assumed to be equal to -1 (which is the same as the string length). In
particular, the call C<string.sub(s,1,j)> returns a prefix of C<s> with
length C<j>, and C<string.sub(s, -i)> returns a suffix of C<s> with
length C<i>.


=head2 C<string.upper (s)>

Receives a string and returns a copy of this string with all lowercase
letters changed to uppercase. All other characters are left unchanged.
The definition of what a lowercase letter is depends on the current
locale.

=head2 5.4.1 - Patterns

=head2 Character Class:

A I<character class> is used to represent a set of characters. The
following combinations are allowed in describing a character class:

=over

=item * B<I<x>:> (where I<x> is not one of the I<magic characters>
C<^$()%.[]*+-?>) represents the character I<x> itself.

=item * B<C<.>:> (a dot) represents all characters.

=item * B<C<%a>:> represents all letters.

=item * B<C<%c>:> represents all control characters.

=item * B<C<%d>:> represents all digits.

=item * B<C<%l>:> represents all lowercase letters.

=item * B<C<%p>:> represents all punctuation characters.

=item * B<C<%s>:> represents all space characters.

=item * B<C<%u>:> represents all uppercase letters.

=item * B<C<%w>:> represents all alphanumeric characters.

=item * B<C<%x>:> represents all hexadecimal digits.

=item * B<C<%z>:> represents the character with representation 0.

=item * B<C<%I<x>>:> (where I<x> is any non-alphanumeric character)
represents the character I<x>. This is the standard way to escape the
magic characters. Any punctuation character (even the non magic) can be
preceded by a 'C<%>' when used to represent itself in a pattern.

=item * B<C<[I<set>]>:> represents the class which is the union of all
characters in I<set>. A range of characters can be specified by
separating the end characters of the range with a 'C<->'. All classes
C<%>I<x> described above can also be used as components in I<set>. All
other characters in I<set> represent themselves. For example, C<[%w_]>
(or C<[_%w]>) represents all alphanumeric characters plus the
underscore, C<[0-7]> represents the octal digits, and C<[0-7%l%-]>
represents the octal digits plus the lowercase letters plus the 'C<->'
character.

The interaction between ranges and classes is not defined. Therefore,
patterns like C<[%a-z]> or C<[a-%%]> have no meaning.

=item * B<C<[^I<set>]>:> represents the complement of I<set>, where
I<set> is interpreted as above.

=back

For all classes represented by single letters (C<%a>, C<%c>, etc.), the
corresponding uppercase letter represents the complement of the class.
For instance, C<%S> represents all non-space characters.

The definitions of letter, space, and other character groups depend on
the current locale. In particular, the class C<[a-z]> may not be
equivalent to C<%l>.

=head2 Pattern Item:

A I<pattern item> can be

=over

=item * a single character class, which matches any single character in
the class;

=item * a single character class followed by 'C<*>', which matches 0 or
more repetitions of characters in the class. These repetition items
will always match the longest possible sequence;

=item * a single character class followed by 'C<+>', which matches 1 or
more repetitions of characters in the class. These repetition items
will always match the longest possible sequence;

=item * a single character class followed by 'C<->', which also matches
0 or more repetitions of characters in the class. Unlike 'C<*>', these
repetition items will always match the I<shortest> possible sequence;

=item * a single character class followed by 'C<?>', which matches 0 or
1 occurrence of a character in the class;

=item * C<%I<n>>, for I<n> between 1 and 9; such item matches a
substring equal to the I<n>-th captured string (see below);

=item * C<%bI<xy>>, where I<x> and I<y> are two distinct characters;
such item matches strings that start with I<x>, end with I<y>, and
where the I<x> and I<y> are I<balanced>. This means that, if one reads
the string from left to right, counting I<+1> for an I<x> and I<-1> for
a I<y>, the ending I<y> is the first I<y> where the count reaches 0.
For instance, the item C<%b()> matches expressions with balanced
parentheses.

=back

=head2 Pattern:

A I<pattern> is a sequence of pattern items. A 'C<^>' at the beginning
of a pattern anchors the match at the beginning of the subject string.
A 'C<$>' at the end of a pattern anchors the match at the end of the
subject string. At other positions, 'C<^>' and 'C<$>' have no special
meaning and represent themselves.

=head2 Captures:

A pattern can contain sub-patterns enclosed in parentheses; they
describe I<captures>. When a match succeeds, the substrings of the
subject string that match captures are stored (I<captured>) for future
use. Captures are numbered according to their left parentheses. For
instance, in the pattern C<"(a*(.)%w(%s*))">, the part of the string
matching C<"a*(.)%w(%s*)"> is stored as the first capture (and
therefore has number 1); the character matching "C<.>" is captured with
number 2, and the part matching "C<%s*>" has number 3.

As a special case, the empty capture C<()> captures the current string
position (a number). For instance, if we apply the pattern C<"()aa()">
on the string C<"flaaap">, there will be two captures: 3 and 5.

A pattern cannot contain embedded zeros. Use C<%z> instead.

=head2 5.5 - Table Manipulation

This library provides generic functions for table manipulation. It
provides all its functions inside the table C<table>.

Most functions in the table library assume that the table represents an
array or a list. For these functions, when we talk about the "length"
of a table we mean the result of the length operator.


=head2 C<table.concat (table [, sep [, i [, j]]])>

Given an array where all elements are strings or numbers, returns
C<table[i]..sep..table[i+1] E<middot>E<middot>E<middot> sep..table[j]>.
The default value for C<sep> is the empty string, the default for C<i>
is 1, and the default for C<j> is the length of the table. If C<i> is
greater than C<j>, returns the empty string.


=head2 C<table.insert (table, [pos,] value)>

Inserts element C<value> at position C<pos> in C<table>, shifting up
other elements to open space, if necessary. The default value for
C<pos> is C<n+1>, where C<n> is the length of the table (see
E<sect>2.5.5), so that a call C<table.insert(t,x)> inserts C<x> at the
end of table C<t>.


=head2 C<table.maxn (table)>

Returns the largest positive numerical index of the given table, or
zero if the table has no positive numerical indices. (To do its job
this function does a linear traversal of the whole table.)


=head2 C<table.remove (table [, pos])>

Removes from C<table> the element at position C<pos>, shifting down
other elements to close the space, if necessary. Returns the value of
the removed element. The default value for C<pos> is C<n>, where C<n>
is the length of the table, so that a call C<table.remove(t)> removes
the last element of table C<t>.


=head2 C<table.sort (table [, comp])>

Sorts table elements in a given order, I<in-place>, from C<table[1]> to
C<table[n]>, where C<n> is the length of the table. If C<comp> is
given, then it must be a function that receives two table elements, and
returns true when the first is less than the second (so that C<not
comp(a[i+1],a[i])> will be true after the sort). If C<comp> is not
given, then the standard Lua operator C<E<lt>> is used instead.

The sort algorithm is not stable; that is, elements considered equal by
the given order may have their relative positions changed by the sort.

=head2 5.6 - Mathematical Functions

This library is an interface to the standard C math library. It
provides all its functions inside the table C<math>.


=head2 C<math.abs (x)>

Returns the absolute value of C<x>.


=head2 C<math.acos (x)>

Returns the arc cosine of C<x> (in radians).


=head2 C<math.asin (x)>

Returns the arc sine of C<x> (in radians).


=head2 C<math.atan (x)>

Returns the arc tangent of C<x> (in radians).


=head2 C<math.atan2 (y, x)>

Returns the arc tangent of C<y/x> (in radians), but uses the signs of
both parameters to find the quadrant of the result. (It also handles
correctly the case of C<x> being zero.)


=head2 C<math.ceil (x)>

Returns the smallest integer larger than or equal to C<x>.


=head2 C<math.cos (x)>

Returns the cosine of C<x> (assumed to be in radians).


=head2 C<math.cosh (x)>

Returns the hyperbolic cosine of C<x>.


=head2 C<math.deg (x)>

Returns the angle C<x> (given in radians) in degrees.


=head2 C<math.exp (x)>

Returns the value I<ex>.


=head2 C<math.floor (x)>

Returns the largest integer smaller than or equal to C<x>.


=head2 C<math.fmod (x, y)>

Returns the remainder of the division of C<x> by C<y> that rounds the
quotient towards zero.


=head2 C<math.frexp (x)>

Returns C<m> and C<e> such that I<x = m2e>, C<e> is an integer and the
absolute value of C<m> is in the range I<[0.5, 1)> (or zero when C<x>
is zero).


=head2 C<math.huge>

The value C<HUGE_VAL>, a value larger than or equal to any other
numerical value.


=head2 C<math.ldexp (m, e)>

Returns I<m2e> (C<e> should be an integer).


=head2 C<math.log (x)>

Returns the natural logarithm of C<x>.


=head2 C<math.log10 (x)>

Returns the base-10 logarithm of C<x>.


=head2 C<math.max (x, E<middot>E<middot>E<middot>)>

Returns the maximum value among its arguments.


=head2 C<math.min (x, E<middot>E<middot>E<middot>)>

Returns the minimum value among its arguments.


=head2 C<math.modf (x)>

Returns two numbers, the integral part of C<x> and the fractional part
of C<x>.


=head2 C<math.pi>

The value of I<pi>.


=head2 C<math.pow (x, y)>

Returns I<xy>. (You can also use the expression C<x^y> to compute this
value.)


=head2 C<math.rad (x)>

Returns the angle C<x> (given in degrees) in radians.


=head2 C<math.random ([m [, n]])>

This function is an interface to the simple pseudo-random generator
function C<rand> provided by ANSI C. (No guarantees can be given for
its statistical properties.)

When called without arguments, returns a uniform pseudo-random real
number in the range I<[0,1)>. When called with an integer number C<m>,
C<math.random> returns a uniform pseudo-random integer in the range
I<[1, m]>. When called with two integer numbers C<m> and C<n>,
C<math.random> returns a uniform pseudo-random integer in the range
I<[m, n]>.


=head2 C<math.randomseed (x)>

Sets C<x> as the "seed" for the pseudo-random generator: equal seeds
produce equal sequences of numbers.


=head2 C<math.sin (x)>

Returns the sine of C<x> (assumed to be in radians).


=head2 C<math.sinh (x)>

Returns the hyperbolic sine of C<x>.


=head2 C<math.sqrt (x)>

Returns the square root of C<x>. (You can also use the expression
C<x^0.5> to compute this value.)


=head2 C<math.tan (x)>

Returns the tangent of C<x> (assumed to be in radians).


=head2 C<math.tanh (x)>

Returns the hyperbolic tangent of C<x>.

=head2 5.7 - Input and Output Facilities

The I/O library provides two different styles for file manipulation.
The first one uses implicit file descriptors; that is, there are
operations to set a default input file and a default output file, and
all input/output operations are over these default files. The second
style uses explicit file descriptors.

When using implicit file descriptors, all operations are supplied by
table C<io>. When using explicit file descriptors, the operation
C<io.open> returns a file descriptor and then all operations are
supplied as methods of the file descriptor.

The table C<io> also provides three predefined file descriptors with
their usual meanings from C: C<io.stdin>, C<io.stdout>, and
C<io.stderr>. The I/O library never closes these files.

Unless otherwise stated, all I/O functions return B<nil> on failure
(plus an error message as a second result and a system-dependent error
code as a third result) and some value different from B<nil> on
success.


=head2 C<io.close ([file])>

Equivalent to C<file:close()>. Without a C<file>, closes the default
output file.


=head2 C<io.flush ()>

Equivalent to C<file:flush> over the default output file.


=head2 C<io.input ([file])>

When called with a file name, it opens the named file (in text mode),
and sets its handle as the default input file. When called with a file
handle, it simply sets this file handle as the default input file. When
called without parameters, it returns the current default input file.

In case of errors this function raises the error, instead of returning
an error code.


=head2 C<io.lines ([filename])>

Opens the given file name in read mode and returns an iterator function
that, each time it is called, returns a new line from the file.
Therefore, the construction

     for line in io.lines(filename) do body end

will iterate over all lines of the file. When the iterator function
detects the end of file, it returns B<nil> (to finish the loop) and
automatically closes the file.

The call C<io.lines()> (with no file name) is equivalent to
C<io.input():lines()>; that is, it iterates over the lines of the
default input file. In this case it does not close the file when the
loop ends.


=head2 C<io.open (filename [, mode])>

This function opens a file, in the mode specified in the string
C<mode>. It returns a new file handle, or, in case of errors, B<nil>
plus an error message.

The C<mode> string can be any of the following:

=over

=item * B<"r":> read mode (the default);

=item * B<"w":> write mode;

=item * B<"a":> append mode;

=item * B<"r+":> update mode, all previous data is preserved;

=item * B<"w+":> update mode, all previous data is erased;

=item * B<"a+":> append update mode, previous data is preserved,
writing is only allowed at the end of file.

=back

The C<mode> string can also have a 'C<b>' at the end, which is needed
in some systems to open the file in binary mode. This string is exactly
what is used in the standard C function C<fopen>.


=head2 C<io.output ([file])>

Similar to C<io.input>, but operates over the default output file.


=head2 C<io.popen (prog [, mode])>

Starts program C<prog> in a separated process and returns a file handle
that you can use to read data from this program (if C<mode> is C<"r">,
the default) or to write data to this program (if C<mode> is C<"w">).

This function is system dependent and is not available on all
platforms.


=head2 C<io.read (E<middot>E<middot>E<middot>)>

Equivalent to C<io.input():read>.


=head2 C<io.tmpfile ()>

Returns a handle for a temporary file. This file is opened in update
mode and it is automatically removed when the program ends.


=head2 C<io.type (obj)>

Checks whether C<obj> is a valid file handle. Returns the string
C<"file"> if C<obj> is an open file handle, C<"closed file"> if C<obj>
is a closed file handle, or B<nil> if C<obj> is not a file handle.


=head2 C<io.write (E<middot>E<middot>E<middot>)>

Equivalent to C<io.output():write>.


=head2 C<file:close ()>

Closes C<file>. Note that files are automatically closed when their
handles are garbage collected, but that takes an unpredictable amount
of time to happen.


=head2 C<file:flush ()>

Saves any written data to C<file>.


=head2 C<file:lines ()>

Returns an iterator function that, each time it is called, returns a
new line from the file. Therefore, the construction

     for line in file:lines() do body end

will iterate over all lines of the file. (Unlike C<io.lines>, this
function does not close the file when the loop ends.)


=head2 C<file:read (E<middot>E<middot>E<middot>)>

Reads the file C<file>, according to the given formats, which specify
what to read. For each format, the function returns a string (or a
number) with the characters read, or B<nil> if it cannot read data with
the specified format. When called without formats, it uses a default
format that reads the entire next line (see below).

The available formats are

=over

=item * B<"*n":> reads a number; this is the only format that returns a
number instead of a string.

=item * B<"*a":> reads the whole file, starting at the current
position. On end of file, it returns the empty string.

=item * B<"*l":> reads the next line (skipping the end of line),
returning B<nil> on end of file. This is the default format.

=item * B<I<number>:> reads a string with up to this number of
characters, returning B<nil> on end of file. If number is zero, it
reads nothing and returns an empty string, or B<nil> on end of file.

=back


=head2 C<file:seek ([whence] [, offset])>

Sets and gets the file position, measured from the beginning of the
file, to the position given by C<offset> plus a base specified by the
string C<whence>, as follows:

=over

=item * B<"set":> base is position 0 (beginning of the file);

=item * B<"cur":> base is current position;

=item * B<"end":> base is end of file;

=back

In case of success, function C<seek> returns the final file position,
measured in bytes from the beginning of the file. If this function
fails, it returns B<nil>, plus a string describing the error.

The default value for C<whence> is C<"cur">, and for C<offset> is 0.
Therefore, the call C<file:seek()> returns the current file position,
without changing it; the call C<file:seek("set")> sets the position to
the beginning of the file (and returns 0); and the call
C<file:seek("end")> sets the position to the end of the file, and
returns its size.


=head2 C<file:setvbuf (mode [, size])>

Sets the buffering mode for an output file. There are three available
modes:

=over

=item * B<"no":> no buffering; the result of any output operation
appears immediately.

=item * B<"full":> full buffering; output operation is performed only
when the buffer is full (or when you explicitly C<flush> the file (see
C<io.flush>)).

=item * B<"line":> line buffering; output is buffered until a newline
is output or there is any input from some special files (such as a
terminal device).

=back

For the last two cases, C<size> specifies the size of the buffer, in
bytes. The default is an appropriate size.


=head2 C<file:write (E<middot>E<middot>E<middot>)>

Writes the value of each of its arguments to the C<file>. The arguments
must be strings or numbers. To write other values, use C<tostring> or
C<string.format> before C<write>.

=head2 5.8 - Operating System Facilities

This library is implemented through table C<os>.


=head2 C<os.clock ()>

Returns an approximation of the amount in seconds of CPU time used by
the program.


=head2 C<os.date ([format [, time]])>

Returns a string or a table containing date and time, formatted
according to the given string C<format>.

If the C<time> argument is present, this is the time to be formatted
(see the C<os.time> function for a description of this value).
Otherwise, C<date> formats the current time.

If C<format> starts with 'C<!>', then the date is formatted in
Coordinated Universal Time. After this optional character, if C<format>
is the string "C<*t>", then C<date> returns a table with the following
fields: C<year> (four digits), C<month> (1--12), C<day> (1--31),
C<hour> (0--23), C<min> (0--59), C<sec> (0--61), C<wday> (weekday,
Sunday is 1), C<yday> (day of the year), and C<isdst> (daylight saving
flag, a boolean).

If C<format> is not "C<*t>", then C<date> returns the date as a string,
formatted according to the same rules as the C function C<strftime>.

When called without arguments, C<date> returns a reasonable date and
time representation that depends on the host system and on the current
locale (that is, C<os.date()> is equivalent to C<os.date("%c")>).


=head2 C<os.difftime (t2, t1)>

Returns the number of seconds from time C<t1> to time C<t2>. In POSIX,
Windows, and some other systems, this value is exactly C<t2>I<->C<t1>.


=head2 C<os.execute ([command])>

This function is equivalent to the C function C<system>. It passes
C<command> to be executed by an operating system shell. It returns a
status code, which is system-dependent. If C<command> is absent, then
it returns nonzero if a shell is available and zero otherwise.


=head2 C<os.exit ([code])>

Calls the C function C<exit>, with an optional C<code>, to terminate
the host program. The default value for C<code> is the success code.


=head2 C<os.getenv (varname)>

Returns the value of the process environment variable C<varname>, or
B<nil> if the variable is not defined.


=head2 C<os.remove (filename)>

Deletes the file or directory with the given name. Directories must be
empty to be removed. If this function fails, it returns B<nil>, plus a
string describing the error.


=head2 C<os.rename (oldname, newname)>

Renames file or directory named C<oldname> to C<newname>. If this
function fails, it returns B<nil>, plus a string describing the error.


=head2 C<os.setlocale (locale [, category])>

Sets the current locale of the program. C<locale> is a string
specifying a locale; C<category> is an optional string describing which
category to change: C<"all">, C<"collate">, C<"ctype">, C<"monetary">,
C<"numeric">, or C<"time">; the default category is C<"all">. The
function returns the name of the new locale, or B<nil> if the request
cannot be honored.

If C<locale> is the empty string, the current locale is set to an
implementation-defined native locale. If C<locale> is the string
"C<C>", the current locale is set to the standard C locale.

When called with B<nil> as the first argument, this function only
returns the name of the current locale for the given category.


=head2 C<os.time ([table])>

Returns the current time when called without arguments, or a time
representing the date and time specified by the given table. This table
must have fields C<year>, C<month>, and C<day>, and may have fields
C<hour>, C<min>, C<sec>, and C<isdst> (for a description of these
fields, see the C<os.date> function).

The returned value is a number, whose meaning depends on your system.
In POSIX, Windows, and some other systems, this number counts the
number of seconds since some given start time (the "epoch"). In other
systems, the meaning is not specified, and the number returned by
C<time> can be used only as an argument to C<date> and C<difftime>.


=head2 C<os.tmpname ()>

Returns a string with a file name that can be used for a temporary
file. The file must be explicitly opened before its use and explicitly
removed when no longer needed.

On some systems (POSIX), this function also creates a file with that
name, to avoid security risks. (Someone else might create the file with
wrong permissions in the time between getting the name and creating the
file.) You still have to open the file to use it and to remove it (even
if you do not use it).

When possible, you may prefer to use C<io.tmpfile>, which automatically
removes the file when the program ends.

=head2 5.9 - The Debug Library

This library provides the functionality of the debug interface to Lua
programs. You should exert care when using this library. The functions
provided here should be used exclusively for debugging and similar
tasks, such as profiling. Please resist the temptation to use them as a
usual programming tool: they can be very slow. Moreover, several of
these functions violate some assumptions about Lua code (e.g., that
variables local to a function cannot be accessed from outside or that
userdata metatables cannot be changed by Lua code) and therefore can
compromise otherwise secure code.

All functions in this library are provided inside the C<debug> table.
All functions that operate over a thread have an optional first
argument which is the thread to operate over. The default is always the
current thread.


=head2 C<debug.debug ()>

Enters an interactive mode with the user, running each string that the
user enters. Using simple commands and other debug facilities, the user
can inspect global and local variables, change their values, evaluate
expressions, and so on. A line containing only the word C<cont>
finishes this function, so that the caller continues its execution.

Note that commands for C<debug.debug> are not lexically nested within
any function, and so have no direct access to local variables.


=head2 C<debug.getfenv (o)>

Returns the environment of object C<o>.


=head2 C<debug.gethook ([thread])>

Returns the current hook settings of the thread, as three values: the
current hook function, the current hook mask, and the current hook
count (as set by the C<debug.sethook> function).


=head2 C<debug.getinfo ([thread,] function [, what])>

Returns a table with information about a function. You can give the
function directly, or you can give a number as the value of
C<function>, which means the function running at level C<function> of
the call stack of the given thread: level 0 is the current function
(C<getinfo> itself); level 1 is the function that called C<getinfo>;
and so on. If C<function> is a number larger than the number of active
functions, then C<getinfo> returns B<nil>.

The returned table can contain all the fields returned by
C<lua_getinfo>, with the string C<what> describing which fields to fill
in. The default for C<what> is to get all information available, except
the table of valid lines. If present, the option 'C<f>' adds a field
named C<func> with the function itself. If present, the option 'C<L>'
adds a field named C<activelines> with the table of valid lines.

For instance, the expression C<debug.getinfo(1,"n").name> returns a
table with a name for the current function, if a reasonable name can be
found, and the expression C<debug.getinfo(print)> returns a table with
all available information about the C<print> function.


=head2 C<debug.getlocal ([thread,] level, local)>

This function returns the name and the value of the local variable with
index C<local> of the function at level C<level> of the stack. (The
first parameter or local variable has index 1, and so on, until the
last active local variable.) The function returns B<nil> if there is no
local variable with the given index, and raises an error when called
with a C<level> out of range. (You can call C<debug.getinfo> to check
whether the level is valid.)

Variable names starting with 'C<(>' (open parentheses) represent
internal variables (loop control variables, temporaries, and C function
locals).


=head2 C<debug.getmetatable (object)>

Returns the metatable of the given C<object> or B<nil> if it does not
have a metatable.


=head2 C<debug.getregistry ()>

Returns the registry table (see E<sect>3.5).


=head2 C<debug.getupvalue (func, up)>

This function returns the name and the value of the upvalue with index
C<up> of the function C<func>. The function returns B<nil> if there is
no upvalue with the given index.


=head2 C<debug.setfenv (object, table)>

Sets the environment of the given C<object> to the given C<table>.
Returns C<object>.


=head2 C<debug.sethook ([thread,] hook, mask [, count])>

Sets the given function as a hook. The string C<mask> and the number
C<count> describe when the hook will be called. The string mask may
have the following characters, with the given meaning:

=over

=item * B<C<"c">:> the hook is called every time Lua calls a function;

=item * B<C<"r">:> the hook is called every time Lua returns from a
function;

=item * B<C<"l">:> the hook is called every time Lua enters a new line
of code.

=back

With a C<count> different from zero, the hook is called after every
C<count> instructions.

When called without arguments, C<debug.sethook> turns off the hook.

When the hook is called, its first parameter is a string describing the
event that has triggered its call: C<"call">, C<"return"> (or C<"tail
return">, when simulating a return from a tail call), C<"line">, and
C<"count">. For line events, the hook also gets the new line number as
its second parameter. Inside a hook, you can call C<getinfo> with level
2 to get more information about the running function (level 0 is the
C<getinfo> function, and level 1 is the hook function), unless the
event is C<"tail return">. In this case, Lua is only simulating the
return, and a call to C<getinfo> will return invalid data.


=head2 C<debug.setlocal ([thread,] level, local, value)>

This function assigns the value C<value> to the local variable with
index C<local> of the function at level C<level> of the stack. The
function returns B<nil> if there is no local variable with the given
index, and raises an error when called with a C<level> out of range.
(You can call C<getinfo> to check whether the level is valid.)
Otherwise, it returns the name of the local variable.


=head2 C<debug.setmetatable (object, table)>

Sets the metatable for the given C<object> to the given C<table> (which
can be B<nil>).


=head2 C<debug.setupvalue (func, up, value)>

This function assigns the value C<value> to the upvalue with index
C<up> of the function C<func>. The function returns B<nil> if there is
no upvalue with the given index. Otherwise, it returns the name of the
upvalue.


=head2 C<debug.traceback ([thread,] [message [, level]])>

Returns a string with a traceback of the call stack. An optional
C<message> string is appended at the beginning of the traceback. An
optional C<level> number tells at which level to start the traceback
(default is 1, the function calling C<traceback>).

=head1 6 - Lua Stand-alone

Although Lua has been designed as an extension language, to be embedded
in a host C program, it is also frequently used as a stand-alone
language. An interpreter for Lua as a stand-alone language, called
simply C<lua>, is provided with the standard distribution. The
stand-alone interpreter includes all standard libraries, including the
debug library. Its usage is:

     lua [options] [script [args]]

The options are:

=over

=item * B<C<-e I<stat>>:> executes string I<stat>;

=item * B<C<-l I<mod>>:> "requires" I<mod>;

=item * B<C<-i>:> enters interactive mode after running I<script>;

=item * B<C<-v>:> prints version information;

=item * B<C<-->:> stops handling options;

=item * B<C<->:> executes C<stdin> as a file and stops handling
options.

=back

After handling its options, C<lua> runs the given I<script>, passing to
it the given I<args> as string arguments. When called without
arguments, C<lua> behaves as C<lua -v -i> when the standard input
(C<stdin>) is a terminal, and as C<lua -> otherwise.

Before running any argument, the interpreter checks for an environment
variable C<LUA_INIT>. If its format is C<@I<filename>>, then C<lua>
executes the file. Otherwise, C<lua> executes the string itself.

All options are handled in order, except C<-i>. For instance, an
invocation like

     $ lua -e'a=1' -e 'print(a)' script.lua

will first set C<a> to 1, then print the value of C<a> (which is
'C<1>'), and finally run the file C<script.lua> with no arguments.
(Here C<$> is the shell prompt. Your prompt may be different.)

Before starting to run the script, C<lua> collects all arguments in the
command line in a global table called C<arg>. The script name is stored
at index 0, the first argument after the script name goes to index 1,
and so on. Any arguments before the script name (that is, the
interpreter name plus the options) go to negative indices. For
instance, in the call

     $ lua -la b.lua t1 t2

the interpreter first runs the file C<a.lua>, then creates a table

     arg = { [-2] = "lua", [-1] = "-la",
             [0] = "b.lua",
             [1] = "t1", [2] = "t2" }

and finally runs the file C<b.lua>. The script is called with
C<arg[1]>, C<arg[2]>, E<middot>E<middot>E<middot> as arguments; it can
also access these arguments with the vararg expression 'C<...>'.

In interactive mode, if you write an incomplete statement, the
interpreter waits for its completion by issuing a different prompt.

If the global variable C<_PROMPT> contains a string, then its value is
used as the prompt. Similarly, if the global variable C<_PROMPT2>
contains a string, its value is used as the secondary prompt (issued
during incomplete statements). Therefore, both prompts can be changed
directly on the command line or in any Lua programs by assigning to
C<_PROMPT>. See the next example:

     $ lua -e"_PROMPT='myprompt> '" -i

(The outer pair of quotes is for the shell, the inner pair is for Lua.)
Note the use of C<-i> to enter interactive mode; otherwise, the program
would just end silently right after the assignment to C<_PROMPT>.

To allow the use of Lua as a script interpreter in Unix systems, the
stand-alone interpreter skips the first line of a chunk if it starts
with C<#>. Therefore, Lua scripts can be made into executable programs
by using C<chmod +x> and the C<#!> form, as in

     #!/usr/local/bin/lua

(Of course, the location of the Lua interpreter may be different in
your machine. If C<lua> is in your C<PATH>, then

     #!/usr/bin/env lua

is a more portable solution.)

=head1 7 - Incompatibilities with the Previous Version

Here we list the incompatibilities that you may find when moving a
program from Lua 5.0 to Lua 5.1. You can avoid most of the
incompatibilities compiling Lua with appropriate options (see file
C<luaconf.h>). However, all these compatibility options will be removed
in the next version of Lua.

=head2 7.1 - Changes in the Language

=over

=item * The vararg system changed from the pseudo-argument C<arg> with
a table with the extra arguments to the vararg expression. (See
compile-time option C<LUA_COMPAT_VARARG> in C<luaconf.h>.)

=item * There was a subtle change in the scope of the implicit
variables of the B<for> statement and for the B<repeat> statement.

=item * The long string/long comment syntax (C<[[I<string>]]>) does not
allow nesting. You can use the new syntax (C<[=[I<string>]=]>) in these
cases. (See compile-time option C<LUA_COMPAT_LSTR> in C<luaconf.h>.)

=back

=head2 7.2 - Changes in the Libraries

=over

=item * Function C<string.gfind> was renamed C<string.gmatch>. (See
compile-time option C<LUA_COMPAT_GFIND> in C<luaconf.h>.)

=item * When C<string.gsub> is called with a function as its third
argument, whenever this function returns B<nil> or B<false> the
replacement string is the whole match, instead of the empty string.

=item * Function C<table.setn> was deprecated. Function C<table.getn>
corresponds to the new length operator (C<#>); use the operator instead
of the function. (See compile-time option C<LUA_COMPAT_GETN> in
C<luaconf.h>.)

=item * Function C<loadlib> was renamed C<package.loadlib>. (See
compile-time option C<LUA_COMPAT_LOADLIB> in C<luaconf.h>.)

=item * Function C<math.mod> was renamed C<math.fmod>. (See
compile-time option C<LUA_COMPAT_MOD> in C<luaconf.h>.)

=item * Functions C<table.foreach> and C<table.foreachi> are
deprecated. You can use a for loop with C<pairs> or C<ipairs> instead.

=item * There were substantial changes in function C<require> due to
the new module system. However, the new behavior is mostly compatible
with the old, but C<require> gets the path from C<package.path> instead
of from C<LUA_PATH>.

=item * Function C<collectgarbage> has different arguments. Function
C<gcinfo> is deprecated; use C<collectgarbage("count")> instead.

=back

=head2 7.3 - Changes in the API

=over

=item * The C<luaopen_*> functions (to open libraries) cannot be called
directly, like a regular C function. They must be called through Lua,
like a Lua function.

=item * Function C<lua_open> was replaced by C<lua_newstate> to allow
the user to set a memory-allocation function. You can use
C<luaL_newstate> from the standard library to create a state with a
standard allocation function (based on C<realloc>).

=item * Functions C<luaL_getn> and C<luaL_setn> (from the auxiliary
library) are deprecated. Use C<lua_objlen> instead of C<luaL_getn> and
nothing instead of C<luaL_setn>.

=item * Function C<luaL_openlib> was replaced by C<luaL_register>.

=item * Function C<luaL_checkudata> now throws an error when the given
value is not a userdata of the expected type. (In Lua 5.0 it returned
C<NULL>.)

=back

=head1 8 - The Complete Syntax of Lua

Here is the complete syntax of Lua in extended BNF. (It does not
describe operator precedences.)

	chunk ::= {stat [`;´]} [laststat [`;´]]
 
	block ::= chunk
 
	stat ::=  varlist `=´ explist | 
		 functioncall | 
		 do block end | 
		 while exp do block end | 
		 repeat block until exp | 
		 if exp then block {elseif exp then block} [else block] end | 
		 for Name `=´ exp `,´ exp [`,´ exp] do block end | 
		 for namelist in explist do block end | 
		 function funcname funcbody | 
		 local function Name funcbody | 
		 local namelist [`=´ explist] 
 
	laststat ::= return [explist] | break
 
	funcname ::= Name {`.´ Name} [`:´ Name]
 
	varlist ::= var {`,´ var}
 
	var ::=  Name | prefixexp `[´ exp `]´ | prefixexp `.´ Name 
 
	namelist ::= Name {`,´ Name}
 
	explist ::= {exp `,´} exp
 
	exp ::=  nil | false | true | Number | String | `...´ | function | 
		 prefixexp | tableconstructor | exp binop exp | unop exp 
 
	prefixexp ::= var | functioncall | `(´ exp `)´
 
	functioncall ::=  prefixexp args | prefixexp `:´ Name args 
 
	args ::=  `(´ [explist] `)´ | tableconstructor | String 
 
	function ::= function funcbody
 
	funcbody ::= `(´ [parlist] `)´ block end
 
	parlist ::= namelist [`,´ `...´] | `...´
 
	tableconstructor ::= `{´ [fieldlist] `}´
 
	fieldlist ::= field {fieldsep field} [fieldsep]
 
	field ::= `[´ exp `]´ `=´ exp | Name `=´ exp | exp
 
	fieldsep ::= `,´ | `;´
 
	binop ::= `+´ | `-´ | `*´ | `/´ | `^´ | `%´ | `..´ | 
		 `<´ | `<=´ | `>´ | `>=´ | `==´ | `~=´ | 
		 and | or
 
	unop ::= `-´ | not | `#E<acute>


Last update: Mon Feb 13 18:54:19 BRST 2012

=cut
