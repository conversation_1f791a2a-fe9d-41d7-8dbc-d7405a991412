=encoding utf-8

=head1 NAME

ngx_http_limit_conn_module - Module ngx_http_limit_conn_module




=head1



The C<ngx_http_limit_conn_module> module is used to
limit the number of connections per the defined key, in
particular, the number of connections from a single IP address.





Not all connections are counted.
A connection is counted only if it has a request being processed by the server
and the whole request header has already been read.




=head1 Example Configuration




    
    http {
        limit_conn_zone $binary_remote_addr zone=addr:10m;
    
        ...
    
        server {
    
            ...
    
            location /download/ {
                limit_conn addr 1;
            }






=head1 Directives

=head2 limit_conn


B<syntax:> limit_conn I<I<C<zone>> I<C<number>>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Sets the shared memory zone
and the maximum allowed number of connections for a given key value.
When this limit is exceeded, the server will return the
error
in reply to a request.
For example, the directives

    
    limit_conn_zone $binary_remote_addr zone=addr:10m;
    
    server {
        location /download/ {
            limit_conn addr 1;
        }


allow only one connection per an IP address at a time.

B<NOTE>

In HTTPE<sol>2 and HTTPE<sol>3,
each concurrent request is considered a separate connection.






There could be several C<limit_conn> directives.
For example, the following configuration will limit the number
of connections to the server per a client IP and, at the same time,
the total number of connections to the virtual server:

    
    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;
    
    server {
        ...
        limit_conn perip 10;
        limit_conn perserver 100;
    }








These directives are inherited from the previous configuration level
if and only if there are no C<limit_conn> directives
defined on the current level.







=head2 limit_conn_dry_run


B<syntax:> limit_conn_dry_run I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.17.6.





Enables the dry run mode.
In this mode, the number of connections is not limited, however,
in the shared memory zone, the number of excessive connections is accounted
as usual.







=head2 limit_conn_log_level


B<syntax:> limit_conn_log_level I<
C<info> E<verbar>
C<notice> E<verbar>
C<warn> E<verbar>
C<error>>


B<default:> I<error>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 0.8.18.





Sets the desired logging level for cases when the server
limits the number of connections.







=head2 limit_conn_status


B<syntax:> limit_conn_status I<I<C<code>>>


B<default:> I<503>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.3.15.





Sets the status code to return in response to rejected requests.







=head2 limit_conn_zone


B<syntax:> limit_conn_zone I<
    I<C<key>>
    C<zone>=I<C<name>>:I<C<size>>>



B<context:> I<http>





Sets parameters for a shared memory zone
that will keep states for various keys.
In particular, the state includes the current number of connections.
The I<C<key>> can contain text, variables, and their combination.
Requests with an empty key value are not accounted.

B<NOTE>

Prior to version 1.7.6, a I<C<key>> could contain exactly one variable.

Usage example:

    
    limit_conn_zone $binary_remote_addr zone=addr:10m;


Here, a client IP address serves as a key.
Note that instead of C<$remote_addr>, the
C<$binary_remote_addr> variable is used here.
The C<$remote_addr> variable’s size can
vary from 7 to 15 bytes.
The stored state occupies either
32 or 64 bytes of memory on 32-bit platforms and always 64
bytes on 64-bit platforms.
The C<$binary_remote_addr> variable’s size
is always 4 bytes for IPv4 addresses or 16 bytes for IPv6 addresses.
The stored state always occupies 32 or 64 bytes
on 32-bit platforms and 64 bytes on 64-bit platforms.
One megabyte zone can keep about 32 thousand 32-byte states
or about 16 thousand 64-byte states.
If the zone storage is exhausted, the server will return the
error
to all further requests.






B<NOTE>

Additionally, as part of our
commercial subscription,
the
L<status information|ngx_http_api_module>
for each such shared memory zone can be
L<obtained|ngx_http_api_module> or
L<reset|ngx_http_api_module>
with the L<API|ngx_http_api_module> since 1.17.7.








=head2 limit_zone


B<syntax:> limit_zone I<
    I<C<name>>
    I<C<$variable>>
    I<C<size>>>



B<context:> I<http>





This directive was made obsolete in version 1.1.8
and was removed in version 1.7.6.
An equivalent L</limit_conn_zone> directive
with a changed syntax should be used instead:

B<NOTE>

C<limit_conn_zone>
I<C<$variable>>
C<zone>=I<C<name>>:I<C<size>>;








=head1 Embedded Variables




=over



=item C<$limit_conn_status>




keeps the result of limiting the number of connections (1.17.6):
C<PASSED>,
C<REJECTED>, or
C<REJECTED_DRY_RUN>




=back






