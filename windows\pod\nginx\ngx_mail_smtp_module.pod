=encoding utf-8

=head1 NAME

ngx_mail_smtp_module - Module ngx_mail_smtp_module




=head1 Directives

=head2 smtp_auth


B<syntax:> smtp_auth I<I<C<method>> ...>


B<default:> I<plain login>


B<context:> I<mail>


B<context:> I<server>





Sets permitted methods of
L<SASL authentication|https://datatracker.ietf.org/doc/html/rfc2554>
for SMTP clients.
Supported methods are:

=over



=item C<plain>




L<AUTH PLAIN|https://datatracker.ietf.org/doc/html/rfc4616>



=item C<login>




L<AUTH LOGIN|https://datatracker.ietf.org/doc/html/draft-murchison-sasl-login-00>



=item C<cram-md5>




L<AUTH CRAM-MD5|https://datatracker.ietf.org/doc/html/rfc2195>.
In order for this method to work, the password must be stored unencrypted.



=item C<external>




L<AUTH EXTERNAL|https://datatracker.ietf.org/doc/html/rfc4422> (1.11.6).



=item C<none>




Authentication is not required.




=back







Plain text authentication methods
(C<AUTH PLAIN> and C<AUTH LOGIN>)
are always enabled,
though if the C<plain> and C<login> methods
are not specified,
C<AUTH PLAIN> and C<AUTH LOGIN>
will not be automatically included in L</smtp_capabilities>.







=head2 smtp_capabilities


B<syntax:> smtp_capabilities I<I<C<extension>> ...>



B<context:> I<mail>


B<context:> I<server>





Sets the SMTP protocol extensions list
that is passed to the client in response to the
C<EHLO> command.
The authentication methods specified in the L</smtp_auth> directive and
L<STARTTLS|https://datatracker.ietf.org/doc/html/rfc3207>
are automatically added to this list depending on the
L<ngx_mail_ssl_module> directive value.





It makes sense to specify the extensions
supported by the MTA
to which the clients are proxied (if these extensions are related to commands
used after the authentication, when nginx transparently proxies the client
connection to the backend).





The current list of standardized extensions is published at
L<www.iana.org|http://www.iana.org/assignments/mail-parameters>.







=head2 smtp_client_buffer


B<syntax:> smtp_client_buffer I<I<C<size>>>


B<default:> I<4kE<verbar>8k>


B<context:> I<mail>


B<context:> I<server>





Sets the I<C<size>> of the buffer used for reading SMTP commands.
By default, the buffer size is equal to one memory page.
This is either 4K or 8K, depending on a platform.







=head2 smtp_greeting_delay


B<syntax:> smtp_greeting_delay I<I<C<time>>>


B<default:> I<0>


B<context:> I<mail>


B<context:> I<server>





Allows setting a delay before sending an SMTP greeting
in order to reject clients who fail to wait for the greeting before
sending SMTP commands.







