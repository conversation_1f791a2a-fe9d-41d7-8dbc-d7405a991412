local geometry = require('lib/geometry-engine-ins')
local json = require('cjson')

ngx.log(ngx.ERR, 'TestRuleEngineGlue start ' .. type(geometry))

--[[
-- 接口请求体格式为：
{
    "file": "" // 文件的绝对路径
}
]]

--- 加载 XML 文件
---@param filename string XML 文件路径
---@return string XML 文件内容
function load_xml_file(filename)
    local f = io.open(filename, 'r')
    if not f then
        error('无法打开文件：' .. filename)
    end
    local xml = f:read('*a')
    f:close()
    return xml
end

local function main()
    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    if data == nil then
        ngx.say('{"code":1,"msg":"请求体不能为空"}')
        return
    end

    local req = json.decode(data)

    if req.file == nil or req.file == "" then
        ngx.say('{"code":1,"msg":"文件路径不能为空"}')
        return
    end
    -- 获取当前请求的几何引擎实例
    local engine_ins = geometry:get_current_instance()
    
    local xml = load_xml_file(req.file)
    engine_ins:create_engine_object(xml)
    local engine_object = engine_ins:get_current_engine_object()
    local a = 1
    local b = 2
    local content = engine_object:add_test_by_id(a, b)
    engine_object:scene_clear()

    ngx.say("a + b = " .. content)
end

main()
