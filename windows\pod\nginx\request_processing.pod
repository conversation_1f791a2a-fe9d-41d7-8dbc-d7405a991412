=encoding utf-8


=head1 Name


request_processing - How nginx processes a request


=head1 Name-based virtual servers



nginx first decides which I<server> should process the request.
Let’s start with a simple configuration
where all three virtual servers listen on port *:80:

    
    server {
        listen      80;
        server_name example.org www.example.org;
        ...
    }
    
    server {
        listen      80;
        server_name example.net www.example.net;
        ...
    }
    
    server {
        listen      80;
        server_name example.com www.example.com;
        ...
    }








In this configuration nginx tests only the request’s header field
C<Host> to determine which server the request should be routed to.
If its value does not match any server name,
or the request does not contain this header field at all,
then nginx will route the request to the default server for this port.
In the configuration above, the default server is the first
oneE<mdash>which is nginx’s standard default behaviour.
It can also be set explicitly which server should be default,
with the C<default_server> parameter
in the L<ngx_http_core_module> directive:

    
    server {
        listen      80 <b>default_server</b>;
        server_name example.net www.example.net;
        ...
    }




B<NOTE>

The C<default_server> parameter has been available since
version 0.8.21.
In earlier versions the C<default> parameter should be used
instead.

Note that the default server is a property of the listen port
and not of the server name.
More about this later.




=head1 How to prevent processing requests with undefined server names



If requests without the C<Host> header field should not be
allowed, a server that just drops the requests can be defined:

    
    server {
        listen      80;
        server_name "";
        return      444;
    }


Here, the server name is set to an empty string that will match
requests without the C<Host> header field,
and a special nginx’s non-standard code 444
is returned that closes the connection.

B<NOTE>

Since version 0.8.48, this is the default setting for the
server name, so the C<server_name ""> can be omitted.
In earlier versions, the machine’s I<hostname> was used as
a default server name.





=head1 Mixed name-based and IP-based virtual servers



Let’s look at a more complex configuration
where some virtual servers listen on different addresses:

    
    server {
        listen      ***********:80;
        server_name example.org www.example.org;
        ...
    }
    
    server {
        listen      ***********:80;
        server_name example.net www.example.net;
        ...
    }
    
    server {
        listen      ***********:80;
        server_name example.com www.example.com;
        ...
    }


In this configuration, nginx first tests the IP address and port
of the request against the
L<ngx_http_core_module> directives
of the
L<ngx_http_core_module> blocks.
It then tests the C<Host>
header field of the request against the
L<ngx_http_core_module>
entries of the
L<ngx_http_core_module>
blocks that matched
the IP address and port.
If the server name is not found, the request will be processed by
the default server.
For example, a request for C<www.example.com> received on
the ***********:80 port will be handled by the default server
of the ***********:80 port, i.e., by the first server,
since there is no C<www.example.com> defined for this port.





As already stated, a default server is a property of the listen port,
and different default servers may be defined for different ports:

    
    server {
        listen      ***********:80;
        server_name example.org www.example.org;
        ...
    }
    
    server {
        listen      ***********:80 <b>default_server</b>;
        server_name example.net www.example.net;
        ...
    }
    
    server {
        listen      ***********:80 <b>default_server</b>;
        server_name example.com www.example.com;
        ...
    }







=head1 A simple PHP site configuration



Now let’s look at how nginx chooses a I<location> to process a request
for a typical, simple PHP site:

    
    server {
        listen      80;
        server_name example.org www.example.org;
        root        /data/www;
    
        location / {
            index   index.html index.php;
        }
    
        location ~* \.(gif|jpg|png)$ {
            expires 30d;
        }
    
        location ~ \.php$ {
            fastcgi_pass  localhost:9000;
            fastcgi_param SCRIPT_FILENAME
                          $document_root$fastcgi_script_name;
            include       fastcgi_params;
        }
    }








nginx first searches for the most specific prefix location given by
literal strings regardless of the listed order.
In the configuration above
the only prefix location is “C<E<sol>>” and since it matches
any request it will be used as a last resort.
Then nginx checks locations given by
regular expression in the order listed in the configuration file.
The first matching expression stops the search and nginx will use this
location.
If no regular expression matches a request, then nginx uses
the most specific prefix location found earlier.





Note that locations of all types test only a URI part of request line
without arguments.
This is done because arguments in the query string may be given in
several ways, for example:

    
    /index.php?user=john&page=1
    /index.php?page=1&user=john


Besides, anyone may request anything in the query string:

    
    /index.php?page=1&something+else&user=john








Now let’s look at how requests would be processed
in the configuration above:

=over




=item *

A request “C<E<sol>logo.gif>” is matched by the prefix location
“C<E<sol>>” first and then by the regular expression
“C<\.(gifE<verbar>jpgE<verbar>png)$>”,
therefore, it is handled by the latter location.
Using the directive “C<rootE<nbsp>E<sol>dataE<sol>www>” the request
is mapped to the file F<E<sol>dataE<sol>wwwE<sol>logo.gif>, and the file
is sent to the client.



=item *

A request “C<E<sol>index.php>” is also matched by the prefix location
“C<E<sol>>” first and then by the regular expression
“C<\.(php)$>”.
Therefore, it is handled by the latter location
and the request is passed to a FastCGI server listening on localhost:9000.
The
L<ngx_http_fastcgi_module>
directive sets the FastCGI parameter
C<SCRIPT_FILENAME> to “C<E<sol>dataE<sol>wwwE<sol>index.php>”,
and the FastCGI server executes the file.
The variable C<$document_root> is equal to
the value of the
L<ngx_http_core_module>
directive and the variable C<$fastcgi_script_name> is equal to
the request URI, i.e. “C<E<sol>index.php>”.



=item *

A request “C<E<sol>about.html>” is matched by the prefix location
“C<E<sol>>” only, therefore, it is handled in this location.
Using the directive “C<root E<sol>dataE<sol>www>” the request is mapped
to the file F<E<sol>dataE<sol>wwwE<sol>about.html>, and the file is sent
to the client.



=item *

Handling a request “C<E<sol>>” is more complex.
It is matched by the prefix location “C<E<sol>>” only,
therefore, it is handled by this location.
Then the
L<ngx_http_index_module>
directive tests for the existence
of index files according to its parameters and
the “C<root E<sol>dataE<sol>www>” directive.
If the file F<E<sol>dataE<sol>wwwE<sol>index.html> does not exist,
and the file F<E<sol>dataE<sol>wwwE<sol>index.php> exists,
then the directive does an internal redirect to “C<E<sol>index.php>”,
and nginx searches the locations again
as if the request had been sent by a client.
As we saw before, the redirected request will eventually be handled
by the FastCGI server.



=back







