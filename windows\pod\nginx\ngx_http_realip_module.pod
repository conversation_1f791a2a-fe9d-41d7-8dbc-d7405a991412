=encoding utf-8

=head1 NAME

ngx_http_realip_module - Module ngx_http_realip_module




=head1



The C<ngx_http_realip_module> module is used
to change the client address and optional port
to those sent in the specified header field.





This module is not built by default, it should be enabled with the
C<--with-http_realip_module>
configuration parameter.




=head1 Example Configuration




    
    set_real_ip_from  ***********/24;
    set_real_ip_from  ***********;
    set_real_ip_from  2001:0db8::/32;
    real_ip_header    X-Forwarded-For;
    real_ip_recursive on;






=head1 Directives

=head2 set_real_ip_from


B<syntax:> set_real_ip_from I<
    I<C<address>> E<verbar>
    I<C<CIDR>> E<verbar>
    C<unix:>>



B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines trusted addresses that are known to send correct
replacement addresses.
If the special value C<unix:> is specified,
all UNIX-domain sockets will be trusted.
Trusted addresses may also be specified using a hostname (1.13.1).

B<NOTE>

IPv6 addresses are supported starting from versions 1.3.0 and 1.2.1.








=head2 real_ip_header


B<syntax:> real_ip_header I<
    I<C<field>> E<verbar>
    C<X-Real-IP> E<verbar>
    C<X-Forwarded-For> E<verbar>
    C<proxy_protocol>>


B<default:> I<X-Real-IP>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>





Defines the request header field
whose value will be used to replace the client address.





The request header field value that contains an optional port
is also used to replace the client port (1.11.0).
The address and port should be specified according to
L<RFC 3986|https://datatracker.ietf.org/doc/html/rfc3986>.





The C<proxy_protocol> parameter (1.5.12) changes
the client address to the one from the PROXY protocol header.
The PROXY protocol must be previously enabled by setting the
C<proxy_protocol> parameter
in the L<ngx_http_core_module> directive.







=head2 real_ip_recursive


B<syntax:> real_ip_recursive I<C<on> E<verbar> C<off>>


B<default:> I<off>


B<context:> I<http>


B<context:> I<server>


B<context:> I<location>



This directive appeared in version 1.3.0.



This directive appeared in version 1.2.1.





If recursive search is disabled, the original client address that
matches one of the trusted addresses is replaced by the last
address sent in the request header field defined by the
L</real_ip_header> directive.
If recursive search is enabled, the original client address that
matches one of the trusted addresses is replaced by the last
non-trusted address sent in the request header field.







=head1 Embedded Variables




=over



=item C<$realip_remote_addr>




keeps the original client address (1.9.7)



=item C<$realip_remote_port>




keeps the original client port (1.11.0)




=back






