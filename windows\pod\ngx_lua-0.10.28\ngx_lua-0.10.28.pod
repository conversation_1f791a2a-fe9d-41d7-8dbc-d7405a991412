=encoding utf-8


=head1 Name

ngx_http_lua_module - Embed the power of Lua into Nginx HTTP Servers.

This module is a core component of L<OpenResty|https://openresty.org>. If you are using this module,
then you are essentially using OpenResty :)

I<This module is not distributed with the Nginx source.> See
L<the installation instructions>.


=head1 Status

Production ready.


=head1 Version

This document describes ngx_lua
L<v0.10.25|https://github.com/openresty/lua-nginx-module/tags>, which was released
on 19 June 2023.


=head1 Videos


=over


=item *

YouTube video "L<Hello World HTTP Example with OpenRestyE<sol>Lua|https://youtu.be/eSfYLvVQMxw>"

[!L<Hello World HTTP Example with OpenRestyE<sol>Lua|https://img.youtube.com/vi/eSfYLvVQMxw/0.jpg>](https://youtu.be/eSfYLvVQMxw)


=back


=over


=item *

YouTube video "L<Write Your Own Lua Modules in OpenRestyE<sol>Nginx Applications|https://youtu.be/vfYxOMl5LVY>"

[!L<Write Your Own Lua Modules in OpenRestyE<sol>Nginx Applications|https://img.youtube.com/vi/vfYxOMl5LVY/0.jpg>](https://youtu.be/vfYxOMl5LVY)


=back


=over


=item *

YouTube video "L<OpenResty's resty Command-Line Utility Demo|https://youtu.be/L1c7aw4mSOo>"

[!L<OpenResty's resty Command-Line Utility Demo|https://img.youtube.com/vi/L1c7aw4mSOo/0.jpg>](https://youtu.be/L1c7aw4mSOo)


=back


=over


=item *

YouTube video "L<Measure Execution Time of Lua Code Correctly in OpenResty|https://youtu.be/VkRYW_qLoME>"

[!L<Measure Execution Time of Lua Code Correctly in OpenResty|https://img.youtube.com/vi/VkRYW_qLoME/0.jpg>](https://youtu.be/VkRYW_qLoME)


=back


=over


=item *

YouTube video "L<Precompile Lua Modules into LuaJIT Bytecode to Speedup OpenResty Startup|https://youtu.be/EP7c0BM2yNo>"

[!L<Precompile Lua Modules into LuaJIT Bytecode to Speedup OpenResty Startup|https://img.youtube.com/vi/EP7c0BM2yNo/0.jpg>](https://youtu.be/EP7c0BM2yNo)


=back

You are welcome to subscribe to our L<official YouTube channel, OpenResty|https://www.youtube.com/channel/UCXVmwF-UCScv2ftsGoMqxhw>.




=head1 Synopsis


     # set search paths for pure Lua external libraries (';;' is the default path):
     lua_package_path '/foo/bar/?.lua;/blah/?.lua;;';
    
     # set search paths for Lua external libraries written in C (can also use ';;'):
     lua_package_cpath '/bar/baz/?.so;/blah/blah/?.so;;';
    
     server {
         location /lua_content {
             # MIME type determined by default_type:
             default_type 'text/plain';
    
             content_by_lua_block {
                 ngx.say('Hello,world!')
             }
         }
    
         location /nginx_var {
             # MIME type determined by default_type:
             default_type 'text/plain';
    
             # try access /nginx_var?a=hello,world
             content_by_lua_block {
                 ngx.say(ngx.var.arg_a)
             }
         }
    
         location = /request_body {
             client_max_body_size 50k;
             client_body_buffer_size 50k;
    
             content_by_lua_block {
                 ngx.req.read_body()  -- explicitly read the req body
                 local data = ngx.req.get_body_data()
                 if data then
                     ngx.say("body data:")
                     ngx.print(data)
                     return
                 end
    
                 -- body may get buffered in a temp file:
                 local file = ngx.req.get_body_file()
                 if file then
                     ngx.say("body is in file ", file)
                 else
                     ngx.say("no body found")
                 end
             }
         }
    
         # transparent non-blocking I/O in Lua via subrequests
         # (well, a better way is to use cosockets)
         location = /lua {
             # MIME type determined by default_type:
             default_type 'text/plain';
    
             content_by_lua_block {
                 local res = ngx.location.capture("/some_other_location")
                 if res then
                     ngx.say("status: ", res.status)
                     ngx.say("body:")
                     ngx.print(res.body)
                 end
             }
         }
    
         location = /foo {
             rewrite_by_lua_block {
                 res = ngx.location.capture("/memc",
                     { args = { cmd = "incr", key = ngx.var.uri } }
                 )
             }
    
             proxy_pass http://blah.blah.com;
         }
    
         location = /mixed {
             rewrite_by_lua_file /path/to/rewrite.lua;
             access_by_lua_file /path/to/access.lua;
             content_by_lua_file /path/to/content.lua;
         }
    
         # use nginx var in code path
         # CAUTION: contents in nginx var must be carefully filtered,
         # otherwise there'll be great security risk!
         location ~ ^/app/([-_a-zA-Z0-9/]+) {
             set $path $1;
             content_by_lua_file /path/to/lua/app/root/$path.lua;
         }
    
         location / {
            client_max_body_size 100k;
            client_body_buffer_size 100k;
    
            access_by_lua_block {
                -- check the client IP address is in our black list
                if ngx.var.remote_addr == "**********" then
                    ngx.exit(ngx.HTTP_FORBIDDEN)
                end
    
                -- check if the URI contains bad words
                if ngx.var.uri and
                       string.match(ngx.var.request_body, "evil")
                then
                    return ngx.redirect("/terms_of_use.html")
                end
    
                -- tests passed
            }
    
            # proxy_pass/fastcgi_pass/etc settings
         }
     }




=head1 Description

This module embeds L<LuaJIT 2.0E<sol>2.1|https://luajit.org/luajit.html> into Nginx.
It is a core component of L<OpenResty|https://openresty.org>. If you are using
this module, then you are essentially using OpenResty.

Since version C<v0.10.16> of this module, the standard Lua
interpreter (also known as "PUC-Rio Lua") is not supported anymore. This
document interchangeably uses the terms "Lua" and "LuaJIT" to refer to the
LuaJIT interpreter.

By leveraging Nginx's subrequests, this module allows the integration of the
powerful Lua threads (known as Lua "coroutines") into the Nginx event model.

Unlike L<Apache's mod_lua|https://httpd.apache.org/docs/trunk/mod/mod_lua.html>
and L<Lighttpd's mod_magnet|http://redmine.lighttpd.net/wiki/1/Docs:ModMagnet>,
Lua code executed using this module can be I<100% non-blocking> on network
traffic as long as the L<Nginx API for Lua> provided by
this module is used to handle requests to upstream services such as MySQL,
PostgreSQL, Memcached, Redis, or upstream HTTP web services.

At least the following Lua libraries and Nginx modules can be used with this
module:


=over


=item *

L<lua-resty-memcached|https://github.com/openresty/lua-resty-memcached>

=item *

L<lua-resty-mysql|https://github.com/openresty/lua-resty-mysql>

=item *

L<lua-resty-redis|https://github.com/openresty/lua-resty-redis>

=item *

L<lua-resty-dns|https://github.com/openresty/lua-resty-dns>

=item *

L<lua-resty-upload|https://github.com/openresty/lua-resty-upload>

=item *

L<lua-resty-websocket|https://github.com/openresty/lua-resty-websocket>

=item *

L<lua-resty-lock|https://github.com/openresty/lua-resty-lock>

=item *

L<lua-resty-logger-socket|https://github.com/cloudflare/lua-resty-logger-socket>

=item *

L<lua-resty-lrucache|https://github.com/openresty/lua-resty-lrucache>

=item *

L<lua-resty-string|https://github.com/openresty/lua-resty-string>

=item *

L<ngx_memc|http://github.com/openresty/memc-nginx-module>

=item *

L<ngx_postgres|https://github.com/FRiCKLE/ngx_postgres>

=item *

L<ngx_redis2|http://github.com/openresty/redis2-nginx-module>

=item *

L<ngx_redis|http://wiki.nginx.org/HttpRedisModule>

=item *

L<ngx_proxy|http://nginx.org/en/docs/http/ngx_http_proxy_module.html>

=item *

L<ngx_fastcgi|http://nginx.org/en/docs/http/ngx_http_fastcgi_module.html>


=back

Almost any Nginx modules can be used with this ngx_lua module by means of
L<ngx.location.capture> or
L<ngx.location.capture_multi> but it is
recommended to use those C<lua-resty-*> libraries instead of creating
subrequests to access the Nginx upstream modules because the former is usually
much more flexible and memory-efficient.

The Lua interpreter (also known as "Lua State" or "LuaJIT VM instance") is
shared across all the requests in a single Nginx worker process to minimize
memory use. Request contexts are segregated using lightweight Lua coroutines.

Loaded Lua modules persist in the Nginx worker process level resulting in a
small memory footprint in Lua even when under heavy loads.

This module is plugged into Nginx's "http" subsystem so it can only speak
downstream communication protocols in the HTTP family (HTTP 0.9/1.0/1.1/2.0,
WebSockets, etc...).  If you want to do generic TCP communications with the
downstream clients, then you should use the
L<ngx_stream_lua|https://github.com/openresty/stream-lua-nginx-module#readme>
module instead, which offers a compatible Lua API.




=head1 Typical Uses

Just to name a few:


=over


=item *

Mashup'ing and processing outputs of various Nginx upstream outputs (proxy, drizzle, postgres, redis, memcached, etc.) in Lua,

=item *

doing arbitrarily complex access control and security checks in Lua before requests actually reach the upstream backends,

=item *

manipulating response headers in an arbitrary way (by Lua)

=item *

fetching backend information from external storage backends (like redis, memcached, mysql, postgresql) and use that information to choose which upstream backend to access on-the-fly,

=item *

coding up arbitrarily complex web applications in a content handler using synchronous but still non-blocking access to the database backends and other storage,

=item *

doing very complex URL dispatch in Lua at rewrite phase,

=item *

using Lua to implement advanced caching mechanism for Nginx's subrequests and arbitrary locations.


=back

The possibilities are unlimited as the module allows bringing together various
elements within Nginx as well as exposing the power of the Lua language to the
user. The module provides the full flexibility of scripting while offering
performance levels comparable with native C language programs both in terms of
CPU time as well as memory footprint thanks to LuaJIT 2.x.

Other scripting language implementations typically struggle to match this
performance level.




=head1 Nginx Compatibility

The latest version of this module is compatible with the following versions of Nginx:


=over


=item *

1.25.x  (last tested: 1.25.1)

=item *

1.21.x  (last tested: 1.21.4)

=item *

1.19.x  (last tested: 1.19.3)

=item *

1.17.x  (last tested: 1.17.8)

=item *

1.15.x  (last tested: 1.15.8)

=item *

1.14.x

=item *

1.13.x  (last tested: 1.13.6)

=item *

1.12.x

=item *

1.11.x  (last tested: 1.11.2)

=item *

1.10.x

=item *

1.9.x (last tested: 1.9.15)

=item *

1.8.x

=item *

1.7.x (last tested: 1.7.10)

=item *

1.6.x


=back

Nginx cores older than 1.6.0 (exclusive) are I<not> supported.




=head1 Installation

It is I<highly> recommended to use L<OpenResty releases|https://openresty.org>
which bundle Nginx, ngx_lua (this module), LuaJIT, as well as other powerful
companion Nginx modules and Lua libraries.

It is discouraged to build this module with Nginx yourself since it is tricky
to set up exactly right.

Note that Nginx, LuaJIT, and OpenSSL official releases have various limitations
and long-standing bugs that can cause some of this module's features to be
disabled, not work properly, or run slower. Official OpenResty releases are
recommended because they bundle L<OpenResty's optimized LuaJIT 2.1 fork|https://github.com/openresty/luajit2> and
[Nginx/OpenSSL
patches](https://github.com/openresty/openresty/tree/master/patches).

Alternatively, ngx_lua can be manually compiled into Nginx:


=over


=item 1.

LuaJIT can be downloaded from the L<latest release of OpenResty's LuaJIT fork|https://github.com/openresty/luajit2/releases>. The official LuaJIT 2.x releases are also supported, although performance will be significantly lower for reasons elaborated above

=item 2.

Download the latest version of the ngx_devel_kit (NDK) module L<HERE|https://github.com/simplresty/ngx_devel_kit/tags>

=item 3.

Download the latest version of ngx_lua L<HERE|https://github.com/openresty/lua-nginx-module/tags>

=item 4.

Download the latest supported version of Nginx L<HERE|https://nginx.org/> (See L<Nginx Compatibility>)

=item 5.

Download the latest version of the lua-resty-core L<HERE|https://github.com/openresty/lua-resty-core>

=item 6.

Download the latest version of the lua-resty-lrucache L<HERE|https://github.com/openresty/lua-resty-lrucache>


=back

Build the source with this module:


     wget 'https://openresty.org/download/nginx-1.19.3.tar.gz'
     tar -xzvf nginx-1.19.3.tar.gz
     cd nginx-1.19.3/
    
     # tell nginx's build system where to find LuaJIT 2.0:
     export LUAJIT_LIB=/path/to/luajit/lib
     export LUAJIT_INC=/path/to/luajit/include/luajit-2.0
    
     # tell nginx's build system where to find LuaJIT 2.1:
     export LUAJIT_LIB=/path/to/luajit/lib
     export LUAJIT_INC=/path/to/luajit/include/luajit-2.1
    
     # Here we assume Nginx is to be installed under /opt/nginx/.
     ./configure --prefix=/opt/nginx \
             --with-ld-opt="-Wl,-rpath,/path/to/luajit/lib" \
             --add-module=/path/to/ngx_devel_kit \
             --add-module=/path/to/lua-nginx-module
    
     # Note that you may also want to add `./configure` options which are used in your
     # current nginx build.
     # You can get usually those options using command nginx -V
    
     # you can change the parallelism number 2 below to fit the number of spare CPU cores in your
     # machine.
     make -j2
     make install
    
     # Note that this version of lug-nginx-module not allow to set `lua_load_resty_core off;` any more.
     # So, you have to install `lua-resty-core` and `lua-resty-lrucache` manually as below.
    
     cd lua-resty-core
     make install PREFIX=/opt/nginx
     cd lua-resty-lrucache
     make install PREFIX=/opt/nginx
    
     # add necessary `lua_package_path` directive to `nginx.conf`, in the http context
    
     lua_package_path "/opt/nginx/lib/lua/?.lua;;";




=head2 Building as a dynamic module

Starting from NGINX 1.9.11, you can also compile this module as a dynamic module, by using the C<--add-dynamic-module=PATH> option instead of C<--add-module=PATH> on the
C<./configure> command line above. And then you can explicitly load the module in your C<nginx.conf> via the L<load_module|https://nginx.org/en/docs/ngx_core_module.html#load_module>
directive, for example,


     load_module /path/to/modules/ndk_http_module.so;  # assuming NDK is built as a dynamic module too
     load_module /path/to/modules/ngx_http_lua_module.so;




=head2 C Macro Configurations

While building this module either via OpenResty or with the Nginx core, you can define the following C macros via the C compiler options:


=over


=item *

C<NGX_LUA_USE_ASSERT>
When defined, will enable assertions in the ngx_lua C code base. Recommended for debugging or testing builds. It can introduce some (small) runtime overhead when enabled. This macro was first introduced in the C<v0.9.10> release.

=item *

C<NGX_LUA_ABORT_AT_PANIC>
When the LuaJIT VM panics, ngx_lua will instruct the current nginx worker process to quit gracefully by default. By specifying this C macro, ngx_lua will abort the current nginx worker process (which usually results in a core dump file) immediately. This option is useful for debugging VM panics. This option was first introduced in the C<v0.9.8> release.


=back

To enable one or more of these macros, just pass extra C compiler options to the C<./configure> script of either Nginx or OpenResty. For instance,

    ./configure --with-cc-opt="-DNGX_LUA_USE_ASSERT -DNGX_LUA_ABORT_AT_PANIC"




=head1 Community




=head2 English Mailing List

The L<openresty-en|https://groups.google.com/group/openresty-en> mailing list is for English speakers.




=head2 Chinese Mailing List

The L<openresty|https://groups.google.com/group/openresty> mailing list is for Chinese speakers.




=head1 Code Repository

The code repository of this project is hosted on GitHub at
L<openrestyE<sol>lua-nginx-module|https://github.com/openresty/lua-nginx-module>.




=head1 Bugs and Patches

Please submit bug reports, wishlists, or patches by


=over


=item 1.

creating a ticket on the L<GitHub Issue Tracker|https://github.com/openresty/lua-nginx-module/issues>,

=item 2.

or posting to the L<OpenResty community>.


=back




=head1 LuaJIT bytecode support

Watch YouTube video "L<Measure Execution Time of Lua Code Correctly in OpenResty|https://youtu.be/VkRYW_qLoME>"

[!L<Precompile Lua Modules into LuaJIT Bytecode to Speedup OpenResty Startup|https://img.youtube.com/vi/EP7c0BM2yNo/0.jpg>](https://youtu.be/EP7c0BM2yNo)

As from the C<v0.5.0rc32> release, all C<*_by_lua_file> configure directives (such as L<content_by_lua_file>) support loading LuaJIT 2.0/2.1 raw bytecode files directly:


     /path/to/luajit/bin/luajit -b /path/to/input_file.lua /path/to/output_file.ljbc

The C<-bg> option can be used to include debug information in the LuaJIT bytecode file:


     /path/to/luajit/bin/luajit -bg /path/to/input_file.lua /path/to/output_file.ljbc

Please refer to the official LuaJIT documentation on the C<-b> option for more details:

E<lt>https://luajit.org/running.html#opt_bE<gt>

Note that the bytecode files generated by LuaJIT 2.1 is I<not> compatible with
LuaJIT 2.0, and vice versa. The support for LuaJIT 2.1 bytecode was first added
in ngx_lua v0.9.3.

Attempts to load standard Lua 5.1 bytecode files into ngx_lua instances linked
to LuaJIT 2.0/2.1 (or vice versa) will result in an Nginx error message such as
the one below:

    [error] 13909#0: *1 failed to load Lua inlined code: bad byte-code header in /path/to/test_file.luac

Loading bytecode files via the Lua primitives like C<require> and
C<dofile> should always work as expected.




=head1 System Environment Variable Support

If you want to access the system environment variable, say, C<foo>, in Lua via the standard Lua API L<os.getenv|https://www.lua.org/manual/5.1/manual.html#pdf-os.getenv>, then you should also list this environment variable name in your C<nginx.conf> file via the L<env directive|https://nginx.org/en/docs/ngx_core_module.html#env>. For example,


     env foo;




=head1 HTTP 1.0 support

The HTTP 1.0 protocol does not support chunked output and requires an explicit C<Content-Length> header when the response body is not empty in order to support the HTTP 1.0 keep-alive.
So when a HTTP 1.0 request is made and the L<lua_http10_buffering> directive is turned C<on>, ngx_lua will buffer the
output of L<ngx.say> and L<ngx.print> calls and also postpone sending response headers until all the response body output is received.
At that time ngx_lua can calculate the total length of the body and construct a proper C<Content-Length> header to return to the HTTP 1.0 client.
If the C<Content-Length> response header is set in the running Lua code, however, this buffering will be disabled even if the L<lua_http10_buffering> directive is turned C<on>.

For large streaming output responses, it is important to disable the L<lua_http10_buffering> directive to minimise memory usage.

Note that common HTTP benchmark tools such as C<ab> and C<http_load> issue HTTP 1.0 requests by default.
To force C<curl> to send HTTP 1.0 requests, use the C<-0> option.




=head1 Statically Linking Pure Lua Modules

With LuaJIT 2.x, it is possible to statically link the bytecode of pure Lua
modules into the Nginx executable.

You can use the C<luajit> executable to compile C<.lua> Lua
module files to C<.o> object files containing the exported bytecode
data, and then link the C<.o> files directly in your Nginx build.

Below is a trivial example to demonstrate this. Consider that we have the following C<.lua> file named C<foo.lua>:


     -- foo.lua
     local _M = {}
    
     function _M.go()
         print("Hello from foo")
     end
    
     return _M

And then we compile this C<.lua> file to C<foo.o> file:


     /path/to/luajit/bin/luajit -bg foo.lua foo.o

What matters here is the name of the C<.lua> file, which determines how you use this module later on the Lua land. The file name C<foo.o> does not matter at all except the C<.o> file extension (which tells C<luajit> what output format is used). If you want to strip the Lua debug information from the resulting bytecode, you can just specify the C<-b> option above instead of C<-bg>.

Then when building Nginx or OpenResty, pass the C<--with-ld-opt="foo.o"> option to the C<./configure> script:


     ./configure --with-ld-opt="/path/to/foo.o" ...

Finally, you can just do the following in any Lua code run by ngx_lua:


     local foo = require "foo"
     foo.go()

And this piece of code no longer depends on the external C<foo.lua> file any more because it has already been compiled into the C<nginx> executable.

If you want to use dot in the Lua module name when calling C<require>, as in


     local foo = require "resty.foo"

then you need to rename the C<foo.lua> file to C<resty_foo.lua> before compiling it down to a C<.o> file with the C<luajit> command-line utility.

It is important to use exactly the same version of LuaJIT when compiling C<.lua> files to C<.o> files as building nginx + ngx_lua. This is because the LuaJIT bytecode format may be incompatible between different LuaJIT versions. When the bytecode format is incompatible, you will see a Lua runtime error saying that the Lua module is not found.

When you have multiple C<.lua> files to compile and link, then just specify their C<.o> files at the same time in the value of the C<--with-ld-opt> option. For instance,


     ./configure --with-ld-opt="/path/to/foo.o /path/to/bar.o" ...

If you have too many C<.o> files, then it might not be feasible to name them all in a single command. In this case, you can build a static library (or archive) for your C<.o> files, as in


     ar rcus libmyluafiles.a *.o

then you can link the C<myluafiles> archive as a whole to your nginx executable:


     ./configure \
         --with-ld-opt="-L/path/to/lib -Wl,--whole-archive -lmyluafiles -Wl,--no-whole-archive"

where C</path/to/lib> is the path of the directory containing the C<libmyluafiles.a> file. It should be noted that the linker option C<--whole-archive> is required here because otherwise our archive will be skipped because no symbols in our archive are mentioned in the main parts of the nginx executable.




=head1 Data Sharing within an Nginx Worker

To globally share data among all the requests handled by the same Nginx worker
process, encapsulate the shared data into a Lua module, use the Lua
C<require> builtin to import the module, and then manipulate the
shared data in Lua. This works because required Lua modules are loaded only
once and all coroutines will share the same copy of the module (both its code
and data).

Note that the use of global Lua variables is I<strongly discouraged>, as it may
lead to unexpected race conditions between concurrent requests.

Here is a small example on sharing data within an Nginx worker via a Lua module:


     -- mydata.lua
     local _M = {}
    
     local data = {
         dog = 3,
         cat = 4,
         pig = 5,
     }
    
     function _M.get_age(name)
         return data[name]
     end
    
     return _M

and then accessing it from C<nginx.conf>:


     location /lua {
         content_by_lua_block {
             local mydata = require "mydata"
             ngx.say(mydata.get_age("dog"))
         }
     }

The C<mydata> module in this example will only be loaded and run on the first request to the location C</lua>,
and all subsequent requests to the same Nginx worker process will use the reloaded instance of the
module as well as the same copy of the data in it, until a C<HUP> signal is sent to the Nginx master process to force a reload.
This data sharing technique is essential for high performance Lua applications based on this module.

Note that this data sharing is on a I<per-worker> basis and not on a I<per-server> basis. That is, when there are multiple Nginx worker processes under an Nginx master, data sharing cannot cross the process boundary between these workers.

It is usually recommended to share read-only data this way. You can also share changeable data among all the concurrent requests of each Nginx worker process as
long as there is I<no> nonblocking I/O operations (including L<ngx.sleep>)
in the middle of your calculations. As long as you do not give the
control back to the Nginx event loop and ngx_lua's light thread
scheduler (even implicitly), there can never be any race conditions in
between. For this reason, always be very careful when you want to share changeable data on the
worker level. Buggy optimizations can easily lead to hard-to-debug
race conditions under load.

If server-wide data sharing is required, then use one or more of the following approaches:


=over


=item 1.

Use the L<ngx.shared.DICT> API provided by this module.

=item 2.

Use only a single Nginx worker and a single server (this is however not recommended when there is a multi core CPU or multiple CPUs in a single machine).

=item 3.

Use data storage mechanisms such as C<memcached>, C<redis>, C<MySQL> or C<PostgreSQL>. L<The OpenResty official releases|https://openresty.org> come with a set of companion Nginx modules and Lua libraries that provide interfaces with these data storage mechanisms.


=back




=head1 Known Issues




=head2 TCP socket connect operation issues

The L<tcpsock:connect> method may indicate C<success> despite connection failures such as with C<Connection Refused> errors.

However, later attempts to manipulate the cosocket object will fail and return the actual error status message generated by the failed connect operation.

This issue is due to limitations in the Nginx event model and only appears to affect Mac OS X.




=head2 Lua Coroutine Yielding/Resuming


=over


=item *

Because Lua's C<dofile> and C<require> builtins are currently implemented as C functions in LuaJIT 2.0/2.1, if the Lua file being loaded by C<dofile> or C<require> invokes L<ngx.location.capture*>, L<ngx.exec>, L<ngx.exit>, or other API functions requiring yielding in the I<top-level> scope of the Lua file, then the Lua error "attempt to yield across C-call boundary" will be raised. To avoid this, put these calls requiring yielding into your own Lua functions in the Lua file instead of the top-level scope of the file.


=back




=head2 Lua Variable Scope

Care must be taken when importing modules, and this form should be used:


     local xxx = require('xxx')

instead of the old deprecated form:


     require('xxx')

Here is the reason: by design, the global environment has exactly the same lifetime as the Nginx request handler associated with it. Each request handler has its own set of Lua global variables and that is the idea of request isolation. The Lua module is actually loaded by the first Nginx request handler and is cached by the C<require()> built-in in the C<package.loaded> table for later reference, and the C<module()> builtin used by some Lua modules has the side effect of setting a global variable to the loaded module table. But this global variable will be cleared at the end of the request handler,  and every subsequent request handler all has its own (clean) global environment. So one will get Lua exception for accessing the C<nil> value.

The use of Lua global variables is a generally inadvisable in the ngx_lua context as:


=over


=item 1.

the misuse of Lua globals has detrimental side effects on concurrent requests when such variables should instead be local in scope,

=item 2.

Lua global variables require Lua table look-ups in the global environment which is computationally expensive, and

=item 3.

some Lua global variable references may include typing errors which make such difficult to debug.


=back

It is therefore I<highly> recommended to always declare such within an appropriate local scope instead.


     -- Avoid
     foo = 123
     -- Recommended
     local foo = 123
    
     -- Avoid
     function foo() return 123 end
     -- Recommended
     local function foo() return 123 end

To find all instances of Lua global variables in your Lua code, run the L<lua-releng tool|https://github.com/openresty/nginx-devel-utils/blob/master/lua-releng> across all C<.lua> source files:

    $ lua-releng
    Checking use of Lua global variables in file lib/foo/bar.lua ...
            1       [1489]  SETGLOBAL       7 -1    ; contains
            55      [1506]  GETGLOBAL       7 -3    ; setvar
            3       [1545]  GETGLOBAL       3 -4    ; varexpand

The output says that the line 1489 of file C<lib/foo/bar.lua> writes to a global variable named C<contains>, the line 1506 reads from the global variable C<setvar>, and line 1545 reads the global C<varexpand>.

This tool will guarantee that local variables in the Lua module functions are all declared with the C<local> keyword, otherwise a runtime exception will be thrown. It prevents undesirable race conditions while accessing such variables. See L<Data Sharing within an Nginx Worker> for the reasons behind this.




=head2 Locations Configured by Subrequest Directives of Other Modules

The L<ngx.location.capture> and L<ngx.location.capture_multi> directives cannot capture locations that include the L<add_before_body|http://nginx.org/en/docs/http/ngx_http_addition_module.html#add_before_body>, L<add_after_body|http://nginx.org/en/docs/http/ngx_http_addition_module.html#add_after_body>, L<auth_request|https://nginx.org/en/docs/http/ngx_http_auth_request_module.html#auth_request>, L<echo_location|http://github.com/openresty/echo-nginx-module#echo_location>, L<echo_location_async|http://github.com/openresty/echo-nginx-module#echo_location_async>, L<echo_subrequest|http://github.com/openresty/echo-nginx-module#echo_subrequest>, or L<echo_subrequest_async|http://github.com/openresty/echo-nginx-module#echo_subrequest_async> directives.


     location /foo {
         content_by_lua_block {
             res = ngx.location.capture("/bar")
         }
     }
     location /bar {
         echo_location /blah;
     }
     location /blah {
         echo "Success!";
     }


     $ curl -i http://example.com/foo

will not work as expected.




=head2 Cosockets Not Available Everywhere

Due to internal limitations in the Nginx core, the cosocket API is disabled in the following contexts: L<set_by_lua*>, L<log_by_lua*>, L<header_filter_by_lua*>, and L<body_filter_by_lua>.

The cosockets are currently also disabled in the L<init_by_lua*> and L<init_worker_by_lua*> directive contexts but we may add support for these contexts in the future because there is no limitation in the Nginx core (or the limitation might be worked around).

There exists a workaround, however, when the original context does I<not> need to wait for the cosocket results. That is, creating a zero-delay timer via the L<ngx.timer.at> API and do the cosocket results in the timer handler, which runs asynchronously as to the original context creating the timer.




=head2 Special Escaping Sequences

B<NOTE> Following the C<v0.9.17> release, this pitfall can be avoided by using the C<*_by_lua_block {}> configuration directives.

PCRE sequences such as C<\d>, C<\s>, or C<\w>, require special attention because in string literals, the backslash character, C<\>, is stripped out by both the Lua language parser and by the Nginx config file parser before processing if not within a C<*_by_lua_block {}> directive. So the following snippet will not work as expected:


     # nginx.conf
     ? location /test {
     ?     content_by_lua '
     ?         local regex = "\d+"  -- THIS IS WRONG OUTSIDE OF A *_by_lua_block DIRECTIVE
     ?         local m = ngx.re.match("hello, 1234", regex)
     ?         if m then ngx.say(m[0]) else ngx.say("not matched!") end
     ?     ';
     ? }
     # evaluates to "not matched!"

To avoid this, I<double> escape the backslash:


     # nginx.conf
     location /test {
         content_by_lua '
             local regex = "\\\\d+"
             local m = ngx.re.match("hello, 1234", regex)
             if m then ngx.say(m[0]) else ngx.say("not matched!") end
         ';
     }
     # evaluates to "1234"

Here, C<\\\\d+> is stripped down to C<\\d+> by the Nginx config file parser and this is further stripped down to C<\d+> by the Lua language parser before running.

Alternatively, the regex pattern can be presented as a long-bracketed Lua string literal by encasing it in "long brackets", C<[[...]]>, in which case backslashes have to only be escaped once for the Nginx config file parser.


     # nginx.conf
     location /test {
         content_by_lua '
             local regex = [[\\d+]]
             local m = ngx.re.match("hello, 1234", regex)
             if m then ngx.say(m[0]) else ngx.say("not matched!") end
         ';
     }
     # evaluates to "1234"

Here, C<[[\\d+]]> is stripped down to C<[[\d+]]> by the Nginx config file parser and this is processed correctly.

Note that a longer from of the long bracket, C<[=[...]=]>, may be required if the regex pattern contains C<[...]> sequences.
The C<[=[...]=]> form may be used as the default form if desired.


     # nginx.conf
     location /test {
         content_by_lua '
             local regex = [=[[0-9]+]=]
             local m = ngx.re.match("hello, 1234", regex)
             if m then ngx.say(m[0]) else ngx.say("not matched!") end
         ';
     }
     # evaluates to "1234"

An alternative approach to escaping PCRE sequences is to ensure that Lua code is placed in external script files and executed using the various C<*_by_lua_file> directives.
With this approach, the backslashes are only stripped by the Lua language parser and therefore only need to be escaped once each.


     -- test.lua
     local regex = "\\d+"
     local m = ngx.re.match("hello, 1234", regex)
     if m then ngx.say(m[0]) else ngx.say("not matched!") end
     -- evaluates to "1234"

Within external script files, PCRE sequences presented as long-bracketed Lua string literals do not require modification.


     -- test.lua
     local regex = [[\d+]]
     local m = ngx.re.match("hello, 1234", regex)
     if m then ngx.say(m[0]) else ngx.say("not matched!") end
     -- evaluates to "1234"

As noted earlier, PCRE sequences presented within C<*_by_lua_block {}> directives (available following the C<v0.9.17> release) do not require modification.


     # nginx.conf
     location /test {
         content_by_lua_block {
             local regex = [[\d+]]
             local m = ngx.re.match("hello, 1234", regex)
             if m then ngx.say(m[0]) else ngx.say("not matched!") end
         }
     }
     # evaluates to "1234"

B<NOTE> You are recommended to use C<by_lua_file> when the Lua code is very long.




=head2 Mixing with SSI Not Supported

Mixing SSI with ngx_lua in the same Nginx request is not supported at all. Just use ngx_lua exclusively. Everything you can do with SSI can be done atop ngx_lua anyway and it can be more efficient when using ngx_lua.




=head2 SPDY Mode Not Fully Supported

Certain Lua APIs provided by ngx_lua do not work in Nginx's SPDY mode yet: L<ngx.location.capture>, L<ngx.location.capture_multi>, and L<ngx.req.socket>.




=head2 Missing data on short circuited requests

Nginx may terminate a request early with (at least):


=over


=item *

400 (Bad Request)

=item *

405 (Not Allowed)

=item *

408 (Request Timeout)

=item *

413 (Request Entity Too Large)

=item *

414 (Request URI Too Large)

=item *

494 (Request Headers Too Large)

=item *

499 (Client Closed Request)

=item *

500 (Internal Server Error)

=item *

501 (Not Implemented)


=back

This means that phases that normally run are skipped, such as the rewrite or
access phase. This also means that later phases that are run regardless, e.g.
L<log_by_lua>, will not have access to information that is normally set in those
phases.




=head1 TODO


=over


=item *

cosocket: implement LuaSocket's unconnected UDP API.

=item *

cosocket: add support in the context of L<init_by_lua*>.

=item *

cosocket: review and merge aviramc's L<patch|https://github.com/openresty/lua-nginx-module/pull/290> for adding the C<bsdrecv> method.

=item *

cosocket: add configure options for different strategies of handling the cosocket connection exceeding in the pools.

=item *

use C<ngx_hash_t> to optimize the built-in header look-up process for L<ngx.req.set_header>, and etc.

=item *

add C<ignore_resp_headers>, C<ignore_resp_body>, and C<ignore_resp> options to L<ngx.location.capture> and L<ngx.location.capture_multi> methods, to allow micro performance tuning on the user side.

=item *

add automatic Lua code time slicing support by yielding and resuming the Lua VM actively via Lua's debug hooks.

=item *

add C<stat> mode similar to L<mod_lua|https://httpd.apache.org/docs/trunk/mod/mod_lua.html>.


=back




=head1 Changes

The changes made in every release of this module are listed in the change logs of the OpenResty bundle:

E<lt>https://openresty.org/#ChangesE<gt>




=head1 Test Suite

The following dependencies are required to run the test suite:


=over


=item *

Nginx version E<gt>= 1.4.2


=back


=over


=item *

Perl modules:

=over


=item *

Test::Nginx: E<lt>https://github.com/openresty/test-nginxE<gt>


=back


=back


=over


=item *

Nginx modules:

=over


=item *

L<ngx_devel_kit|https://github.com/simplresty/ngx_devel_kit>

=item *

L<ngx_set_misc|https://github.com/openresty/set-misc-nginx-module>

=item *

L<ngx_auth_request|http://mdounin.ru/files/ngx_http_auth_request_module-0.2.tar.gz> (this is not needed if you're using Nginx 1.5.4+.

=item *

L<ngx_echo|https://github.com/openresty/echo-nginx-module>

=item *

L<ngx_memc|https://github.com/openresty/memc-nginx-module>

=item *

L<ngx_srcache|https://github.com/openresty/srcache-nginx-module>

=item *

ngx_lua (i.e., this module)

=item *

L<ngx_lua_upstream|https://github.com/openresty/lua-upstream-nginx-module>

=item *

L<ngx_headers_more|https://github.com/openresty/headers-more-nginx-module>

=item *

L<ngx_drizzle|https://github.com/openresty/drizzle-nginx-module>

=item *

L<ngx_rds_json|https://github.com/openresty/rds-json-nginx-module>

=item *

L<ngx_coolkit|https://github.com/FRiCKLE/ngx_coolkit>

=item *

L<ngx_redis2|https://github.com/openresty/redis2-nginx-module>


=back


=back

The order in which these modules are added during configuration is important because the position of any filter module in the
filtering chain determines the final output, for example. The correct adding order is shown above.


=over


=item *

3rd-party Lua libraries:

=over


=item *

L<lua-cjson|https://www.kyne.au/~mark/software/lua-cjson.php>


=back


=back


=over


=item *

Applications:

=over


=item *

mysql: create database 'ngx_test', grant all privileges to user 'ngx_test', password is 'ngx_test'

=item *

memcached: listening on the default port, 11211.

=item *

redis: listening on the default port, 6379.


=back


=back

See also the L<developer build script|https://github.com/openresty/lua-nginx-module/blob/master/util/build.sh> for more details on setting up the testing environment.

To run the whole test suite in the default testing mode:

    cd /path/to/lua-nginx-module
    export PATH=/path/to/your/nginx/sbin:$PATH
    prove -I/path/to/test-nginx/lib -r t

To run specific test files:

    cd /path/to/lua-nginx-module
    export PATH=/path/to/your/nginx/sbin:$PATH
    prove -I/path/to/test-nginx/lib t/002-content.t t/003-errors.t

To run a specific test block in a particular test file, add the line C<--- ONLY> to the test block you want to run, and then use the C<prove> utility to run that C<.t> file.

There are also various testing modes based on mockeagain, valgrind, and etc. Refer to the L<Test::Nginx documentation|https://search.cpan.org/perldoc?Test::Nginx> for more details for various advanced testing modes. See also the test reports for the Nginx test cluster running on Amazon EC2: E<lt>https://qa.openresty.orgE<gt>.




=head1 Copyright and License

This module is licensed under the BSD license.

Copyright (C) 2009-2017, by Xiaozhe Wang (chaoslawful) E<lt><EMAIL><gt>.

Copyright (C) 2009-2019, by Yichun "agentzh" Zhang (章亦春) E<lt><EMAIL><gt>, OpenResty Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:


=over


=item *

Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.


=back


=over


=item *

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also

Blog posts:


=over


=item *

L<Introduction to Lua-Land CPU Flame Graphs|https://blog.openresty.com/en/lua-cpu-flame-graph/?src=gh_ngxlua>

=item *

L<How OpenResty and Nginx Allocate and Manage Memory|https://blog.openresty.com/en//how-or-alloc-mem?src=gh_ngxlua>

=item *

L<How OpenResty and Nginx Shared Memory Zones Consume RAM|https://blog.openresty.com/en/how-nginx-shm-consume-ram/?src=gh_ngxlua>

=item *

L<Memory Fragmentation in OpenResty and Nginx's Shared Memory Zones|https://blog.openresty.com/en/nginx-shm-frag/?src=gh_ngxlua>


=back

Other related modules and libraries:


=over


=item *

L<ngx_stream_lua_module|https://github.com/openresty/stream-lua-nginx-module#readme> for an official port of this module for the Nginx "stream" subsystem (doing generic downstream TCP communications).

=item *

L<lua-resty-memcached|https://github.com/openresty/lua-resty-memcached> library based on ngx_lua cosocket.

=item *

L<lua-resty-redis|https://github.com/openresty/lua-resty-redis> library based on ngx_lua cosocket.

=item *

L<lua-resty-mysql|https://github.com/openresty/lua-resty-mysql> library based on ngx_lua cosocket.

=item *

L<lua-resty-upload|https://github.com/openresty/lua-resty-upload> library based on ngx_lua cosocket.

=item *

L<lua-resty-dns|https://github.com/openresty/lua-resty-dns> library based on ngx_lua cosocket.

=item *

L<lua-resty-websocket|https://github.com/openresty/lua-resty-websocket> library for both WebSocket server and client, based on ngx_lua cosocket.

=item *

L<lua-resty-string|https://github.com/openresty/lua-resty-string> library based on L<LuaJIT FFI|https://luajit.org/ext_ffi.html>.

=item *

L<lua-resty-lock|https://github.com/openresty/lua-resty-lock> library for a nonblocking simple lock API.

=item *

L<lua-resty-cookie|https://github.com/cloudflare/lua-resty-cookie> library for HTTP cookie manipulation.

=item *

L<Routing requests to different MySQL queries based on URI arguments|https://openresty.org/#RoutingMySQLQueriesBasedOnURIArgs>

=item *

L<Dynamic Routing Based on Redis and Lua|https://openresty.org/#DynamicRoutingBasedOnRedis>

=item *

L<Using LuaRocks with ngx_lua|https://openresty.org/#UsingLuaRocks>

=item *

L<Introduction to ngx_lua|https://github.com/openresty/lua-nginx-module/wiki/Introduction>

=item *

L<ngx_devel_kit|https://github.com/simplresty/ngx_devel_kit>

=item *

L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>

=item *

L<drizzle-nginx-module|http://github.com/openresty/drizzle-nginx-module>

=item *

L<postgres-nginx-module|https://github.com/FRiCKLE/ngx_postgres>

=item *

L<memc-nginx-module|http://github.com/openresty/memc-nginx-module>

=item *

L<The OpenResty bundle|https://openresty.org>

=item *

L<Nginx Systemtap Toolkit|https://github.com/openresty/nginx-systemtap-toolkit>


=back




=head1 Directives


=over


=item *

L<lua_load_resty_core>

=item *

L<lua_capture_error_log>

=item *

L<lua_use_default_type>

=item *

L<lua_malloc_trim>

=item *

L<lua_code_cache>

=item *

L<lua_thread_cache_max_entries>

=item *

L<lua_regex_cache_max_entries>

=item *

L<lua_regex_match_limit>

=item *

L<lua_package_path>

=item *

L<lua_package_cpath>

=item *

L<init_by_lua>

=item *

L<init_by_lua_block>

=item *

L<init_by_lua_file>

=item *

L<init_worker_by_lua>

=item *

L<init_worker_by_lua_block>

=item *

L<init_worker_by_lua_file>

=item *

L<exit_worker_by_lua_block>

=item *

L<exit_worker_by_lua_file>

=item *

L<set_by_lua>

=item *

L<set_by_lua_block>

=item *

L<set_by_lua_file>

=item *

L<content_by_lua>

=item *

L<content_by_lua_block>

=item *

L<content_by_lua_file>

=item *

L<server_rewrite_by_lua_block>

=item *

L<server_rewrite_by_lua_file>

=item *

L<rewrite_by_lua>

=item *

L<rewrite_by_lua_block>

=item *

L<rewrite_by_lua_file>

=item *

L<access_by_lua>

=item *

L<access_by_lua_block>

=item *

L<access_by_lua_file>

=item *

L<header_filter_by_lua>

=item *

L<header_filter_by_lua_block>

=item *

L<header_filter_by_lua_file>

=item *

L<body_filter_by_lua>

=item *

L<body_filter_by_lua_block>

=item *

L<body_filter_by_lua_file>

=item *

L<log_by_lua>

=item *

L<log_by_lua_block>

=item *

L<log_by_lua_file>

=item *

L<balancer_by_lua_block>

=item *

L<balancer_by_lua_file>

=item *

L<balancer_keepalive>

=item *

L<lua_need_request_body>

=item *

L<ssl_client_hello_by_lua_block>

=item *

L<ssl_client_hello_by_lua_file>

=item *

L<ssl_certificate_by_lua_block>

=item *

L<ssl_certificate_by_lua_file>

=item *

L<ssl_session_fetch_by_lua_block>

=item *

L<ssl_session_fetch_by_lua_file>

=item *

L<ssl_session_store_by_lua_block>

=item *

L<ssl_session_store_by_lua_file>

=item *

L<lua_shared_dict>

=item *

L<lua_socket_connect_timeout>

=item *

L<lua_socket_send_timeout>

=item *

L<lua_socket_send_lowat>

=item *

L<lua_socket_read_timeout>

=item *

L<lua_socket_buffer_size>

=item *

L<lua_socket_pool_size>

=item *

L<lua_socket_keepalive_timeout>

=item *

L<lua_socket_log_errors>

=item *

L<lua_ssl_ciphers>

=item *

L<lua_ssl_crl>

=item *

L<lua_ssl_protocols>

=item *

L<lua_ssl_certificate>

=item *

L<lua_ssl_certificate_key>

=item *

L<lua_ssl_trusted_certificate>

=item *

L<lua_ssl_verify_depth>

=item *

L<lua_ssl_conf_command>

=item *

L<lua_http10_buffering>

=item *

L<rewrite_by_lua_no_postpone>

=item *

L<access_by_lua_no_postpone>

=item *

L<lua_transform_underscores_in_response_headers>

=item *

L<lua_check_client_abort>

=item *

L<lua_max_pending_timers>

=item *

L<lua_max_running_timers>

=item *

L<lua_sa_restart>

=item *

L<lua_worker_thread_vm_pool_size>


=back

The basic building blocks of scripting Nginx with Lua are directives. Directives are used to specify when the user Lua code is run and
how the result will be used. Below is a diagram showing the order in which directives are executed.

!L<Lua Nginx Modules Directives|./doc/images/lua_nginx_modules_directives.drawio.png>




=head2 lua_load_resty_core

B<syntax:> I<lua_load_resty_core on|off>

B<default:> I<lua_load_resty_core on>

B<context:> I<http>

This directive is deprecated since the C<v0.10.16> release of this
module. The C<resty.core> module from
L<lua-resty-core|https://github.com/openresty/lua-resty-core> is now mandatorily
loaded during the Lua VM initialization. Specifying this directive will have no
effect.

This directive was first introduced in the C<v0.10.15> release and
used to optionally load the C<resty.core> module.




=head2 lua_capture_error_log

B<syntax:> I<lua_capture_error_log size>

B<default:> I<none>

B<context:> I<http>

Enables a buffer of the specified C<size> for capturing all the Nginx error log message data (not just those produced
by this module or the Nginx http subsystem, but everything) without touching files or disks.

You can use units like C<k> and C<m> in the C<size> value, as in


     lua_capture_error_log 100k;

As a rule of thumb, a 4KB buffer can usually hold about 20 typical error log messages. So do the maths!

This buffer never grows. If it is full, new error log messages will replace the oldest ones in the buffer.

The size of the buffer must be bigger than the maximum length of a single error log message (which is 4K in OpenResty and 2K in stock NGINX).

You can read the messages in the buffer on the Lua land via the
L<get_logs()|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/errlog.md#get_logs>
function of the
L<ngx.errlog|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/errlog.md#readme>
module of the L<lua-resty-core|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/errlog.md#readme>
library. This Lua API function will return the captured error log messages and
also remove these already read from the global capturing buffer, making room
for any new error log data. For this reason, the user should not configure this
buffer to be too big if the user read the buffered error log data fast enough.

Note that the log level specified in the standard L<error_log|https://nginx.org/r/error_log> directive
I<does> have effect on this capturing facility. It only captures log
messages of a level no lower than the specified log level in the L<error_log|https://nginx.org/r/error_log> directive.
The user can still choose to set an even higher filtering log level on the fly via the Lua API function
L<errlog.set_filter_level|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/errlog.md#set_filter_level>.
So it is more flexible than the static L<error_log|https://nginx.org/r/error_log> directive.

It is worth noting that there is no way to capture the debugging logs
without building OpenResty or Nginx with the C<./configure>
option C<--with-debug>. And enabling debugging logs is
strongly discouraged in production builds due to high overhead.

This directive was first introduced in the C<v0.10.9> release.




=head2 lua_use_default_type

B<syntax:> I<lua_use_default_type on | off>

B<default:> I<lua_use_default_type on>

B<context:> I<http, server, location, location if>

Specifies whether to use the MIME type specified by the L<default_type|https://nginx.org/en/docs/http/ngx_http_core_module.html#default_type> directive for the default value of the C<Content-Type> response header. Deactivate this directive if a default C<Content-Type> response header for Lua request handlers is not desired.

This directive is turned on by default.

This directive was first introduced in the C<v0.9.1> release.




=head2 lua_malloc_trim

B<syntax:> I<lua_malloc_trim E<lt>request-countE<gt>>

B<default:> I<lua_malloc_trim 1000>

B<context:> I<http>

Asks the underlying C<libc> runtime library to release its cached free memory back to the operating system every
C<N> requests processed by the Nginx core. By default, C<N> is 1000. You can configure the request count
by using your own numbers. Smaller numbers mean more frequent releases, which may introduce higher CPU time consumption and
smaller memory footprint while larger numbers usually lead to less CPU time overhead and relatively larger memory footprint.
Just tune the number for your own use cases.

Configuring the argument to C<0> essentially turns off the periodical memory trimming altogether.


     lua_malloc_trim 0;  # turn off trimming completely

The current implementation uses an Nginx log phase handler to do the request counting. So the appearance of the
L<log_subrequest on|https://nginx.org/en/docs/http/ngx_http_core_module.html#log_subrequest> directives in C<nginx.conf>
may make the counting faster when subrequests are involved. By default, only "main requests" count.

Note that this directive does I<not> affect the memory allocated by LuaJIT's own allocator based on the C<mmap>
system call.

This directive was first introduced in the C<v0.10.7> release.




=head2 lua_code_cache

B<syntax:> I<lua_code_cache on | off>

B<default:> I<lua_code_cache on>

B<context:> I<http, server, location, location if>

Enables or disables the Lua code cache for Lua code in C<*_by_lua_file> directives (like L<set_by_lua_file> and
L<content_by_lua_file>) and Lua modules.

When turning off, every request served by ngx_lua will run in a separate Lua VM instance, starting from the C<0.9.3> release. So the Lua files referenced in L<set_by_lua_file>,
L<content_by_lua_file>, L<access_by_lua_file>,
and etc will not be cached
and all Lua modules used will be loaded from scratch. With this in place, developers can adopt an edit-and-refresh approach.

Please note however, that Lua code written inlined within nginx.conf
such as those specified by L<set_by_lua>, L<content_by_lua>,
L<access_by_lua>, and L<rewrite_by_lua> will not be updated when you edit the inlined Lua code in your C<nginx.conf> file because only the Nginx config file parser can correctly parse the C<nginx.conf>
file and the only way is to reload the config file
by sending a C<HUP> signal or just to restart Nginx.

Even when the code cache is enabled, Lua files which are loaded by C<dofile> or C<loadfile>
in *_by_lua_file cannot be cached (unless you cache the results yourself). Usually you can either use the L<init_by_lua>
or L<init_by_lua_file> directives to load all such files or just make these Lua files true Lua modules
and load them via C<require>.

The ngx_lua module does not support the C<stat> mode available with the
Apache C<mod_lua> module (yet).

Disabling the Lua code cache is strongly
discouraged for production use and should only be used during
development as it has a significant negative impact on overall performance. For example, the performance of a "hello world" Lua example can drop by an order of magnitude after disabling the Lua code cache.




=head2 lua_thread_cache_max_entries

B<syntax:> I<lua_thread_cache_max_entries E<lt>numE<gt>>

B<default:> I<lua_thread_cache_max_entries 1024>

B<context:> I<http>

Specifies the maximum number of entries allowed in the worker process level lua thread object cache.

This cache recycles the lua thread GC objects among all our "light threads".

A zero value of C<< <num> >> disables the cache.

Note that this feature requires OpenResty's LuaJIT with the new C API C<lua_resetthread>.

This feature was first introduced in verson C<v0.10.9>.




=head2 lua_regex_cache_max_entries

B<syntax:> I<lua_regex_cache_max_entries E<lt>numE<gt>>

B<default:> I<lua_regex_cache_max_entries 1024>

B<context:> I<http>

Specifies the maximum number of entries allowed in the worker process level compiled regex cache.

The regular expressions used in L<ngx.re.match>, L<ngx.re.gmatch>, L<ngx.re.sub>, and L<ngx.re.gsub> will be cached within this cache if the regex option C<o> (i.e., compile-once flag) is specified.

The default number of entries allowed is 1024 and when this limit is reached, new regular expressions will not be cached (as if the C<o> option was not specified) and there will be one, and only one, warning in the C<error.log> file:

    2011/08/27 23:18:26 [warn] 31997#0: *1 lua exceeding regex cache max entries (1024), ...

If you are using the C<ngx.re.*> implementation of L<lua-resty-core|https://github.com/openresty/lua-resty-core> by loading the C<resty.core.regex> module (or just the C<resty.core> module), then an LRU cache is used for the regex cache being used here.

Do not activate the C<o> option for regular expressions (and/or C<replace> string arguments for L<ngx.re.sub> and L<ngx.re.gsub>) that are generated I<on the fly> and give rise to infinite variations to avoid hitting the specified limit.




=head2 lua_regex_match_limit

B<syntax:> I<lua_regex_match_limit E<lt>numE<gt>>

B<default:> I<lua_regex_match_limit 0>

B<context:> I<http>

Specifies the "match limit" used by the PCRE library when executing the L<ngx.re API>. To quote the PCRE manpage, "the limit ... has the effect of limiting the amount of backtracking that can take place."

When the limit is hit, the error string "pcre_exec() failed: -8" will be returned by the L<ngx.re API> functions on the Lua land.

When setting the limit to 0, the default "match limit" when compiling the PCRE library is used. And this is the default value of this directive.

This directive was first introduced in the C<v0.8.5> release.




=head2 lua_package_path

B<syntax:> I<lua_package_path E<lt>lua-style-path-strE<gt>>

B<default:> I<The content of LUA_PATH environment variable or Lua's compiled-in defaults.>

B<context:> I<http>

Sets the Lua module search path used by scripts specified by L<set_by_lua>,
L<content_by_lua> and others. The path string is in standard Lua path form, and C<;;>
can be used to stand for the original search paths.

As from the C<v0.5.0rc29> release, the special notation C<$prefix> or C<${prefix}> can be used in the search path string to indicate the path of the C<server prefix> usually determined by the C<-p PATH> command-line option while starting the Nginx server.




=head2 lua_package_cpath

B<syntax:> I<lua_package_cpath E<lt>lua-style-cpath-strE<gt>>

B<default:> I<The content of LUA_CPATH environment variable or Lua's compiled-in defaults.>

B<context:> I<http>

Sets the Lua C-module search path used by scripts specified by L<set_by_lua>,
L<content_by_lua> and others. The cpath string is in standard Lua cpath form, and C<;;>
can be used to stand for the original cpath.

As from the C<v0.5.0rc29> release, the special notation C<$prefix> or C<${prefix}> can be used in the search path string to indicate the path of the C<server prefix> usually determined by the C<-p PATH> command-line option while starting the Nginx server.




=head2 init_by_lua

B<syntax:> I<init_by_lua E<lt>lua-script-strE<gt>>

B<context:> I<http>

B<phase:> I<loading-config>

B<NOTE> Use of this directive is I<discouraged> following the C<v0.9.17> release. Use the L<init_by_lua_block> directive instead.

Similar to the L<init_by_lua_block> directive, but accepts the Lua source directly in an Nginx string literal (which requires
special character escaping).

For instance,


     init_by_lua '
         print("I need no extra escaping here, for example: \r\nblah")
     '

This directive was first introduced in the C<v0.5.5> release.




=head2 init_by_lua_block

B<syntax:> I<init_by_lua_block { lua-script }>

B<context:> I<http>

B<phase:> I<loading-config>

When Nginx receives the C<HUP> signal and starts reloading the config file, the Lua VM will also be re-created and C<init_by_lua_block> will run again on the new Lua VM. In case that the L<lua_code_cache> directive is turned off (default on), the C<init_by_lua_block> handler will run upon every request because in this special mode a standalone Lua VM is always created for each request.

Usually you can pre-load Lua modules at server start-up by means of this hook and take advantage of modern operating systems' copy-on-write (COW) optimization. Here is an example for pre-loading Lua modules:


     # this runs before forking out nginx worker processes:
     init_by_lua_block { require "cjson" }
    
     server {
         location = /api {
             content_by_lua_block {
                 -- the following require() will just  return
                 -- the already loaded module from package.loaded:
                 ngx.say(require "cjson".encode{dog = 5, cat = 6})
             }
         }
     }

You can also initialize the L<lua_shared_dict> shm storage at this phase. Here is an example for this:


     lua_shared_dict dogs 1m;
    
     init_by_lua_block {
         local dogs = ngx.shared.dogs
         dogs:set("Tom", 56)
     }
    
     server {
         location = /api {
             content_by_lua_block {
                 local dogs = ngx.shared.dogs
                 ngx.say(dogs:get("Tom"))
             }
         }
     }

But note that, the L<lua_shared_dict>'s shm storage will not be cleared through a config reload (via the C<HUP> signal, for example). So if you do I<not> want to re-initialize the shm storage in your C<init_by_lua_block> code in this case, then you just need to set a custom flag in the shm storage and always check the flag in your C<init_by_lua_block> code.

Because the Lua code in this context runs before Nginx forks its worker processes (if any), data or code loaded here will enjoy the L<Copy-on-write (COW)|https://en.wikipedia.org/wiki/Copy-on-write> feature provided by many operating systems among all the worker processes, thus saving a lot of memory.

Do I<not> initialize your own Lua global variables in this context because use of Lua global variables have performance penalties and can lead to global namespace pollution (see the L<Lua Variable Scope> section for more details). The recommended way is to use proper L<Lua module|https://www.lua.org/manual/5.1/manual.html#5.3> files (but do not use the standard Lua function L<module()|https://www.lua.org/manual/5.1/manual.html#pdf-module> to define Lua modules because it pollutes the global namespace as well) and call L<require()|https://www.lua.org/manual/5.1/manual.html#pdf-require> to load your own module files in C<init_by_lua_block> or other contexts (L<require()|https://www.lua.org/manual/5.1/manual.html#pdf-require> does cache the loaded Lua modules in the global C<package.loaded> table in the Lua registry so your modules will only loaded once for the whole Lua VM instance).

Only a small set of the L<Nginx API for Lua> is supported in this context:


=over


=item *

Logging APIs: L<ngx.log> and L<print>,

=item *

Shared Dictionary API: L<ngx.shared.DICT>.


=back

More Nginx APIs for Lua may be supported in this context upon future user requests.

Basically you can safely use Lua libraries that do blocking I/O in this very context because blocking the master process during server start-up is completely okay. Even the Nginx core does blocking I/O (at least on resolving upstream's host names) at the configure-loading phase.

You should be very careful about potential security vulnerabilities in your Lua code registered in this context because the Nginx master process is often run under the C<root> account.

This directive was first introduced in the C<v0.9.17> release.

See also the following blog posts for more details on OpenResty and Nginx's shared memory zones:


=over


=item *

L<How OpenResty and Nginx Shared Memory Zones Consume RAM|https://blog.openresty.com/en/how-nginx-shm-consume-ram/?src=gh_ngxlua>

=item *

L<Memory Fragmentation in OpenResty and Nginx's Shared Memory Zones|https://blog.openresty.com/en/nginx-shm-frag/?src=gh_ngxlua>


=back




=head2 init_by_lua_file

B<syntax:> I<init_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<http>

B<phase:> I<loading-config>

Equivalent to L<init_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code or L<LuaJIT bytecode> to be executed.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

This directive was first introduced in the C<v0.5.5> release.




=head2 init_worker_by_lua

B<syntax:> I<init_worker_by_lua E<lt>lua-script-strE<gt>>

B<context:> I<http>

B<phase:> I<starting-worker>

B<NOTE> Use of this directive is I<discouraged> following the C<v0.9.17> release. Use the L<init_worker_by_lua_block> directive instead.

Similar to the L<init_worker_by_lua_block> directive, but accepts the Lua source directly in an Nginx string literal (which requires
special character escaping).

For instance,


     init_worker_by_lua '
         print("I need no extra escaping here, for example: \r\nblah")
     ';

This directive was first introduced in the C<v0.9.5> release.

This hook no longer runs in the cache manager and cache loader processes since the C<v0.10.12> release.




=head2 init_worker_by_lua_block

B<syntax:> I<init_worker_by_lua_block { lua-script }>

B<context:> I<http>

B<phase:> I<starting-worker>

Runs the specified Lua code upon every Nginx worker process's startup when the master process is enabled. When the master process is disabled, this hook will just run after L<init_by_lua*>.

This hook is often used to create per-worker reoccurring timers (via the L<ngx.timer.at> Lua API), either for backend health-check or other timed routine work. Below is an example,


     init_worker_by_lua_block {
         local delay = 3  -- in seconds
         local new_timer = ngx.timer.at
         local log = ngx.log
         local ERR = ngx.ERR
         local check
    
         check = function(premature)
             if not premature then
                 -- do the health check or other routine work
                 local ok, err = new_timer(delay, check)
                 if not ok then
                     log(ERR, "failed to create timer: ", err)
                     return
                 end
             end
    
             -- do something in timer
         end
    
         local hdl, err = new_timer(delay, check)
         if not hdl then
             log(ERR, "failed to create timer: ", err)
             return
         end
    
         -- other job in init_worker_by_lua
     }

This directive was first introduced in the C<v0.9.17> release.

This hook no longer runs in the cache manager and cache loader processes since the C<v0.10.12> release.




=head2 init_worker_by_lua_file

B<syntax:> I<init_worker_by_lua_file E<lt>lua-file-pathE<gt>>

B<context:> I<http>

B<phase:> I<starting-worker>

Similar to L<init_worker_by_lua_block>, but accepts the file path to a Lua source file or Lua bytecode file.

This directive was first introduced in the C<v0.9.5> release.

This hook no longer runs in the cache manager and cache loader processes since the C<v0.10.12> release.




=head2 exit_worker_by_lua_block

B<syntax:> I<exit_worker_by_lua_block { lua-script }>

B<context:> I<http>

B<phase:> I<exiting-worker>

Runs the specified Lua code upon every Nginx worker process's exit when the master process is enabled. When the master process is disabled, this hook will run before the Nginx process exits.

This hook is often used to release resources allocated by each worker (e.g. resources allocated by L<init_worker_by_lua*>), or to prevent workers from exiting abnormally.

For example,


     exit_worker_by_lua_block {
         print("log from exit_worker_by_lua_block")
     }

It's not allowed to create a timer (even a 0-delay timer) here since it runs after all timers have been processed.

This directive was first introduced in the C<v0.10.18> release.




=head2 exit_worker_by_lua_file

B<syntax:> I<exit_worker_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<http>

B<phase:> I<exiting-worker>

Similar to L<exit_worker_by_lua_block>, but accepts the file path to a Lua source file or Lua bytecode file.

This directive was first introduced in the C<v0.10.18> release.




=head2 set_by_lua

B<syntax:> I<set_by_lua $res E<lt>lua-script-strE<gt> [$arg1 $arg2 ...]>

B<context:> I<server, server if, location, location if>

B<phase:> I<rewrite>

B<NOTE> Use of this directive is I<discouraged> following the C<v0.9.17> release. Use the L<set_by_lua_block> directive instead.

Similar to the L<set_by_lua_block> directive, but accepts the Lua source directly in an Nginx string literal (which requires
special character escaping), and

=over


=item 1.

this directive support extra arguments after the Lua script.


=back

For example,


     set_by_lua $res ' return 32 + math.cos(32) ';
     # $res now has the value "32.834223360507" or alike.

As from the C<v0.5.0rc29> release, Nginx variable interpolation is disabled in the C<< <lua-script-str> >> argument of this directive and therefore, the dollar sign character (C<$>) can be used directly.

This directive requires the L<ngx_devel_kit|https://github.com/simplresty/ngx_devel_kit> module.




=head2 set_by_lua_block

B<syntax:> I<set_by_lua_block $res { lua-script }>

B<context:> I<server, server if, location, location if>

B<phase:> I<rewrite>

Executes code specified inside a pair of curly braces (C<{}>), and returns string output to C<$res>.
The code inside a pair of curly braces (C<{}>) can make L<API calls> and can retrieve input arguments from the C<ngx.arg> table (index starts from C<1> and increases sequentially).

This directive is designed to execute short, fast running code blocks as the Nginx event loop is blocked during code execution. Time consuming code sequences should therefore be avoided.

This directive is implemented by injecting custom commands into the standard L<ngx_http_rewrite_module|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html>'s command list. Because L<ngx_http_rewrite_module|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html> does not support nonblocking I/O in its commands, Lua APIs requiring yielding the current Lua "light thread" cannot work in this directive.

At least the following API functions are currently disabled within the context of C<set_by_lua_block>:


=over


=item *

Output API functions (e.g., L<ngx.say> and L<ngx.send_headers>)

=item *

Control API functions (e.g., L<ngx.exit>)

=item *

Subrequest API functions (e.g., L<ngx.location.capture> and L<ngx.location.capture_multi>)

=item *

Cosocket API functions (e.g., L<ngx.socket.tcp> and L<ngx.req.socket>).

=item *

Sleeping API function L<ngx.sleep>.


=back

In addition, note that this directive can only write out a value to a single Nginx variable at
a time. However, a workaround is possible using the L<ngx.var.VARIABLE> interface.


     location /foo {
         set $diff ''; # we have to predefine the $diff variable here
    
         set_by_lua_block $sum {
             local a = 32
             local b = 56
    
             ngx.var.diff = a - b  -- write to $diff directly
             return a + b          -- return the $sum value normally
         }
    
         echo "sum = $sum, diff = $diff";
     }

This directive can be freely mixed with all directives of the L<ngx_http_rewrite_module|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html>, L<set-misc-nginx-module|http://github.com/openresty/set-misc-nginx-module>, and L<array-var-nginx-module|http://github.com/openresty/array-var-nginx-module> modules. All of these directives will run in the same order as they appear in the config file.


     set $foo 32;
     set_by_lua_block $bar { return tonumber(ngx.var.foo) + 1 }
     set $baz "bar: $bar";  # $baz == "bar: 33"

No special escaping is required in the Lua code block.

This directive requires the L<ngx_devel_kit|https://github.com/simplresty/ngx_devel_kit> module.

This directive was first introduced in the C<v0.9.17> release.




=head2 set_by_lua_file

B<syntax:> I<set_by_lua_file $res E<lt>path-to-lua-script-fileE<gt> [$arg1 $arg2 ...]>

B<context:> I<server, server if, location, location if>

B<phase:> I<rewrite>

Equivalent to L<set_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or, as from the C<v0.5.0rc32> release, the L<LuaJIT bytecode> to be executed.

Nginx variable interpolation is supported in the C<< <path-to-lua-script-file> >> argument string of this directive. But special care must be taken for injection attacks.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

When the Lua code cache is turned on (by default), the user code is loaded once at the first request and cached
and the Nginx config must be reloaded each time the Lua source file is modified.
The Lua code cache can be temporarily disabled during development by
switching L<lua_code_cache> C<off> in C<nginx.conf> to avoid reloading Nginx.

This directive requires the L<ngx_devel_kit|https://github.com/simplresty/ngx_devel_kit> module.




=head2 content_by_lua

B<syntax:> I<content_by_lua E<lt>lua-script-strE<gt>>

B<context:> I<location, location if>

B<phase:> I<content>

B<NOTE> Use of this directive is I<discouraged> following the C<v0.9.17> release. Use the L<content_by_lua_block> directive instead.

Similar to the L<content_by_lua_block> directive, but accepts the Lua source directly in an Nginx string literal (which requires
special character escaping).

For instance,


     content_by_lua '
         ngx.say("I need no extra escaping here, for example: \r\nblah")
     ';




=head2 content_by_lua_block

B<syntax:> I<content_by_lua_block { lua-script }>

B<context:> I<location, location if>

B<phase:> I<content>

For instance,


     content_by_lua_block {
         ngx.say("I need no extra escaping here, for example: \r\nblah")
     }

Acts as a "content handler" and executes Lua code string specified in C<{ lua-script }> for every request.
The Lua code may make L<API calls> and is executed as a new spawned coroutine in an independent global environment (i.e. a sandbox).

Do not use this directive and other content handler directives in the same location. For example, this directive and the L<proxy_pass|http://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_pass> directive should not be used in the same location.

This directive was first introduced in the C<v0.9.17> release.




=head2 content_by_lua_file

B<syntax:> I<content_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<location, location if>

B<phase:> I<content>

Equivalent to L<content_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or, as from the C<v0.5.0rc32> release, the L<LuaJIT bytecode> to be executed.

If the file is not found, a C<404 Not Found> status code will be returned, and a C<503 Service Temporarily Unavailable> status code will be returned in case of errors in reading other files.

Nginx variables can be used in the C<< <path-to-lua-script-file> >> string to provide flexibility. This however carries some risks and is not ordinarily recommended.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

When the Lua code cache is turned on (by default), the user code is loaded once at the first request and cached
and the Nginx config must be reloaded each time the Lua source file is modified.
The Lua code cache can be temporarily disabled during development by
switching L<lua_code_cache> C<off> in C<nginx.conf> to avoid reloading Nginx.

Nginx variables are supported in the file path for dynamic dispatch, for example:


     # CAUTION: contents in nginx var must be carefully filtered,
     # otherwise there'll be great security risk!
     location ~ ^/app/([-_a-zA-Z0-9/]+) {
         set $path $1;
         content_by_lua_file /path/to/lua/app/root/$path.lua;
     }

But be very careful about malicious user inputs and always carefully validate or filter out the user-supplied path components.




=head2 server_rewrite_by_lua_block

B<syntax:> I<server_rewrite_by_lua_block { lua-script }>

B<context:> I<http, server>

B<phase:> I<server rewrite>

Acts as a server rewrite phase handler and executes Lua code string specified in C<{ lua-script }> for every request.
The Lua code may make L<API calls> and is executed as a new spawned coroutine in an independent global environment (i.e. a sandbox).


     server {
         ...
    
         server_rewrite_by_lua_block {
             ngx.ctx.a = "server_rewrite_by_lua_block in http"
         }
    
         location /lua {
             content_by_lua_block {
                 ngx.say(ngx.ctx.a)
                 ngx.log(ngx.INFO, ngx.ctx.a)
            	}
         }
     }

Just as any other rewrite phase handlers, L<server_rewrite_by_lua_block> also runs in subrequests.


     server {
         server_rewrite_by_lua_block {
             ngx.log(ngx.INFO, "is_subrequest:", ngx.is_subrequest)
         }
    
         location /lua {
             content_by_lua_block {
                 local res = ngx.location.capture("/sub")
                 ngx.print(res.body)
             }
         }
    
         location /sub {
             content_by_lua_block {
                 ngx.say("OK")
             }
         }
     }

Note that when calling C<ngx.exit(ngx.OK)> within a L<server_rewrite_by_lua_block> handler, the Nginx request processing control flow will still continue to the content handler. To terminate the current request from within a L<server_rewrite_by_lua_block> handler, call L<ngx.exit> with status E<gt>= 200 (C<ngx.HTTP_OK>) and status E<lt> 300 (C<ngx.HTTP_SPECIAL_RESPONSE>) for successful quits and C<ngx.exit(ngx.HTTP_INTERNAL_SERVER_ERROR)> (or its friends) for failures.


     server_rewrite_by_lua_block {
         ngx.exit(503)
     }
    
     location /bar {
         ...
         # never exec
     }




=head2 server_rewrite_by_lua_file

B<syntax:> I<server_rewrite_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<http, server>

B<phase:> I<server rewrite>

Equivalent to L<server_rewrite_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or, as from the C<v0.10.22> release, the L<LuaJIT bytecode> to be executed.

Nginx variables can be used in the C<< <path-to-lua-script-file> >> string to provide flexibility. This however carries some risks and is not ordinarily recommended.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

When the Lua code cache is turned on (by default), the user code is loaded once at the first request and cached and the Nginx config must be reloaded each time the Lua source file is modified. The Lua code cache can be temporarily disabled during development by switching L<lua_code_cache> C<off> in C<nginx.conf> to avoid reloading Nginx.




=head2 rewrite_by_lua

B<syntax:> I<rewrite_by_lua E<lt>lua-script-strE<gt>>

B<context:> I<http, server, location, location if>

B<phase:> I<rewrite tail>

B<NOTE> Use of this directive is I<discouraged> following the C<v0.9.17> release. Use the L<rewrite_by_lua_block> directive instead.

Similar to the L<rewrite_by_lua_block> directive, but accepts the Lua source directly in an Nginx string literal (which requires
special character escaping).

For instance,


     rewrite_by_lua '
         do_something("hello, world!\nhiya\n")
     ';




=head2 rewrite_by_lua_block

B<syntax:> I<rewrite_by_lua_block { lua-script }>

B<context:> I<http, server, location, location if>

B<phase:> I<rewrite tail>

Acts as a rewrite phase handler and executes Lua code string specified in C<{ lua-script }> for every request.
The Lua code may make L<API calls> and is executed as a new spawned coroutine in an independent global environment (i.e. a sandbox).

Note that this handler always runs I<after> the standard L<ngx_http_rewrite_module|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html>. So the following will work as expected:


     location /foo {
         set $a 12; # create and initialize $a
         set $b ""; # create and initialize $b
         rewrite_by_lua_block {
             ngx.var.b = tonumber(ngx.var.a) + 1
         }
         echo "res = $b";
     }

because C<set $a 12> and C<set $b ""> run I<before> L<rewrite_by_lua_block>.

On the other hand, the following will not work as expected:


     ?  location /foo {
     ?      set $a 12; # create and initialize $a
     ?      set $b ''; # create and initialize $b
     ?      rewrite_by_lua_block {
     ?          ngx.var.b = tonumber(ngx.var.a) + 1
     ?      }
     ?      if ($b = '13') {
     ?         rewrite ^ /bar redirect;
     ?         break;
     ?      }
     ?
     ?      echo "res = $b";
     ?  }

because C<if> runs I<before> L<rewrite_by_lua_block> even if it is placed after L<rewrite_by_lua_block> in the config.

The right way of doing this is as follows:


     location /foo {
         set $a 12; # create and initialize $a
         set $b ''; # create and initialize $b
         rewrite_by_lua_block {
             ngx.var.b = tonumber(ngx.var.a) + 1
             if tonumber(ngx.var.b) == 13 then
                 return ngx.redirect("/bar")
             end
         }
    
         echo "res = $b";
     }

Note that the L<ngx_eval|http://www.grid.net.ru/nginx/eval.en.html> module can be approximated by using L<rewrite_by_lua_block>. For example,


     location / {
         eval $res {
             proxy_pass http://foo.com/check-spam;
         }
    
         if ($res = 'spam') {
             rewrite ^ /terms-of-use.html redirect;
         }
    
         fastcgi_pass ...;
     }

can be implemented in ngx_lua as:


     location = /check-spam {
         internal;
         proxy_pass http://foo.com/check-spam;
     }
    
     location / {
         rewrite_by_lua_block {
             local res = ngx.location.capture("/check-spam")
             if res.body == "spam" then
                 return ngx.redirect("/terms-of-use.html")
             end
         }
    
         fastcgi_pass ...;
     }

Just as any other rewrite phase handlers, L<rewrite_by_lua_block> also runs in subrequests.

Note that when calling C<ngx.exit(ngx.OK)> within a L<rewrite_by_lua_block> handler, the Nginx request processing control flow will still continue to the content handler. To terminate the current request from within a L<rewrite_by_lua_block> handler, call L<ngx.exit> with status E<gt>= 200 (C<ngx.HTTP_OK>) and status E<lt> 300 (C<ngx.HTTP_SPECIAL_RESPONSE>) for successful quits and C<ngx.exit(ngx.HTTP_INTERNAL_SERVER_ERROR)> (or its friends) for failures.

If the L<ngx_http_rewrite_module|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html>'s L<rewrite|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html#rewrite> directive is used to change the URI and initiate location re-lookups (internal redirections), then any L<rewrite_by_lua_block> or L<rewrite_by_lua_file_block> code sequences within the current location will not be executed. For example,


     location /foo {
         rewrite ^ /bar;
         rewrite_by_lua_block {
             ngx.exit(503)
         }
     }
     location /bar {
         ...
     }

Here the Lua code C<ngx.exit(503)> will never run. This will be the case if C<rewrite ^ /bar last> is used as this will similarly initiate an internal redirection. If the C<break> modifier is used instead, there will be no internal redirection and the C<rewrite_by_lua_block> code will be executed.

The C<rewrite_by_lua_block> code will always run at the end of the C<rewrite> request-processing phase unless L<rewrite_by_lua_no_postpone> is turned on.

This directive was first introduced in the C<v0.9.17> release.




=head2 rewrite_by_lua_file

B<syntax:> I<rewrite_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<http, server, location, location if>

B<phase:> I<rewrite tail>

Equivalent to L<rewrite_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or, as from the C<v0.5.0rc32> release, the L<LuaJIT bytecode> to be executed.

Nginx variables can be used in the C<< <path-to-lua-script-file> >> string to provide flexibility. This however carries some risks and is not ordinarily recommended.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

When the Lua code cache is turned on (by default), the user code is loaded once at the first request and cached and the Nginx config must be reloaded each time the Lua source file is modified. The Lua code cache can be temporarily disabled during development by switching L<lua_code_cache> C<off> in C<nginx.conf> to avoid reloading Nginx.

The C<rewrite_by_lua_file> code will always run at the end of the C<rewrite> request-processing phase unless L<rewrite_by_lua_no_postpone> is turned on.

Nginx variables are supported in the file path for dynamic dispatch just as in L<content_by_lua_file>.




=head2 access_by_lua

B<syntax:> I<access_by_lua E<lt>lua-script-strE<gt>>

B<context:> I<http, server, location, location if>

B<phase:> I<access tail>

B<NOTE> Use of this directive is I<discouraged> following the C<v0.9.17> release. Use the L<access_by_lua_block> directive instead.

Similar to the L<access_by_lua_block> directive, but accepts the Lua source directly in an Nginx string literal (which requires
special character escaping).

For instance,


     access_by_lua '
         do_something("hello, world!\nhiya\n")
     ';




=head2 access_by_lua_block

B<syntax:> I<access_by_lua_block { lua-script }>

B<context:> I<http, server, location, location if>

B<phase:> I<access tail>

Acts as an access phase handler and executes Lua code string specified in C<{ <lua-script }> for every request.
The Lua code may make L<API calls> and is executed as a new spawned coroutine in an independent global environment (i.e. a sandbox).

Note that this handler always runs I<after> the standard L<ngx_http_access_module|http://nginx.org/en/docs/http/ngx_http_access_module.html>. So the following will work as expected:


     location / {
         deny    ***********;
         allow   ***********/24;
         allow   ********/16;
         deny    all;
    
         access_by_lua_block {
             local res = ngx.location.capture("/mysql", { ... })
             ...
         }
    
         # proxy_pass/fastcgi_pass/...
     }

That is, if a client IP address is in the blacklist, it will be denied before the MySQL query for more complex authentication is executed by L<access_by_lua_block>.

Note that the L<ngx_auth_request|http://mdounin.ru/hg/ngx_http_auth_request_module/> module can be approximated by using L<access_by_lua_block>:


     location / {
         auth_request /auth;
    
         # proxy_pass/fastcgi_pass/postgres_pass/...
     }

can be implemented in ngx_lua as:


     location / {
         access_by_lua_block {
             local res = ngx.location.capture("/auth")
    
             if res.status == ngx.HTTP_OK then
                 return
             end
    
             if res.status == ngx.HTTP_FORBIDDEN then
                 ngx.exit(res.status)
             end
    
             ngx.exit(ngx.HTTP_INTERNAL_SERVER_ERROR)
         }
    
         # proxy_pass/fastcgi_pass/postgres_pass/...
     }

As with other access phase handlers, L<access_by_lua_block> will I<not> run in subrequests.

Note that when calling C<ngx.exit(ngx.OK)> within a L<access_by_lua_block> handler, the Nginx request processing control flow will still continue to the content handler. To terminate the current request from within a L<access_by_lua_block> handler, call L<ngx.exit> with status E<gt>= 200 (C<ngx.HTTP_OK>) and status E<lt> 300 (C<ngx.HTTP_SPECIAL_RESPONSE>) for successful quits and C<ngx.exit(ngx.HTTP_INTERNAL_SERVER_ERROR)> (or its friends) for failures.

Starting from the C<v0.9.20> release, you can use the L<access_by_lua_no_postpone>
directive to control when to run this handler inside the "access" request-processing phase
of Nginx.

This directive was first introduced in the C<v0.9.17> release.




=head2 access_by_lua_file

B<syntax:> I<access_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<http, server, location, location if>

B<phase:> I<access tail>

Equivalent to L<access_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or, as from the C<v0.5.0rc32> release, the L<LuaJIT bytecode> to be executed.

Nginx variables can be used in the C<< <path-to-lua-script-file> >> string to provide flexibility. This however carries some risks and is not ordinarily recommended.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

When the Lua code cache is turned on (by default), the user code is loaded once at the first request and cached
and the Nginx config must be reloaded each time the Lua source file is modified.
The Lua code cache can be temporarily disabled during development by switching L<lua_code_cache> C<off> in C<nginx.conf> to avoid repeatedly reloading Nginx.

Nginx variables are supported in the file path for dynamic dispatch just as in L<content_by_lua_file>.




=head2 header_filter_by_lua

B<syntax:> I<header_filter_by_lua E<lt>lua-script-strE<gt>>

B<context:> I<http, server, location, location if>

B<phase:> I<output-header-filter>

B<NOTE> Use of this directive is I<discouraged> following the C<v0.9.17> release. Use the L<header_filter_by_lua_block> directive instead.

Similar to the L<header_filter_by_lua_block> directive, but accepts the Lua source directly in an Nginx string literal (which requires
special character escaping).

For instance,


     header_filter_by_lua '
         ngx.header["content-length"] = nil
     ';

This directive was first introduced in the C<v0.2.1rc20> release.




=head2 header_filter_by_lua_block

B<syntax:> I<header_filter_by_lua_block { lua-script }>

B<context:> I<http, server, location, location if>

B<phase:> I<output-header-filter>

Uses Lua code specified in C<{ lua-script }> to define an output header filter.

Note that the following API functions are currently disabled within this context:


=over


=item *

Output API functions (e.g., L<ngx.say> and L<ngx.send_headers>)

=item *

Control API functions (e.g., L<ngx.redirect> and L<ngx.exec>)

=item *

Subrequest API functions (e.g., L<ngx.location.capture> and L<ngx.location.capture_multi>)

=item *

Cosocket API functions (e.g., L<ngx.socket.tcp> and L<ngx.req.socket>).


=back

Here is an example of overriding a response header (or adding one if absent) in our Lua header filter:


     location / {
         proxy_pass http://mybackend;
         header_filter_by_lua_block {
             ngx.header.Foo = "blah"
         }
     }

This directive was first introduced in the C<v0.9.17> release.




=head2 header_filter_by_lua_file

B<syntax:> I<header_filter_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<http, server, location, location if>

B<phase:> I<output-header-filter>

Equivalent to L<header_filter_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or as from the C<v0.5.0rc32> release, the L<LuaJIT bytecode> to be executed.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

This directive was first introduced in the C<v0.2.1rc20> release.




=head2 body_filter_by_lua

B<syntax:> I<body_filter_by_lua E<lt>lua-script-strE<gt>>

B<context:> I<http, server, location, location if>

B<phase:> I<output-body-filter>

B<NOTE> Use of this directive is I<discouraged> following the C<v0.9.17> release. Use the L<body_filter_by_lua_block> directive instead.

Similar to the L<body_filter_by_lua_block> directive, but accepts the Lua source directly in an Nginx string literal (which requires
special character escaping).

For instance,


     body_filter_by_lua '
         local data, eof = ngx.arg[1], ngx.arg[2]
     ';

This directive was first introduced in the C<v0.5.0rc32> release.




=head2 body_filter_by_lua_block

B<syntax:> I<body_filter_by_lua_block { lua-script-str }>

B<context:> I<http, server, location, location if>

B<phase:> I<output-body-filter>

Uses Lua code specified in C<{ lua-script }> to define an output body filter.

The input data chunk is passed via L<ngx.arg>[1] (as a Lua string value) and the "eof" flag indicating the end of the response body data stream is passed via L<ngx.arg>[2] (as a Lua boolean value).

Behind the scene, the "eof" flag is just the C<last_buf> (for main requests) or C<last_in_chain> (for subrequests) flag of the Nginx chain link buffers. (Before the C<v0.7.14> release, the "eof" flag does not work at all in subrequests.)

The output data stream can be aborted immediately by running the following Lua statement:


     return ngx.ERROR

This will truncate the response body and usually result in incomplete and also invalid responses.

The Lua code can pass its own modified version of the input data chunk to the downstream Nginx output body filters by overriding L<ngx.arg>[1] with a Lua string or a Lua table of strings. For example, to transform all the lowercase letters in the response body, we can just write:


     location / {
         proxy_pass http://mybackend;
         body_filter_by_lua_block {
             ngx.arg[1] = string.upper(ngx.arg[1])
         }
     }

When setting C<nil> or an empty Lua string value to C<ngx.arg[1]>, no data chunk will be passed to the downstream Nginx output filters at all.

Likewise, new "eof" flag can also be specified by setting a boolean value to L<ngx.arg>[2]. For example,


     location /t {
         echo hello world;
         echo hiya globe;
    
         body_filter_by_lua_block {
             local chunk = ngx.arg[1]
             if string.match(chunk, "hello") then
                 ngx.arg[2] = true  -- new eof
                 return
             end
    
             -- just throw away any remaining chunk data
             ngx.arg[1] = nil
         }
     }

Then C<GET /t> will just return the output

    hello world

That is, when the body filter sees a chunk containing the word "hello", then it will set the "eof" flag to true immediately, resulting in truncated but still valid responses.

When the Lua code may change the length of the response body, then it is required to always clear out the C<Content-Length> response header (if any) in a header filter to enforce streaming output, as in


     location /foo {
         # fastcgi_pass/proxy_pass/...
    
         header_filter_by_lua_block {
             ngx.header.content_length = nil
         }
         body_filter_by_lua_block {
             ngx.arg[1] = string.len(ngx.arg[1]) .. "\n"
         }
     }

Note that the following API functions are currently disabled within this context due to the limitations in Nginx output filter's current implementation:


=over


=item *

Output API functions (e.g., L<ngx.say> and L<ngx.send_headers>)

=item *

Control API functions (e.g., L<ngx.exit> and L<ngx.exec>)

=item *

Subrequest API functions (e.g., L<ngx.location.capture> and L<ngx.location.capture_multi>)

=item *

Cosocket API functions (e.g., L<ngx.socket.tcp> and L<ngx.req.socket>).


=back

Nginx output filters may be called multiple times for a single request because response body may be delivered in chunks. Thus, the Lua code specified by in this directive may also run multiple times in the lifetime of a single HTTP request.

This directive was first introduced in the C<v0.9.17> release.




=head2 body_filter_by_lua_file

B<syntax:> I<body_filter_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<http, server, location, location if>

B<phase:> I<output-body-filter>

Equivalent to L<body_filter_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or, as from the C<v0.5.0rc32> release, the L<LuaJIT bytecode> to be executed.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

This directive was first introduced in the C<v0.5.0rc32> release.




=head2 log_by_lua

B<syntax:> I<log_by_lua E<lt>lua-script-strE<gt>>

B<context:> I<http, server, location, location if>

B<phase:> I<log>

B<NOTE> Use of this directive is I<discouraged> following the C<v0.9.17> release. Use the L<log_by_lua_block> directive instead.

Similar to the L<log_by_lua_block> directive, but accepts the Lua source directly in an Nginx string literal (which requires
special character escaping).

For instance,


     log_by_lua '
         print("I need no extra escaping here, for example: \r\nblah")
     ';

This directive was first introduced in the C<v0.5.0rc31> release.




=head2 log_by_lua_block

B<syntax:> I<log_by_lua_block { lua-script }>

B<context:> I<http, server, location, location if>

B<phase:> I<log>

Runs the Lua source code inlined as the C<{ lua-script }> at the C<log> request processing phase. This does not replace the current access logs, but runs before.

Note that the following API functions are currently disabled within this context:


=over


=item *

Output API functions (e.g., L<ngx.say> and L<ngx.send_headers>)

=item *

Control API functions (e.g., L<ngx.exit>)

=item *

Subrequest API functions (e.g., L<ngx.location.capture> and L<ngx.location.capture_multi>)

=item *

Cosocket API functions (e.g., L<ngx.socket.tcp> and L<ngx.req.socket>).


=back

Here is an example of gathering average data for L<$upstream_response_time|http://nginx.org/en/docs/http/ngx_http_upstream_module.html#var_upstream_response_time>:


     lua_shared_dict log_dict 5M;
    
     server {
         location / {
             proxy_pass http://mybackend;
    
             log_by_lua_block {
                 local log_dict = ngx.shared.log_dict
                 local upstream_time = tonumber(ngx.var.upstream_response_time)
    
                 local sum = log_dict:get("upstream_time-sum") or 0
                 sum = sum + upstream_time
                 log_dict:set("upstream_time-sum", sum)
    
                 local newval, err = log_dict:incr("upstream_time-nb", 1)
                 if not newval and err == "not found" then
                     log_dict:add("upstream_time-nb", 0)
                     log_dict:incr("upstream_time-nb", 1)
                 end
             }
         }
    
         location = /status {
             content_by_lua_block {
                 local log_dict = ngx.shared.log_dict
                 local sum = log_dict:get("upstream_time-sum")
                 local nb = log_dict:get("upstream_time-nb")
    
                 if nb and sum then
                     ngx.say("average upstream response time: ", sum / nb,
                             " (", nb, " reqs)")
                 else
                     ngx.say("no data yet")
                 end
             }
         }
     }

This directive was first introduced in the C<v0.9.17> release.




=head2 log_by_lua_file

B<syntax:> I<log_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<http, server, location, location if>

B<phase:> I<log>

Equivalent to L<log_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or, as from the C<v0.5.0rc32> release, the L<LuaJIT bytecode> to be executed.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

This directive was first introduced in the C<v0.5.0rc31> release.




=head2 balancer_by_lua_block

B<syntax:> I<balancer_by_lua_block { lua-script }>

B<context:> I<upstream>

B<phase:> I<content>

This directive runs Lua code as an upstream balancer for any upstream entities defined
by the C<upstream {}> configuration block.

For instance,


     upstream foo {
         server 127.0.0.1;
         balancer_by_lua_block {
             -- use Lua to do something interesting here
             -- as a dynamic balancer
         }
     }
    
     server {
         location / {
             proxy_pass http://foo;
         }
     }

The resulting Lua load balancer can work with any existing Nginx upstream modules
like L<ngx_proxy|https://nginx.org/en/docs/http/ngx_http_proxy_module.html> and
L<ngx_fastcgi|https://nginx.org/en/docs/http/ngx_http_fastcgi_module.html>.

Also, the Lua load balancer can work with the standard upstream connection pool mechanism,
i.e., the standard L<keepalive|https://nginx.org/en/docs/http/ngx_http_upstream_module.html#keepalive> directive.
Just ensure that the L<keepalive|https://nginx.org/en/docs/http/ngx_http_upstream_module.html#keepalive> directive
is used I<after> this C<balancer_by_lua_block> directive in a single C<upstream {}> configuration block.

The Lua load balancer can totally ignore the list of servers defined in the C<upstream {}> block
and select peer from a completely dynamic server list (even changing per request) via the
L<ngx.balancer|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/balancer.md> module
from the L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

The Lua code handler registered by this directive might get called more than once in a single
downstream request when the Nginx upstream mechanism retries the request on conditions
specified by directives like the L<proxy_next_upstream|https://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_next_upstream>
directive.

This Lua code execution context does not support yielding, so Lua APIs that may yield
(like cosockets and "light threads") are disabled in this context. One can usually work
around this limitation by doing such operations in an earlier phase handler (like
L<access_by_lua*>) and passing along the result into this context
via the L<ngx.ctx> table.

This directive was first introduced in the C<v0.10.0> release.




=head2 balancer_by_lua_file

B<syntax:> I<balancer_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<upstream>

B<phase:> I<content>

Equivalent to L<balancer_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or, as from the C<v0.5.0rc32> release, the L<LuaJIT bytecode> to be executed.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

This directive was first introduced in the C<v0.10.0> release.




=head2 balancer_keepalive

B<syntax:> I<balancer_keepalive E<lt>total-connectionsE<gt>>

B<context:> I<upstream>

B<phase:> I<loading-config>

The C<total-connections> parameter sets the maximum number of idle
keepalive connections to upstream servers that are preserved in the cache of
each worker process. When this number is exceeded, the least recently used
connections are closed.

It should be particularly noted that the keepalive directive does not limit the
total number of connections to upstream servers that an nginx worker process
can open. The connections parameter should be set to a number small enough to
let upstream servers process new incoming connections as well.

This directive was first introduced in the C<v0.10.21> release.




=head2 lua_need_request_body

B<syntax:> I<lua_need_request_body E<lt>on|offE<gt>>

B<default:> I<off>

B<context:> I<http, server, location, location if>

B<phase:> I<depends on usage>

Determines whether to force the request body data to be read before running rewrite/access/content_by_lua* or not. The Nginx core does not read the client request body by default and if request body data is required, then this directive should be turned C<on> or the L<ngx.req.read_body> function should be called within the Lua code.

To read the request body data within the L<$request_body|http://nginx.org/en/docs/http/ngx_http_core_module.html#var_request_body> variable,
L<client_body_buffer_size|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_body_buffer_size> must have the same value as L<client_max_body_size|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_max_body_size>. Because when the content length exceeds L<client_body_buffer_size|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_body_buffer_size> but less than L<client_max_body_size|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_max_body_size>, Nginx will buffer the data into a temporary file on the disk, which will lead to empty value in the L<$request_body|http://nginx.org/en/docs/http/ngx_http_core_module.html#var_request_body> variable.

If the current location includes L<rewrite_by_lua*> directives,
then the request body will be read just before the L<rewrite_by_lua*> code is run (and also at the
C<rewrite> phase). Similarly, if only L<content_by_lua> is specified,
the request body will not be read until the content handler's Lua code is
about to run (i.e., the request body will be read during the content phase).

It is recommended however, to use the L<ngx.req.read_body> and L<ngx.req.discard_body> functions for finer control over the request body reading process instead.

This also applies to L<access_by_lua*>.




=head2 ssl_client_hello_by_lua_block

B<syntax:> I<ssl_client_hello_by_lua_block { lua-script }>

B<context:> I<http, server>

B<phase:> I<right-after-client-hello-message-was-processed>

This directive runs user Lua code when Nginx is about to post-process the SSL client hello message for the downstream
SSL (https) connections.

It is particularly useful for dynamically setting the SSL protocols according to the SNI.

It is also useful to do some custom operations according to the per-connection information in the client hello message.

For example, one can parse custom client hello extension and do the corresponding handling in pure Lua.

This Lua handler will always run whether the SSL session is resumed (via SSL session IDs or TLS session tickets) or not.
While the C<ssl_certificate_by_lua*> Lua handler will only runs when initiating a full SSL handshake.

The L<ngx.ssl.clienthello|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl/clienthello.md> Lua modules
provided by the L<lua-resty-core|https://github.com/openresty/lua-resty-core/#readme>
library are particularly useful in this context.

Note that this handler runs in extremely early stage of SSL handshake, before the SSL client hello extensions are parsed.
So you can not use some Lua API like C<ssl.server_name()> which is dependent on the later stage's processing.

Also note that only the directive in default server is valid for several virtual servers with the same IP address and port.

Below is a trivial example using the
L<ngx.ssl.clienthello|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl/clienthello.md> module
at the same time:


     server {
         listen 443 ssl;
         server_name   test.com;
         ssl_certificate /path/to/cert.crt;
         ssl_certificate_key /path/to/key.key;
         ssl_client_hello_by_lua_block {
             local ssl_clt = require "ngx.ssl.clienthello"
             local host, err = ssl_clt.get_client_hello_server_name()
             if host == "test.com" then
                 ssl_clt.set_protocols({"TLSv1", "TLSv1.1"})
             elseif host == "test2.com" then
                 ssl_clt.set_protocols({"TLSv1.2", "TLSv1.3"})
             elseif not host then
                 ngx.log(ngx.ERR, "failed to get the SNI name: ", err)
                 ngx.exit(ngx.ERROR)
             else
                 ngx.log(ngx.ERR, "unknown SNI name: ", host)
                 ngx.exit(ngx.ERROR)
             end
         }
         ...
     }
     server {
         listen 443 ssl;
         server_name   test2.com;
         ssl_certificate /path/to/cert.crt;
         ssl_certificate_key /path/to/key.key;
         ...
     }

See more information in the L<ngx.ssl.clienthello|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl/clienthello.md>
Lua modules' official documentation.

Uncaught Lua exceptions in the user Lua code immediately abort the current SSL session, so does the
L<ngx.exit> call with an error code like C<ngx.ERROR>.

This Lua code execution context I<does> support yielding, so Lua APIs that may yield
(like cosockets, sleeping, and "light threads")
are enabled in this context

Note, you need to configure the L<ssl_certificate|https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_certificate>
and L<ssl_certificate_key|https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_certificate_key>
to avoid the following error while starting NGINX:

    nginx: [emerg] no ssl configured for the server

This directive requires OpenSSL 1.1.1 or greater.

If you are using the [official pre-built
packages](https://openresty.org/en/linux-packages.html) for
L<OpenResty|https://openresty.org/> ******** or later, then everything should
work out of the box.

If you are not using the Nginx core shipped with
L<OpenResty|https://openresty.org> ******** or later, you will need to apply
patches to the standard Nginx core:

E<lt>https://openresty.org/en/nginx-ssl-patches.htmlE<gt>

This directive was first introduced in the C<v0.10.21> release.




=head2 ssl_client_hello_by_lua_file

B<syntax:> I<ssl_client_hello_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<http, server>

B<phase:> I<right-after-client-hello-message-was-processed>

Equivalent to L<ssl_client_hello_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or, as from the C<v0.5.0rc32> release, the L<LuaJIT bytecode> to be executed.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

This directive was first introduced in the C<v0.10.21> release.




=head2 ssl_certificate_by_lua_block

B<syntax:> I<ssl_certificate_by_lua_block { lua-script }>

B<context:> I<server>

B<phase:> I<right-before-SSL-handshake>

This directive runs user Lua code when Nginx is about to start the SSL handshake for the downstream
SSL (https) connections.

It is particularly useful for setting the SSL certificate chain and the corresponding private key on a per-request
basis. It is also useful to load such handshake configurations nonblockingly from the remote (for example,
with the L<cosocket> API). And one can also do per-request OCSP stapling handling in pure
Lua here as well.

Another typical use case is to do SSL handshake traffic control nonblockingly in this context,
with the help of the L<lua-resty-limit-traffic#readme|https://github.com/openresty/lua-resty-limit-traffic>
library, for example.

One can also do interesting things with the SSL handshake requests from the client side, like
rejecting old SSL clients using the SSLv3 protocol or even below selectively.

The L<ngx.ssl|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl.md>
and L<ngx.ocsp|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ocsp.md> Lua modules
provided by the L<lua-resty-core|https://github.com/openresty/lua-resty-core/#readme>
library are particularly useful in this context. You can use the Lua API offered by these two Lua modules
to manipulate the SSL certificate chain and private key for the current SSL connection
being initiated.

This Lua handler does not run at all, however, when Nginx/OpenSSL successfully resumes
the SSL session via SSL session IDs or TLS session tickets for the current SSL connection. In
other words, this Lua handler only runs when Nginx has to initiate a full SSL handshake.

Below is a trivial example using the
L<ngx.ssl|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl.md> module
at the same time:


     server {
         listen 443 ssl;
         server_name   test.com;
    
         ssl_certificate_by_lua_block {
             print("About to initiate a new SSL handshake!")
         }
    
         location / {
             root html;
         }
     }

See more complicated examples in the L<ngx.ssl|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl.md>
and L<ngx.ocsp|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ocsp.md>
Lua modules' official documentation.

Uncaught Lua exceptions in the user Lua code immediately abort the current SSL session, so does the
L<ngx.exit> call with an error code like C<ngx.ERROR>.

This Lua code execution context I<does> support yielding, so Lua APIs that may yield
(like cosockets, sleeping, and "light threads")
are enabled in this context.

Note, however, you still need to configure the L<ssl_certificate|https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_certificate> and
L<ssl_certificate_key|https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_certificate_key>
directives even though you will not use this static certificate and private key at all. This is
because the NGINX core requires their appearance otherwise you are seeing the following error
while starting NGINX:

    nginx: [emerg] no ssl configured for the server

This directive requires OpenSSL 1.0.2e or greater.

If you are using the [official pre-built
packages](https://openresty.org/en/linux-packages.html) for
L<OpenResty|https://openresty.org/> ******* or later, then everything should
work out of the box.

If you are not using the Nginx core shipped with
L<OpenResty|https://openresty.org> ******* or later, you will need to apply
patches to the standard Nginx core:

E<lt>https://openresty.org/en/nginx-ssl-patches.htmlE<gt>

This directive was first introduced in the C<v0.10.0> release.




=head2 ssl_certificate_by_lua_file

B<syntax:> I<ssl_certificate_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<server>

B<phase:> I<right-before-SSL-handshake>

Equivalent to L<ssl_certificate_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or, as from the C<v0.5.0rc32> release, the L<LuaJIT bytecode> to be executed.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

This directive was first introduced in the C<v0.10.0> release.




=head2 ssl_session_fetch_by_lua_block

B<syntax:> I<ssl_session_fetch_by_lua_block { lua-script }>

B<context:> I<http>

B<phase:> I<right-before-SSL-handshake>

This directive runs Lua code to look up and load the SSL session (if any) according to the session ID
provided by the current SSL handshake request for the downstream.

The Lua API for obtaining the current session ID and loading a cached SSL session data
is provided in the L<ngx.ssl.session|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl/session.md>
Lua module shipped with the L<lua-resty-core|https://github.com/openresty/lua-resty-core#readme>
library.

Lua APIs that may yield, like L<ngx.sleep> and L<cosockets>,
are enabled in this context.

This hook, together with the L<ssl_session_store_by_lua*> hook,
can be used to implement distributed caching mechanisms in pure Lua (based
on the L<cosocket> API, for example). If a cached SSL session is found
and loaded into the current SSL connection context,
SSL session resumption can then get immediately initiated and bypass the full SSL handshake process which is very expensive in terms of CPU time.

Please note that TLS session tickets are very different and it is the clients' responsibility
to cache the SSL session state when session tickets are used. SSL session resumptions based on
TLS session tickets would happen automatically without going through this hook (nor the
L<ssl_session_store_by_lua*> hook). This hook is mainly
for older or less capable SSL clients that can only do SSL sessions by session IDs.

When L<ssl_certificate_by_lua*> is specified at the same time,
this hook usually runs before L<ssl_certificate_by_lua*>.
When the SSL session is found and successfully loaded for the current SSL connection,
SSL session resumption will happen and thus bypass the L<ssl_certificate_by_lua*>
hook completely. In this case, Nginx also bypasses the L<ssl_session_store_by_lua*>
hook, for obvious reasons.

To easily test this hook locally with a modern web browser, you can temporarily put the following line
in your https server block to disable the TLS session ticket support:

    ssl_session_tickets off;

But do not forget to comment this line out before publishing your site to the world.

If you are using the L<official pre-built packages|https://openresty.org/en/linux-packages.html> for L<OpenResty|https://openresty.org/>
******** or later, then everything should work out of the box.

If you are not using one of the [OpenSSL
packages](https://openresty.org/en/linux-packages.html) provided by
L<OpenResty|https://openresty.org>, you will need to apply patches to OpenSSL
in order to use this directive:

E<lt>https://openresty.org/en/openssl-patches.htmlE<gt>

Similarly, if you are not using the Nginx core shipped with
L<OpenResty|https://openresty.org> ******** or later, you will need to apply
patches to the standard Nginx core:

E<lt>https://openresty.org/en/nginx-ssl-patches.htmlE<gt>

This directive was first introduced in the C<v0.10.6> release.

Note that this directive can only be used in the B<http context> starting
with the C<v0.10.7> release since SSL session resumption happens
before server name dispatch.




=head2 ssl_session_fetch_by_lua_file

B<syntax:> I<ssl_session_fetch_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<http>

B<phase:> I<right-before-SSL-handshake>

Equivalent to L<ssl_session_fetch_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or rather, the L<LuaJIT bytecode> to be executed.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

This directive was first introduced in the C<v0.10.6> release.

Note that: this directive is only allowed to used in B<http context> from the C<v0.10.7> release
(because SSL session resumption happens before server name dispatch).




=head2 ssl_session_store_by_lua_block

B<syntax:> I<ssl_session_store_by_lua_block { lua-script }>

B<context:> I<http>

B<phase:> I<right-after-SSL-handshake>

This directive runs Lua code to fetch and save the SSL session (if any) according to the session ID
provided by the current SSL handshake request for the downstream. The saved or cached SSL
session data can be used for future SSL connections to resume SSL sessions without going
through the full SSL handshake process (which is very expensive in terms of CPU time).

Lua APIs that may yield, like L<ngx.sleep> and L<cosockets>,
are I<disabled> in this context. You can still, however, use the L<ngx.timer.at> API
to create 0-delay timers to save the SSL session data asynchronously to external services (like C<redis> or C<memcached>).

The Lua API for obtaining the current session ID and the associated session state data
is provided in the L<ngx.ssl.session|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl/session.md#readme>
Lua module shipped with the L<lua-resty-core|https://github.com/openresty/lua-resty-core#readme>
library.

To easily test this hook locally with a modern web browser, you can temporarily put the following line
in your https server block to disable the TLS session ticket support:

    ssl_session_tickets off;

But do not forget to comment this line out before publishing your site to the world.

This directive was first introduced in the C<v0.10.6> release.

Note that: this directive is only allowed to used in B<http context> from the C<v0.10.7> release
(because SSL session resumption happens before server name dispatch).




=head2 ssl_session_store_by_lua_file

B<syntax:> I<ssl_session_store_by_lua_file E<lt>path-to-lua-script-fileE<gt>>

B<context:> I<http>

B<phase:> I<right-after-SSL-handshake>

Equivalent to L<ssl_session_store_by_lua_block>, except that the file specified by C<< <path-to-lua-script-file> >> contains the Lua code, or rather, the L<LuaJIT bytecode> to be executed.

When a relative path like C<foo/bar.lua> is given, they will be turned into the absolute path relative to the C<server prefix> path determined by the C<-p PATH> command-line option while starting the Nginx server.

This directive was first introduced in the C<v0.10.6> release.

Note that: this directive is only allowed to used in B<http context> from the C<v0.10.7> release
(because SSL session resumption happens before server name dispatch).




=head2 lua_shared_dict

B<syntax:> I<lua_shared_dict E<lt>nameE<gt> E<lt>sizeE<gt>>

B<default:> I<no>

B<context:> I<http>

B<phase:> I<depends on usage>

Declares a shared memory zone, C<< <name> >>, to serve as storage for the shm based Lua dictionary C<< ngx.shared.<name> >>.

Shared memory zones are always shared by all the Nginx worker processes in the current Nginx server instance.

The C<< <size> >> argument accepts size units such as C<k> and C<m>:


     http {
         lua_shared_dict dogs 10m;
         ...
     }

The hard-coded minimum size is 8KB while the practical minimum size depends
on actual user data set (some people start with 12KB).

See L<ngx.shared.DICT> for details.

This directive was first introduced in the C<v0.3.1rc22> release.




=head2 lua_socket_connect_timeout

B<syntax:> I<lua_socket_connect_timeout E<lt>timeE<gt>>

B<default:> I<lua_socket_connect_timeout 60s>

B<context:> I<http, server, location>

This directive controls the default timeout value used in TCP/unix-domain socket object's L<connect> method and can be overridden by the L<settimeout> or L<settimeouts> methods.

The C<< <time> >> argument can be an integer, with an optional time unit, like C<s> (second), C<ms> (millisecond), C<m> (minute). The default time unit is C<s>, i.e., "second". The default setting is C<60s>.

This directive was first introduced in the C<v0.5.0rc1> release.




=head2 lua_socket_send_timeout

B<syntax:> I<lua_socket_send_timeout E<lt>timeE<gt>>

B<default:> I<lua_socket_send_timeout 60s>

B<context:> I<http, server, location>

Controls the default timeout value used in TCP/unix-domain socket object's L<send> method and can be overridden by the L<settimeout> or L<settimeouts> methods.

The C<< <time> >> argument can be an integer, with an optional time unit, like C<s> (second), C<ms> (millisecond), C<m> (minute). The default time unit is C<s>, i.e., "second". The default setting is C<60s>.

This directive was first introduced in the C<v0.5.0rc1> release.




=head2 lua_socket_send_lowat

B<syntax:> I<lua_socket_send_lowat E<lt>sizeE<gt>>

B<default:> I<lua_socket_send_lowat 0>

B<context:> I<http, server, location>

Controls the C<lowat> (low water) value for the cosocket send buffer.




=head2 lua_socket_read_timeout

B<syntax:> I<lua_socket_read_timeout E<lt>timeE<gt>>

B<default:> I<lua_socket_read_timeout 60s>

B<context:> I<http, server, location>

B<phase:> I<depends on usage>

This directive controls the default timeout value used in TCP/unix-domain socket object's L<receive> method and iterator functions returned by the L<receiveuntil> method. This setting can be overridden by the L<settimeout> or L<settimeouts> methods.

The C<< <time> >> argument can be an integer, with an optional time unit, like C<s> (second), C<ms> (millisecond), C<m> (minute). The default time unit is C<s>, i.e., "second". The default setting is C<60s>.

This directive was first introduced in the C<v0.5.0rc1> release.




=head2 lua_socket_buffer_size

B<syntax:> I<lua_socket_buffer_size E<lt>sizeE<gt>>

B<default:> I<lua_socket_buffer_size 4k/8k>

B<context:> I<http, server, location>

Specifies the buffer size used by cosocket reading operations.

This buffer does not have to be that big to hold everything at the same time because cosocket supports 100% non-buffered reading and parsing. So even C<1> byte buffer size should still work everywhere but the performance could be terrible.

This directive was first introduced in the C<v0.5.0rc1> release.




=head2 lua_socket_pool_size

B<syntax:> I<lua_socket_pool_size E<lt>sizeE<gt>>

B<default:> I<lua_socket_pool_size 30>

B<context:> I<http, server, location>

Specifies the size limit (in terms of connection count) for every cosocket connection pool associated with every remote server (i.e., identified by either the host-port pair or the unix domain socket file path).

Default to 30 connections for every pool.

When the connection pool exceeds the available size limit, the least recently used (idle) connection already in the pool will be closed to make room for the current connection.

Note that the cosocket connection pool is per Nginx worker process rather than per Nginx server instance, so size limit specified here also applies to every single Nginx worker process.

This directive was first introduced in the C<v0.5.0rc1> release.




=head2 lua_socket_keepalive_timeout

B<syntax:> I<lua_socket_keepalive_timeout E<lt>timeE<gt>>

B<default:> I<lua_socket_keepalive_timeout 60s>

B<context:> I<http, server, location>

This directive controls the default maximal idle time of the connections in the cosocket built-in connection pool. When this timeout reaches, idle connections will be closed and removed from the pool. This setting can be overridden by cosocket objects' L<setkeepalive> method.

The C<< <time> >> argument can be an integer, with an optional time unit, like C<s> (second), C<ms> (millisecond), C<m> (minute). The default time unit is C<s>, i.e., "second". The default setting is C<60s>.

This directive was first introduced in the C<v0.5.0rc1> release.




=head2 lua_socket_log_errors

B<syntax:> I<lua_socket_log_errors on|off>

B<default:> I<lua_socket_log_errors on>

B<context:> I<http, server, location>

This directive can be used to toggle error logging when a failure occurs for the TCP or UDP cosockets. If you are already doing proper error handling and logging in your Lua code, then it is recommended to turn this directive off to prevent data flushing in your Nginx error log files (which is usually rather expensive).

This directive was first introduced in the C<v0.5.13> release.




=head2 lua_ssl_ciphers

B<syntax:> I<lua_ssl_ciphers E<lt>ciphersE<gt>>

B<default:> I<lua_ssl_ciphers DEFAULT>

B<context:> I<http, server, location>

Specifies the enabled ciphers for requests to a SSL/TLS server in the L<tcpsock:sslhandshake> method. The ciphers are specified in the format understood by the OpenSSL library.

The full list can be viewed using the “openssl ciphers” command.

This directive was first introduced in the C<v0.9.11> release.




=head2 lua_ssl_crl

B<syntax:> I<lua_ssl_crl E<lt>fileE<gt>>

B<default:> I<no>

B<context:> I<http, server, location>

Specifies a file with revoked certificates (CRL) in the PEM format used to verify the certificate of the SSL/TLS server in the L<tcpsock:sslhandshake> method.

This directive was first introduced in the C<v0.9.11> release.




=head2 lua_ssl_protocols

B<syntax:> I<lua_ssl_protocols [SSLv2] [SSLv3] [TLSv1] [TLSv1.1] [TLSv1.2] [TLSv1.3]>

B<default:> I<lua_ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3>

B<context:> I<http, server, location>

Enables the specified protocols for requests to a SSL/TLS server in the L<tcpsock:sslhandshake> method.

The support for the C<TLSv1.3> parameter requires version C<v0.10.12> I<and> OpenSSL 1.1.1.
From version v0.10.25, the default value change from C<SSLV3 TLSv1 TLSv1.1 TLSv1.2> to C<TLSv1 TLSv1.1 TLSv1.2 TLSv1.3>.

This directive was first introduced in the C<v0.9.11> release.




=head2 lua_ssl_certificate

B<syntax:> I<lua_ssl_certificate E<lt>fileE<gt>>

B<default:> I<none>

B<context:> I<http, server, location>

Specifies the file path to the SSL/TLS certificate in PEM format used for the L<tcpsock:sslhandshake> method.

This directive allows you to specify the SSL/TLS certificate that will be presented to server during the SSL/TLS handshake process.

This directive was first introduced in the C<v0.10.26> release.

See also L<lua_ssl_certificate_key> and L<lua_ssl_verify_depth>.




=head2 lua_ssl_certificate_key

B<syntax:> I<lua_ssl_certificate_key E<lt>fileE<gt>>

B<default:> I<none>

B<context:> I<http, server, location>

Specifies the file path to the private key associated with the SSL/TLS certificate used in the L<tcpsock:sslhandshake> method.

This directive allows you to specify the private key file corresponding to the SSL/TLS certificate specified by lua_ssl_certificate. The private key should be in PEM format and must match the certificate.

This directive was first introduced in the C<v0.10.26> release.

See also L<lua_ssl_certificate> and L<lua_ssl_verify_depth>.




=head2 lua_ssl_trusted_certificate

B<syntax:> I<lua_ssl_trusted_certificate E<lt>fileE<gt>>

B<default:> I<none>

B<context:> I<http, server, location>

Specifies a file path with trusted CA certificates in the PEM format used to verify the certificate of the SSL/TLS server in the L<tcpsock:sslhandshake> method.

This directive was first introduced in the C<v0.9.11> release.

See also L<lua_ssl_verify_depth>.




=head2 lua_ssl_verify_depth

B<syntax:> I<lua_ssl_verify_depth E<lt>numberE<gt>>

B<default:> I<lua_ssl_verify_depth 1>

B<context:> I<http, server, location>

Sets the verification depth in the server certificates chain.

This directive was first introduced in the C<v0.9.11> release.

See also L<lua_ssl_certificate>, L<lua_ssl_certificate_key> and L<lua_ssl_trusted_certificate>.




=head2 lua_ssl_conf_command

B<syntax:> I<lua_ssl_conf_command E<lt>commandE<gt>>

B<default:> I<no>

B<context:> I<http, server, location>

Sets arbitrary OpenSSL configuration L<commands|https://www.openssl.org/docs/man1.1.1/man3/SSL_CONF_cmd.html>.

The directive is supported when using OpenSSL 1.0.2 or higher and nginx 1.19.4 or higher. According to the specify command, higher OpenSSL version may be needed.

Several C<lua_ssl_conf_command> directives can be specified on the same level:


     lua_ssl_conf_command Options PrioritizeChaCha;
     lua_ssl_conf_command Ciphersuites TLS_CHACHA20_POLY1305_SHA256;

Configuration commands are applied after OpenResty own configuration for SSL, so they can be used to override anything set by OpenResty.

Note though that configuring OpenSSL directly with C<lua_ssl_conf_command> might result in a behaviour OpenResty does not expect, and should be done with care.

This directive was first introduced in the C<v0.10.21> release.




=head2 lua_http10_buffering

B<syntax:> I<lua_http10_buffering on|off>

B<default:> I<lua_http10_buffering on>

B<context:> I<http, server, location, location-if>

Enables or disables automatic response buffering for HTTP 1.0 (or older) requests. This buffering mechanism is mainly used for HTTP 1.0 keep-alive which relies on a proper C<Content-Length> response header.

If the Lua code explicitly sets a C<Content-Length> response header before sending the headers (either explicitly via L<ngx.send_headers> or implicitly via the first L<ngx.say> or L<ngx.print> call), then the HTTP 1.0 response buffering will be disabled even when this directive is turned on.

To output very large response data in a streaming fashion (via the L<ngx.flush> call, for example), this directive MUST be turned off to minimize memory usage.

This directive is turned C<on> by default.

This directive was first introduced in the C<v0.5.0rc19> release.




=head2 rewrite_by_lua_no_postpone

B<syntax:> I<rewrite_by_lua_no_postpone on|off>

B<default:> I<rewrite_by_lua_no_postpone off>

B<context:> I<http>

Controls whether or not to disable postponing L<rewrite_by_lua*> directives to run at the end of the C<rewrite> request-processing phase. By default, this directive is turned off and the Lua code is postponed to run at the end of the C<rewrite> phase.

This directive was first introduced in the C<v0.5.0rc29> release.




=head2 access_by_lua_no_postpone

B<syntax:> I<access_by_lua_no_postpone on|off>

B<default:> I<access_by_lua_no_postpone off>

B<context:> I<http>

Controls whether or not to disable postponing L<access_by_lua*> directives to run at the end of the C<access> request-processing phase. By default, this directive is turned off and the Lua code is postponed to run at the end of the C<access> phase.

This directive was first introduced in the C<v0.9.20> release.




=head2 lua_transform_underscores_in_response_headers

B<syntax:> I<lua_transform_underscores_in_response_headers on|off>

B<default:> I<lua_transform_underscores_in_response_headers on>

B<context:> I<http, server, location, location-if>

Controls whether to transform underscores (C<_>) in the response header names specified in the L<ngx.header.HEADER> API to hyphens (C<->).

This directive was first introduced in the C<v0.5.0rc32> release.




=head2 lua_check_client_abort

B<syntax:> I<lua_check_client_abort on|off>

B<default:> I<lua_check_client_abort off>

B<context:> I<http, server, location, location-if>

This directive controls whether to check for premature client connection abortion.

When this directive is on, the ngx_lua module will monitor the premature connection close event on the downstream connections and when there is such an event, it will call the user Lua function callback (registered by L<ngx.on_abort>) or just stop and clean up all the Lua "light threads" running in the current request's request handler when there is no user callback function registered.

According to the current implementation, however, if the client closes the connection before the Lua code finishes reading the request body data via L<ngx.req.socket>, then ngx_lua will neither stop all the running "light threads" nor call the user callback (if L<ngx.on_abort> has been called). Instead, the reading operation on L<ngx.req.socket> will just return the error message "client aborted" as the second return value (the first return value is surely C<nil>).

When TCP keepalive is disabled, it is relying on the client side to close the socket gracefully (by sending a C<FIN> packet or something like that). For (soft) real-time web applications, it is highly recommended to configure the L<TCP keepalive|http://tldp.org/HOWTO/TCP-Keepalive-HOWTO/overview.html> support in your system's TCP stack implementation in order to detect "half-open" TCP connections in time.

For example, on Linux, you can configure the standard L<listen|http://nginx.org/en/docs/http/ngx_http_core_module.html#listen> directive in your C<nginx.conf> file like this:


     listen 80 so_keepalive=2s:2s:8;

On FreeBSD, you can only tune the system-wide configuration for TCP keepalive, for example:

    # sysctl net.inet.tcp.keepintvl=2000
    # sysctl net.inet.tcp.keepidle=2000

This directive was first introduced in the C<v0.7.4> release.

See also L<ngx.on_abort>.




=head2 lua_max_pending_timers

B<syntax:> I<lua_max_pending_timers E<lt>countE<gt>>

B<default:> I<lua_max_pending_timers 1024>

B<context:> I<http>

Controls the maximum number of pending timers allowed.

Pending timers are those timers that have not expired yet.

When exceeding this limit, the L<ngx.timer.at> call will immediately return C<nil> and the error string "too many pending timers".

This directive was first introduced in the C<v0.8.0> release.




=head2 lua_max_running_timers

B<syntax:> I<lua_max_running_timers E<lt>countE<gt>>

B<default:> I<lua_max_running_timers 256>

B<context:> I<http>

Controls the maximum number of "running timers" allowed.

Running timers are those timers whose user callback functions are still running or C<lightthreads> spawned in callback functions are still running.

When exceeding this limit, Nginx will stop running the callbacks of newly expired timers and log an error message "N lua_max_running_timers are not enough" where "N" is the current value of this directive.

This directive was first introduced in the C<v0.8.0> release.




=head2 lua_sa_restart

B<syntax:> I<lua_sa_restart on|off>

B<default:> I<lua_sa_restart on>

B<context:> I<http>

When enabled, this module will set the C<SA_RESTART> flag on Nginx workers signal dispositions.

This allows Lua I/O primitives to not be interrupted by Nginx's handling of various signals.

This directive was first introduced in the C<v0.10.14> release.




=head2 lua_worker_thread_vm_pool_size

B<syntax:> I<lua_worker_thread_vm_pool_size E<lt>sizeE<gt>>

B<default:> I<lua_worker_thread_vm_pool_size 10>

B<context:> I<http>

Specifies the size limit of the Lua VM pool (default 100) that will be used in the L<ngx.run_worker_thread> API.

Also, it is not allowed to create Lua VMs that exceeds the pool size limit.

The Lua VM in the VM pool is used to execute Lua code in separate thread.

The pool is global at Nginx worker level. And it is used to reuse Lua VMs between requests.

B<Warning:> Each worker thread uses a separate Lua VM and caches the Lua VM for reuse in subsequent operations. Configuring too many worker threads can result in consuming a lot of memory.




=head1 Nginx API for Lua


=over


=item *

L<Introduction>

=item *

L<ngx.arg>

=item *

L<ngx.var.VARIABLE>

=item *

L<Core constants>

=item *

L<HTTP method constants>

=item *

L<HTTP status constants>

=item *

L<Nginx log level constants>

=item *

L<print>

=item *

L<ngx.ctx>

=item *

L<ngx.location.capture>

=item *

L<ngx.location.capture_multi>

=item *

L<ngx.status>

=item *

L<ngx.header.HEADER>

=item *

L<ngx.resp.get_headers>

=item *

L<ngx.req.is_internal>

=item *

L<ngx.req.start_time>

=item *

L<ngx.req.http_version>

=item *

L<ngx.req.raw_header>

=item *

L<ngx.req.get_method>

=item *

L<ngx.req.set_method>

=item *

L<ngx.req.set_uri>

=item *

L<ngx.req.set_uri_args>

=item *

L<ngx.req.get_uri_args>

=item *

L<ngx.req.get_post_args>

=item *

L<ngx.req.get_headers>

=item *

L<ngx.req.set_header>

=item *

L<ngx.req.clear_header>

=item *

L<ngx.req.read_body>

=item *

L<ngx.req.discard_body>

=item *

L<ngx.req.get_body_data>

=item *

L<ngx.req.get_body_file>

=item *

L<ngx.req.set_body_data>

=item *

L<ngx.req.set_body_file>

=item *

L<ngx.req.init_body>

=item *

L<ngx.req.append_body>

=item *

L<ngx.req.finish_body>

=item *

L<ngx.req.socket>

=item *

L<ngx.exec>

=item *

L<ngx.redirect>

=item *

L<ngx.send_headers>

=item *

L<ngx.headers_sent>

=item *

L<ngx.print>

=item *

L<ngx.say>

=item *

L<ngx.log>

=item *

L<ngx.flush>

=item *

L<ngx.exit>

=item *

L<ngx.eof>

=item *

L<ngx.sleep>

=item *

L<ngx.escape_uri>

=item *

L<ngx.unescape_uri>

=item *

L<ngx.encode_args>

=item *

L<ngx.decode_args>

=item *

L<ngx.encode_base64>

=item *

L<ngx.decode_base64>

=item *

L<ngx.decode_base64mime>

=item *

L<ngx.crc32_short>

=item *

L<ngx.crc32_long>

=item *

L<ngx.hmac_sha1>

=item *

L<ngx.md5>

=item *

L<ngx.md5_bin>

=item *

L<ngx.sha1_bin>

=item *

L<ngx.quote_sql_str>

=item *

L<ngx.today>

=item *

L<ngx.time>

=item *

L<ngx.now>

=item *

L<ngx.update_time>

=item *

L<ngx.localtime>

=item *

L<ngx.utctime>

=item *

L<ngx.cookie_time>

=item *

L<ngx.http_time>

=item *

L<ngx.parse_http_time>

=item *

L<ngx.is_subrequest>

=item *

L<ngx.re.match>

=item *

L<ngx.re.find>

=item *

L<ngx.re.gmatch>

=item *

L<ngx.re.sub>

=item *

L<ngx.re.gsub>

=item *

L<ngx.shared.DICT>

=item *

L<ngx.shared.DICT.get>

=item *

L<ngx.shared.DICT.get_stale>

=item *

L<ngx.shared.DICT.set>

=item *

L<ngx.shared.DICT.safe_set>

=item *

L<ngx.shared.DICT.add>

=item *

L<ngx.shared.DICT.safe_add>

=item *

L<ngx.shared.DICT.replace>

=item *

L<ngx.shared.DICT.delete>

=item *

L<ngx.shared.DICT.incr>

=item *

L<ngx.shared.DICT.lpush>

=item *

L<ngx.shared.DICT.rpush>

=item *

L<ngx.shared.DICT.lpop>

=item *

L<ngx.shared.DICT.rpop>

=item *

L<ngx.shared.DICT.llen>

=item *

L<ngx.shared.DICT.ttl>

=item *

L<ngx.shared.DICT.expire>

=item *

L<ngx.shared.DICT.flush_all>

=item *

L<ngx.shared.DICT.flush_expired>

=item *

L<ngx.shared.DICT.get_keys>

=item *

L<ngx.shared.DICT.capacity>

=item *

L<ngx.shared.DICT.free_space>

=item *

L<ngx.socket.udp>

=item *

L<udpsock:bind>

=item *

L<udpsock:setpeername>

=item *

L<udpsock:send>

=item *

L<udpsock:receive>

=item *

L<udpsock:close>

=item *

L<udpsock:settimeout>

=item *

L<ngx.socket.stream>

=item *

L<ngx.socket.tcp>

=item *

L<tcpsock:bind>

=item *

L<tcpsock:connect>

=item *

L<tcpsock:setclientcert>

=item *

L<tcpsock:sslhandshake>

=item *

L<tcpsock:send>

=item *

L<tcpsock:receive>

=item *

L<tcpsock:receiveany>

=item *

L<tcpsock:receiveuntil>

=item *

L<tcpsock:close>

=item *

L<tcpsock:settimeout>

=item *

L<tcpsock:settimeouts>

=item *

L<tcpsock:setoption>

=item *

L<tcpsock:setkeepalive>

=item *

L<tcpsock:getreusedtimes>

=item *

L<ngx.socket.connect>

=item *

L<ngx.get_phase>

=item *

L<ngx.thread.spawn>

=item *

L<ngx.thread.wait>

=item *

L<ngx.thread.kill>

=item *

L<ngx.on_abort>

=item *

L<ngx.timer.at>

=item *

L<ngx.timer.every>

=item *

L<ngx.timer.running_count>

=item *

L<ngx.timer.pending_count>

=item *

L<ngx.config.subsystem>

=item *

L<ngx.config.debug>

=item *

L<ngx.config.prefix>

=item *

L<ngx.config.nginx_version>

=item *

L<ngx.config.nginx_configure>

=item *

L<ngx.config.ngx_lua_version>

=item *

L<ngx.worker.exiting>

=item *

L<ngx.worker.pid>

=item *

L<ngx.worker.pids>

=item *

L<ngx.worker.count>

=item *

L<ngx.worker.id>

=item *

L<ngx.semaphore>

=item *

L<ngx.balancer>

=item *

L<ngx.ssl>

=item *

L<ngx.ocsp>

=item *

L<ndk.set_var.DIRECTIVE>

=item *

L<coroutine.create>

=item *

L<coroutine.resume>

=item *

L<coroutine.yield>

=item *

L<coroutine.wrap>

=item *

L<coroutine.running>

=item *

L<coroutine.status>

=item *

L<ngx.run_worker_thread>


=back




=head2 Introduction

The various C<*_by_lua>, C<*_by_lua_block> and C<*_by_lua_file> configuration directives serve as gateways to the Lua API within the C<nginx.conf> file. The Nginx Lua API described below can only be called within the user Lua code run in the context of these configuration directives.

The API is exposed to Lua in the form of two standard packages C<ngx> and C<ndk>. These packages are in the default global scope within ngx_lua and are always available within ngx_lua directives.

The packages can be introduced into external Lua modules like this:


     local say = ngx.say
    
     local _M = {}
    
     function _M.foo(a)
         say(a)
     end
    
     return _M

Use of the L<package.seeall|https://www.lua.org/manual/5.1/manual.html#pdf-package.seeall> flag is strongly discouraged due to its various bad side-effects.

It is also possible to directly require the packages in external Lua modules:


     local ngx = require "ngx"
     local ndk = require "ndk"

The ability to require these packages was introduced in the C<v0.2.1rc19> release.

Network I/O operations in user code should only be done through the Nginx Lua API calls as the Nginx event loop may be blocked and performance drop off dramatically otherwise. Disk operations with relatively small amount of data can be done using the standard Lua C<io> library but huge file reading and writing should be avoided wherever possible as they may block the Nginx process significantly. Delegating all network and disk I/O operations to Nginx's subrequests (via the L<ngx.location.capture> method and similar) is strongly recommended for maximum performance.




=head2 ngx.arg

B<syntax:> I<val = ngx.arg[index]>

B<context:> I<set_by_luaE<42>, body_filter_by_luaE<42>>

When this is used in the context of the L<set_by_lua*> directives, this table is read-only and holds the input arguments to the config directives:


     value = ngx.arg[n]

Here is an example


     location /foo {
         set $a 32;
         set $b 56;
    
         set_by_lua $sum
             'return tonumber(ngx.arg[1]) + tonumber(ngx.arg[2])'
             $a $b;
    
         echo $sum;
     }

that writes out C<88>, the sum of C<32> and C<56>.

When this table is used in the context of L<body_filter_by_lua*>, the first element holds the input data chunk to the output filter code and the second element holds the boolean flag for the "eof" flag indicating the end of the whole output data stream.

The data chunk and "eof" flag passed to the downstream Nginx output filters can also be overridden by assigning values directly to the corresponding table elements. When setting C<nil> or an empty Lua string value to C<ngx.arg[1]>, no data chunk will be passed to the downstream Nginx output filters at all.




=head2 ngx.var.VARIABLE

B<syntax:> I<ngx.var.VAR_NAME>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, balancer_by_luaE<42>>

Read and write Nginx variable values.


     value = ngx.var.some_nginx_variable_name
     ngx.var.some_nginx_variable_name = value

Note that only already defined Nginx variables can be written to.
For example:


     location /foo {
         set $my_var ''; # this line is required to create $my_var at config time
         content_by_lua_block {
             ngx.var.my_var = 123
             ...
         }
     }

That is, Nginx variables cannot be created on-the-fly. Here is a list of pre-defined
L<Nginx variables|http://nginx.org/en/docs/varindex.html>.

Some special Nginx variables like C<$args> and C<$limit_rate> can be assigned a value,
many others are not, like C<$query_string>, C<$arg_PARAMETER>, and C<$http_NAME>.

Nginx regex group capturing variables C<$1>, C<$2>, C<$3>, and etc, can be read by this
interface as well, by writing C<ngx.var[1]>, C<ngx.var[2]>, C<ngx.var[3]>, and etc.

Setting C<ngx.var.Foo> to a C<nil> value will unset the C<$Foo> Nginx variable.


     ngx.var.args = nil

B<CAUTION> When reading from an Nginx variable, Nginx will allocate memory in the per-request memory pool which is freed only at request termination. So when you need to read from an Nginx variable repeatedly in your Lua code, cache the Nginx variable value to your own Lua variable, for example,


     local val = ngx.var.some_var
     --- use the val repeatedly later

to prevent (temporary) memory leaking within the current request's lifetime. Another way of caching the result is to use the L<ngx.ctx> table.

Undefined Nginx variables are evaluated to C<nil> while uninitialized (but defined) Nginx variables are evaluated to an empty Lua string.

This API requires a relatively expensive metamethod call and it is recommended to avoid using it on hot code paths.




=head2 Core constants

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, E<42>log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>


       ngx.OK (0)
       ngx.ERROR (-1)
       ngx.AGAIN (-2)
       ngx.DONE (-4)
       ngx.DECLINED (-5)

Note that only three of these constants are utilized by the L<Nginx API for Lua> (i.e., L<ngx.exit> accepts C<ngx.OK>, C<ngx.ERROR>, and C<ngx.DECLINED> as input).


       ngx.null

The C<ngx.null> constant is a C<NULL> light userdata usually used to represent nil values in Lua tables etc and is similar to the L<lua-cjson|http://www.kyne.com.au/~mark/software/lua-cjson.php> library's C<cjson.null> constant. This constant was first introduced in the C<v0.5.0rc5> release.

The C<ngx.DECLINED> constant was first introduced in the C<v0.5.0rc19> release.




=head2 HTTP method constants

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

      ngx.HTTP_GET
      ngx.HTTP_HEAD
      ngx.HTTP_PUT
      ngx.HTTP_POST
      ngx.HTTP_DELETE
      ngx.HTTP_OPTIONS   (added in the v0.5.0rc24 release)
      ngx.HTTP_MKCOL     (added in the v0.8.2 release)
      ngx.HTTP_COPY      (added in the v0.8.2 release)
      ngx.HTTP_MOVE      (added in the v0.8.2 release)
      ngx.HTTP_PROPFIND  (added in the v0.8.2 release)
      ngx.HTTP_PROPPATCH (added in the v0.8.2 release)
      ngx.HTTP_LOCK      (added in the v0.8.2 release)
      ngx.HTTP_UNLOCK    (added in the v0.8.2 release)
      ngx.HTTP_PATCH     (added in the v0.8.2 release)
      ngx.HTTP_TRACE     (added in the v0.8.2 release)

These constants are usually used in L<ngx.location.capture> and L<ngx.location.capture_multi> method calls.




=head2 HTTP status constants

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>


       value = ngx.HTTP_CONTINUE (100) (first added in the v0.9.20 release)
       value = ngx.HTTP_SWITCHING_PROTOCOLS (101) (first added in the v0.9.20 release)
       value = ngx.HTTP_OK (200)
       value = ngx.HTTP_CREATED (201)
       value = ngx.HTTP_ACCEPTED (202) (first added in the v0.9.20 release)
       value = ngx.HTTP_NO_CONTENT (204) (first added in the v0.9.20 release)
       value = ngx.HTTP_PARTIAL_CONTENT (206) (first added in the v0.9.20 release)
       value = ngx.HTTP_SPECIAL_RESPONSE (300)
       value = ngx.HTTP_MOVED_PERMANENTLY (301)
       value = ngx.HTTP_MOVED_TEMPORARILY (302)
       value = ngx.HTTP_SEE_OTHER (303)
       value = ngx.HTTP_NOT_MODIFIED (304)
       value = ngx.HTTP_TEMPORARY_REDIRECT (307) (first added in the v0.9.20 release)
       value = ngx.HTTP_PERMANENT_REDIRECT (308)
       value = ngx.HTTP_BAD_REQUEST (400)
       value = ngx.HTTP_UNAUTHORIZED (401)
       value = ngx.HTTP_PAYMENT_REQUIRED (402) (first added in the v0.9.20 release)
       value = ngx.HTTP_FORBIDDEN (403)
       value = ngx.HTTP_NOT_FOUND (404)
       value = ngx.HTTP_NOT_ALLOWED (405)
       value = ngx.HTTP_NOT_ACCEPTABLE (406) (first added in the v0.9.20 release)
       value = ngx.HTTP_REQUEST_TIMEOUT (408) (first added in the v0.9.20 release)
       value = ngx.HTTP_CONFLICT (409) (first added in the v0.9.20 release)
       value = ngx.HTTP_GONE (410)
       value = ngx.HTTP_UPGRADE_REQUIRED (426) (first added in the v0.9.20 release)
       value = ngx.HTTP_TOO_MANY_REQUESTS (429) (first added in the v0.9.20 release)
       value = ngx.HTTP_CLOSE (444) (first added in the v0.9.20 release)
       value = ngx.HTTP_ILLEGAL (451) (first added in the v0.9.20 release)
       value = ngx.HTTP_INTERNAL_SERVER_ERROR (500)
       value = ngx.HTTP_NOT_IMPLEMENTED (501)
       value = ngx.HTTP_METHOD_NOT_IMPLEMENTED (501) (kept for compatibility)
       value = ngx.HTTP_BAD_GATEWAY (502) (first added in the v0.9.20 release)
       value = ngx.HTTP_SERVICE_UNAVAILABLE (503)
       value = ngx.HTTP_GATEWAY_TIMEOUT (504) (first added in the v0.3.1rc38 release)
       value = ngx.HTTP_VERSION_NOT_SUPPORTED (505) (first added in the v0.9.20 release)
       value = ngx.HTTP_INSUFFICIENT_STORAGE (507) (first added in the v0.9.20 release)




=head2 Nginx log level constants

B<context:> I<init_by_luaE<42>, init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>


       ngx.STDERR
       ngx.EMERG
       ngx.ALERT
       ngx.CRIT
       ngx.ERR
       ngx.WARN
       ngx.NOTICE
       ngx.INFO
       ngx.DEBUG

These constants are usually used by the L<ngx.log> method.




=head2 print

B<syntax:> I<print(...)>

B<context:> I<init_by_luaE<42>, init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Writes argument values into the Nginx C<error.log> file with the C<ngx.NOTICE> log level.

It is equivalent to


     ngx.log(ngx.NOTICE, ...)

Lua C<nil> arguments are accepted and result in literal C<"nil"> strings while Lua booleans result in literal C<"true"> or C<"false"> strings. And the C<ngx.null> constant will yield the C<"null"> string output.

There is a hard coded C<2048> byte limitation on error message lengths in the Nginx core. This limit includes trailing newlines and leading time stamps. If the message size exceeds this limit, Nginx will truncate the message text accordingly. This limit can be manually modified by editing the C<NGX_MAX_ERROR_STR> macro definition in the C<src/core/ngx_log.h> file in the Nginx source tree.




=head2 ngx.ctx

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, exit_worker_by_luaE<42>>

This table can be used to store per-request Lua context data and has a life time identical to the current request (as with the Nginx variables).

Consider the following example,


     location /test {
         rewrite_by_lua_block {
             ngx.ctx.foo = 76
         }
         access_by_lua_block {
             ngx.ctx.foo = ngx.ctx.foo + 3
         }
         content_by_lua_block {
             ngx.say(ngx.ctx.foo)
         }
     }

Then C<GET /test> will yield the output


     79

That is, the C<ngx.ctx.foo> entry persists across the rewrite, access, and content phases of a request.

Every request, including subrequests, has its own copy of the table. For example:


     location /sub {
         content_by_lua_block {
             ngx.say("sub pre: ", ngx.ctx.blah)
             ngx.ctx.blah = 32
             ngx.say("sub post: ", ngx.ctx.blah)
         }
     }
    
     location /main {
         content_by_lua_block {
             ngx.ctx.blah = 73
             ngx.say("main pre: ", ngx.ctx.blah)
             local res = ngx.location.capture("/sub")
             ngx.print(res.body)
             ngx.say("main post: ", ngx.ctx.blah)
         }
     }

Then C<GET /main> will give the output


     main pre: 73
     sub pre: nil
     sub post: 32
     main post: 73

Here, modification of the C<ngx.ctx.blah> entry in the subrequest does not affect the one in the parent request. This is because they have two separate versions of C<ngx.ctx.blah>.

Internal redirects (triggered by nginx configuration directives like C<error_page>, C<try_files>, C<index>, etc.) will destroy the original request C<ngx.ctx> data (if any) and the new request will have an empty C<ngx.ctx> table. For instance,


     location /new {
         content_by_lua_block {
             ngx.say(ngx.ctx.foo)
         }
     }
    
     location /orig {
         content_by_lua_block {
             ngx.ctx.foo = "hello"
             ngx.exec("/new")
         }
     }

Then C<GET /orig> will give


     nil

rather than the original C<"hello"> value.

Because HTTP request is created after SSL handshake, the C<ngx.ctx> created
in L<ssl_certificate_by_lua*>, L<ssl_session_store_by_lua*>, L<ssl_session_fetch_by_lua*> and L<ssl_client_hello_by_lua*>
is not available in the following phases like L<rewrite_by_lua*>.

Since C<v0.10.18>, the C<ngx.ctx> created during a SSL handshake
will be inherited by the requests which share the same TCP connection established by the handshake.
Note that overwrite values in C<ngx.ctx> in the http request phases (like C<rewrite_by_lua*>) will only take affect in the current http request.

Arbitrary data values, including Lua closures and nested tables, can be inserted into this "magic" table. It also allows the registration of custom meta methods.

Overriding C<ngx.ctx> with a new Lua table is also supported, for example,


     ngx.ctx = { foo = 32, bar = 54 }

When being used in the context of L<init_worker_by_lua*>, this table just has the same lifetime of the current Lua handler.

The C<ngx.ctx> lookup requires relatively expensive metamethod calls and it is much slower than explicitly passing per-request data along by your own function arguments. So do not abuse this API for saving your own function arguments because it usually has quite some performance impact.

Because of the metamethod magic, never "local" the C<ngx.ctx> table outside your Lua function scope on the Lua module level due to L<worker-level data sharing>. For example, the following is bad:


     -- mymodule.lua
     local _M = {}
    
     -- the following line is bad since ngx.ctx is a per-request
     -- data while this <code>ctx</code> variable is on the Lua module level
     -- and thus is per-nginx-worker.
     local ctx = ngx.ctx
    
     function _M.main()
         ctx.foo = "bar"
     end
    
     return _M

Use the following instead:


     -- mymodule.lua
     local _M = {}
    
     function _M.main(ctx)
         ctx.foo = "bar"
     end
    
     return _M

That is, let the caller pass the C<ctx> table explicitly via a function argument.




=head2 ngx.location.capture

B<syntax:> I<res = ngx.location.capture(uri, options?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Issues a synchronous but still non-blocking I<Nginx Subrequest> using C<uri>.

Nginx's subrequests provide a powerful way to make non-blocking internal requests to other locations configured with disk file directory or I<any> other Nginx C modules like C<ngx_proxy>, C<ngx_fastcgi>, C<ngx_memc>,
C<ngx_postgres>, C<ngx_drizzle>, and even ngx_lua itself and etc etc etc.

Also note that subrequests just mimic the HTTP interface but there is I<no> extra HTTP/TCP traffic I<nor> IPC involved. Everything works internally, efficiently, on the C level.

Subrequests are completely different from HTTP 301/302 redirection (via L<ngx.redirect>) and internal redirection (via L<ngx.exec>).

You should always read the request body (by either calling L<ngx.req.read_body> or configuring L<lua_need_request_body> on) before initiating a subrequest.

This API function (as well as L<ngx.location.capture_multi>) always buffers the whole response body of the subrequest in memory. Thus, you should use L<cosockets>
and streaming processing instead if you have to handle large subrequest responses.

Here is a basic example:


     res = ngx.location.capture(uri)

Returns a Lua table with 4 slots: C<res.status>, C<res.header>, C<res.body>, and C<res.truncated>.

C<res.status> holds the response status code for the subrequest response.

C<res.header> holds all the response headers of the
subrequest and it is a normal Lua table. For multi-value response headers,
the value is a Lua (array) table that holds all the values in the order that
they appear. For instance, if the subrequest response headers contain the following
lines:


     Set-Cookie: a=3
     Set-Cookie: foo=bar
     Set-Cookie: baz=blah

Then C<res.header["Set-Cookie"]> will be evaluated to the table value
C<{"a=3", "foo=bar", "baz=blah"}>.

C<res.body> holds the subrequest's response body data, which might be truncated. You always need to check the C<res.truncated> boolean flag to see if C<res.body> contains truncated data. The data truncation here can only be caused by those unrecoverable errors in your subrequests like the cases that the remote end aborts the connection prematurely in the middle of the response body data stream or a read timeout happens when your subrequest is receiving the response body data from the remote.

URI query strings can be concatenated to URI itself, for instance,


     res = ngx.location.capture('/foo/bar?a=3&b=4')

Named locations like C<@foo> are not allowed due to a limitation in
the Nginx core. Use normal locations combined with the C<internal> directive to
prepare internal-only locations.

An optional option table can be fed as the second
argument, which supports the options:


=over


=item *

C<method>
specify the subrequest's request method, which only accepts constants like C<ngx.HTTP_POST>.

=item *

C<body>
specify the subrequest's request body (string value only).

=item *

C<args>
specify the subrequest's URI query arguments (both string value and Lua tables are accepted)

=item *

C<headers>
specify the subrequest's request headers (Lua table only). this headers will override the original headers of the subrequest.

=item *

C<ctx>
specify a Lua table to be the L<ngx.ctx> table for the subrequest. It can be the current request's L<ngx.ctx> table, which effectively makes the parent and its subrequest to share exactly the same context table. This option was first introduced in the C<v0.3.1rc25> release.

=item *

C<vars>
take a Lua table which holds the values to set the specified Nginx variables in the subrequest as this option's value. This option was first introduced in the C<v0.3.1rc31> release.

=item *

C<copy_all_vars>
specify whether to copy over all the Nginx variable values of the current request to the subrequest in question. modifications of the Nginx variables in the subrequest will not affect the current (parent) request. This option was first introduced in the C<v0.3.1rc31> release.

=item *

C<share_all_vars>
specify whether to share all the Nginx variables of the subrequest with the current (parent) request. modifications of the Nginx variables in the subrequest will affect the current (parent) request. Enabling this option may lead to hard-to-debug issues due to bad side-effects and is considered bad and harmful. Only enable this option when you completely know what you are doing.

=item *

C<always_forward_body>
when set to true, the current (parent) request's request body will always be forwarded to the subrequest being created if the C<body> option is not specified. The request body read by either L<ngx.req.read_body()> or L<lua_need_request_body on> will be directly forwarded to the subrequest without copying the whole request body data when creating the subrequest (no matter the request body data is buffered in memory buffers or temporary files). By default, this option is C<false> and when the C<body> option is not specified, the request body of the current (parent) request is only forwarded when the subrequest takes the C<PUT> or C<POST> request method.


=back

Issuing a POST subrequest, for example, can be done as follows


     res = ngx.location.capture(
         '/foo/bar',
         { method = ngx.HTTP_POST, body = 'hello, world' }
     )

See HTTP method constants methods other than POST.
The C<method> option is C<ngx.HTTP_GET> by default.

The C<args> option can specify extra URI arguments, for instance,


     ngx.location.capture('/foo?a=1',
         { args = { b = 3, c = ':' } }
     )

is equivalent to


     ngx.location.capture('/foo?a=1&b=3&c=%3a')

that is, this method will escape argument keys and values according to URI rules and
concatenate them together into a complete query string. The format for the Lua table passed as the C<args> argument is identical to the format used in the L<ngx.encode_args> method.

The C<args> option can also take plain query strings:


     ngx.location.capture('/foo?a=1',
         { args = 'b=3&c=%3a' }
     )

This is functionally identical to the previous examples.

The C<share_all_vars> option controls whether to share Nginx variables among the current request and its subrequests.
If this option is set to C<true>, then the current request and associated subrequests will share the same Nginx variable scope. Hence, changes to Nginx variables made by a subrequest will affect the current request.

Care should be taken in using this option as variable scope sharing can have unexpected side effects. The C<args>, C<vars>, or C<copy_all_vars> options are generally preferable instead.

This option is set to C<false> by default


     location /other {
         set $dog "$dog world";
         echo "$uri dog: $dog";
     }
    
     location /lua {
         set $dog 'hello';
         content_by_lua_block {
             res = ngx.location.capture("/other",
                 { share_all_vars = true })
    
             ngx.print(res.body)
             ngx.say(ngx.var.uri, ": ", ngx.var.dog)
         }
     }

Accessing location C</lua> gives

    /other dog: hello world
    /lua: hello world

The C<copy_all_vars> option provides a copy of the parent request's Nginx variables to subrequests when such subrequests are issued. Changes made to these variables by such subrequests will not affect the parent request or any other subrequests sharing the parent request's variables.


     location /other {
         set $dog "$dog world";
         echo "$uri dog: $dog";
     }
    
     location /lua {
         set $dog 'hello';
         content_by_lua_block {
             res = ngx.location.capture("/other",
                 { copy_all_vars = true })
    
             ngx.print(res.body)
             ngx.say(ngx.var.uri, ": ", ngx.var.dog)
         }
     }

Request C<GET /lua> will give the output

    /other dog: hello world
    /lua: hello

Note that if both C<share_all_vars> and C<copy_all_vars> are set to true, then C<share_all_vars> takes precedence.

In addition to the two settings above, it is possible to specify
values for variables in the subrequest using the C<vars> option. These
variables are set after the sharing or copying of variables has been
evaluated, and provides a more efficient method of passing specific
values to a subrequest over encoding them as URL arguments and
unescaping them in the Nginx config file.


     location /other {
         content_by_lua_block {
             ngx.say("dog = ", ngx.var.dog)
             ngx.say("cat = ", ngx.var.cat)
         }
     }
    
     location /lua {
         set $dog '';
         set $cat '';
         content_by_lua_block {
             res = ngx.location.capture("/other",
                 { vars = { dog = "hello", cat = 32 }})
    
             ngx.print(res.body)
         }
     }

Accessing C</lua> will yield the output

    dog = hello
    cat = 32

The C<headers> option can be used to specify the request headers for the subrequest. The value of this option should be a Lua table where the keys are the header names and the values are the header values. For example,


    location /foo {
        content_by_lua_block {
            ngx.print(ngx.var.http_x_test)
        }
    }
    
    location /lua {
        content_by_lua_block {
            local res = ngx.location.capture("/foo", {
                headers = {
                    ["X-Test"] = "aa",
                }
            })
            ngx.print(res.body)
        }
    }

Accessing C</lua> will yield the output

    aa

The C<ctx> option can be used to specify a custom Lua table to serve as the L<ngx.ctx> table for the subrequest.


     location /sub {
         content_by_lua_block {
             ngx.ctx.foo = "bar";
         }
     }
     location /lua {
         content_by_lua_block {
             local ctx = {}
             res = ngx.location.capture("/sub", { ctx = ctx })
    
             ngx.say(ctx.foo)
             ngx.say(ngx.ctx.foo)
         }
     }

Then request C<GET /lua> gives

    bar
    nil

It is also possible to use this C<ctx> option to share the same L<ngx.ctx> table between the current (parent) request and the subrequest:


     location /sub {
         content_by_lua_block {
             ngx.ctx.foo = "bar"
         }
     }
     location /lua {
         content_by_lua_block {
             res = ngx.location.capture("/sub", { ctx = ngx.ctx })
             ngx.say(ngx.ctx.foo)
         }
     }

Request C<GET /lua> yields the output

    bar

Note that subrequests issued by L<ngx.location.capture> inherit all the
request headers of the current request by default and that this may have unexpected side effects on the
subrequest responses. For example, when using the standard C<ngx_proxy> module to serve
subrequests, an "Accept-Encoding: gzip" header in the main request may result
in gzipped responses that cannot be handled properly in Lua code. Original request headers should be ignored by setting
L<proxy_pass_request_headers|http://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_pass_request_headers> to C<off> in subrequest locations.

When the C<body> option is not specified and the C<always_forward_body> option is false (the default value), the C<POST> and C<PUT> subrequests will inherit the request bodies of the parent request (if any).

There is a hard-coded upper limit on the number of subrequests possible for every main request. In older versions of Nginx, the limit was C<50> concurrent subrequests and in more recent versions, Nginx C<1.9.5> onwards, the same limit is changed to limit the depth of recursive subrequests. When this limit is exceeded, the following error message is added to the C<error.log> file:

    [error] 13983#0: *1 subrequests cycle while processing "/uri"

The limit can be manually modified if required by editing the definition of the C<NGX_HTTP_MAX_SUBREQUESTS> macro in the C<nginx/src/http/ngx_http_request.h> file in the Nginx source tree.

Please also refer to restrictions on capturing locations configured by L<subrequest directives of other modules>.




=head2 ngx.location.capture_multi

B<syntax:> I<res1, res2, ... = ngx.location.capture_multi({ {uri, options?}, {uri, options?}, ... })>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Just like L<ngx.location.capture>, but supports multiple subrequests running in parallel.

This function issues several parallel subrequests specified by the input table and returns their results in the same order. For example,


     res1, res2, res3 = ngx.location.capture_multi{
         { "/foo", { args = "a=3&b=4" } },
         { "/bar" },
         { "/baz", { method = ngx.HTTP_POST, body = "hello" } },
     }
    
     if res1.status == ngx.HTTP_OK then
         ...
     end
    
     if res2.body == "BLAH" then
         ...
     end

This function will not return until all the subrequests terminate.
The total latency is the longest latency of the individual subrequests rather than the sum.

Lua tables can be used for both requests and responses when the number of subrequests to be issued is not known in advance:


     -- construct the requests table
     local reqs = {}
     table.insert(reqs, { "/mysql" })
     table.insert(reqs, { "/postgres" })
     table.insert(reqs, { "/redis" })
     table.insert(reqs, { "/memcached" })
    
     -- issue all the requests at once and wait until they all return
     local resps = {
         ngx.location.capture_multi(reqs)
     }
    
     -- loop over the responses table
     for i, resp in ipairs(resps) do
         -- process the response table "resp"
     end

The L<ngx.location.capture> function is just a special form
of this function. Logically speaking, the L<ngx.location.capture> can be implemented like this


     ngx.location.capture =
         function (uri, args)
             return ngx.location.capture_multi({ {uri, args} })
         end

Please also refer to restrictions on capturing locations configured by L<subrequest directives of other modules>.




=head2 ngx.status

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>>

Read and write the current request's response status. This should be called
before sending out the response headers.


     ngx.status = ngx.HTTP_CREATED
     status = ngx.status

Setting C<ngx.status> after the response header is sent out has no effect but leaving an error message in your Nginx's error log file:

    attempt to set ngx.status after sending out response headers




=head2 ngx.header.HEADER

B<syntax:> I<ngx.header.HEADER = VALUE>

B<syntax:> I<value = ngx.header.HEADER>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>>

Set, add to, or clear the current request's C<HEADER> response header that is to be sent.

Underscores (C<_>) in the header names will be replaced by hyphens (C<->) by default. This transformation can be turned off via the L<lua_transform_underscores_in_response_headers> directive.

The header names are matched case-insensitively.


     -- equivalent to ngx.header["Content-Type"] = 'text/plain'
     ngx.header.content_type = 'text/plain'
    
     ngx.header["X-My-Header"] = 'blah blah'

Multi-value headers can be set this way:


     ngx.header['Set-Cookie'] = {'a=32; path=/', 'b=4; path=/'}

will yield


     Set-Cookie: a=32; path=/
     Set-Cookie: b=4; path=/

in the response headers.

Only Lua tables are accepted (Only the last element in the table will take effect for standard headers such as C<Content-Type> that only accept a single value).


     ngx.header.content_type = {'a', 'b'}

is equivalent to


     ngx.header.content_type = 'b'

Setting a slot to C<nil> effectively removes it from the response headers:


     ngx.header["X-My-Header"] = nil

The same applies to assigning an empty table:


     ngx.header["X-My-Header"] = {}

Setting C<ngx.header.HEADER> after sending out response headers (either explicitly with L<ngx.send_headers> or implicitly with L<ngx.print> and similar) will log an error message.

Reading C<ngx.header.HEADER> will return the value of the response header named C<HEADER>.

Underscores (C<_>) in the header names will also be replaced by dashes (C<->) and the header names will be matched case-insensitively. If the response header is not present at all, C<nil> will be returned.

This is particularly useful in the context of L<header_filter_by_lua*>, for example,


     location /test {
         set $footer '';
    
         proxy_pass http://some-backend;
    
         header_filter_by_lua_block {
             if ngx.header["X-My-Header"] == "blah" then
                 ngx.var.footer = "some value"
             end
         }
    
         echo_after_body $footer;
     }

For multi-value headers, all of the values of header will be collected in order and returned as a Lua table. For example, response headers

    Foo: bar
    Foo: baz

will result in


     {"bar", "baz"}

to be returned when reading C<ngx.header.Foo>.

Note that C<ngx.header> is not a normal Lua table and as such, it is not possible to iterate through it using the Lua C<ipairs> function.

Note: this function throws a Lua error if C<HEADER> or
C<VALUE> contain unsafe characters (control characters).

For reading I<request> headers, use the L<ngx.req.get_headers> function instead.




=head2 ngx.resp.get_headers

B<syntax:> I<headers, err = ngx.resp.get_headers(max_headers?, raw?)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, balancer_by_luaE<42>>

Returns a Lua table holding all the current response headers for the current request.


     local h, err = ngx.resp.get_headers()
    
     if err == "truncated" then
         -- one can choose to ignore or reject the current response here
     end
    
     for k, v in pairs(h) do
         ...
     end

This function has the same signature as L<ngx.req.get_headers> except getting response headers instead of request headers.

Note that a maximum of 100 response headers are parsed by default (including those with the same name) and that additional response headers are silently discarded to guard against potential denial of service attacks. Since C<v0.10.13>, when the limit is exceeded, it will return a second value which is the string C<"truncated">.

This API was first introduced in the C<v0.9.5> release.




=head2 ngx.req.is_internal

B<syntax:> I<is_internal = ngx.req.is_internal()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>>

Returns a boolean indicating whether the current request is an "internal request", i.e.,
a request initiated from inside the current Nginx server instead of from the client side.

Subrequests are all internal requests and so are requests after internal redirects.

This API was first introduced in the C<v0.9.20> release.




=head2 ngx.req.start_time

B<syntax:> I<secs = ngx.req.start_time()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>>

Returns a floating-point number representing the timestamp (including milliseconds as the decimal part) when the current request was created.

The following example emulates the C<$request_time> variable value (provided by L<ngx_http_log_module|http://nginx.org/en/docs/http/ngx_http_log_module.html>) in pure Lua:


     local request_time = ngx.now() - ngx.req.start_time()

This function was first introduced in the C<v0.7.7> release.

See also L<ngx.now> and L<ngx.update_time>.




=head2 ngx.req.http_version

B<syntax:> I<num = ngx.req.http_version()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>>

Returns the HTTP version number for the current request as a Lua number.

Current possible values are 3.0, 2.0, 1.0, 1.1, and 0.9. Returns C<nil> for unrecognized values.

This method was first introduced in the C<v0.7.17> release.




=head2 ngx.req.raw_header

B<syntax:> I<str = ngx.req.raw_header(no_request_line?)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>>

Returns the original raw HTTP protocol header received by the Nginx server.

By default, the request line and trailing C<CR LF> terminator will also be included. For example,


     ngx.print(ngx.req.raw_header())

gives something like this:

    GET /t HTTP/1.1
    Host: localhost
    Connection: close
    Foo: bar

You can specify the optional
C<no_request_line> argument as a C<true> value to exclude the request line from the result. For example,


     ngx.print(ngx.req.raw_header(true))

outputs something like this:

    Host: localhost
    Connection: close
    Foo: bar

This method was first introduced in the C<v0.7.17> release.

This method does not work in HTTP/2 requests yet.




=head2 ngx.req.get_method

B<syntax:> I<method_name = ngx.req.get_method()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, balancer_by_luaE<42>, log_by_luaE<42>>

Retrieves the current request's request method name. Strings like C<"GET"> and C<"POST"> are returned instead of numerical L<method constants>.

If the current request is an Nginx subrequest, then the subrequest's method name will be returned.

This method was first introduced in the C<v0.5.6> release.

See also L<ngx.req.set_method>.




=head2 ngx.req.set_method

B<syntax:> I<ngx.req.set_method(method_id)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>>

Overrides the current request's request method with the C<method_id> argument. Currently only numerical L<method constants> are supported, like C<ngx.HTTP_POST> and C<ngx.HTTP_GET>.

If the current request is an Nginx subrequest, then the subrequest's method will be overridden.

This method was first introduced in the C<v0.5.6> release.

See also L<ngx.req.get_method>.




=head2 ngx.req.set_uri

B<syntax:> I<ngx.req.set_uri(uri, jump?, binary?)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>>

Rewrite the current request's (parsed) URI by the C<uri> argument. The C<uri> argument must be a Lua string and cannot be of zero length, or a Lua exception will be thrown.

The optional boolean C<jump> argument can trigger location rematch (or location jump) as L<ngx_http_rewrite_module|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html>'s L<rewrite|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html#rewrite> directive, that is, when C<jump> is C<true> (default to C<false>), this function will never return and it will tell Nginx to try re-searching locations with the new URI value at the later C<post-rewrite> phase and jumping to the new location.

Location jump will not be triggered otherwise, and only the current request's URI will be modified, which is also the default behavior. This function will return but with no returned values when the C<jump> argument is C<false> or absent altogether.

For example, the following Nginx config snippet


     rewrite ^ /foo last;

can be coded in Lua like this:


     ngx.req.set_uri("/foo", true)

Similarly, Nginx config


     rewrite ^ /foo break;

can be coded in Lua as


     ngx.req.set_uri("/foo", false)

or equivalently,


     ngx.req.set_uri("/foo")

The C<jump> argument can only be set to C<true> in L<rewrite_by_lua*>. Use of jump in other contexts is prohibited and will throw out a Lua exception.

A more sophisticated example involving regex substitutions is as follows


     location /test {
         rewrite_by_lua_block {
             local uri = ngx.re.sub(ngx.var.uri, "^/test/(.*)", "/$1", "o")
             ngx.req.set_uri(uri)
         }
         proxy_pass http://my_backend;
     }

which is functionally equivalent to


     location /test {
         rewrite ^/test/(.*) /$1 break;
         proxy_pass http://my_backend;
     }

Note: this function throws a Lua error if the C<uri> argument
contains unsafe characters (control characters).

Note that it is not possible to use this interface to rewrite URI arguments and that L<ngx.req.set_uri_args> should be used for this instead. For instance, Nginx config


     rewrite ^ /foo?a=3? last;

can be coded as


     ngx.req.set_uri_args("a=3")
     ngx.req.set_uri("/foo", true)

or


     ngx.req.set_uri_args({a = 3})
     ngx.req.set_uri("/foo", true)

Starting from C<0.10.16> of this module, this function accepts an
optional boolean C<binary> argument to allow arbitrary binary URI
data. By default, this C<binary> argument is false and this function
will throw out a Lua error such as the one below when the C<uri>
argument contains any control characters (ASCII Code 0 ~ 0x08, 0x0A ~ 0x1F and 0x7F).

    [error] 23430#23430: *1 lua entry thread aborted: runtime error:
    content_by_lua(nginx.conf:44):3: ngx.req.set_uri unsafe byte "0x00"
    in "\x00foo" (maybe you want to set the 'binary' argument?)

This interface was first introduced in the C<v0.3.1rc14> release.




=head2 ngx.req.set_uri_args

B<syntax:> I<ngx.req.set_uri_args(args)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>>

Rewrite the current request's URI query arguments by the C<args> argument. The C<args> argument can be either a Lua string, as in


     ngx.req.set_uri_args("a=3&b=hello%20world")

or a Lua table holding the query arguments' key-value pairs, as in


     ngx.req.set_uri_args({ a = 3, b = "hello world" })

In the former case, i.e., when the whole query-string is provided directly,
the input Lua string should already be well-formed with the URI encoding.
For security considerations, this method will automatically escape any control and
whitespace characters (ASCII code 0x00 ~ 0x20 and 0x7F) in the Lua string.

In the latter case, this method will escape argument keys and values according to the URI escaping rule.

Multi-value arguments are also supported:


     ngx.req.set_uri_args({ a = 3, b = {5, 6} })

which will result in a query string like C<a=3&b=5&b=6> or C<b=5&b=6&a=3>.

B<Note that when using Lua table as the C<arg> argument, the order of the arguments in the result query string which change from time to time. If you would like to get an ordered result, you need to use Lua string as the C<arg> argument.>

This interface was first introduced in the C<v0.3.1rc13> release.

See also L<ngx.req.set_uri>.




=head2 ngx.req.get_uri_args

B<syntax:> I<args, err = ngx.req.get_uri_args(max_args?, tab?)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, balancer_by_luaE<42>>

Returns a Lua table holding all the current request URL query arguments. An optional C<tab> argument
can be used to reuse the table returned by this method.


     location = /test {
         content_by_lua_block {
             local args, err = ngx.req.get_uri_args()
    
             if err == "truncated" then
                 -- one can choose to ignore or reject the current request here
             end
    
             for key, val in pairs(args) do
                 if type(val) == "table" then
                     ngx.say(key, ": ", table.concat(val, ", "))
                 else
                     ngx.say(key, ": ", val)
                 end
             end
         }
     }

Then C<GET /test?foo=bar&bar=baz&bar=blah> will yield the response body


     foo: bar
     bar: baz, blah

Multiple occurrences of an argument key will result in a table value holding all the values for that key in order.

Keys and values are unescaped according to URI escaping rules. In the settings above, C<GET /test?a%20b=1%61+2> will yield:


     a b: 1a 2

Arguments without the C<< =<value> >> parts are treated as boolean arguments. C<GET /test?foo&bar> will yield:


     foo: true
     bar: true

That is, they will take Lua boolean values C<true>. However, they are different from arguments taking empty string values. C<GET /test?foo=&bar=> will give something like


     foo:
     bar:

Empty key arguments are discarded. C<GET /test?=hello&=world> will yield an empty output for instance.

Updating query arguments via the Nginx variable C<$args> (or C<ngx.var.args> in Lua) at runtime is also supported:


     ngx.var.args = "a=3&b=42"
     local args, err = ngx.req.get_uri_args()

Here the C<args> table will always look like


     {a = 3, b = 42}

regardless of the actual request query string.

Note that a maximum of 100 request arguments are parsed by default (including those with the same name) and that additional request arguments are silently discarded to guard against potential denial of service attacks. Since C<v0.10.13>, when the limit is exceeded, it will return a second value which is the string C<"truncated">.

However, the optional C<max_args> function argument can be used to override this limit:


     local args, err = ngx.req.get_uri_args(10)
     if err == "truncated" then
         -- one can choose to ignore or reject the current request here
     end

This argument can be set to zero to remove the limit and to process all request arguments received:


     local args, err = ngx.req.get_uri_args(0)

Removing the C<max_args> cap is strongly discouraged.




=head2 ngx.req.get_post_args

B<syntax:> I<args, err = ngx.req.get_post_args(max_args?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>>

Returns a Lua table holding all the current request POST query arguments (of the MIME type C<application/x-www-form-urlencoded>). Call L<ngx.req.read_body> to read the request body first or turn on the L<lua_need_request_body> directive to avoid errors.


     location = /test {
         content_by_lua_block {
             ngx.req.read_body()
             local args, err = ngx.req.get_post_args()
    
             if err == "truncated" then
                 -- one can choose to ignore or reject the current request here
             end
    
             if not args then
                 ngx.say("failed to get post args: ", err)
                 return
             end
             for key, val in pairs(args) do
                 if type(val) == "table" then
                     ngx.say(key, ": ", table.concat(val, ", "))
                 else
                     ngx.say(key, ": ", val)
                 end
             end
         }
     }

Then


     # Post request with the body 'foo=bar&bar=baz&bar=blah'
     $ curl --data 'foo=bar&bar=baz&bar=blah' localhost/test

will yield the response body like


     foo: bar
     bar: baz, blah

Multiple occurrences of an argument key will result in a table value holding all of the values for that key in order.

Keys and values will be unescaped according to URI escaping rules.

With the settings above,


     # POST request with body 'a%20b=1%61+2'
     $ curl -d 'a%20b=1%61+2' localhost/test

will yield:


     a b: 1a 2

Arguments without the C<< =<value> >> parts are treated as boolean arguments. C<POST /test> with the request body C<foo&bar> will yield:


     foo: true
     bar: true

That is, they will take Lua boolean values C<true>. However, they are different from arguments taking empty string values. C<POST /test> with request body C<foo=&bar=> will return something like


     foo:
     bar:

Empty key arguments are discarded. C<POST /test> with body C<=hello&=world> will yield empty outputs for instance.

Note that a maximum of 100 request arguments are parsed by default (including those with the same name) and that additional request arguments are silently discarded to guard against potential denial of service attacks. Since C<v0.10.13>, when the limit is exceeded, it will return a second value which is the string C<"truncated">.

However, the optional C<max_args> function argument can be used to override this limit:


     local args, err = ngx.req.get_post_args(10)
     if err == "truncated" then
         -- one can choose to ignore or reject the current request here
     end

This argument can be set to zero to remove the limit and to process all request arguments received:


     local args, err = ngx.req.get_post_args(0)

Removing the C<max_args> cap is strongly discouraged.




=head2 ngx.req.get_headers

B<syntax:> I<headers, err = ngx.req.get_headers(max_headers?, raw?)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>>

Returns a Lua table holding all the current request headers.


     local h, err = ngx.req.get_headers()
    
     if err == "truncated" then
         -- one can choose to ignore or reject the current request here
     end
    
     for k, v in pairs(h) do
         ...
     end

To read an individual header:


     ngx.say("Host: ", ngx.req.get_headers()["Host"])

Note that the L<ngx.var.HEADER> API call, which uses core L<$http_HEADER|http://nginx.org/en/docs/http/ngx_http_core_module.html#var_http_> variables, may be more preferable for reading individual request headers.

For multiple instances of request headers such as:


     Foo: foo
     Foo: bar
     Foo: baz

the value of C<ngx.req.get_headers()["Foo"]> will be a Lua (array) table such as:


     {"foo", "bar", "baz"}

Note that a maximum of 100 request headers are parsed by default (including those with the same name) and that additional request headers are silently discarded to guard against potential denial of service attacks. Since C<v0.10.13>, when the limit is exceeded, it will return a second value which is the string C<"truncated">.

However, the optional C<max_headers> function argument can be used to override this limit:


     local headers, err = ngx.req.get_headers(10)
    
     if err == "truncated" then
         -- one can choose to ignore or reject the current request here
     end

This argument can be set to zero to remove the limit and to process all request headers received:


     local headers, err = ngx.req.get_headers(0)

Removing the C<max_headers> cap is strongly discouraged.

Since the C<0.6.9> release, all the header names in the Lua table returned are converted to the pure lower-case form by default, unless the C<raw> argument is set to C<true> (default to C<false>).

Also, by default, an C<__index> metamethod is added to the resulting Lua table and will normalize the keys to a pure lowercase form with all underscores converted to dashes in case of a lookup miss. For example, if a request header C<My-Foo-Header> is present, then the following invocations will all pick up the value of this header correctly:


     ngx.say(headers.my_foo_header)
     ngx.say(headers["My-Foo-Header"])
     ngx.say(headers["my-foo-header"])

The C<__index> metamethod will not be added when the C<raw> argument is set to C<true>.




=head2 ngx.req.set_header

B<syntax:> I<ngx.req.set_header(header_name, header_value)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>>

Set the current request's request header named C<header_name> to value C<header_value>, overriding any existing ones.

The input Lua string C<header_name> and C<header_value> should already be well-formed with the URI encoding.
For security considerations, this method will automatically escape " ", """, "(", ")", ",", "/", ":", ";", "?",
"E<lt>", "=", "E<gt>", "?", "@", "[", "]", "\", "{", "}", 0x00-0x1F, 0x7F-0xFF in C<header_name> and automatically escape
"0x00-0x08, 0x0A-0x0F, 0x7F in C<header_value>.

By default, all the subrequests subsequently initiated by L<ngx.location.capture> and L<ngx.location.capture_multi> will inherit the new header.

It is not a Lua's equivalent of nginx C<proxy_set_header> directive (same is true about L<ngx.req.clear_header>). C<proxy_set_header> only affects the upstream request while C<ngx.req.set_header> change the incoming request. Record the http headers in the access log file will show the difference. But you still can use it as an alternative of nginx C<proxy_set_header> directive as long as you know the difference.

Here is an example of setting the C<Content-Type> header:


     ngx.req.set_header("Content-Type", "text/css")

The C<header_value> can take an array list of values,
for example,


     ngx.req.set_header("Foo", {"a", "abc"})

will produce two new request headers:


     Foo: a
     Foo: abc

and old C<Foo> headers will be overridden if there is any.

When the C<header_value> argument is C<nil>, the request header will be removed. So


     ngx.req.set_header("X-Foo", nil)

is equivalent to


     ngx.req.clear_header("X-Foo")

Note: this function throws a Lua error if C<header_name> or
C<header_value> contain unsafe characters (control characters).




=head2 ngx.req.clear_header

B<syntax:> I<ngx.req.clear_header(header_name)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>>

Clears the current request's request header named C<header_name>. None of the current request's existing subrequests will be affected but subsequently initiated subrequests will inherit the change by default.




=head2 ngx.req.read_body

B<syntax:> I<ngx.req.read_body()>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Reads the client request body synchronously without blocking the Nginx event loop.


     ngx.req.read_body()
     local args = ngx.req.get_post_args()

If the request body is already read previously by turning on L<lua_need_request_body> or by using other modules, then this function does not run and returns immediately.

If the request body has already been explicitly discarded, either by the L<ngx.req.discard_body> function or other modules, this function does not run and returns immediately.

In case of errors, such as connection errors while reading the data, this method will throw out a Lua exception I<or> terminate the current request with a 500 status code immediately.

The request body data read using this function can be retrieved later via L<ngx.req.get_body_data> or, alternatively, the temporary file name for the body data cached to disk using L<ngx.req.get_body_file>. This depends on


=over


=item 1.

whether the current request body is already larger than the L<client_body_buffer_size|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_body_buffer_size>,

=item 2.

and whether L<client_body_in_file_only|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_body_in_file_only> has been switched on.


=back

In cases where current request may have a request body and the request body data is not required, The L<ngx.req.discard_body> function must be used to explicitly discard the request body to avoid breaking things under HTTP 1.1 keepalive or HTTP 1.1 pipelining.

This function was first introduced in the C<v0.3.1rc17> release.




=head2 ngx.req.discard_body

B<syntax:> I<ngx.req.discard_body()>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Explicitly discard the request body, i.e., read the data on the connection and throw it away immediately (without using the request body by any means).

This function is an asynchronous call and returns immediately.

If the request body has already been read, this function does nothing and returns immediately.

This function was first introduced in the C<v0.3.1rc17> release.

See also L<ngx.req.read_body>.




=head2 ngx.req.get_body_data

B<syntax:> I<data = ngx.req.get_body_data(max_bytes?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, log_by_luaE<42>>

Retrieves in-memory request body data. It returns a Lua string rather than a Lua table holding all the parsed query arguments. Use the L<ngx.req.get_post_args> function instead if a Lua table is required.

The optional C<max_bytes> argument can be used when you don't need the entire body.

This function returns C<nil> if


=over


=item 1.

the request body has not been read,

=item 2.

the request body has been read into disk temporary files,

=item 3.

or the request body has zero size.


=back

If the request body has not been read yet, call L<ngx.req.read_body> first (or turn on L<lua_need_request_body> to force this module to read the request body. This is not recommended however).

If the request body has been read into disk files, try calling the L<ngx.req.get_body_file> function instead.

To force in-memory request bodies, try setting L<client_body_buffer_size|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_body_buffer_size> to the same size value in L<client_max_body_size|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_max_body_size>.

Note that calling this function instead of using C<ngx.var.request_body> or C<ngx.var.echo_request_body> is more efficient because it can save one dynamic memory allocation and one data copy.

This function was first introduced in the C<v0.3.1rc17> release.

See also L<ngx.req.get_body_file>.




=head2 ngx.req.get_body_file

B<syntax:> I<file_name = ngx.req.get_body_file()>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Retrieves the file name for the in-file request body data. Returns C<nil> if the request body has not been read or has been read into memory.

The returned file is read only and is usually cleaned up by Nginx's memory pool. It should not be manually modified, renamed, or removed in Lua code.

If the request body has not been read yet, call L<ngx.req.read_body> first (or turn on L<lua_need_request_body> to force this module to read the request body. This is not recommended however).

If the request body has been read into memory, try calling the L<ngx.req.get_body_data> function instead.

To force in-file request bodies, try turning on L<client_body_in_file_only|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_body_in_file_only>.

Note that this function is also work for balancer phase but it needs to call L<balancer.recreate_request|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/balancer.md#recreate_request> to make the change take effect after set the request body data or headers.

This function was first introduced in the C<v0.3.1rc17> release.

See also L<ngx.req.get_body_data>.




=head2 ngx.req.set_body_data

B<syntax:> I<ngx.req.set_body_data(data)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, balancer_by_luaE<42>,>

Set the current request's request body using the in-memory data specified by the C<data> argument.

If the request body has not been read yet, call L<ngx.req.read_body> first (or turn on L<lua_need_request_body> to force this module to read the request body. This is not recommended however). Additionally, the request body must not have been previously discarded by L<ngx.req.discard_body>.

Whether the previous request body has been read into memory or buffered into a disk file, it will be freed or the disk file will be cleaned up immediately, respectively.

Note that this function is also work for balancer phase but it needs to call L<balancer.recreate_request|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/balancer.md#recreate_request> to make the change take effect after set the request body data or headers.

This function was first introduced in the C<v0.3.1rc18> release.

See also L<ngx.req.set_body_file>.




=head2 ngx.req.set_body_file

B<syntax:> I<ngx.req.set_body_file(file_name, auto_clean?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, balancer_by_luaE<42>,>

Set the current request's request body using the in-file data specified by the C<file_name> argument.

If the request body has not been read yet, call L<ngx.req.read_body> first (or turn on L<lua_need_request_body> to force this module to read the request body. This is not recommended however). Additionally, the request body must not have been previously discarded by L<ngx.req.discard_body>.

If the optional C<auto_clean> argument is given a C<true> value, then this file will be removed at request completion or the next time this function or L<ngx.req.set_body_data> are called in the same request. The C<auto_clean> is default to C<false>.

Please ensure that the file specified by the C<file_name> argument exists and is readable by an Nginx worker process by setting its permission properly to avoid Lua exception errors.

Whether the previous request body has been read into memory or buffered into a disk file, it will be freed or the disk file will be cleaned up immediately, respectively.

This function was first introduced in the C<v0.3.1rc18> release.

See also L<ngx.req.set_body_data>.




=head2 ngx.req.init_body

B<syntax:> I<ngx.req.init_body(buffer_size?)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Creates a new blank request body for the current request and initializes the buffer for later request body data writing via the L<ngx.req.append_body> and L<ngx.req.finish_body> APIs.

If the C<buffer_size> argument is specified, then its value will be used for the size of the memory buffer for body writing with L<ngx.req.append_body>. If the argument is omitted, then the value specified by the standard L<client_body_buffer_size|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_body_buffer_size> directive will be used instead.

When the data can no longer be hold in the memory buffer for the request body, then the data will be flushed onto a temporary file just like the standard request body reader in the Nginx core.

It is important to always call the L<ngx.req.finish_body> after all the data has been appended onto the current request body. Also, when this function is used together with L<ngx.req.socket>, it is required to call L<ngx.req.socket> I<before> this function, or you will get the "request body already exists" error message.

The usage of this function is often like this:


     ngx.req.init_body(128 * 1024)  -- buffer is 128KB
     for chunk in next_data_chunk() do
         ngx.req.append_body(chunk) -- each chunk can be 4KB
     end
     ngx.req.finish_body()

This function can be used with L<ngx.req.append_body>, L<ngx.req.finish_body>, and L<ngx.req.socket> to implement efficient input filters in pure Lua (in the context of L<rewrite_by_lua*> or L<access_by_lua*>), which can be used with other Nginx content handler or upstream modules like L<ngx_http_proxy_module|http://nginx.org/en/docs/http/ngx_http_proxy_module.html> and L<ngx_http_fastcgi_module|http://nginx.org/en/docs/http/ngx_http_fastcgi_module.html>.

This function was first introduced in the C<v0.5.11> release.




=head2 ngx.req.append_body

B<syntax:> I<ngx.req.append_body(data_chunk)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Append new data chunk specified by the C<data_chunk> argument onto the existing request body created by the L<ngx.req.init_body> call.

When the data can no longer be hold in the memory buffer for the request body, then the data will be flushed onto a temporary file just like the standard request body reader in the Nginx core.

It is important to always call the L<ngx.req.finish_body> after all the data has been appended onto the current request body.

This function can be used with L<ngx.req.init_body>, L<ngx.req.finish_body>, and L<ngx.req.socket> to implement efficient input filters in pure Lua (in the context of L<rewrite_by_lua*> or L<access_by_lua*>), which can be used with other Nginx content handler or upstream modules like L<ngx_http_proxy_module|http://nginx.org/en/docs/http/ngx_http_proxy_module.html> and L<ngx_http_fastcgi_module|http://nginx.org/en/docs/http/ngx_http_fastcgi_module.html>.

This function was first introduced in the C<v0.5.11> release.

See also L<ngx.req.init_body>.




=head2 ngx.req.finish_body

B<syntax:> I<ngx.req.finish_body()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Completes the construction process of the new request body created by the L<ngx.req.init_body> and L<ngx.req.append_body> calls.

This function can be used with L<ngx.req.init_body>, L<ngx.req.append_body>, and L<ngx.req.socket> to implement efficient input filters in pure Lua (in the context of L<rewrite_by_lua*> or L<access_by_lua*>), which can be used with other Nginx content handler or upstream modules like L<ngx_http_proxy_module|http://nginx.org/en/docs/http/ngx_http_proxy_module.html> and L<ngx_http_fastcgi_module|http://nginx.org/en/docs/http/ngx_http_fastcgi_module.html>.

This function was first introduced in the C<v0.5.11> release.

See also L<ngx.req.init_body>.




=head2 ngx.req.socket

B<syntax:> I<tcpsock, err = ngx.req.socket()>

B<syntax:> I<tcpsock, err = ngx.req.socket(raw)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Returns a read-only cosocket object that wraps the downstream connection. Only L<receive>, L<receiveany> and L<receiveuntil> methods are supported on this object.

In case of error, C<nil> will be returned as well as a string describing the error.

B<Note:> This method will block while waiting for client request body to be fully received. Block time depends on the L<client_body_timeout|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_body_timeout> directive and maximum body size specified by the L<client_max_body_size|http://nginx.org/en/docs/http/ngx_http_core_module.html#client_max_body_size> directive. If read timeout occurs or client body size exceeds the defined limit, this function will not return and C<408 Request Time-out> or C<413 Request Entity Too Large> response will be returned to the client instead.

The socket object returned by this method is usually used to read the current request's body in a streaming fashion. Do not turn on the L<lua_need_request_body> directive, and do not mix this call with L<ngx.req.read_body> and L<ngx.req.discard_body>.

If any request body data has been pre-read into the Nginx core request header buffer, the resulting cosocket object will take care of this to avoid potential data loss resulting from such pre-reading.
Chunked request bodies are not yet supported in this API.

Since the C<v0.9.0> release, this function accepts an optional boolean C<raw> argument. When this argument is C<true>, this function returns a full-duplex cosocket object wrapping around the raw downstream connection socket, upon which you can call the L<receive>, L<receiveany>, L<receiveuntil>, and L<send> methods.

When the C<raw> argument is C<true>, it is required that no pending data from any previous L<ngx.say>, L<ngx.print>, or L<ngx.send_headers> calls exists. So if you have these downstream output calls previously, you should call L<ngx.flush(true)> before calling C<ngx.req.socket(true)> to ensure that there is no pending output data. If the request body has not been read yet, then this "raw socket" can also be used to read the request body.

You can use the "raw request socket" returned by C<ngx.req.socket(true)> to implement fancy protocols like L<WebSocket|https://en.wikipedia.org/wiki/WebSocket>, or just emit your own raw HTTP response header or body data. You can refer to the L<lua-resty-websocket library|https://github.com/openresty/lua-resty-websocket> for a real world example.

This function was first introduced in the C<v0.5.0rc1> release.




=head2 ngx.exec

B<syntax:> I<ngx.exec(uri, args?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Does an internal redirect to C<uri> with C<args> and is similar to the L<echo_exec|http://github.com/openresty/echo-nginx-module#echo_exec> directive of the L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>.


     ngx.exec('/some-location')
     ngx.exec('/some-location', 'a=3&b=5&c=6')
     ngx.exec('/some-location?a=3&b=5', 'c=6')

The optional second C<args> can be used to specify extra URI query arguments, for example:


     ngx.exec("/foo", "a=3&b=hello%20world")

Alternatively, a Lua table can be passed for the C<args> argument for ngx_lua to carry out URI escaping and string concatenation.


     ngx.exec("/foo", { a = 3, b = "hello world" })

The result is exactly the same as the previous example.

The format for the Lua table passed as the C<args> argument is identical to the format used in the L<ngx.encode_args> method.

Named locations are also supported but the second C<args> argument will be ignored if present and the querystring for the new target is inherited from the referring location (if any).

C<GET /foo/file.php?a=hello> will return "hello" and not "goodbye" in the example below


     location /foo {
         content_by_lua_block {
             ngx.exec("@bar", "a=goodbye")
         }
     }
    
     location @bar {
         content_by_lua_block {
             local args = ngx.req.get_uri_args()
             for key, val in pairs(args) do
                 if key == "a" then
                     ngx.say(val)
                 end
             end
         }
     }

Note that the C<ngx.exec> method is different from L<ngx.redirect> in that
it is purely an internal redirect and that no new external HTTP traffic is involved.

Also note that this method call terminates the processing of the current request and that it I<must> be called before L<ngx.send_headers> or explicit response body
outputs by either L<ngx.print> or L<ngx.say>.

It is recommended that a coding style that combines this method call with the C<return> statement, i.e., C<return ngx.exec(...)> be adopted when this method call is used in contexts other than L<header_filter_by_lua*> to reinforce the fact that the request processing is being terminated.




=head2 ngx.redirect

B<syntax:> I<ngx.redirect(uri, status?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Issue an C<HTTP 301> or C<302> redirection to C<uri>.

Note: this function throws a Lua error if the C<uri> argument
contains unsafe characters (control characters).

The optional C<status> parameter specifies the HTTP status code to be used. The following status codes are supported right now:


=over


=item *

C<301>

=item *

C<302> (default)

=item *

C<303>

=item *

C<307>

=item *

C<308>


=back

It is C<302> (C<ngx.HTTP_MOVED_TEMPORARILY>) by default.

Here is an example assuming the current server name is C<localhost> and that it is listening on port 1984:


     return ngx.redirect("/foo")

which is equivalent to


     return ngx.redirect("/foo", ngx.HTTP_MOVED_TEMPORARILY)

Redirecting arbitrary external URLs is also supported, for example:


     return ngx.redirect("http://www.google.com")

We can also use the numerical code directly as the second C<status> argument:


     return ngx.redirect("/foo", 301)

This method is similar to the L<rewrite|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html#rewrite> directive with the C<redirect> modifier in the standard
L<ngx_http_rewrite_module|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html>, for example, this C<nginx.conf> snippet


     rewrite ^ /foo? redirect;  # nginx config

is equivalent to the following Lua code


     return ngx.redirect('/foo')  -- Lua code

while


     rewrite ^ /foo? permanent;  # nginx config

is equivalent to


     return ngx.redirect('/foo', ngx.HTTP_MOVED_PERMANENTLY)  -- Lua code

URI arguments can be specified as well, for example:


     return ngx.redirect('/foo?a=3&b=4')

Note that this method call terminates the processing of the current request and that it I<must> be called before L<ngx.send_headers> or explicit response body
outputs by either L<ngx.print> or L<ngx.say>.

It is recommended that a coding style that combines this method call with the C<return> statement, i.e., C<return ngx.redirect(...)> be adopted when this method call is used in contexts other than L<header_filter_by_lua*> to reinforce the fact that the request processing is being terminated.




=head2 ngx.send_headers

B<syntax:> I<ok, err = ngx.send_headers()>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Explicitly send out the response headers.

Since C<v0.8.3> this function returns C<1> on success, or returns C<nil> and a string describing the error otherwise.

Note that there is normally no need to manually send out response headers as ngx_lua will automatically send headers out
before content is output with L<ngx.say> or L<ngx.print> or when L<content_by_lua*> exits normally.




=head2 ngx.headers_sent

B<syntax:> I<value = ngx.headers_sent>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Returns C<true> if the response headers have been sent (by ngx_lua), and C<false> otherwise.

This API was first introduced in ngx_lua v0.3.1rc6.




=head2 ngx.print

B<syntax:> I<ok, err = ngx.print(...)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Emits arguments concatenated to the HTTP client (as response body). If response headers have not been sent, this function will send headers out first and then output body data.

Since C<v0.8.3> this function returns C<1> on success, or returns C<nil> and a string describing the error otherwise.

Lua C<nil> values will output C<"nil"> strings and Lua boolean values will output C<"true"> and C<"false"> literal strings respectively.

Nested arrays of strings are permitted and the elements in the arrays will be sent one by one:


     local table = {
         "hello, ",
         {"world: ", true, " or ", false,
             {": ", nil}}
     }
     ngx.print(table)

will yield the output


     hello, world: true or false: nil

Non-array table arguments will cause a Lua exception to be thrown.

The C<ngx.null> constant will yield the C<"null"> string output.

This is an asynchronous call and will return immediately without waiting for all the data to be written into the system send buffer. To run in synchronous mode, call C<ngx.flush(true)> after calling C<ngx.print>. This can be particularly useful for streaming output. See L<ngx.flush> for more details.

Please note that both C<ngx.print> and L<ngx.say> will always invoke the whole Nginx output body filter chain, which is an expensive operation. So be careful when calling either of these two in a tight loop; buffer the data yourself in Lua and save the calls.




=head2 ngx.say

B<syntax:> I<ok, err = ngx.say(...)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Just as L<ngx.print> but also emit a trailing newline.




=head2 ngx.log

B<syntax:> I<ngx.log(log_level, ...)>

B<context:> I<init_by_luaE<42>, init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Log arguments concatenated to error.log with the given logging level.

Lua C<nil> arguments are accepted and result in literal C<"nil"> string while Lua booleans result in literal C<"true"> or C<"false"> string outputs. And the C<ngx.null> constant will yield the C<"null"> string output.

The C<log_level> argument can take constants like C<ngx.ERR> and C<ngx.WARN>. Check out L<Nginx log level constants> for details.

There is a hard coded C<2048> byte limitation on error message lengths in the Nginx core. This limit includes trailing newlines and leading time stamps. If the message size exceeds this limit, Nginx will truncate the message text accordingly. This limit can be manually modified by editing the C<NGX_MAX_ERROR_STR> macro definition in the C<src/core/ngx_log.h> file in the Nginx source tree.




=head2 ngx.flush

B<syntax:> I<ok, err = ngx.flush(wait?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Flushes response output to the client.

C<ngx.flush> accepts an optional boolean C<wait> argument (Default: C<false>) first introduced in the C<v0.3.1rc34> release. When called with the default argument, it issues an asynchronous call (Returns immediately without waiting for output data to be written into the system send buffer). Calling the function with the C<wait> argument set to C<true> switches to synchronous mode.

In synchronous mode, the function will not return until all output data has been written into the system send buffer or until the L<send_timeout|http://nginx.org/en/docs/http/ngx_http_core_module.html#send_timeout> setting has expired. Note that using the Lua coroutine mechanism means that this function does not block the Nginx event loop even in the synchronous mode.

When C<ngx.flush(true)> is called immediately after L<ngx.print> or L<ngx.say>, it causes the latter functions to run in synchronous mode. This can be particularly useful for streaming output.

Note that C<ngx.flush> is not functional when in the HTTP 1.0 output buffering mode. See L<HTTP 1.0 support>.

Since C<v0.8.3> this function returns C<1> on success, or returns C<nil> and a string describing the error otherwise.




=head2 ngx.exit

B<syntax:> I<ngx.exit(status)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

When C<< status >= 200 >> (i.e., C<ngx.HTTP_OK> and above), it will interrupt the execution of the current request and return status code to Nginx.

When C<status == 0> (i.e., C<ngx.OK>), it will only quit the current phase handler (or the content handler if the L<content_by_lua*> directive is used) and continue to run later phases (if any) for the current request.

The C<status> argument can be C<ngx.OK>, C<ngx.ERROR>, C<ngx.HTTP_NOT_FOUND>,
C<ngx.HTTP_MOVED_TEMPORARILY>, or other L<HTTP status constants>.

To return an error page with custom contents, use code snippets like this:


     ngx.status = ngx.HTTP_GONE
     ngx.say("This is our own content")
     -- to cause quit the whole request rather than the current phase handler
     ngx.exit(ngx.HTTP_OK)

The effect in action:


     $ curl -i http://localhost/test
     HTTP/1.1 410 Gone
     Server: nginx/1.0.6
     Date: Thu, 15 Sep 2011 00:51:48 GMT
     Content-Type: text/plain
     Transfer-Encoding: chunked
     Connection: keep-alive
    
     This is our own content

Number literals can be used directly as the argument, for instance,


     ngx.exit(501)

Note that while this method accepts all L<HTTP status constants> as input, it only accepts C<ngx.OK> and C<ngx.ERROR> of the L<core constants>.

Also note that this method call terminates the processing of the current request and that it is recommended that a coding style that combines this method call with the C<return> statement, i.e., C<return ngx.exit(...)> be used to reinforce the fact that the request processing is being terminated.

When being used in the contexts of L<header_filter_by_lua*>, L<balancer_by_lua*>, and
L<ssl_session_store_by_lua*>, C<ngx.exit()> is
an asynchronous operation and will return immediately. This behavior may change in future and it is recommended that users always use C<return> in combination as suggested above.




=head2 ngx.eof

B<syntax:> I<ok, err = ngx.eof()>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Explicitly specify the end of the response output stream. In the case of HTTP 1.1 chunked encoded output, it will just trigger the Nginx core to send out the "last chunk".

When you disable the HTTP 1.1 keep-alive feature for your downstream connections, you can rely on well written HTTP clients to close the connection actively for you when you call this method. This trick can be used do back-ground jobs without letting the HTTP clients to wait on the connection, as in the following example:


     location = /async {
         keepalive_timeout 0;
         content_by_lua_block {
             ngx.say("got the task!")
             ngx.eof()  -- well written HTTP clients will close the connection at this point
             -- access MySQL, PostgreSQL, Redis, Memcached, and etc here...
         }
     }

But if you create subrequests to access other locations configured by Nginx upstream modules, then you should configure those upstream modules to ignore client connection abortions if they are not by default. For example, by default the standard L<ngx_http_proxy_module|http://nginx.org/en/docs/http/ngx_http_proxy_module.html> will terminate both the subrequest and the main request as soon as the client closes the connection, so it is important to turn on the L<proxy_ignore_client_abort|http://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_ignore_client_abort> directive in your location block configured by L<ngx_http_proxy_module|http://nginx.org/en/docs/http/ngx_http_proxy_module.html>:


     proxy_ignore_client_abort on;

A better way to do background jobs is to use the L<ngx.timer.at> API.

Since C<v0.8.3> this function returns C<1> on success, or returns C<nil> and a string describing the error otherwise.




=head2 ngx.sleep

B<syntax:> I<ngx.sleep(seconds)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Sleeps for the specified seconds without blocking. One can specify time resolution up to 0.001 seconds (i.e., one millisecond).

Behind the scene, this method makes use of the Nginx timers.

Since the C<0.7.20> release, The C<0> time argument can also be specified.

This method was introduced in the C<0.5.0rc30> release.




=head2 ngx.escape_uri

B<syntax:> I<newstr = ngx.escape_uri(str, type?)>

B<context:> I<init_by_luaE<42>, init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Since C<v0.10.16>, this function accepts an optional C<type> argument.
It accepts the following values (defaults to C<2>):


=over


=item *

C<0>: escapes C<str> as a full URI. And the characters
C< > (space), C<#>, C<%>,
C<?>, 0x00 ~ 0x1F, 0x7F ~ 0xFF will be escaped.

=item *

C<2>: escape C<str> as a URI component. All characters except
alphabetic characters, digits, C<->, C<.>, C<_>,
C<~> will be encoded as C<%XX>.


=back




=head2 ngx.unescape_uri

B<syntax:> I<newstr = ngx.unescape_uri(str)>

B<context:> I<init_by_luaE<42>, init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Unescape C<str> as an escaped URI component.

For example,


     ngx.say(ngx.unescape_uri("b%20r56+7"))

gives the output

    b r56 7

Invalid escaping sequences are handled in a conventional way: C<%>s are left unchanged. Also, characters that should not appear in escaped string are simply left unchanged.

For example,


     ngx.say(ngx.unescape_uri("try %search%%20%again%"))

gives the output

    try %search% %again%

(Note that C<%20> following C<%> got unescaped, even it can be considered a part of invalid sequence.)




=head2 ngx.encode_args

B<syntax:> I<str = ngx.encode_args(table)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Encode the Lua table to a query args string according to the URI encoded rules.

For example,


     ngx.encode_args({foo = 3, ["b r"] = "hello world"})

yields

    foo=3&b%20r=hello%20world

The table keys must be Lua strings.

Multi-value query args are also supported. Just use a Lua table for the argument's value, for example:


     ngx.encode_args({baz = {32, "hello"}})

gives

    baz=32&baz=hello

If the value table is empty and the effect is equivalent to the C<nil> value.

Boolean argument values are also supported, for instance,


     ngx.encode_args({a = true, b = 1})

yields

    a&b=1

If the argument value is C<false>, then the effect is equivalent to the C<nil> value.

This method was first introduced in the C<v0.3.1rc27> release.




=head2 ngx.decode_args

B<syntax:> I<table, err = ngx.decode_args(str, max_args?)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Decodes a URI encoded query-string into a Lua table. This is the inverse function of L<ngx.encode_args>.

The optional C<max_args> argument can be used to specify the maximum number of arguments parsed from the C<str> argument. By default, a maximum of 100 request arguments are parsed (including those with the same name) and that additional URI arguments are silently discarded to guard against potential denial of service attacks. Since C<v0.10.13>, when the limit is exceeded, it will return a second value which is the string C<"truncated">.

This argument can be set to zero to remove the limit and to process all request arguments received:


     local args = ngx.decode_args(str, 0)

Removing the C<max_args> cap is strongly discouraged.

This method was introduced in the C<v0.5.0rc29>.




=head2 ngx.encode_base64

B<syntax:> I<newstr = ngx.encode_base64(str, no_padding?)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Encodes C<str> to a base64 digest. For base64url encoding use L<`base64.encode_base64url`|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/base64.md#encode_base64url>.

Since the C<0.9.16> release, an optional boolean-typed C<no_padding> argument can be specified to control whether the base64 padding should be appended to the resulting digest (default to C<false>, i.e., with padding enabled).




=head2 ngx.decode_base64

B<syntax:> I<newstr = ngx.decode_base64(str)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Decodes the C<str> argument as a base64 digest to the raw form. For base64url decoding use L<`base64.decode_base64url`|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/base64.md#decode_base64url>.

The C<str> should be standard 'base64' encoding for RFC 3548 or RFC 4648, and will returns C<nil> if is not well formed or any characters not in the base encoding alphabet. Padding may be omitted from the input.




=head2 ngx.decode_base64mime

B<syntax:> I<newstr = ngx.decode_base64mime(str)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>>

B<requires:> C<resty.core.base64> or C<resty.core>

Decodes the C<str> argument as a base64 digest to the raw form.
The C<str> follows base64 transfer encoding for MIME (RFC 2045), and will discard characters outside the base encoding alphabet.
Returns C<nil> if C<str> is not well formed.

'''Note:''' This method requires the E<lt>codeE<gt>resty.core.base64E<lt>/codeE<gt> or E<lt>codeE<gt>resty.coreE<lt>/codeE<gt> modules from the L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.




=head2 ngx.crc32_short

B<syntax:> I<intval = ngx.crc32_short(str)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Calculates the CRC-32 (Cyclic Redundancy Code) digest for the C<str> argument.

This method performs better on relatively short C<str> inputs (i.e., less than 30 ~ 60 bytes), as compared to L<ngx.crc32_long>. The result is exactly the same as L<ngx.crc32_long>.

Behind the scene, it is just a thin wrapper around the C<ngx_crc32_short> function defined in the Nginx core.

This API was first introduced in the C<v0.3.1rc8> release.




=head2 ngx.crc32_long

B<syntax:> I<intval = ngx.crc32_long(str)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Calculates the CRC-32 (Cyclic Redundancy Code) digest for the C<str> argument.

This method performs better on relatively long C<str> inputs (i.e., longer than 30 ~ 60 bytes), as compared to L<ngx.crc32_short>.  The result is exactly the same as L<ngx.crc32_short>.

Behind the scene, it is just a thin wrapper around the C<ngx_crc32_long> function defined in the Nginx core.

This API was first introduced in the C<v0.3.1rc8> release.




=head2 ngx.hmac_sha1

B<syntax:> I<digest = ngx.hmac_sha1(secret_key, str)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Computes the L<HMAC-SHA1|https://en.wikipedia.org/wiki/HMAC> digest of the argument C<str> and turns the result using the secret key C<< <secret_key> >>.

The raw binary form of the C<HMAC-SHA1> digest will be generated, use L<ngx.encode_base64>, for example, to encode the result to a textual representation if desired.

For example,


     local key = "thisisverysecretstuff"
     local src = "some string we want to sign"
     local digest = ngx.hmac_sha1(key, src)
     ngx.say(ngx.encode_base64(digest))

yields the output

    R/pvxzHC4NLtj7S+kXFg/NePTmk=

This API requires the OpenSSL library enabled in the Nginx build (usually by passing the C<--with-http_ssl_module> option to the C<./configure> script).

This function was first introduced in the C<v0.3.1rc29> release.




=head2 ngx.md5

B<syntax:> I<digest = ngx.md5(str)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns the hexadecimal representation of the MD5 digest of the C<str> argument.

For example,


     location = /md5 {
         content_by_lua_block {
             ngx.say(ngx.md5("hello"))
         }
     }

yields the output

    5d41402abc4b2a76b9719d911017c592

See L<ngx.md5_bin> if the raw binary MD5 digest is required.




=head2 ngx.md5_bin

B<syntax:> I<digest = ngx.md5_bin(str)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns the binary form of the MD5 digest of the C<str> argument.

See L<ngx.md5> if the hexadecimal form of the MD5 digest is required.




=head2 ngx.sha1_bin

B<syntax:> I<digest = ngx.sha1_bin(str)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns the binary form of the SHA-1 digest of the C<str> argument.

This function requires SHA-1 support in the Nginx build. (This usually just means OpenSSL should be installed while building Nginx).

This function was first introduced in the C<v0.5.0rc6>.




=head2 ngx.quote_sql_str

B<syntax:> I<quoted_value = ngx.quote_sql_str(raw_value)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns a quoted SQL string literal according to the MySQL quoting rules.




=head2 ngx.today

B<syntax:> I<str = ngx.today()>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns current date (in the format C<yyyy-mm-dd>) from the Nginx cached time (no syscall involved unlike Lua's date library).

This is the local time.




=head2 ngx.time

B<syntax:> I<secs = ngx.time()>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns the elapsed seconds from the epoch for the current time stamp from the Nginx cached time (no syscall involved unlike Lua's date library).

Updates of the Nginx time cache can be forced by calling L<ngx.update_time> first.




=head2 ngx.now

B<syntax:> I<secs = ngx.now()>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns a floating-point number for the elapsed time in seconds (including milliseconds as the decimal part) from the epoch for the current time stamp from the Nginx cached time (no syscall involved unlike Lua's date library).

You can forcibly update the Nginx time cache by calling L<ngx.update_time> first.

This API was first introduced in C<v0.3.1rc32>.




=head2 ngx.update_time

B<syntax:> I<ngx.update_time()>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Forcibly updates the Nginx current time cache. This call involves a syscall and thus has some overhead, so do not abuse it.

This API was first introduced in C<v0.3.1rc32>.




=head2 ngx.localtime

B<syntax:> I<str = ngx.localtime()>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns the current time stamp (in the format C<yyyy-mm-dd hh:mm:ss>) of the Nginx cached time (no syscall involved unlike Lua's L<os.date|https://www.lua.org/manual/5.1/manual.html#pdf-os.date> function).

This is the local time.




=head2 ngx.utctime

B<syntax:> I<str = ngx.utctime()>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns the current time stamp (in the format C<yyyy-mm-dd hh:mm:ss>) of the Nginx cached time (no syscall involved unlike Lua's L<os.date|https://www.lua.org/manual/5.1/manual.html#pdf-os.date> function).

This is the UTC time.




=head2 ngx.cookie_time

B<syntax:> I<str = ngx.cookie_time(sec)>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns a formatted string can be used as the cookie expiration time. The parameter C<sec> is the time stamp in seconds (like those returned from L<ngx.time>).


     ngx.say(ngx.cookie_time(1290079655))
         -- yields "Thu, 18-Nov-10 11:27:35 GMT"




=head2 ngx.http_time

B<syntax:> I<str = ngx.http_time(sec)>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns a formated string can be used as the http header time (for example, being used in C<Last-Modified> header). The parameter C<sec> is the time stamp in seconds (like those returned from L<ngx.time>).


     ngx.say(ngx.http_time(1290079655))
         -- yields "Thu, 18 Nov 2010 11:27:35 GMT"




=head2 ngx.parse_http_time

B<syntax:> I<sec = ngx.parse_http_time(str)>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Parse the http time string (as returned by L<ngx.http_time>) into seconds. Returns the seconds or C<nil> if the input string is in bad forms.


     local time = ngx.parse_http_time("Thu, 18 Nov 2010 11:27:35 GMT")
     if time == nil then
         ...
     end




=head2 ngx.is_subrequest

B<syntax:> I<value = ngx.is_subrequest>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>>

Returns C<true> if the current request is an Nginx subrequest, or C<false> otherwise.




=head2 ngx.re.match

B<syntax:> I<captures, err = ngx.re.match(subject, regex, options?, ctx?, res_table?)>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Matches the C<subject> string using the Perl compatible regular expression C<regex> with the optional C<options>.

Only the first occurrence of the match is returned, or C<nil> if no match is found. In case of errors, like seeing a bad regular expression or exceeding the PCRE stack limit, C<nil> and a string describing the error will be returned.

When a match is found, a Lua table C<captures> is returned, where C<captures[0]> holds the whole substring being matched, and C<captures[1]> holds the first parenthesized sub-pattern's capturing, C<captures[2]> the second, and so on.


     local m, err = ngx.re.match("hello, 1234", "[0-9]+")
     if m then
         -- m[0] == "1234"
    
     else
         if err then
             ngx.log(ngx.ERR, "error: ", err)
             return
         end
    
         ngx.say("match not found")
     end


     local m, err = ngx.re.match("hello, 1234", "([0-9])[0-9]+")
     -- m[0] == "1234"
     -- m[1] == "1"

Named captures are also supported since the C<v0.7.14> release
and are returned in the same Lua table as key-value pairs as the numbered captures.


     local m, err = ngx.re.match("hello, 1234", "([0-9])(?<remaining>[0-9]+)")
     -- m[0] == "1234"
     -- m[1] == "1"
     -- m[2] == "234"
     -- m["remaining"] == "234"

Unmatched subpatterns will have C<false> values in their C<captures> table fields.


     local m, err = ngx.re.match("hello, world", "(world)|(hello)|(?<named>howdy)")
     -- m[0] == "hello"
     -- m[1] == false
     -- m[2] == "hello"
     -- m[3] == false
     -- m["named"] == false

Specify C<options> to control how the match operation will be performed. The following option characters are supported:

    a             anchored mode (only match from the beginning)

    d             enable the DFA mode (or the longest token match semantics).
                  this requires PCRE 6.0+ or else a Lua exception will be thrown.
                  first introduced in ngx_lua v0.3.1rc30.

    D             enable duplicate named pattern support. This allows named
                  subpattern names to be repeated, returning the captures in
                  an array-like Lua table. for example,
                    local m = ngx.re.match("hello, world",
                                           "(?<named>\w+), (?<named>\w+)",
                                           "D")
                    -- m["named"] == {"hello", "world"}
                  this option was first introduced in the v0.7.14 release.
                  this option requires at least PCRE 8.12.

    i             case insensitive mode (similar to Perl's /i modifier)

    j             enable PCRE JIT compilation, this requires PCRE 8.21+ which
                  must be built with the --enable-jit option. for optimum performance,
                  this option should always be used together with the 'o' option.
                  first introduced in ngx_lua v0.3.1rc30.

    J             enable the PCRE Javascript compatible mode. this option was
                  first introduced in the v0.7.14 release. this option requires
                  at least PCRE 8.12.

    m             multi-line mode (similar to Perl's /m modifier)

    o             compile-once mode (similar to Perl's /o modifier),
                  to enable the worker-process-level compiled-regex cache

    s             single-line mode (similar to Perl's /s modifier)

    u             UTF-8 mode. this requires PCRE to be built with
                  the --enable-utf8 option or else a Lua exception will be thrown.

    U             similar to "u" but disables PCRE's UTF-8 validity check on
                  the subject string. first introduced in ngx_lua v0.8.1.

    x             extended mode (similar to Perl's /x modifier)

These options can be combined:


     local m, err = ngx.re.match("hello, world", "HEL LO", "ix")
     -- m[0] == "hello"


     local m, err = ngx.re.match("hello, 美好生活", "HELLO, (.{2})", "iu")
     -- m[0] == "hello, 美好"
     -- m[1] == "美好"

The C<o> option is useful for performance tuning, because the regex pattern in question will only be compiled once, cached in the worker-process level, and shared among all requests in the current Nginx worker process. The upper limit of the regex cache can be tuned via the L<lua_regex_cache_max_entries> directive.

The optional fourth argument, C<ctx>, can be a Lua table holding an optional C<pos> field. When the C<pos> field in the C<ctx> table argument is specified, C<ngx.re.match> will start matching from that offset (starting from 1). Regardless of the presence of the C<pos> field in the C<ctx> table, C<ngx.re.match> will always set this C<pos> field to the position I<after> the substring matched by the whole pattern in case of a successful match. When match fails, the C<ctx> table will be left intact.


     local ctx = {}
     local m, err = ngx.re.match("1234, hello", "[0-9]+", "", ctx)
          -- m[0] = "1234"
          -- ctx.pos == 5


     local ctx = { pos = 2 }
     local m, err = ngx.re.match("1234, hello", "[0-9]+", "", ctx)
          -- m[0] = "234"
          -- ctx.pos == 5

The C<ctx> table argument combined with the C<a> regex modifier can be used to construct a lexer atop C<ngx.re.match>.

Note that, the C<options> argument is not optional when the C<ctx> argument is specified and that the empty Lua string (C<"">) must be used as placeholder for C<options> if no meaningful regex options are required.

This method requires the PCRE library enabled in Nginx (L<Known Issue With Special Escaping Sequences>).

To confirm that PCRE JIT is enabled, activate the Nginx debug log by adding the C<--with-debug> option to Nginx or OpenResty's C<./configure> script. Then, enable the "debug" error log level in C<error_log> directive. The following message will be generated if PCRE JIT is enabled:

    pcre JIT compiling result: 1

Starting from the C<0.9.4> release, this function also accepts a 5th argument, C<res_table>, for letting the caller supply the Lua table used to hold all the capturing results. Starting from C<0.9.6>, it is the caller's responsibility to ensure this table is empty. This is very useful for recycling Lua tables and saving GC and table allocation overhead.

This feature was introduced in the C<v0.2.1rc11> release.




=head2 ngx.re.find

B<syntax:> I<from, to, err = ngx.re.find(subject, regex, options?, ctx?, nth?)>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Similar to L<ngx.re.match> but only returns the beginning index (C<from>) and end index (C<to>) of the matched substring. The returned indexes are 1-based and can be fed directly into the L<string.sub|https://www.lua.org/manual/5.1/manual.html#pdf-string.sub> API function to obtain the matched substring.

In case of errors (like bad regexes or any PCRE runtime errors), this API function returns two C<nil> values followed by a string describing the error.

If no match is found, this function just returns a C<nil> value.

Below is an example:


     local s = "hello, 1234"
     local from, to, err = ngx.re.find(s, "([0-9]+)", "jo")
     if from then
         ngx.say("from: ", from)
         ngx.say("to: ", to)
         ngx.say("matched: ", string.sub(s, from, to))
     else
         if err then
             ngx.say("error: ", err)
             return
         end
         ngx.say("not matched!")
     end

This example produces the output

    from: 8
    to: 11
    matched: 1234

Because this API function does not create new Lua strings nor new Lua tables, it is much faster than L<ngx.re.match>. It should be used wherever possible.

Since the C<0.9.3> release, an optional 5th argument, C<nth>, is supported to specify which (submatch) capture's indexes to return. When C<nth> is 0 (which is the default), the indexes for the whole matched substring is returned; when C<nth> is 1, then the 1st submatch capture's indexes are returned; when C<nth> is 2, then the 2nd submatch capture is returned, and so on. When the specified submatch does not have a match, then two C<nil> values will be returned. Below is an example for this:


     local str = "hello, 1234"
     local from, to = ngx.re.find(str, "([0-9])([0-9]+)", "jo", nil, 2)
     if from then
         ngx.say("matched 2nd submatch: ", string.sub(str, from, to))  -- yields "234"
     end

This API function was first introduced in the C<v0.9.2> release.




=head2 ngx.re.gmatch

B<syntax:> I<iterator, err = ngx.re.gmatch(subject, regex, options?)>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Similar to L<ngx.re.match>, but returns a Lua iterator instead, so as to let the user programmer iterate all the matches over the C<< <subject> >> string argument with the PCRE C<regex>.

In case of errors, like seeing an ill-formed regular expression, C<nil> and a string describing the error will be returned.

Here is a small example to demonstrate its basic usage:


     local iterator, err = ngx.re.gmatch("hello, world!", "([a-z]+)", "i")
     if not iterator then
         ngx.log(ngx.ERR, "error: ", err)
         return
     end
    
     local m
     m, err = iterator()    -- m[0] == m[1] == "hello"
     if err then
         ngx.log(ngx.ERR, "error: ", err)
         return
     end
    
     m, err = iterator()    -- m[0] == m[1] == "world"
     if err then
         ngx.log(ngx.ERR, "error: ", err)
         return
     end
    
     m, err = iterator()    -- m == nil
     if err then
         ngx.log(ngx.ERR, "error: ", err)
         return
     end

More often we just put it into a Lua loop:


     local it, err = ngx.re.gmatch("hello, world!", "([a-z]+)", "i")
     if not it then
         ngx.log(ngx.ERR, "error: ", err)
         return
     end
    
     while true do
         local m, err = it()
         if err then
             ngx.log(ngx.ERR, "error: ", err)
             return
         end
    
         if not m then
             -- no match found (any more)
             break
         end
    
         -- found a match
         ngx.say(m[0])
         ngx.say(m[1])
     end

The optional C<options> argument takes exactly the same semantics as the L<ngx.re.match> method.

The current implementation requires that the iterator returned should only be used in a single request. That is, one should I<not> assign it to a variable belonging to persistent namespace like a Lua package.

This method requires the PCRE library enabled in Nginx (L<Known Issue With Special Escaping Sequences>).

This feature was first introduced in the C<v0.2.1rc12> release.




=head2 ngx.re.sub

B<syntax:> I<newstr, n, err = ngx.re.sub(subject, regex, replace, options?)>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Substitutes the first match of the Perl compatible regular expression C<regex> on the C<subject> argument string with the string or function argument C<replace>. The optional C<options> argument has exactly the same meaning as in L<ngx.re.match>.

This method returns the resulting new string as well as the number of successful substitutions. In case of failures, like syntax errors in the regular expressions or the C<< <replace> >> string argument, it will return C<nil> and a string describing the error.

When the C<replace> is a string, then it is treated as a special template for string replacement. For example,


     local newstr, n, err = ngx.re.sub("hello, 1234", "([0-9])[0-9]", "[$0][$1]")
     if not newstr then
         ngx.log(ngx.ERR, "error: ", err)
         return
     end
    
     -- newstr == "hello, [12][1]34"
     -- n == 1

where C<$0> referring to the whole substring matched by the pattern and C<$1> referring to the first parenthesized capturing substring.

Curly braces can also be used to disambiguate variable names from the background string literals:


     local newstr, n, err = ngx.re.sub("hello, 1234", "[0-9]", "${0}00")
     -- newstr == "hello, 100234"
     -- n == 1

Literal dollar sign characters (C<$>) in the C<replace> string argument can be escaped by another dollar sign, for instance,


     local newstr, n, err = ngx.re.sub("hello, 1234", "[0-9]", "$$")
     -- newstr == "hello, $234"
     -- n == 1

Do not use backlashes to escape dollar signs; it will not work as expected.

When the C<replace> argument is of type "function", then it will be invoked with the "match table" as the argument to generate the replace string literal for substitution. The "match table" fed into the C<replace> function is exactly the same as the return value of L<ngx.re.match>. Here is an example:


     local func = function (m)
         return "[" .. m[0] .. "][" .. m[1] .. "]"
     end
    
     local newstr, n, err = ngx.re.sub("hello, 1234", "( [0-9] ) [0-9]", func, "x")
     -- newstr == "hello, [12][1]34"
     -- n == 1

The dollar sign characters in the return value of the C<replace> function argument are not special at all.

This method requires the PCRE library enabled in Nginx (L<Known Issue With Special Escaping Sequences>).

This feature was first introduced in the C<v0.2.1rc13> release.




=head2 ngx.re.gsub

B<syntax:> I<newstr, n, err = ngx.re.gsub(subject, regex, replace, options?)>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Just like L<ngx.re.sub>, but does global substitution.

Here is some examples:


     local newstr, n, err = ngx.re.gsub("hello, world", "([a-z])[a-z]+", "[$0,$1]", "i")
     if not newstr then
         ngx.log(ngx.ERR, "error: ", err)
         return
     end
    
     -- newstr == "[hello,h], [world,w]"
     -- n == 2


     local func = function (m)
         return "[" .. m[0] .. "," .. m[1] .. "]"
     end
     local newstr, n, err = ngx.re.gsub("hello, world", "([a-z])[a-z]+", func, "i")
     -- newstr == "[hello,h], [world,w]"
     -- n == 2

This method requires the PCRE library enabled in Nginx (L<Known Issue With Special Escaping Sequences>).

This feature was first introduced in the C<v0.2.1rc15> release.




=head2 ngx.shared.DICT

B<syntax:> I<dict = ngx.shared.DICT>

B<syntax:> I<dict = ngx.shared[name_var]>

B<context:> I<init_by_luaE<42>, init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Fetching the shm-based Lua dictionary object for the shared memory zone named C<DICT> defined by the L<lua_shared_dict> directive.

Shared memory zones are always shared by all the Nginx worker processes in the current Nginx server instance.

The resulting object C<dict> has the following methods:


=over


=item *

L<get>

=item *

L<get_stale>

=item *

L<set>

=item *

L<safe_set>

=item *

L<add>

=item *

L<safe_add>

=item *

L<replace>

=item *

L<delete>

=item *

L<incr>

=item *

L<lpush>

=item *

L<rpush>

=item *

L<lpop>

=item *

L<rpop>

=item *

L<llen>

=item *

L<ttl>

=item *

L<expire>

=item *

L<flush_all>

=item *

L<flush_expired>

=item *

L<get_keys>

=item *

L<capacity>

=item *

L<free_space>


=back

All these methods are I<atomic> operations, that is, safe from concurrent accesses from multiple Nginx worker processes for the same C<lua_shared_dict> zone.

Here is an example:


     http {
         lua_shared_dict dogs 10m;
         server {
             location /set {
                 content_by_lua_block {
                     local dogs = ngx.shared.dogs
                     dogs:set("Jim", 8)
                     ngx.say("STORED")
                 }
             }
             location /get {
                 content_by_lua_block {
                     local dogs = ngx.shared.dogs
                     ngx.say(dogs:get("Jim"))
                 }
             }
         }
     }

Let us test it:


     $ curl localhost/set
     STORED
    
     $ curl localhost/get
     8
    
     $ curl localhost/get
     8

The number C<8> will be consistently output when accessing C</get> regardless of how many Nginx workers there are because the C<dogs> dictionary resides in the shared memory and visible to I<all> of the worker processes.

The shared dictionary will retain its contents through a server config reload (either by sending the C<HUP> signal to the Nginx process or by using the C<-s reload> command-line option).

The contents in the dictionary storage will be lost, however, when the Nginx server quits.

This feature was first introduced in the C<v0.3.1rc22> release.




=head2 ngx.shared.DICT.get

B<syntax:> I<value, flags = ngx.shared.DICT:get(key)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Retrieving the value in the dictionary L<ngx.shared.DICT> for the key C<key>. If the key does not exist or has expired, then C<nil> will be returned.

In case of errors, C<nil> and a string describing the error will be returned.

The value returned will have the original data type when they were inserted into the dictionary, for example, Lua booleans, numbers, or strings.

The first argument to this method must be the dictionary object itself, for example,


     local cats = ngx.shared.cats
     local value, flags = cats.get(cats, "Marry")

or use Lua's syntactic sugar for method calls:


     local cats = ngx.shared.cats
     local value, flags = cats:get("Marry")

These two forms are fundamentally equivalent.

If the user flags is C<0> (the default), then no flags value will be returned.

This feature was first introduced in the C<v0.3.1rc22> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.get_stale

B<syntax:> I<value, flags, stale = ngx.shared.DICT:get_stale(key)>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Similar to the L<get> method but returns the value even if the key has already expired.

Returns a 3rd value, C<stale>, indicating whether the key has expired or not.

Note that the value of an expired key is not guaranteed to be available so one should never rely on the availability of expired items.

This method was first introduced in the C<0.8.6> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.set

B<syntax:> I<success, err, forcible = ngx.shared.DICT:set(key, value, exptime?, flags?)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Unconditionally sets a key-value pair into the shm-based dictionary L<ngx.shared.DICT>. Returns three values:


=over


=item *

C<success>: boolean value to indicate whether the key-value pair is stored or not.

=item *

C<err>: textual error message, can be C<"no memory">.

=item *

C<forcible>: a boolean value to indicate whether other valid items have been removed forcibly when out of storage in the shared memory zone.


=back

The C<value> argument inserted can be Lua booleans, numbers, strings, or C<nil>. Their value type will also be stored into the dictionary and the same data type can be retrieved later via the L<get> method.

The optional C<exptime> argument specifies expiration time (in seconds) for the inserted key-value pair. The time resolution is C<0.001> seconds. If the C<exptime> takes the value C<0> (which is the default), then the item will never expire.

The optional C<flags> argument specifies a user flags value associated with the entry to be stored. It can also be retrieved later with the value. The user flags is stored as an unsigned 32-bit integer internally. Defaults to C<0>. The user flags argument was first introduced in the C<v0.5.0rc2> release.

When it fails to allocate memory for the current key-value item, then C<set> will try removing existing items in the storage according to the Least-Recently Used (LRU) algorithm. Note that, LRU takes priority over expiration time here. If up to tens of existing items have been removed and the storage left is still insufficient (either due to the total capacity limit specified by L<lua_shared_dict> or memory segmentation), then the C<err> return value will be C<no memory> and C<success> will be C<false>.

If the sizes of items in the dictionary are not multiples or even powers of a certain value (like 2), it is easier to encounter C<no memory> error because of memory fragmentation. It is recommended to use different dictionaries for different sizes of items.

When you encounter C<no memory> error, you can also evict more least-recently-used items by retrying this method call more times to to make room for the current item.

If this method succeeds in storing the current item by forcibly removing other not-yet-expired items in the dictionary via LRU, the C<forcible> return value will be C<true>. If it stores the item without forcibly removing other valid items, then the return value C<forcible> will be C<false>.

The first argument to this method must be the dictionary object itself, for example,


     local cats = ngx.shared.cats
     local succ, err, forcible = cats.set(cats, "Marry", "it is a nice cat!")

or use Lua's syntactic sugar for method calls:


     local cats = ngx.shared.cats
     local succ, err, forcible = cats:set("Marry", "it is a nice cat!")

These two forms are fundamentally equivalent.

This feature was first introduced in the C<v0.3.1rc22> release.

Please note that while internally the key-value pair is set atomically, the atomicity does not go across the method call boundary.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.safe_set

B<syntax:> I<ok, err = ngx.shared.DICT:safe_set(key, value, exptime?, flags?)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Similar to the L<set> method, but never overrides the (least recently used) unexpired items in the store when running out of storage in the shared memory zone. In this case, it will immediately return C<nil> and the string "no memory".

This feature was first introduced in the C<v0.7.18> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.add

B<syntax:> I<success, err, forcible = ngx.shared.DICT:add(key, value, exptime?, flags?)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Just like the L<set> method, but only stores the key-value pair into the dictionary L<ngx.shared.DICT> if the key does I<not> exist.

If the C<key> argument already exists in the dictionary (and not expired for sure), the C<success> return value will be C<false> and the C<err> return value will be C<"exists">.

This feature was first introduced in the C<v0.3.1rc22> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.safe_add

B<syntax:> I<ok, err = ngx.shared.DICT:safe_add(key, value, exptime?, flags?)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Similar to the L<add> method, but never overrides the (least recently used) unexpired items in the store when running out of storage in the shared memory zone. In this case, it will immediately return C<nil> and the string "no memory".

This feature was first introduced in the C<v0.7.18> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.replace

B<syntax:> I<success, err, forcible = ngx.shared.DICT:replace(key, value, exptime?, flags?)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Just like the L<set> method, but only stores the key-value pair into the dictionary L<ngx.shared.DICT> if the key I<does> exist.

If the C<key> argument does I<not> exist in the dictionary (or expired already), the C<success> return value will be C<false> and the C<err> return value will be C<"not found">.

This feature was first introduced in the C<v0.3.1rc22> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.delete

B<syntax:> I<ngx.shared.DICT:delete(key)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Unconditionally removes the key-value pair from the shm-based dictionary L<ngx.shared.DICT>.

It is equivalent to C<ngx.shared.DICT:set(key, nil)>.

This feature was first introduced in the C<v0.3.1rc22> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.incr

B<syntax:> I<newval, err, forcible? = ngx.shared.DICT:incr(key, value, init?, init_ttl?)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

B<optional requirement:> C<resty.core.shdict> or C<resty.core>

Increments the (numerical) value for C<key> in the shm-based dictionary L<ngx.shared.DICT> by the step value C<value>. Returns the new resulting number if the operation is successfully completed or C<nil> and an error message otherwise.

When the key does not exist or has already expired in the shared dictionary,


=over


=item 1.

if the C<init> argument is not specified or takes the value C<nil>, this method will return C<nil> and the error string C<"not found">, or

=item 2.

if the C<init> argument takes a number value, this method will create a new C<key> with the value C<init + value>.


=back

Like the L<add> method, it also overrides the (least recently used) unexpired items in the store when running out of storage in the shared memory zone.

The optional C<init_ttl> argument specifies expiration time (in seconds) of the value when it is initialized via the C<init> argument. The time resolution is C<0.001> seconds. If C<init_ttl> takes the value C<0> (which is the default), then the item will never expire. This argument cannot be provided without providing the C<init> argument as well, and has no effect if the value already exists (e.g., if it was previously inserted via L<set> or the likes).

B<Note:> Usage of the C<init_ttl> argument requires the C<resty.core.shdict> or C<resty.core> modules from the L<lua-resty-core|https://github.com/openresty/lua-resty-core> library. Example:


     require "resty.core"
    
     local cats = ngx.shared.cats
     local newval, err = cats:incr("black_cats", 1, 0, 0.1)
    
     print(newval) -- 1
    
     ngx.sleep(0.2)
    
     local val, err = cats:get("black_cats")
     print(val) -- nil

The C<forcible> return value will always be C<nil> when the C<init> argument is not specified.

If this method succeeds in storing the current item by forcibly removing other not-yet-expired items in the dictionary via LRU, the C<forcible> return value will be C<true>. If it stores the item without forcibly removing other valid items, then the return value C<forcible> will be C<false>.

If the original value is not a valid Lua number in the dictionary, it will return C<nil> and C<"not a number">.

The C<value> argument and C<init> argument can be any valid Lua numbers, like negative numbers or floating-point numbers.

This method was first introduced in the C<v0.3.1rc22> release.

The optional C<init> parameter was first added in the C<v0.10.6> release.

The optional C<init_ttl> parameter was introduced in the C<v0.10.12rc2> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.lpush

B<syntax:> I<length, err = ngx.shared.DICT:lpush(key, value)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Inserts the specified (numerical or string) C<value> at the head of the list named C<key> in the shm-based dictionary L<ngx.shared.DICT>. Returns the number of elements in the list after the push operation.

If C<key> does not exist, it is created as an empty list before performing the push operation. When the C<key> already takes a value that is not a list, it will return C<nil> and C<"value not a list">.

It never overrides the (least recently used) unexpired items in the store when running out of storage in the shared memory zone. In this case, it will immediately return C<nil> and the string "no memory".

This feature was first introduced in the C<v0.10.6> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.rpush

B<syntax:> I<length, err = ngx.shared.DICT:rpush(key, value)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Similar to the L<lpush> method, but inserts the specified (numerical or string) C<value> at the tail of the list named C<key>.

This feature was first introduced in the C<v0.10.6> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.lpop

B<syntax:> I<val, err = ngx.shared.DICT:lpop(key)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Removes and returns the first element of the list named C<key> in the shm-based dictionary L<ngx.shared.DICT>.

If C<key> does not exist, it will return C<nil>. When the C<key> already takes a value that is not a list, it will return C<nil> and C<"value not a list">.

This feature was first introduced in the C<v0.10.6> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.rpop

B<syntax:> I<val, err = ngx.shared.DICT:rpop(key)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Removes and returns the last element of the list named C<key> in the shm-based dictionary L<ngx.shared.DICT>.

If C<key> does not exist, it will return C<nil>. When the C<key> already takes a value that is not a list, it will return C<nil> and C<"value not a list">.

This feature was first introduced in the C<v0.10.6> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.llen

B<syntax:> I<len, err = ngx.shared.DICT:llen(key)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns the number of elements in the list named C<key> in the shm-based dictionary L<ngx.shared.DICT>.

If key does not exist, it is interpreted as an empty list and 0 is returned. When the C<key> already takes a value that is not a list, it will return C<nil> and C<"value not a list">.

This feature was first introduced in the C<v0.10.6> release.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.ttl

B<syntax:> I<ttl, err = ngx.shared.DICT:ttl(key)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

B<requires:> C<resty.core.shdict> or C<resty.core>

Retrieves the remaining TTL (time-to-live in seconds) of a key-value pair in the shm-based dictionary L<ngx.shared.DICT>. Returns the TTL as a number if the operation is successfully completed or C<nil> and an error message otherwise.

If the key does not exist (or has already expired), this method will return C<nil> and the error string C<"not found">.

The TTL is originally determined by the C<exptime> argument of the L<set>, L<add>, L<replace> (and the likes) methods. It has a time resolution of C<0.001> seconds. A value of C<0> means that the item will never expire.

Example:


     require "resty.core"
    
     local cats = ngx.shared.cats
     local succ, err = cats:set("Marry", "a nice cat", 0.5)
    
     ngx.sleep(0.2)
    
     local ttl, err = cats:ttl("Marry")
     ngx.say(ttl) -- 0.3

This feature was first introduced in the C<v0.10.11> release.

B<Note:> This method requires the C<resty.core.shdict> or C<resty.core> modules from the L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.expire

B<syntax:> I<success, err = ngx.shared.DICT:expire(key, exptime)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

B<requires:> C<resty.core.shdict> or C<resty.core>

Updates the C<exptime> (in second) of a key-value pair in the shm-based dictionary L<ngx.shared.DICT>. Returns a boolean indicating success if the operation completes or C<nil> and an error message otherwise.

If the key does not exist, this method will return C<nil> and the error string C<"not found">.

The C<exptime> argument has a resolution of C<0.001> seconds. If C<exptime> is C<0>, then the item will never expire.

Example:


     require "resty.core"
    
     local cats = ngx.shared.cats
     local succ, err = cats:set("Marry", "a nice cat", 0.1)
    
     succ, err = cats:expire("Marry", 0.5)
    
     ngx.sleep(0.2)
    
     local val, err = cats:get("Marry")
     ngx.say(val) -- "a nice cat"

This feature was first introduced in the C<v0.10.11> release.

B<Note:> This method requires the C<resty.core.shdict> or C<resty.core> modules from the L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.flush_all

B<syntax:> I<ngx.shared.DICT:flush_all()>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Flushes out all the items in the dictionary. This method does not actually free up all the memory blocks in the dictionary but just marks all the existing items as expired.

This feature was first introduced in the C<v0.5.0rc17> release.

See also L<ngx.shared.DICT.flush_expired> and L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.flush_expired

B<syntax:> I<flushed = ngx.shared.DICT:flush_expired(max_count?)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Flushes out the expired items in the dictionary, up to the maximal number specified by the optional C<max_count> argument. When the C<max_count> argument is given C<0> or not given at all, then it means unlimited. Returns the number of items that have actually been flushed.

Unlike the L<flush_all> method, this method actually frees up the memory used by the expired items.

This feature was first introduced in the C<v0.6.3> release.

See also L<ngx.shared.DICT.flush_all> and L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.get_keys

B<syntax:> I<keys = ngx.shared.DICT:get_keys(max_count?)>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Fetch a list of the keys from the dictionary, up to C<< <max_count> >>.

By default, only the first 1024 keys (if any) are returned. When the C<< <max_count> >> argument is given the value C<0>, then all the keys will be returned even there is more than 1024 keys in the dictionary.

B<CAUTION> Avoid calling this method on dictionaries with a very large number of keys as it may lock the dictionary for significant amount of time and block Nginx worker processes trying to access the dictionary.

This feature was first introduced in the C<v0.7.3> release.




=head2 ngx.shared.DICT.capacity

B<syntax:> I<capacity_bytes = ngx.shared.DICT:capacity()>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

B<requires:> C<resty.core.shdict> or C<resty.core>

Retrieves the capacity in bytes for the shm-based dictionary L<ngx.shared.DICT> declared with
the L<lua_shared_dict> directive.

Example:


     require "resty.core.shdict"
    
     local cats = ngx.shared.cats
     local capacity_bytes = cats:capacity()

This feature was first introduced in the C<v0.10.11> release.

B<Note:> This method requires the C<resty.core.shdict> or C<resty.core> modules from the L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

This feature requires at least Nginx core version C<0.7.3>.

See also L<ngx.shared.DICT>.




=head2 ngx.shared.DICT.free_space

B<syntax:> I<free_page_bytes = ngx.shared.DICT:free_space()>

B<context:> I<init_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

B<requires:> C<resty.core.shdict> or C<resty.core>

Retrieves the free page size in bytes for the shm-based dictionary L<ngx.shared.DICT>.

B<Note:> The memory for ngx.shared.DICT is allocated via the Nginx slab allocator which has each slot for
data size ranges like \~8, 9\~16, 17\~32, ..., 1025\~2048, 2048\~ bytes. And pages are assigned to a slot if there
is no room in already assigned pages for the slot.

So even if the return value of the C<free_space> method is zero, there may be room in already assigned pages, so
you may successfully set a new key value pair to the shared dict without getting C<true> for C<forcible> or
non nil C<err> from the C<ngx.shared.DICT.set>.

On the other hand, if already assigned pages for a slot are full and a new key value pair is added to the
slot and there is no free page, you may get C<true> for C<forcible> or non nil C<err> from the
C<ngx.shared.DICT.set> method.

Example:


     require "resty.core.shdict"
    
     local cats = ngx.shared.cats
     local free_page_bytes = cats:free_space()

This feature was first introduced in the C<v0.10.11> release.

B<Note:> This method requires the C<resty.core.shdict> or C<resty.core> modules from the L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

This feature requires at least Nginx core version C<1.11.7>.

See also L<ngx.shared.DICT>.




=head2 ngx.socket.udp

B<syntax:> I<udpsock = ngx.socket.udp()>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Creates and returns a UDP or datagram-oriented unix domain socket object (also known as one type of the "cosocket" objects). The following methods are supported on this object:


=over


=item *

L<bind>

=item *

L<setpeername>

=item *

L<send>

=item *

L<receive>

=item *

L<close>

=item *

L<settimeout>


=back

It is intended to be compatible with the UDP API of the L<LuaSocket|http://w3.impa.br/~diego/software/luasocket/udp.html> library but is 100% nonblocking out of the box.

This feature was first introduced in the C<v0.5.7> release.

See also L<ngx.socket.tcp>.




=head2 udpsock:bind

B<syntax:> I<ok, err = udpsock:bind(address)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>,ssl_session_fetch_by_luaE<42>,ssl_client_hello_by_luaE<42>>

Just like the standard L<proxy_bind|http://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_bind> directive, this api makes the outgoing connection to a upstream server originate from the specified local IP address.

Only IP addresses can be specified as the C<address> argument.

Here is an example for connecting to a TCP server from the specified local IP address:


     location /test {
         content_by_lua_block {
             local sock = ngx.socket.udp()
             -- assume "************" is the local ip address
             local ok, err = sock:bind("************")
             if not ok then
                 ngx.say("failed to bind: ", err)
                 return
             end
             sock:close()
         }
     }




=head2 udpsock:setpeername

B<syntax:> I<ok, err = udpsock:setpeername(host, port)>

B<syntax:> I<ok, err = udpsock:setpeername("unix:/path/to/unix-domain.socket")>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Attempts to connect a UDP socket object to a remote server or to a datagram unix domain socket file. Because the datagram protocol is actually connection-less, this method does not really establish a "connection", but only just set the name of the remote peer for subsequent read/write operations.

Both IP addresses and domain names can be specified as the C<host> argument. In case of domain names, this method will use Nginx core's dynamic resolver to parse the domain name without blocking and it is required to configure the L<resolver|http://nginx.org/en/docs/http/ngx_http_core_module.html#resolver> directive in the C<nginx.conf> file like this:


     resolver *******;  # use Google's public DNS nameserver

If the nameserver returns multiple IP addresses for the host name, this method will pick up one randomly.

In case of error, the method returns C<nil> followed by a string describing the error. In case of success, the method returns C<1>.

Here is an example for connecting to a UDP (memcached) server:


     location /test {
         resolver *******;
    
         content_by_lua_block {
             local sock = ngx.socket.udp()
             local ok, err = sock:setpeername("my.memcached.server.domain", 11211)
             if not ok then
                 ngx.say("failed to connect to memcached: ", err)
                 return
             end
             ngx.say("successfully connected to memcached!")
             sock:close()
         }
     }

Since the C<v0.7.18> release, connecting to a datagram unix domain socket file is also possible on Linux:


     local sock = ngx.socket.udp()
     local ok, err = sock:setpeername("unix:/tmp/some-datagram-service.sock")
     if not ok then
         ngx.say("failed to connect to the datagram unix domain socket: ", err)
         return
     end
    
     -- do something after connect
     -- such as sock:send or sock:receive

assuming the datagram service is listening on the unix domain socket file C</tmp/some-datagram-service.sock> and the client socket will use the "autobind" feature on Linux.

Calling this method on an already connected socket object will cause the original connection to be closed first.

This method was first introduced in the C<v0.5.7> release.




=head2 udpsock:send

B<syntax:> I<ok, err = udpsock:send(data)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Sends data on the current UDP or datagram unix domain socket object.

In case of success, it returns C<1>. Otherwise, it returns C<nil> and a string describing the error.

The input argument C<data> can either be a Lua string or a (nested) Lua table holding string fragments. In case of table arguments, this method will copy all the string elements piece by piece to the underlying Nginx socket send buffers, which is usually optimal than doing string concatenation operations on the Lua land.

This feature was first introduced in the C<v0.5.7> release.




=head2 udpsock:receive

B<syntax:> I<data, err = udpsock:receive(size?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Receives data from the UDP or datagram unix domain socket object with an optional receive buffer size argument, C<size>.

This method is a synchronous operation and is 100% nonblocking.

In case of success, it returns the data received; in case of error, it returns C<nil> with a string describing the error.

If the C<size> argument is specified, then this method will use this size as the receive buffer size. But when this size is greater than C<8192>, then C<8192> will be used instead.

If no argument is specified, then the maximal buffer size, C<8192> is assumed.

Timeout for the reading operation is controlled by the L<lua_socket_read_timeout> config directive and the L<settimeout> method. And the latter takes priority. For example:


     sock:settimeout(1000)  -- one second timeout
     local data, err = sock:receive()
     if not data then
         ngx.say("failed to read a packet: ", err)
         return
     end
     ngx.say("successfully read a packet: ", data)

It is important here to call the L<settimeout> method I<before> calling this method.

This feature was first introduced in the C<v0.5.7> release.




=head2 udpsock:close

B<syntax:> I<ok, err = udpsock:close()>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Closes the current UDP or datagram unix domain socket. It returns the C<1> in case of success and returns C<nil> with a string describing the error otherwise.

Socket objects that have not invoked this method (and associated connections) will be closed when the socket object is released by the Lua GC (Garbage Collector) or the current client HTTP request finishes processing.

This feature was first introduced in the C<v0.5.7> release.




=head2 udpsock:settimeout

B<syntax:> I<udpsock:settimeout(time)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Set the timeout value in milliseconds for subsequent socket operations (like L<receive>).

Settings done by this method takes priority over those config directives, like L<lua_socket_read_timeout>.

This feature was first introduced in the C<v0.5.7> release.




=head2 ngx.socket.stream

Just an alias to L<ngx.socket.tcp>. If the stream-typed cosocket may also connect to a unix domain
socket, then this API name is preferred.

This API function was first added to the C<v0.10.1> release.




=head2 ngx.socket.tcp

B<syntax:> I<tcpsock = ngx.socket.tcp()>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Creates and returns a TCP or stream-oriented unix domain socket object (also known as one type of the "cosocket" objects). The following methods are supported on this object:


=over


=item *

L<bind>

=item *

L<connect>

=item *

L<setclientcert>

=item *

L<sslhandshake>

=item *

L<send>

=item *

L<receive>

=item *

L<close>

=item *

L<settimeout>

=item *

L<settimeouts>

=item *

L<setoption>

=item *

L<receiveany>

=item *

L<receiveuntil>

=item *

L<setkeepalive>

=item *

L<getreusedtimes>


=back

It is intended to be compatible with the TCP API of the L<LuaSocket|http://w3.impa.br/~diego/software/luasocket/tcp.html> library but is 100% nonblocking out of the box. Also, we introduce some new APIs to provide more functionalities.

The cosocket object created by this API function has exactly the same lifetime as the Lua handler creating it. So never pass the cosocket object to any other Lua handler (including ngx.timer callback functions) and never share the cosocket object between different Nginx requests.

For every cosocket object's underlying connection, if you do not
explicitly close it (via L<close>) or put it back to the connection
pool (via L<setkeepalive>), then it is automatically closed when one of
the following two events happens:


=over


=item *

the current request handler completes, or

=item *

the Lua cosocket object value gets collected by the Lua GC.


=back

Fatal errors in cosocket operations always automatically close the current
connection (note that, read timeout error is the only error that is
not fatal), and if you call L<close> on a closed connection, you will get
the "closed" error.

Starting from the C<0.9.9> release, the cosocket object here is full-duplex, that is, a reader "light thread" and a writer "light thread" can operate on a single cosocket object simultaneously (both "light threads" must belong to the same Lua handler though, see reasons above). But you cannot have two "light threads" both reading (or writing or connecting) the same cosocket, otherwise you might get an error like "socket busy reading" when calling the methods of the cosocket object.

This feature was first introduced in the C<v0.5.0rc1> release.

See also L<ngx.socket.udp>.




=head2 tcpsock:bind

B<syntax:> I<ok, err = tcpsock:bind(address)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>,ssl_session_fetch_by_luaE<42>,ssl_client_hello_by_luaE<42>>

Just like the standard L<proxy_bind|http://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_bind> directive, this api makes the outgoing connection to a upstream server originate from the specified local IP address.

Only IP addresses can be specified as the C<address> argument.

Here is an example for connecting to a TCP server from the specified local IP address:


     location /test {
         content_by_lua_block {
             local sock = ngx.socket.tcp()
             -- assume "************" is the local ip address
             local ok, err = sock:bind("************")
             if not ok then
                 ngx.say("failed to bind")
                 return
             end
             local ok, err = sock:connect("************", 80)
             if not ok then
                 ngx.say("failed to connect server: ", err)
                 return
             end
             ngx.say("successfully connected!")
             sock:close()
         }
     }




=head2 tcpsock:connect

B<syntax:> I<ok, err = tcpsock:connect(host, port, options_table?)>

B<syntax:> I<ok, err = tcpsock:connect("unix:/path/to/unix-domain.socket", options_table?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Attempts to connect a TCP socket object to a remote server or to a stream unix domain socket file without blocking.

Before actually resolving the host name and connecting to the remote backend, this method will always look up the connection pool for matched idle connections created by previous calls of this method (or the L<ngx.socket.connect> function).

Both IP addresses and domain names can be specified as the C<host> argument. In case of domain names, this method will use Nginx core's dynamic resolver to parse the domain name without blocking and it is required to configure the L<resolver|http://nginx.org/en/docs/http/ngx_http_core_module.html#resolver> directive in the C<nginx.conf> file like this:


     resolver *******;  # use Google's public DNS nameserver

If the nameserver returns multiple IP addresses for the host name, this method will pick up one randomly.

In case of error, the method returns C<nil> followed by a string describing the error. In case of success, the method returns C<1>.

Here is an example for connecting to a TCP server:


     location /test {
         resolver *******;
    
         content_by_lua_block {
             local sock = ngx.socket.tcp()
             local ok, err = sock:connect("www.google.com", 80)
             if not ok then
                 ngx.say("failed to connect to google: ", err)
                 return
             end
             ngx.say("successfully connected to google!")
             sock:close()
         }
     }

Connecting to a Unix Domain Socket file is also possible:


     local sock = ngx.socket.tcp()
     local ok, err = sock:connect("unix:/tmp/memcached.sock")
     if not ok then
         ngx.say("failed to connect to the memcached unix domain socket: ", err)
         return
     end
    
     -- do something after connect
     -- such as sock:send or sock:receive

assuming memcached (or something else) is listening on the unix domain socket file C</tmp/memcached.sock>.

Timeout for the connecting operation is controlled by the L<lua_socket_connect_timeout> config directive and the L<settimeout> method. And the latter takes priority. For example:


     local sock = ngx.socket.tcp()
     sock:settimeout(1000)  -- one second timeout
     local ok, err = sock:connect(host, port)

It is important here to call the L<settimeout> method I<before> calling this method.

Calling this method on an already connected socket object will cause the original connection to be closed first.

An optional Lua table can be specified as the last argument to this method to specify various connect options:


=over


=item *

C<pool>
specify a custom name for the connection pool being used. If omitted, then the connection pool name will be generated from the string template C<< "<host>:<port>" >> or C<< "<unix-socket-path>" >>.


=back


=over


=item *

C<pool_size>
specify the size of the connection pool. If omitted and no
C<backlog> option was provided, no pool will be created. If omitted
but C<backlog> was provided, the pool will be created with a default
size equal to the value of the L<lua_socket_pool_size>
directive.
The connection pool holds up to C<pool_size> alive connections
ready to be reused by subsequent calls to L<connect>, but
note that there is no upper limit to the total number of opened connections
outside of the pool. If you need to restrict the total number of opened
connections, specify the C<backlog> option.
When the connection pool would exceed its size limit, the least recently used
(kept-alive) connection already in the pool will be closed to make room for
the current connection.
Note that the cosocket connection pool is per Nginx worker process rather
than per Nginx server instance, so the size limit specified here also applies
to every single Nginx worker process. Also note that the size of the connection
pool cannot be changed once it has been created.
This option was first introduced in the C<v0.10.14> release.


=back


=over


=item *

C<backlog>
if specified, this module will limit the total number of opened connections
for this pool. No more connections than C<pool_size> can be opened
for this pool at any time. If C<pool_size> number of connections are in use,
subsequent connect operations will be queued into a queue equal to this
option's value (the "backlog" queue).
If the number of queued connect operations is equal to C<backlog>,
subsequent connect operations will fail and return C<nil> plus the
error string C<"too many waiting connect operations">.
The queued connect operations will be resumed once the number of active
connections becomes less than C<pool_size>.
The queued connect operation will abort once they have been queued for more
than C<connect_timeout>, controlled by
L<settimeouts>, and will return C<nil> plus
the error string C<"timeout">.
This option was first introduced in the C<v0.10.14> release.


=back

The support for the options table argument was first introduced in the C<v0.5.7> release.

This method was first introduced in the C<v0.5.0rc1> release.




=head2 tcpsock:setclientcert

B<syntax:> I<ok, err = tcpsock:setclientcert(cert, pkey)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Set client certificate chain and corresponding private key to the TCP socket object.
The certificate chain and private key provided will be used later by the L<tcpsock:sslhandshake> method.


=over


=item *

C<cert> specify a client certificate chain cdata object that will be used while handshaking with
remote server. These objects can be created using L<ngx.ssl.parse\_pem\_cert|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl.md#parse_pem_cert> or L<ngx.ssl.parse\_der\_cert|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl.md#parse_der_cert>
function provided by lua-resty-core. Note that specifying the C<cert> option requires
corresponding C<pkey> be provided too. See below.

=item *

C<pkey> specify a private key corresponds to the C<cert> option above.
These objects can be created using L<ngx.ssl.parse\_pem\_priv\_key|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl.md#parse_pem_priv_key> or L<ngx.ssl.parse\_der\_priv\_key|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl.md#parse_der_priv_key>
function provided by lua-resty-core.


=back

If both of C<cert> and C<pkey> are C<nil>, this method will clear any existing client certificate and private key
that was previously set on the cosocket object.

This method was first introduced in the C<v0.10.22> release.




=head2 tcpsock:sslhandshake

B<syntax:> I<session, err = tcpsock:sslhandshake(reused_session?, server_name?, ssl_verify?, send_status_req?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Does SSL/TLS handshake on the currently established connection.

The optional C<reused_session> argument can take a former SSL
session userdata returned by a previous C<sslhandshake>
call for exactly the same target. For short-lived connections, reusing SSL
sessions can usually speed up the handshake by one order by magnitude but it
is not so useful if the connection pool is enabled. This argument defaults to
C<nil>. If this argument takes the boolean C<false> value, no SSL session
userdata would return by this call and only a Lua boolean will be returned as
the first return value; otherwise the current SSL session will
always be returned as the first argument in case of successes.

The optional C<server_name> argument is used to specify the server
name for the new TLS extension Server Name Indication (SNI). Use of SNI can
make different servers share the same IP address on the server side. Also,
when SSL verification is enabled, this C<server_name> argument is
also used to validate the server name specified in the server certificate sent from
the remote.

The optional C<ssl_verify> argument takes a Lua boolean value to
control whether to perform SSL verification. When set to C<true>, the server
certificate will be verified according to the CA certificates specified by
the L<lua_ssl_trusted_certificate> directive.
You may also need to adjust the L<lua_ssl_verify_depth>
directive to control how deep we should follow along the certificate chain.
Also, when the C<ssl_verify> argument is true and the
C<server_name> argument is also specified, the latter will be used
to validate the server name in the server certificate.

The optional C<send_status_req> argument takes a boolean that controls whether to send
the OCSP status request in the SSL handshake request (which is for requesting OCSP stapling).

For connections that have already done SSL/TLS handshake, this method returns
immediately.

This method was first introduced in the C<v0.9.11> release.




=head2 tcpsock:send

B<syntax:> I<bytes, err = tcpsock:send(data)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Sends data without blocking on the current TCP or Unix Domain Socket connection.

This method is a synchronous operation that will not return until I<all> the data has been flushed into the system socket send buffer or an error occurs.

In case of success, it returns the total number of bytes that have been sent. Otherwise, it returns C<nil> and a string describing the error.

The input argument C<data> can either be a Lua string or a (nested) Lua table holding string fragments. In case of table arguments, this method will copy all the string elements piece by piece to the underlying Nginx socket send buffers, which is usually optimal than doing string concatenation operations on the Lua land.

Timeout for the sending operation is controlled by the L<lua_socket_send_timeout> config directive and the L<settimeout> method. And the latter takes priority. For example:


     sock:settimeout(1000)  -- one second timeout
     local bytes, err = sock:send(request)

It is important here to call the L<settimeout> method I<before> calling this method.

In case of any connection errors, this method always automatically closes the current connection.

This feature was first introduced in the C<v0.5.0rc1> release.




=head2 tcpsock:receive

B<syntax:> I<data, err, partial = tcpsock:receive(size)>

B<syntax:> I<data, err, partial = tcpsock:receive(pattern?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Receives data from the connected socket according to the reading pattern or size.

This method is a synchronous operation just like the L<send> method and is 100% nonblocking.

In case of success, it returns the data received; in case of error, it returns C<nil> with a string describing the error and the partial data received so far.

If a number-like argument is specified (including strings that look like numbers), then it is interpreted as a size. This method will not return until it reads exactly this size of data or an error occurs.

If a non-number-like string argument is specified, then it is interpreted as a "pattern". The following patterns are supported:


=over


=item *

C<'*a'>: reads from the socket until the connection is closed. No end-of-line translation is performed;

=item *

C<'*l'>: reads a line of text from the socket. The line is terminated by a C<Line Feed> (LF) character (ASCII 10), optionally preceded by a C<Carriage Return> (CR) character (ASCII 13). The CR and LF characters are not included in the returned line. In fact, all CR characters are ignored by the pattern.


=back

If no argument is specified, then it is assumed to be the pattern C<'*l'>, that is, the line reading pattern.

Timeout for the reading operation is controlled by the L<lua_socket_read_timeout> config directive and the L<settimeout> method. And the latter takes priority. For example:


     sock:settimeout(1000)  -- one second timeout
     local line, err, partial = sock:receive()
     if not line then
         ngx.say("failed to read a line: ", err)
         return
     end
     ngx.say("successfully read a line: ", line)

It is important here to call the L<settimeout> method I<before> calling this method.

Since the C<v0.8.8> release, this method no longer automatically closes the current connection when the read timeout error happens. For other connection errors, this method always automatically closes the connection.

This feature was first introduced in the C<v0.5.0rc1> release.




=head2 tcpsock:receiveany

B<syntax:> I<data, err = tcpsock:receiveany(max)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns any data received by the connected socket, at most C<max> bytes.

This method is a synchronous operation just like the L<send> method and is 100% nonblocking.

In case of success, it returns the data received; in case of error, it returns C<nil> with a string describing the error.

If the received data is more than this size, this method will return with exactly this size of data.
The remaining data in the underlying receive buffer could be returned in the next reading operation.

Timeout for the reading operation is controlled by the L<lua_socket_read_timeout> config directive and the L<settimeouts> method. And the latter takes priority. For example:


     sock:settimeouts(1000, 1000, 1000)  -- one second timeout for connect/read/write
     local data, err = sock:receiveany(10 * 1024) -- read any data, at most 10K
     if not data then
         ngx.say("failed to read any data: ", err)
         return
     end
     ngx.say("successfully read: ", data)

This method doesn't automatically close the current connection when the read timeout error occurs. For other connection errors, this method always automatically closes the connection.

This feature was first introduced in the C<v0.10.14> release.




=head2 tcpsock:receiveuntil

B<syntax:> I<iterator = tcpsock:receiveuntil(pattern, options?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

This method returns an iterator Lua function that can be called to read the data stream until it sees the specified pattern or an error occurs.

Here is an example for using this method to read a data stream with the boundary sequence C<--abcedhb>:


     local reader = sock:receiveuntil("\r\n--abcedhb")
     local data, err, partial = reader()
     if not data then
         ngx.say("failed to read the data stream: ", err)
     end
     ngx.say("read the data stream: ", data)

When called without any argument, the iterator function returns the received data right I<before> the specified pattern string in the incoming data stream. So for the example above, if the incoming data stream is C<'hello, world! -agentzh\r\n--abcedhb blah blah'>, then the string C<'hello, world! -agentzh'> will be returned.

In case of error, the iterator function will return C<nil> along with a string describing the error and the partial data bytes that have been read so far.

The iterator function can be called multiple times and can be mixed safely with other cosocket method calls or other iterator function calls.

The iterator function behaves differently (i.e., like a real iterator) when it is called with a C<size> argument. That is, it will read that C<size> of data on each invocation and will return C<nil> at the last invocation (either sees the boundary pattern or meets an error). For the last successful invocation of the iterator function, the C<err> return value will be C<nil> too. The iterator function will be reset after the last successful invocation that returns C<nil> data and C<nil> error. Consider the following example:


     local reader = sock:receiveuntil("\r\n--abcedhb")
    
     while true do
         local data, err, partial = reader(4)
         if not data then
             if err then
                 ngx.say("failed to read the data stream: ", err)
                 break
             end
    
             ngx.say("read done")
             break
         end
         ngx.say("read chunk: [", data, "]")
     end

Then for the incoming data stream C<'hello, world! -agentzh\r\n--abcedhb blah blah'>, we shall get the following output from the sample code above:

    read chunk: [hell]
    read chunk: [o, w]
    read chunk: [orld]
    read chunk: [! -a]
    read chunk: [gent]
    read chunk: [zh]
    read done

Note that, the actual data returned I<might> be a little longer than the size limit specified by the C<size> argument when the boundary pattern has ambiguity for streaming parsing. Near the boundary of the data stream, the data string actually returned could also be shorter than the size limit.

Timeout for the iterator function's reading operation is controlled by the L<lua_socket_read_timeout> config directive and the L<settimeout> method. And the latter takes priority. For example:


     local readline = sock:receiveuntil("\r\n")
    
     sock:settimeout(1000)  -- one second timeout
     line, err, partial = readline()
     if not line then
         ngx.say("failed to read a line: ", err)
         return
     end
     ngx.say("successfully read a line: ", line)

It is important here to call the L<settimeout> method I<before> calling the iterator function (note that the C<receiveuntil> call is irrelevant here).

As from the C<v0.5.1> release, this method also takes an optional C<options> table argument to control the behavior. The following options are supported:


=over


=item *

C<inclusive>


=back

The C<inclusive> takes a boolean value to control whether to include the pattern string in the returned data string. Default to C<false>. For example,


     local reader = tcpsock:receiveuntil("_END_", { inclusive = true })
     local data = reader()
     ngx.say(data)

Then for the input data stream C<"hello world _END_ blah blah blah">, then the example above will output C<hello world _END_>, including the pattern string C<_END_> itself.

Since the C<v0.8.8> release, this method no longer automatically closes the current connection when the read timeout error happens. For other connection errors, this method always automatically closes the connection.

This method was first introduced in the C<v0.5.0rc1> release.




=head2 tcpsock:close

B<syntax:> I<ok, err = tcpsock:close()>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Closes the current TCP or stream unix domain socket. It returns the C<1> in case of success and returns C<nil> with a string describing the error otherwise.

Note that there is no need to call this method on socket objects that have invoked the L<setkeepalive> method because the socket object is already closed (and the current connection is saved into the built-in connection pool).

Socket objects that have not invoked this method (and associated connections) will be closed when the socket object is released by the Lua GC (Garbage Collector) or the current client HTTP request finishes processing.

This feature was first introduced in the C<v0.5.0rc1> release.




=head2 tcpsock:settimeout

B<syntax:> I<tcpsock:settimeout(time)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Set the timeout value in milliseconds for subsequent socket operations (L<connect>, L<receive>, and iterators returned from L<receiveuntil>).

Settings done by this method take priority over those specified via config directives (i.e. L<lua_socket_connect_timeout>, L<lua_socket_send_timeout>, and L<lua_socket_read_timeout>).

Note that this method does I<not> affect the L<lua_socket_keepalive_timeout> setting; the C<timeout> argument to the L<setkeepalive> method should be used for this purpose instead.

This feature was first introduced in the C<v0.5.0rc1> release.




=head2 tcpsock:settimeouts

B<syntax:> I<tcpsock:settimeouts(connect_timeout, send_timeout, read_timeout)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Respectively sets the connect, send, and read timeout thresholds (in milliseconds) for subsequent socket
operations (L<connect>, L<send>, L<receive>, and iterators returned from L<receiveuntil>).

Settings done by this method take priority over those specified via config directives (i.e. L<lua_socket_connect_timeout>, L<lua_socket_send_timeout>, and L<lua_socket_read_timeout>).

It is recommended to use L<settimeouts> instead of L<settimeout>.

Note that this method does I<not> affect the L<lua_socket_keepalive_timeout> setting; the C<timeout> argument to the L<setkeepalive> method should be used for this purpose instead.

This feature was first introduced in the C<v0.10.7> release.




=head2 tcpsock:setoption

B<syntax:> I<ok, err = tcpsock:setoption(option, value?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

This function is added for L<LuaSocket|http://w3.impa.br/~diego/software/luasocket/tcp.html> API compatibility, its functionality is implemented C<v0.10.18>.

This feature was first introduced in the C<v0.5.0rc1> release.

In case of success, it returns C<true>. Otherwise, it returns nil and a string describing the error.

The C<option> is a string with the option name, and the value depends on the option being set:


=over


=item *

C<keepalive>


=back

Setting this option to true enables sending of keep-alive messages on
connection-oriented sockets. Make sure the C<connect> function
had been called before, for example,

    ```lua

    local ok, err = tcpsock:setoption("keepalive", true)
    if not ok then
        ngx.say("setoption keepalive failed: ", err)
    end
    ```

=over


=item *

C<reuseaddr>


=back

Enabling this option indicates that the rules used in validating addresses
supplied in a call to bind should allow reuse of local addresses. Make sure
the C<connect> function had been called before, for example,

    ```lua

    local ok, err = tcpsock:setoption("reuseaddr", 0)
    if not ok then
        ngx.say("setoption reuseaddr failed: ", err)
    end
    ```

=over


=item *

C<tcp-nodelay>


=back

Setting this option to true disables the Nagle's algorithm for the connection.
Make sure the C<connect> function had been called before, for example,

    ```lua

    local ok, err = tcpsock:setoption("tcp-nodelay", true)
    if not ok then
        ngx.say("setoption tcp-nodelay failed: ", err)
    end
    ```

=over


=item *

C<sndbuf>


=back

Sets the maximum socket send buffer in bytes. The kernel doubles this value
(to allow space for bookkeeping overhead) when it is set using setsockopt().
Make sure the C<connect> function had been called before, for example,

    ```lua

    local ok, err = tcpsock:setoption("sndbuf", 1024 * 10)
    if not ok then
        ngx.say("setoption sndbuf failed: ", err)
    end
    ```

=over


=item *

C<rcvbuf>


=back

Sets the maximum socket receive buffer in bytes. The kernel doubles this value
(to allow space for bookkeeping overhead) when it is set using setsockopt. Make
sure the C<connect> function had been called before, for example,

    ```lua

    local ok, err = tcpsock:setoption("rcvbuf", 1024 * 10)
    if not ok then
        ngx.say("setoption rcvbuf failed: ", err)
    end
    ```

NOTE: Once the option is set, it will become effective until the connection is closed. If you know the connection is from the connection pool and all the in-pool connections already have called the setoption() method with the desired socket option state, then you can just skip calling setoption() again to avoid the overhead of repeated calls, for example,


     local count, err = tcpsock:getreusedtimes()
     if not count then
         ngx.say("getreusedtimes failed: ", err)
         return
     end
    
     if count == 0 then
         local ok, err = tcpsock:setoption("rcvbuf", 1024 * 10)
         if not ok then
             ngx.say("setoption rcvbuf failed: ", err)
             return
         end
     end

These options described above are supported in C<v0.10.18>, and more options will be implemented in future.




=head2 tcpsock:setkeepalive

B<syntax:> I<ok, err = tcpsock:setkeepalive(timeout?, size?)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Puts the current socket's connection immediately into the cosocket built-in connection pool and keep it alive until other L<connect> method calls request it or the associated maximal idle timeout is expired.

The first optional argument, C<timeout>, can be used to specify the maximal idle timeout (in milliseconds) for the current connection. If omitted, the default setting in the L<lua_socket_keepalive_timeout> config directive will be used. If the C<0> value is given, then the timeout interval is unlimited.

The second optional argument C<size> is considered deprecated since
the C<v0.10.14> release of this module, in favor of the
C<pool_size> option of the L<connect> method.
Since the C<v0.10.14> release, this option will only take effect if
the call to L<connect> did not already create a connection
pool.
When this option takes effect (no connection pool was previously created by
L<connect>), it will specify the size of the connection pool,
and create it.
If omitted (and no pool was previously created), the default size is the value
of the L<lua_socket_pool_size> directive.
The connection pool holds up to C<size> alive connections ready to be
reused by subsequent calls to L<connect>, but note that there
is no upper limit to the total number of opened connections outside of the
pool.
When the connection pool would exceed its size limit, the least recently used
(kept-alive) connection already in the pool will be closed to make room for
the current connection.
Note that the cosocket connection pool is per Nginx worker process rather
than per Nginx server instance, so the size limit specified here also applies
to every single Nginx worker process. Also note that the size of the connection
pool cannot be changed once it has been created.
If you need to restrict the total number of opened connections, specify both
the C<pool_size> and C<backlog> option in the call to
L<connect>.

In case of success, this method returns C<1>; otherwise, it returns C<nil> and a string describing the error.

When the system receive buffer for the current connection has unread data, then this method will return the "connection in dubious state" error message (as the second return value) because the previous session has unread data left behind for the next session and the connection is not safe to be reused.

This method also makes the current cosocket object enter the "closed" state, so there is no need to manually call the L<close> method on it afterwards.

This feature was first introduced in the C<v0.5.0rc1> release.




=head2 tcpsock:getreusedtimes

B<syntax:> I<count, err = tcpsock:getreusedtimes()>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

This method returns the (successfully) reused times for the current connection. In case of error, it returns C<nil> and a string describing the error.

If the current connection does not come from the built-in connection pool, then this method always returns C<0>, that is, the connection has never been reused (yet). If the connection comes from the connection pool, then the return value is always non-zero. So this method can also be used to determine if the current connection comes from the pool.

This feature was first introduced in the C<v0.5.0rc1> release.




=head2 ngx.socket.connect

B<syntax:> I<tcpsock, err = ngx.socket.connect(host, port)>

B<syntax:> I<tcpsock, err = ngx.socket.connect("unix:/path/to/unix-domain.socket")>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>>

This function is a shortcut for combining L<ngx.socket.tcp()> and the L<connect()> method call in a single operation. It is actually implemented like this:


     local sock = ngx.socket.tcp()
     local ok, err = sock:connect(...)
     if not ok then
         return nil, err
     end
     return sock

There is no way to use the L<settimeout> method to specify connecting timeout for this method and the L<lua_socket_connect_timeout> directive must be set at configure time instead.

This feature was first introduced in the C<v0.5.0rc1> release.




=head2 ngx.get_phase

B<syntax:> I<str = ngx.get_phase()>

B<context:> I<init_by_luaE<42>, init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Retrieves the current running phase name. Possible return values are


=over


=item *

C<init>
for the context of L<init_by_lua*>.

=item *

C<init_worker>
for the context of L<init_worker_by_lua*>.

=item *

C<ssl_cert>
for the context of L<ssl_certificate_by_lua*>.

=item *

C<ssl_session_fetch>
for the context of L<ssl_session_fetch_by_lua*>.

=item *

C<ssl_session_store>
for the context of L<ssl_session_store_by_lua*>.

=item *

C<ssl_client_hello>
for the context of L<ssl_client_hello_by_lua*>.

=item *

C<set>
for the context of L<set_by_lua*>.

=item *

C<rewrite>
for the context of L<rewrite_by_lua*>.

=item *

C<balancer>
for the context of L<balancer_by_lua*>.

=item *

C<access>
for the context of L<access_by_lua*>.

=item *

C<content>
for the context of L<content_by_lua*>.

=item *

C<header_filter>
for the context of L<header_filter_by_lua*>.

=item *

C<body_filter>
for the context of L<body_filter_by_lua*>.

=item *

C<log>
for the context of L<log_by_lua*>.

=item *

C<timer>
for the context of user callback functions for L<ngx.timer.*>.

=item *

C<exit_worker>
for the context of L<exit_worker_by_lua*>.


=back

This API was first introduced in the C<v0.5.10> release.




=head2 ngx.thread.spawn

B<syntax:> I<co = ngx.thread.spawn(func, arg1, arg2, ...)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Spawns a new user "light thread" with the Lua function C<func> as well as those optional arguments C<arg1>, C<arg2>, and etc. Returns a Lua thread (or Lua coroutine) object represents this "light thread".

"Light threads" are just a special kind of Lua coroutines that are scheduled by the ngx_lua module.

Before C<ngx.thread.spawn> returns, the C<func> will be called with those optional arguments until it returns, aborts with an error, or gets yielded due to I/O operations via the L<Nginx API for Lua> (like L<tcpsock:receive>).

After C<ngx.thread.spawn> returns, the newly-created "light thread" will keep running asynchronously usually at various I/O events.

All the Lua code chunks running by L<rewrite_by_lua>, L<access_by_lua>, and L<content_by_lua> are in a boilerplate "light thread" created automatically by ngx_lua. Such boilerplate "light thread" are also called "entry threads".

By default, the corresponding Nginx handler (e.g., L<rewrite_by_lua> handler) will not terminate until


=over


=item 1.

both the "entry thread" and all the user "light threads" terminates,

=item 2.

a "light thread" (either the "entry thread" or a user "light thread") aborts by calling L<ngx.exit>, L<ngx.exec>, L<ngx.redirect>, or L<ngx.req.set_uri(uri, true)>, or

=item 3.

the "entry thread" terminates with a Lua error.


=back

When the user "light thread" terminates with a Lua error, however, it will not abort other running "light threads" like the "entry thread" does.

Due to the limitation in the Nginx subrequest model, it is not allowed to abort a running Nginx subrequest in general. So it is also prohibited to abort a running "light thread" that is pending on one ore more Nginx subrequests. You must call L<ngx.thread.wait> to wait for those "light thread" to terminate before quitting the "world". A notable exception here is that you can abort pending subrequests by calling L<ngx.exit> with and only with the status code C<ngx.ERROR> (-1), C<408>, C<444>, or C<499>.

The "light threads" are not scheduled in a pre-emptive way. In other words, no time-slicing is performed automatically. A "light thread" will keep running exclusively on the CPU until


=over


=item 1.

a (nonblocking) I/O operation cannot be completed in a single run,

=item 2.

it calls L<coroutine.yield> to actively give up execution, or

=item 3.

it is aborted by a Lua error or an invocation of L<ngx.exit>, L<ngx.exec>, L<ngx.redirect>, or L<ngx.req.set_uri(uri, true)>.


=back

For the first two cases, the "light thread" will usually be resumed later by the ngx_lua scheduler unless a "stop-the-world" event happens.

User "light threads" can create "light threads" themselves. And normal user coroutines created by L<coroutine.create> can also create "light threads". The coroutine (be it a normal Lua coroutine or a "light thread") that directly spawns the "light thread" is called the "parent coroutine" for the "light thread" newly spawned.

The "parent coroutine" can call L<ngx.thread.wait> to wait on the termination of its child "light thread".

You can call coroutine.status() and coroutine.yield() on the "light thread" coroutines.

The status of the "light thread" coroutine can be "zombie" if


=over


=item 1.

the current "light thread" already terminates (either successfully or with an error),

=item 2.

its parent coroutine is still alive, and

=item 3.

its parent coroutine is not waiting on it with L<ngx.thread.wait>.


=back

The following example demonstrates the use of coroutine.yield() in the "light thread" coroutines
to do manual time-slicing:


     local yield = coroutine.yield
    
     function f()
         local self = coroutine.running()
         ngx.say("f 1")
         yield(self)
         ngx.say("f 2")
         yield(self)
         ngx.say("f 3")
     end
    
     local self = coroutine.running()
     ngx.say("0")
     yield(self)
    
     ngx.say("1")
     ngx.thread.spawn(f)
    
     ngx.say("2")
     yield(self)
    
     ngx.say("3")
     yield(self)
    
     ngx.say("4")

Then it will generate the output

    0
    1
    f 1
    2
    f 2
    3
    f 3
    4

"Light threads" are mostly useful for making concurrent upstream requests in a single Nginx request handler, much like a generalized version of L<ngx.location.capture_multi> that can work with all the L<Nginx API for Lua>. The following example demonstrates parallel requests to MySQL, Memcached, and upstream HTTP services in a single Lua handler, and outputting the results in the order that they actually return (similar to Facebook's BigPipe model):


     -- query mysql, memcached, and a remote http service at the same time,
     -- output the results in the order that they
     -- actually return the results.
    
     local mysql = require "resty.mysql"
     local memcached = require "resty.memcached"
    
     local function query_mysql()
         local db = mysql:new()
         db:connect{
                     host = "127.0.0.1",
                     port = 3306,
                     database = "test",
                     user = "monty",
                     password = "mypass"
                   }
         local res, err, errno, sqlstate =
                 db:query("select * from cats order by id asc")
         db:set_keepalive(0, 100)
         ngx.say("mysql done: ", cjson.encode(res))
     end
    
     local function query_memcached()
         local memc = memcached:new()
         memc:connect("127.0.0.1", 11211)
         local res, err = memc:get("some_key")
         ngx.say("memcached done: ", res)
     end
    
     local function query_http()
         local res = ngx.location.capture("/my-http-proxy")
         ngx.say("http done: ", res.body)
     end
    
     ngx.thread.spawn(query_mysql)      -- create thread 1
     ngx.thread.spawn(query_memcached)  -- create thread 2
     ngx.thread.spawn(query_http)       -- create thread 3

This API was first enabled in the C<v0.7.0> release.




=head2 ngx.thread.wait

B<syntax:> I<ok, res1, res2, ... = ngx.thread.wait(thread1, thread2, ...)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Waits on one or more child "light threads" and returns the results of the first "light thread" that terminates (either successfully or with an error).

The arguments C<thread1>, C<thread2>, and etc are the Lua thread objects returned by earlier calls of L<ngx.thread.spawn>.

The return values have exactly the same meaning as L<coroutine.resume>, that is, the first value returned is a boolean value indicating whether the "light thread" terminates successfully or not, and subsequent values returned are the return values of the user Lua function that was used to spawn the "light thread" (in case of success) or the error object (in case of failure).

Only the direct "parent coroutine" can wait on its child "light thread", otherwise a Lua exception will be raised.

The following example demonstrates the use of C<ngx.thread.wait> and L<ngx.location.capture> to emulate L<ngx.location.capture_multi>:


     local capture = ngx.location.capture
     local spawn = ngx.thread.spawn
     local wait = ngx.thread.wait
     local say = ngx.say
    
     local function fetch(uri)
         return capture(uri)
     end
    
     local threads = {
         spawn(fetch, "/foo"),
         spawn(fetch, "/bar"),
         spawn(fetch, "/baz")
     }
    
     for i = 1, #threads do
         local ok, res = wait(threads[i])
         if not ok then
             say(i, ": failed to run: ", res)
         else
             say(i, ": status: ", res.status)
             say(i, ": body: ", res.body)
         end
     end

Here it essentially implements the "wait all" model.

And below is an example demonstrating the "wait any" model:


     function f()
         ngx.sleep(0.2)
         ngx.say("f: hello")
         return "f done"
     end
    
     function g()
         ngx.sleep(0.1)
         ngx.say("g: hello")
         return "g done"
     end
    
     local tf, err = ngx.thread.spawn(f)
     if not tf then
         ngx.say("failed to spawn thread f: ", err)
         return
     end
    
     ngx.say("f thread created: ", coroutine.status(tf))
    
     local tg, err = ngx.thread.spawn(g)
     if not tg then
         ngx.say("failed to spawn thread g: ", err)
         return
     end
    
     ngx.say("g thread created: ", coroutine.status(tg))
    
     ok, res = ngx.thread.wait(tf, tg)
     if not ok then
         ngx.say("failed to wait: ", res)
         return
     end
    
     ngx.say("res: ", res)
    
     -- stop the "world", aborting other running threads
     ngx.exit(ngx.OK)

And it will generate the following output:

    f thread created: running
    g thread created: running
    g: hello
    res: g done

This API was first enabled in the C<v0.7.0> release.




=head2 ngx.thread.kill

B<syntax:> I<ok, err = ngx.thread.kill(thread)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, ngx.timer.E<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Kills a running "light thread" created by L<ngx.thread.spawn>. Returns a true value when successful or C<nil> and a string describing the error otherwise.

According to the current implementation, only the parent coroutine (or "light thread") can kill a thread. Also, a running "light thread" with pending Nginx subrequests (initiated by L<ngx.location.capture> for example) cannot be killed due to a limitation in the Nginx core.

This API was first enabled in the C<v0.9.9> release.




=head2 ngx.on_abort

B<syntax:> I<ok, err = ngx.on_abort(callback)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

Registers a user Lua function as the callback which gets called automatically when the client closes the (downstream) connection prematurely.

Returns C<1> if the callback is registered successfully or returns C<nil> and a string describing the error otherwise.

All the L<Nginx API for Lua> can be used in the callback function because the function is run in a special "light thread", just as those "light threads" created by L<ngx.thread.spawn>.

The callback function can decide what to do with the client abortion event all by itself. For example, it can simply ignore the event by doing nothing and the current Lua request handler will continue executing without interruptions. And the callback function can also decide to terminate everything by calling L<ngx.exit>, for example,


     local function my_cleanup()
         -- custom cleanup work goes here, like cancelling a pending DB transaction
    
         -- now abort all the "light threads" running in the current request handler
         ngx.exit(499)
     end
    
     local ok, err = ngx.on_abort(my_cleanup)
     if not ok then
         ngx.log(ngx.ERR, "failed to register the on_abort callback: ", err)
         ngx.exit(500)
     end

When L<lua_check_client_abort> is set to C<off> (which is the default), then this function call will always return the error message "lua_check_client_abort is off".

According to the current implementation, this function can only be called once in a single request handler; subsequent calls will return the error message "duplicate call".

This API was first introduced in the C<v0.7.4> release.

See also L<lua_check_client_abort>.




=head2 ngx.timer.at

B<syntax:> I<hdl, err = ngx.timer.at(delay, callback, user_arg1, user_arg2, ...)>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Creates an Nginx timer with a user callback function as well as optional user arguments.

The first argument, C<delay>, specifies the delay for the timer,
in seconds. One can specify fractional seconds like C<0.001> to mean 1
millisecond here. C<0> delay can also be specified, in which case the
timer will immediately expire when the current handler yields
execution.

The second argument, C<callback>, can
be any Lua function, which will be invoked later in a background
"light thread" after the delay specified. The user callback will be
called automatically by the Nginx core with the arguments C<premature>,
C<user_arg1>, C<user_arg2>, and etc, where the C<premature>
argument takes a boolean value indicating whether it is a premature timer
expiration or not(for the C<0> delay timer it is always C<false>), and C<user_arg1>, C<user_arg2>, and etc, are
those (extra) user arguments specified when calling C<ngx.timer.at>
as the remaining arguments.

Premature timer expiration happens when the Nginx worker process is
trying to shut down, as in an Nginx configuration reload triggered by
the C<HUP> signal or in an Nginx server shutdown. When the Nginx worker
is trying to shut down, one can no longer call C<ngx.timer.at> to
create new timers with nonzero delays and in that case C<ngx.timer.at> will return a "conditional false" value and
a string describing the error, that is, "process exiting".

Starting from the C<v0.9.3> release, it is allowed to create zero-delay timers even when the Nginx worker process starts shutting down.

When a timer expires, the user Lua code in the timer callback is
running in a "light thread" detached completely from the original
request creating the timer. So objects with the same lifetime as the
request creating them, like L<cosockets>, cannot be shared between the
original request and the timer user callback function.

Here is a simple example:


     location / {
         ...
         log_by_lua_block {
             local function push_data(premature, uri, args, status)
                 -- push the data uri, args, and status to the remote
                 -- via ngx.socket.tcp or ngx.socket.udp
                 -- (one may want to buffer the data in Lua a bit to
                 -- save I/O operations)
             end
             local ok, err = ngx.timer.at(0, push_data,
                                          ngx.var.uri, ngx.var.args, ngx.header.status)
             if not ok then
                 ngx.log(ngx.ERR, "failed to create timer: ", err)
                 return
             end
    
             -- other job in log_by_lua_block
         }
     }

One can also create infinite re-occurring timers, for instance, a timer getting triggered every C<5> seconds, by calling C<ngx.timer.at> recursively in the timer callback function. Here is such an example,


     local delay = 5
     local handler
     handler = function (premature)
         -- do some routine job in Lua just like a cron job
         if premature then
             return
         end
         local ok, err = ngx.timer.at(delay, handler)
         if not ok then
             ngx.log(ngx.ERR, "failed to create the timer: ", err)
             return
         end
    
         -- do something in timer
     end
    
     local ok, err = ngx.timer.at(delay, handler)
     if not ok then
         ngx.log(ngx.ERR, "failed to create the timer: ", err)
         return
     end
    
     -- do other jobs

It is recommended, however, to use the L<ngx.timer.every> API function
instead for creating recurring timers since it is more robust.

Because timer callbacks run in the background and their running time
will not add to any client request's response time, they can easily
accumulate in the server and exhaust system resources due to either
Lua programming mistakes or just too much client traffic. To prevent
extreme consequences like crashing the Nginx server, there are
built-in limitations on both the number of "pending timers" and the
number of "running timers" in an Nginx worker process. The "pending
timers" here mean timers that have not yet been expired and "running
timers" are those whose user callbacks are currently running.

The maximal number of pending timers allowed in an Nginx
worker is controlled by the L<lua_max_pending_timers>
directive. The maximal number of running timers is controlled by the
L<lua_max_running_timers> directive.

According to the current implementation, each "running timer" will
take one (fake) connection record from the global connection record
list configured by the standard L<worker_connections|http://nginx.org/en/docs/ngx_core_module.html#worker_connections> directive in
C<nginx.conf>. So ensure that the
L<worker_connections|http://nginx.org/en/docs/ngx_core_module.html#worker_connections> directive is set to
a large enough value that takes into account both the real connections
and fake connections required by timer callbacks (as limited by the
L<lua_max_running_timers> directive).

A lot of the Lua APIs for Nginx are enabled in the context of the timer
callbacks, like stream/datagram cosockets (L<ngx.socket.tcp> and L<ngx.socket.udp>), shared
memory dictionaries (L<ngx.shared.DICT>), user coroutines (L<coroutine.*>),
user "light threads" (L<ngx.thread.*>), L<ngx.exit>, L<ngx.now>/L<ngx.time>,
L<ngx.md5>/L<ngx.sha1_bin>, are all allowed. But the subrequest API (like
L<ngx.location.capture>), the L<ngx.req.*> API, the downstream output API
(like L<ngx.say>, L<ngx.print>, and L<ngx.flush>) are explicitly disabled in
this context.

You must notice that each timer will be based on a fake request (this fake request is also based on a fake connection). Because Nginx's memory release is based on the connection closure, if you run a lot of APIs that apply for memory resources in a timer, such as L<tcpsock:connect>, will cause the accumulation of memory resources. So it is recommended to create a new timer after running several times to release memory resources.

You can pass most of the standard Lua values (nils, booleans, numbers, strings, tables, closures, file handles, etc.) into the timer callback, either explicitly as user arguments or implicitly as upvalues for the callback closure. There are several exceptions, however: you I<cannot> pass any thread objects returned by L<coroutine.create> and L<ngx.thread.spawn> or any cosocket objects returned by L<ngx.socket.tcp>, L<ngx.socket.udp>, and L<ngx.req.socket> because these objects' lifetime is bound to the request context creating them while the timer callback is detached from the creating request's context (by design) and runs in its own (fake) request context. If you try to share the thread or cosocket objects across the boundary of the creating request, then you will get the "no co ctx found" error (for threads) or "bad request" (for cosockets). It is fine, however, to create all these objects inside your timer callback.

Please note that the timer Lua handler has its own copy of the C<ngx.ctx> magic
table. It won't share the same C<ngx.ctx> with the Lua handler creating the timer.
If you need to pass data from the timer creator to the timer handler, please
use the extra parameters of C<ngx.timer.at()>.

This API was first introduced in the C<v0.8.0> release.




=head2 ngx.timer.every

B<syntax:> I<hdl, err = ngx.timer.every(delay, callback, user_arg1, user_arg2, ...)>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Similar to the L<ngx.timer.at> API function, but


=over


=item 1.

C<delay> I<cannot> be zero,

=item 2.

timer will be created every C<delay> seconds until the current Nginx worker process starts exiting.


=back

Like L<ngx.timer.at>, the C<callback> argument will be called
automatically with the arguments C<premature>, C<user_arg1>, C<user_arg2>, etc.

When success, returns a "conditional true" value (but not a C<true>). Otherwise, returns a "conditional false" value and a string describing the error.

This API also respect the L<lua_max_pending_timers> and L<lua_max_running_timers>.

This API was first introduced in the C<v0.10.9> release.




=head2 ngx.timer.running_count

B<syntax:> I<count = ngx.timer.running_count()>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns the number of timers currently running.

This directive was first introduced in the C<v0.9.20> release.




=head2 ngx.timer.pending_count

B<syntax:> I<count = ngx.timer.pending_count()>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Returns the number of pending timers.

This directive was first introduced in the C<v0.9.20> release.




=head2 ngx.config.subsystem

B<syntax:> I<subsystem = ngx.config.subsystem>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, init_by_luaE<42>, init_worker_by_luaE<42>, exit_worker_by_luaE<42>>

This string field indicates the Nginx subsystem the current Lua environment is based on. For this module, this field always takes the string value C<"http">. For
L<ngx_stream_lua_module|https://github.com/openresty/stream-lua-nginx-module#readme>, however, this field takes the value C<"stream">.

This field was first introduced in the C<0.10.1>.




=head2 ngx.config.debug

B<syntax:> I<debug = ngx.config.debug>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, init_by_luaE<42>, init_worker_by_luaE<42>, exit_worker_by_luaE<42>>

This boolean field indicates whether the current Nginx is a debug build, i.e., being built by the C<./configure> option C<--with-debug>.

This field was first introduced in the C<0.8.7>.




=head2 ngx.config.prefix

B<syntax:> I<prefix = ngx.config.prefix()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, init_by_luaE<42>, init_worker_by_luaE<42>, exit_worker_by_luaE<42>>

Returns the Nginx server "prefix" path, as determined by the C<-p> command-line option when running the Nginx executable, or the path specified by the C<--prefix> command-line option when building Nginx with the C<./configure> script.

This function was first introduced in the C<0.9.2>.




=head2 ngx.config.nginx_version

B<syntax:> I<ver = ngx.config.nginx_version>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, init_by_luaE<42>, init_worker_by_luaE<42>, exit_worker_by_luaE<42>>

This field take an integral value indicating the version number of the current Nginx core being used. For example, the version number C<1.4.3> results in the Lua number 1004003.

This API was first introduced in the C<0.9.3> release.




=head2 ngx.config.nginx_configure

B<syntax:> I<str = ngx.config.nginx_configure()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, init_by_luaE<42>>

This function returns a string for the Nginx C<./configure> command's arguments string.

This API was first introduced in the C<0.9.5> release.




=head2 ngx.config.ngx_lua_version

B<syntax:> I<ver = ngx.config.ngx_lua_version>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, init_by_luaE<42>>

This field take an integral value indicating the version number of the current C<ngx_lua> module being used. For example, the version number C<0.9.3> results in the Lua number 9003.

This API was first introduced in the C<0.9.3> release.




=head2 ngx.worker.exiting

B<syntax:> I<exiting = ngx.worker.exiting()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, init_by_luaE<42>, init_worker_by_luaE<42>, exit_worker_by_luaE<42>>

This function returns a boolean value indicating whether the current Nginx worker process already starts exiting. Nginx worker process exiting happens on Nginx server quit or configuration reload (aka HUP reload).

This API was first introduced in the C<0.9.3> release.




=head2 ngx.worker.pid

B<syntax:> I<pid = ngx.worker.pid()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, init_by_luaE<42>, init_worker_by_luaE<42>, exit_worker_by_luaE<42>>

This function returns a Lua number for the process ID (PID) of the current Nginx worker process. This API is more efficient than C<ngx.var.pid> and can be used in contexts where the L<ngx.var.VARIABLE> API cannot be used (like L<init_worker_by_lua>).

This API was first introduced in the C<0.9.5> release.




=head2 ngx.worker.pids

B<syntax:> I<pids = ngx.worker.pids()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, exit_worker_by_luaE<42>>

This function returns a Lua table for all Nginx worker process IDs (PIDs). Nginx uses channel to send the current worker PID to another worker in the worker process start or restart. So this API can get all current worker PIDs. Windows does not have this API.

This API was first introduced in the C<0.10.23> release.




=head2 ngx.worker.count

B<syntax:> I<count = ngx.worker.count()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, init_by_luaE<42>, init_worker_by_luaE<42>, exit_worker_by_luaE<42>>

Returns the total number of the Nginx worker processes (i.e., the value configured
by the L<worker_processes|https://nginx.org/en/docs/ngx_core_module.html#worker_processes>
directive in C<nginx.conf>).

This API was first introduced in the C<0.9.20> release.




=head2 ngx.worker.id

B<syntax:> I<id = ngx.worker.id()>

B<context:> I<set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, init_worker_by_luaE<42>, exit_worker_by_luaE<42>>

Returns the ordinal number of the current Nginx worker processes (starting from number 0).

So if the total number of workers is C<N>, then this method may return a number between 0
and C<N - 1> (inclusive).

This function returns meaningful values only for Nginx 1.9.1+. With earlier versions of Nginx, it
always returns C<nil>.

See also L<ngx.worker.count>.

This API was first introduced in the C<0.9.20> release.




=head2 ngx.semaphore

B<syntax:> I<local semaphore = require "ngx.semaphore">

This is a Lua module that implements a classic-style semaphore API for efficient synchronizations among
different "light threads". Sharing the same semaphore among different "light threads" created in different (request)
contexts are also supported as long as the "light threads" reside in the same Nginx worker process
and the L<lua_code_cache> directive is turned on (which is the default).

This Lua module does not ship with this ngx_lua module itself rather it is shipped with
the
L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

Please refer to the L<documentation|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/semaphore.md>
for this C<ngx.semaphore> Lua module in L<lua-resty-core|https://github.com/openresty/lua-resty-core>
for more details.

This feature requires at least ngx_lua C<v0.10.0>.




=head2 ngx.balancer

B<syntax:> I<local balancer = require "ngx.balancer">

This is a Lua module that provides a Lua API to allow defining completely dynamic load balancers
in pure Lua.

This Lua module does not ship with this ngx_lua module itself rather it is shipped with
the
L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

Please refer to the L<documentation|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/balancer.md>
for this C<ngx.balancer> Lua module in L<lua-resty-core|https://github.com/openresty/lua-resty-core>
for more details.

This feature requires at least ngx_lua C<v0.10.0>.




=head2 ngx.ssl

B<syntax:> I<local ssl = require "ngx.ssl">

This Lua module provides API functions to control the SSL handshake process in contexts like
L<ssl_certificate_by_lua*>.

This Lua module does not ship with this ngx_lua module itself rather it is shipped with
the
L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

Please refer to the L<documentation|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl.md>
for this C<ngx.ssl> Lua module for more details.

This feature requires at least ngx_lua C<v0.10.0>.




=head2 ngx.ocsp

B<syntax:> I<local ocsp = require "ngx.ocsp">

This Lua module provides API to perform OCSP queries, OCSP response validations, and
OCSP stapling planting.

Usually, this module is used together with the L<ngx.ssl|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ssl.md>
module in the
context of L<ssl_certificate_by_lua*>.

This Lua module does not ship with this ngx_lua module itself rather it is shipped with
the
L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

Please refer to the L<documentation|https://github.com/openresty/lua-resty-core/blob/master/lib/ngx/ocsp.md>
for this C<ngx.ocsp> Lua module for more details.

This feature requires at least ngx_lua C<v0.10.0>.




=head2 ndk.set_var.DIRECTIVE

B<syntax:> I<res = ndk.set_var.DIRECTIVE_NAME>

B<context:> I<init_worker_by_luaE<42>, set_by_luaE<42>, rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, log_by_luaE<42>, ngx.timer.E<42>, balancer_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, exit_worker_by_luaE<42>, ssl_client_hello_by_luaE<42>>

This mechanism allows calling other Nginx C modules' directives that are implemented by L<Nginx Devel Kit|https://github.com/simplresty/ngx_devel_kit> (NDK)'s set_var submodule's C<ndk_set_var_value>.

For example, the following L<set-misc-nginx-module|http://github.com/openresty/set-misc-nginx-module> directives can be invoked this way:


=over


=item *

L<set_quote_sql_str|http://github.com/openresty/set-misc-nginx-module#set_quote_sql_str>

=item *

L<set_quote_pgsql_str|http://github.com/openresty/set-misc-nginx-module#set_quote_pgsql_str>

=item *

L<set_quote_json_str|http://github.com/openresty/set-misc-nginx-module#set_quote_json_str>

=item *

L<set_unescape_uri|http://github.com/openresty/set-misc-nginx-module#set_unescape_uri>

=item *

L<set_escape_uri|http://github.com/openresty/set-misc-nginx-module#set_escape_uri>

=item *

L<set_encode_base32|http://github.com/openresty/set-misc-nginx-module#set_encode_base32>

=item *

L<set_decode_base32|http://github.com/openresty/set-misc-nginx-module#set_decode_base32>

=item *

L<set_encode_base64|http://github.com/openresty/set-misc-nginx-module#set_encode_base64>

=item *

L<set_decode_base64|http://github.com/openresty/set-misc-nginx-module#set_decode_base64>

=item *

L<set_encode_hex|http://github.com/openresty/set-misc-nginx-module#set_encode_base64>

=item *

L<set_decode_hex|http://github.com/openresty/set-misc-nginx-module#set_decode_base64>

=item *

L<set_sha1|http://github.com/openresty/set-misc-nginx-module#set_encode_base64>

=item *

L<set_md5|http://github.com/openresty/set-misc-nginx-module#set_decode_base64>


=back

For instance,


     local res = ndk.set_var.set_escape_uri('a/b')
     -- now res == 'a%2fb'

Similarly, the following directives provided by L<encrypted-session-nginx-module|http://github.com/openresty/encrypted-session-nginx-module> can be invoked from within Lua too:


=over


=item *

L<set_encrypt_session|http://github.com/openresty/encrypted-session-nginx-module#set_encrypt_session>

=item *

L<set_decrypt_session|http://github.com/openresty/encrypted-session-nginx-module#set_decrypt_session>


=back

This feature requires the L<ngx_devel_kit|https://github.com/simplresty/ngx_devel_kit> module.




=head2 coroutine.create

B<syntax:> I<co = coroutine.create(f)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, init_by_luaE<42>, ngx.timer.E<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Creates a user Lua coroutines with a Lua function, and returns a coroutine object.

Similar to the standard Lua L<coroutine.create|https://www.lua.org/manual/5.1/manual.html#pdf-coroutine.create> API, but works in the context of the Lua coroutines created by ngx_lua.

This API was first usable in the context of L<init_by_lua*> since the C<0.9.2>.

This API was first introduced in the C<v0.6.0> release.




=head2 coroutine.resume

B<syntax:> I<ok, ... = coroutine.resume(co, ...)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, init_by_luaE<42>, ngx.timer.E<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Resumes the execution of a user Lua coroutine object previously yielded or just created.

Similar to the standard Lua L<coroutine.resume|https://www.lua.org/manual/5.1/manual.html#pdf-coroutine.resume> API, but works in the context of the Lua coroutines created by ngx_lua.

This API was first usable in the context of L<init_by_lua*> since the C<0.9.2>.

This API was first introduced in the C<v0.6.0> release.




=head2 coroutine.yield

B<syntax:> I<... = coroutine.yield(...)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, init_by_luaE<42>, ngx.timer.E<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Yields the execution of the current user Lua coroutine.

Similar to the standard Lua L<coroutine.yield|https://www.lua.org/manual/5.1/manual.html#pdf-coroutine.yield> API, but works in the context of the Lua coroutines created by ngx_lua.

This API was first usable in the context of L<init_by_lua*> since the C<0.9.2>.

This API was first introduced in the C<v0.6.0> release.




=head2 coroutine.wrap

B<syntax:> I<co = coroutine.wrap(f)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, init_by_luaE<42>, ngx.timer.E<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Similar to the standard Lua L<coroutine.wrap|https://www.lua.org/manual/5.1/manual.html#pdf-coroutine.wrap> API, but works in the context of the Lua coroutines created by ngx_lua.

This API was first usable in the context of L<init_by_lua*> since the C<0.9.2>.

This API was first introduced in the C<v0.6.0> release.




=head2 coroutine.running

B<syntax:> I<co = coroutine.running()>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, init_by_luaE<42>, ngx.timer.E<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Identical to the standard Lua L<coroutine.running|https://www.lua.org/manual/5.1/manual.html#pdf-coroutine.running> API.

This API was first usable in the context of L<init_by_lua*> since the C<0.9.2>.

This API was first enabled in the C<v0.6.0> release.




=head2 coroutine.status

B<syntax:> I<status = coroutine.status(co)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>, init_by_luaE<42>, ngx.timer.E<42>, header_filter_by_luaE<42>, body_filter_by_luaE<42>, ssl_certificate_by_luaE<42>, ssl_session_fetch_by_luaE<42>, ssl_session_store_by_luaE<42>, ssl_client_hello_by_luaE<42>>

Identical to the standard Lua L<coroutine.status|https://www.lua.org/manual/5.1/manual.html#pdf-coroutine.status> API.

This API was first usable in the context of L<init_by_lua*> since the C<0.9.2>.

This API was first enabled in the C<v0.6.0> release.




=head2 ngx.run_worker_thread

B<syntax:> I<ok, res1, res2, ... = ngx.run_worker_thread(threadpool, module_name, func_name, arg1, arg2, ...)>

B<context:> I<rewrite_by_luaE<42>, access_by_luaE<42>, content_by_luaE<42>>

B<This API is still experimental and may change in the future without notice.>

B<This API is available only for Linux.>

Wrap the L<nginx worker thread|http://nginx.org/en/docs/dev/development_guide.html#threads> to execute lua function. The caller coroutine would yield until the function returns.

Only the following ngx_lua APIs could be used in C<function_name> function of the C<module> module:


=over


=item *

C<ngx.encode_base64>

=item *

C<ngx.decode_base64>


=back


=over


=item *

C<ngx.hmac_sha1>

=item *

C<ngx.encode_args>

=item *

C<ngx.decode_args>

=item *

C<ngx.quote_sql_str>


=back


=over


=item *

C<ngx.crc32_short>

=item *

C<ngx.crc32_long>

=item *

C<ngx.hmac_sha1>

=item *

C<ngx.md5_bin>

=item *

C<ngx.md5>


=back


=over


=item *

C<ngx.config.subsystem>

=item *

C<ngx.config.debug>

=item *

C<ngx.config.prefix>

=item *

C<ngx.config.nginx_version>

=item *

C<ngx.config.nginx_configure>

=item *

C<ngx.config.ngx_lua_version>


=back


=over


=item *

C<ngx.shared.DICT>


=back

The first argument C<threadpool> specifies the Nginx thread pool name defined by L<thread_pool|https://nginx.org/en/docs/ngx_core_module.html#thread_pool>.

The second argument C<module_name> specifies the lua module name to execute in the worker thread, which would return a lua table. The module must be inside the package path, e.g.


     lua_package_path '/opt/openresty/?.lua;;';

The third argument C<func_name> specifies the function field in the module table as the second argument.

The type of C<args> must be one of type below:


=over


=item *

boolean

=item *

number

=item *

string

=item *

nil

=item *

table (the table may be recursive, and contains members of types above.)


=back

The C<ok> is in boolean type, which indicate the C land error (failed to get thread from thread pool, pcall the module function failed, etc.). If C<ok> is C<false>, the C<res1> is the error string.

The return values (res1, ...) are returned by invocation of the module function. Normally, the C<res1> should be in boolean type, so that the caller could inspect the error.

This API is useful when you need to execute the below types of tasks:


=over


=item *

CPU bound task, e.g. do md5 calculation

=item *

File I/O task

=item *

Call C<os.execute()> or blocking C API via C<ffi>

=item *

Call external Lua library not based on cosocket or nginx


=back

Example1: do md5 calculation.


     location /calc_md5 {
         default_type 'text/plain';
    
         content_by_lua_block {
             local ok, md5_or_err = ngx.run_worker_thread("testpool", "md5", "md5")
             ngx.say(ok, " : ", md5_or_err)
         }
     }

C<md5.lua>


    local function md5()
        return ngx.md5("hello")
    end
    
    return { md5=md5, }

Example2: write logs into the log file.


     location /write_log_file {
         default_type 'text/plain';
    
         content_by_lua_block {
             local ok, err = ngx.run_worker_thread("testpool", "write_log_file", "log", ngx.var.arg_str)
             if not ok then
                 ngx.say(ok, " : ", err)
                 return
             end
             ngx.say(ok)
         }
     }

C<write_log_file.lua>


     local function log(str)
         local file, err = io.open("/tmp/tmp.log", "a")
         if not file then
             return false, err
         end
         file:write(str)
         file:flush()
         file:close()
         return true
     end
     return {log=log}




=head1 Obsolete Sections

This section is just holding obsolete documentation sections that have been either renamed or removed so that existing links over the web are still valid.




=head2 Special PCRE Sequences

This section has been renamed to L<Special Escaping Sequences>.




=head2 Lua/LuaJIT bytecode support

This section has been renamed to
L<LuaJIT bytecode support>. As of version
C<v0.10.16> of this module, the standard Lua interpreter (also known
as "PUC-Rio Lua") is not supported anymore.
