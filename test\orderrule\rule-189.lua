-- 计算生产参数 - 模块：靠墙立柜1格【左右】，厂商：左侧板 【立柜】，参数：WJPD，五金判断

local _M = {}

local array_util = require("dynamic.array-util")
local geometry_engine_util = require("dynamic.geometry-engine-util")
local board_xml_util = require("dynamic.board-xml-util")
local check_dir_target_in_relations = require("dynamic.check-dir-target-in-relations")
local Engine = require("lib.oop-rule-engine")

-- 定义规则执行入口
function _M.dowork(root, target, context)
    local engine_object = Engine.current()
    if not engine_object then
        return nil, "获取不到几何引擎对象"
    end
    
    -- 获取目标侧板ID
    local board_a_id = target:get_attribute("id")
    if not board_a_id then
        return nil, "获取不到侧板ID"
    end

    -- 1. 查找目标侧板下方的板件
    local relation_list = engine_object:get_objects_distance_value_array("id", "==", {board_a_id}, {"down"}, "<", 1, "prodCatId", "==", {713})
    if not relation_list or #relation_list == 0 then
        return 0
    end
    
    -- 2. 查找A下面有B的结构
    local connected_side_boards = {}
    if relation_list and #relation_list > 0 then
        connected_side_boards = check_dir_target_in_relations.collect_connected_side_boards({board_a_id}, relation_list, "down")
    end

    -- 3. 确认 A和B的关系是 180
    local a_b_180_boards = {}
    local b_board_ids = {}
    for _, connected_side_board in ipairs(connected_side_boards) do

        local board_a_id = connected_side_board.board_a_id
        local left_right_relation_list = engine_object:get_objects_distance_value_array("id", "==", {board_a_id}, {"left", "right"}, "<", 1, "BJBQ", "==", {4})
        -- A 必须没有左右底固层
        if not left_right_relation_list or #left_right_relation_list == 0 then
            local board_b_ids = connected_side_board.board_b_ids
            local board_b_ids_180 = geometry_engine_util.filter_angle_boards(board_a_id, board_b_ids, engine_object, 0)

            if board_b_ids_180 and #board_b_ids_180 > 0 then
                table.insert(a_b_180_boards, {
                    board_a_id = board_a_id,
                    board_b_ids = board_b_ids_180
                })
                b_board_ids = array_util.contact_table(b_board_ids, board_b_ids_180)
            end
        end
    end

    -- 4. 找到有B 且有C的集合
    local b_c_boards = {}
    local left_right_relation_list = {}
    
    if b_board_ids and #b_board_ids > 0 then
        b_board_ids = array_util.unique_table(b_board_ids)
        left_right_relation_list = engine_object:get_objects_distance_value_array("id", "==", b_board_ids, {"left", "right"}, "<", 1, "BJBQ", "==", {3})
    end

    for _, connected_side_board in ipairs(a_b_180_boards) do
        local board_b_ids = connected_side_board.board_b_ids
        local left_right_boards = {}

        for _, board_b_id in ipairs(board_b_ids) do
            -- 找所有B的 左右关系
            local left_relation = check_dir_target_in_relations.check_dir_target_in_relations("left", board_b_id, left_right_relation_list)
            local right_relation = check_dir_target_in_relations.check_dir_target_in_relations("right", board_b_id, left_right_relation_list)
            
            left_right_boards = array_util.contact_table(left_relation, right_relation)
            left_right_boards = array_util.unique_table(left_right_boards)

            if left_right_boards and #left_right_boards > 0 then
                table.insert(b_c_boards, {
                    board_b_id = board_b_id, 
                    board_c_ids = left_right_boards
                })
            end
        end
    end

    -- 5. 确认 B和C的关系是 90
    local b_c_90_boards = {}
    for _, connected_side_board in ipairs(b_c_boards) do
        local board_b_id = connected_side_board.board_b_id    
        local board_c_ids = connected_side_board.board_c_ids
        local board_b_ids_90 = geometry_engine_util.filter_angle_boards(board_b_id, board_c_ids, engine_object, 90)

        if board_b_ids_90 and #board_b_ids_90 > 0 then
            table.insert(b_c_90_boards, {
                board_b_id = board_b_id,
                board_c_ids = board_b_ids_90
            })
        end
    end

    -- 6. 过滤 C不满足条件的关系列表
    local top_c_in_b = {}
    if b_c_90_boards and #b_c_90_boards > 0 then
        for _, connected_side_board in ipairs(b_c_90_boards) do
            local board_b_id = tostring(connected_side_board.board_b_id)    
            local board_c_ids = connected_side_board.board_c_ids
            local board_b_xml = root:search("//Part[@id='" .. board_b_id .. "']")[1]

            for _, board_c_id in ipairs(board_c_ids) do
                local board_c_xml = root:search("//Part[@id='" .. board_c_id .. "']")[1]
                local board_c_depth = board_c_xml:get_attribute("D")

                -- 检查C的深度是否满足条件
                if board_c_depth and tonumber(board_c_depth) >= 70 then
                    -- C完全在B的Z+H-C.H的区间内
                    local is_c_in_b = board_xml_util.is_top_parallel(board_b_xml, board_c_xml, 1)

                    if is_c_in_b then
                        table.insert(top_c_in_b, board_b_id)
                        break
                    end
                end
            end
        end
    end

    -- 7. 判断搭接关系是否成立
    local has_connected = false
    if #a_b_180_boards > 0 then
        for _, a_b_relation in ipairs(a_b_180_boards) do
            for _, board_b_id in ipairs(a_b_relation.board_b_ids) do
                if array_util.table_contains(top_c_in_b, board_b_id) then
                    has_connected = true
                    break
                end
            end
            if has_connected then break end
        end
    end

    -- 8. 返回结果
    return has_connected and 1 or 0
end

return _M