-- 检测背板的长宽是否超出最大尺寸限制
-- 竖纹 （textureAngleDegree=0） 先读 H 再读 W
-- 横纹 （textureAngleDegree=90） 先读 W 再读 H


-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local check_rule_result = require("lib.check-rule-result")

local M = {}

local material_size_limits = {
    ["ML1053AG_可可胡桃"] = {
        [5] = {a_max = 2440, b_max = 1220},
        [9] = {a_max = 2440, b_max = 1220},
        [18] = {a_max = 2800, b_max = 1220},
        [25] = {a_max = 2800, b_max = 1220}
    },
    ["ML1054AG_秋韵黄橡"] = {
        [5] = {a_max = 2440, b_max = 1220},
        [9] = {a_max = 2440, b_max = 1220},
        [18] = {a_max = 2800, b_max = 1220},
        [25] = {a_max = 2800, b_max = 1220}
    },
    ["ML966JA_臻白"] = {
        [5] = {a_max = 2440, b_max = 1220},
        [18] = {a_max = 2440, b_max = 1220},
		[25] = {a_max = 2800, b_max = 1220}
    }
}

local function get_material_size_limits(material_name, thickness)
    return material_size_limits[material_name][thickness]
end

-- 定义规则执行入口
function M.dowork(root, target, context)
	local rule_result = check_rule_result.new("背板超尺寸检查", check_rule_result.LEVEL.ERROR)

	if not root then
		return rule_result:pass("xml is nil")
	end
	
	-- 存储结果
	local result = {}
	local logs = {}

	-- 获取所有的板件
	local parts_xml = root:search("//Part[@prodCatId='713']")
	if not parts_xml or #parts_xml == 0 then
		return rule_result:pass("没有找到任何 prodCatId=713 的Part节点")
	end

	-- 获取背板
	local back_boards_xml = {}
	for _, part_xml in ipairs(parts_xml) do
		local bjbq = xml_search.get_value_in_parameter(part_xml, "BJBQ")
		if bjbq == "6" then
			table.insert(back_boards_xml, part_xml)
		end
	end

	local err_back_board_list = {}
	for _, back_board_xml in ipairs(back_boards_xml) do
		local id = back_board_xml:get_attribute("id")
		local name = back_board_xml:get_attribute("name")
		local texture_name = back_board_xml:get_attribute("textureName")
		local czfx = xml_search.get_value_in_parameter(back_board_xml, "textureAngleDegree")
		local d = tonumber(back_board_xml:get_attribute("D"))
		local h = tonumber(back_board_xml:get_attribute("H"))
		local w = tonumber(back_board_xml:get_attribute("W"))
		
		-- 如果纹理名称不是1053AG，则跳过
		local limit_size = get_material_size_limits(texture_name, d)

		if limit_size and czfx then
			local h_max = 0
			local w_max = 0
			if tonumber(czfx) == 0 then
				h_max = limit_size.a_max
				w_max = limit_size.b_max
			elseif tonumber(czfx) == 90 then
				h_max = limit_size.b_max
				w_max = limit_size.a_max
			end

			local h_over_size = h > h_max
			local w_over_size = w > w_max
			if h_over_size then
				table.insert(err_back_board_list, {
					prompt = string.format("背板 %s 的高度 %s 超出最大限制 %d ，请用中竖板/层板分段背板", name, h, h_max),
					related_ids = {id}
				})
			end

			if w_over_size then
				table.insert(err_back_board_list, {
					prompt = string.format("背板 %s 的宽度 %s 超出最大限制 %d ，请用中竖板/层板分段背板", name, w, w_max),
					related_ids = {id}
				})
			end
		end
	end

	table.insert(logs, string.format("已收集 %d 个背板数据，其中异常的数据有 %d 个", #back_boards_xml, #err_back_board_list))

	return rule_result:error(err_back_board_list, logs)
end

return M