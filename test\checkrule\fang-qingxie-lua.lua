local _M = {}

-- 加载所需模块
local relation_grouping = require("lib.relation-grouping")
local geometry_engine = require("lib.oop-rule-engine")
local filter_unit_deep = require("dynamic.filter-unit-deep")
local filter_unit_top = require("dynamic.filter-unit-top")
local xml_search = require("lib.xml-search-in-node")
local check_rule_result = require("lib.check-rule-result")
local array_util = require("dynamic.array-util")
local check_dir_target = require("dynamic.check-dir-target-in-relations")


-- 定义规则执行入口
function _M.dowork(root, target, context)
	local rule_result = check_rule_result.new("防倾倒风险检查", check_rule_result.LEVEL.INFO)

	-- 检查几何引擎初始化状态

	-- 存储结果
	local result = {}
	-- 存储日志
	local logs = {}

	local engine_object = geometry_engine.current()
    
    local xml_parts = root:search("/Root/Parts")
	local xml_units = xml_search.get_part_by_mkbq(xml_parts, 1)
	if not xml_units or #xml_units == 0 then
        return rule_result:pass("获取Part节点失败")
    end

	local room_height = 0
	-- 通过 root/Structures/Walls/Wall的属性 intervalE 找到墙体高
	local walls = root:search("/Root/Structures/Walls/Wall")
	if not walls or #walls == 0 then
		return rule_result:pass("获取Wall节点失败")
	end

	for _, wall in ipairs(walls) do
		local intervalE = tonumber(wall:get_attribute("intervalE")) or 0
		if intervalE > room_height then
			room_height = intervalE
		end
	end

	local unit_data_list = {}
	-- 遍历units 收集 单元id列表
	-- 收集满足深度检查的单元
	-- 收集满足到顶检查的单元
    for _, unit in ipairs(xml_units) do
		local left_side_boards = xml_search.get_part_by_bjbq(unit, 1)
		local right_side_boards = xml_search.get_part_by_bjbq(unit, 2)
		local left_d = 0
		local right_d = 0
		if left_side_boards and #left_side_boards > 0 then
			left_d = left_side_boards[1]:get_attribute("D") or 0
		end

		if right_side_boards and #right_side_boards > 0 then
			right_d = right_side_boards[1]:get_attribute("D") or 0
		end

		local unit_data = {
			id = unit:get_attribute("id"),
			name = unit:get_attribute("name"),
			h = tonumber(unit:get_attribute("H")) or 0,
			z = tonumber(unit:get_attribute("Z")) or 0,
			left_d = left_d,
			right_d = right_d,
		}

		table.insert(unit_data_list, unit_data)
	end

	-- 深度检查
	local deep_rst = filter_unit_deep.filter_unit_Deep(unit_data_list, 450)
	local deep_passed = deep_rst.passed
	local deep_failed = deep_rst.failed
	table.insert(logs, string.format("深度检查结果	 成功: %d, 失败: %d", #deep_rst.passed, #deep_rst.failed))

	-- 到顶检查
	local top_rst = filter_unit_top.filter_unit_Top(deep_failed, room_height)
	local top_passed = top_rst.passed
	local top_failed = top_rst.failed
	table.insert(logs, string.format("到顶检查结果	 成功: %d, 失败: %d", #top_rst.passed, #top_rst.failed))

	-- 顶封板检查
	local failed_untop_ids = {}
	for _, unit_data in ipairs(top_failed) do
		table.insert(failed_untop_ids, unit_data.id)
	end

    local top_cover_passed = {}
    local top_cover_failed = {}
	-- 找柜体上有顶封板的单元关系
    local top_relations = engine_object:get_objects_distance("MKBQ", "==", "1" , {"up"}, "<", 1, "MKBQ", "==", "2")
    if top_relations and #top_relations > 0 then
        for _, unit in ipairs(unit_data_list) do
			local id = unit.id
			local top_cover_ids = check_dir_target.check_dir_target_in_relations("up", id, top_relations)
			if top_cover_ids and #top_cover_ids > 0 then
				table.insert(top_cover_passed, unit)
			else
				table.insert(top_cover_failed, unit)
			end
		end
    end

    table.insert(logs, string.format("顶封板检查结果	 成功: %d, 失败: %d", #top_cover_passed, #top_cover_failed))

	-- 收集所有安全单元
	-- 合并深度检查通过和到顶检查通过的单元到安全单元列表
	local safe_units = {}
	for _, unit_data in ipairs(deep_passed) do
		safe_units[unit_data.id] = true
	end

	for _, unit_data in ipairs(top_passed) do
		safe_units[unit_data.id] = true
	end

	for _, unit_data in ipairs(top_cover_passed) do
		safe_units[unit_data.id] = true
	end

	-- 组合校验最后一波
	-- 获取所有单元的关系
	-- 查找所有 BQ==1 之间的关系
	-- 吊柜往下 判断有没有相邻的地柜`
	-- 柜体往上 判断有没有顶封板
    local relations = engine_object:get_objects_distance("MKBQ", "==", "1" , {"down", "up", "left", "right", "front", "back"}, "<", 1, "MKBQ", "==", "1")
	local grouped_relations = relation_grouping.group_relations(relations)
	local group_units = {}
	for _, group in ipairs(grouped_relations) do
		local group_has_safe = false

		-- 组合内任意一个单元安全 则组合内所有单元都安全
		for _, unit_id in ipairs(group) do
			if safe_units[unit_id] then
				group_has_safe = true
			end

			-- 统计组合的uid列表
			if #group > 1 then
				table.insert(group_units, unit_id)
			end
		end

		if group_has_safe then
			for _, unit_id in ipairs(group) do
				safe_units[unit_id] = true
			end
		end
	end

	local safe_count = 0
	for _, unit_data in ipairs(unit_data_list) do
		local is_group = array_util.table_contains(group_units, unit_data.id)
		local is_safe = safe_units[unit_data.id] or false

		if not is_safe then
			table.insert(result, {
				prompt = string.format("单元 %s 是否安全: %s, 是否是组合: %s", unit_data.name, is_safe and "安全" or "不安全", is_group and "是" or "否"),
				related_ids = {unit_data.id}
			})
		end

		if safe_units[unit_data.id] then
			safe_count = safe_count + 1
		end
	end

	table.insert(logs, string.format("防倾斜检查汇总: %d, 不安全总数: %d", #unit_data_list, #result))

    return rule_result:error(result, logs)
end

return _M
