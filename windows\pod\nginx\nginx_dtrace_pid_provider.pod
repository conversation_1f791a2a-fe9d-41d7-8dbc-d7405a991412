=encoding utf-8


=head1 Name


nginx_dtrace_pid_provider - Debugging nginx with DTrace pid provider


=head1



This article assumes the reader has a general knowledge of nginx internals and
DTrace.





Although nginx built with the L<--with-debug|debugging_log>
option already provides a lot of information about request processing,
it is sometimes desirable to trace particular parts of code path more
thoroughly and at the same time omit the rest of debugging output.
DTrace pid provider (available on Solaris, macOS) is a useful tool to
explore userland program’s internals, since it doesn’t require any code
changes and it can help with the task.
A simple DTrace script to trace and print nginx function calls
may look like this:


    
    #pragma D option flowindent
    
    pid$target:nginx::entry {
    }
    
    pid$target:nginx::return {
    }








DTrace capabilities for function calls tracing provide only a limited amount
of useful information, though.
Real-time inspection of function arguments is typically more interesting,
but also a bit more complicated.
Examples below are intended to help the reader become more familiar with
DTrace and the process of analyzing nginx behavior using DTrace.





One of the common scenarios for using DTrace with nginx is the following:
attach to the nginx worker process to log request lines and request start times.
The corresponding function to attach is
C<ngx_http_process_request>, and the argument in question
is a pointer to the C<ngx_http_request_t> structure.
DTrace script for such request logging can be as simple as:


    
    pid$target::*ngx_http_process_request:entry
    {
        this->request = (ngx_http_request_t *)copyin(arg0, sizeof(ngx_http_request_t));
        this->request_line = stringof(copyin((uintptr_t)this->request->request_line.data,
                                             this->request->request_line.len));
        printf("request line = %s\n", this->request_line);
        printf("request start sec = %d\n", this->request->start_sec);
    }








It should be noted that in the example above DTrace requires some knowledge
about the C<ngx_http_request_t> structure.
Unfortunately while it is possible to use a specific C<#include>
directive in the DTrace script and then pass it to a C preprocessor
(with the C<-C> flag), that doesn’t really work.
Due to a lot of cross dependencies, almost all nginx header files
have to be included.
In turn, based on C<configure> script settings,
nginx headers will include PCRE,
OpenSSL and a variety of system header files.
While in theory all those header files related to a specific nginx build
might be included in DTrace script preprocessing and compilation, in reality
DTrace script most probably will fail to compile because of unknown syntax in
some header files.





The problem above can be solved by including only the relevant and
necessary structure and type definitions in the DTrace script.
DTrace has to know sizes of structures, types, and fields offsets.
Thus dependencies can be further reduced by manually optimizing
structure definitions for use with DTrace.





Let’s use DTrace script example above and see what structure definitions
it needs to work properly.





First of all C<objsE<sol>ngx_auto_config.h> file generated by
configure should be included, because it defines a number of constants
affecting various C<#ifdef>’s.
After that, some basic types and definitions
like C<ngx_str_t>, C<ngx_table_elt_t>,
C<ngx_uint_t> etc. should be put at the beginning of the
DTrace script.
These definitions are compact, commonly used and unlikely to be
frequently changed.





Then there’s the C<ngx_http_request_t> structure that
contains a lot of pointers to other structures.
Because these pointers are really irrelevant to this script, and because they
have the same size, it is possible to just replace them with void pointers.
Instead of changing definitions, it is better to add appropriate typedefs,
though:


    
    typedef ngx_http_upstream_t     void;
    typedef ngx_http_request_body_t void;



Last but not least it is necessary to add definitions of two member structures
(C<ngx_http_headers_in_t>,
C<ngx_http_headers_out_t>),
declarations of callback functions and definitions of constants.





The final DTrace script can be downloaded from
L<here|http://nginx.org/download/trace_process_request.d>.





The following example shows the output of running this script:


    
    # dtrace -C -I ./objs -s trace_process_request.d -p 4848
    dtrace: script 'trace_process_request.d' matched 1 probe
    CPU     ID                    FUNCTION:NAME
      1      4 .XAbmO.ngx_http_process_request:entry request line = GET / HTTP/1.1
    request start sec = **********
    
      0      4 .XAbmO.ngx_http_process_request:entry request line = GET /en/docs/nginx_dtrace_pid_provider.html HTTP/1.1
    request start sec = **********







Using similar techniques the reader should be able to trace other
nginx function calls.




=head1 See also




=over




=item *

L<Solaris Dynamic Tracing Guide|http://docs.oracle.com/cd/E19253-01/817-6223/index.html>



=item *

L<Introduction article on DTrace pid provider|http://dtrace.org/blogs/brendan/2011/02/09/dtrace-pid-provider/>



=back






