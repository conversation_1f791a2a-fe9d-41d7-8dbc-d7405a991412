local _M = {}
local check_dir_target = require("dynamic.check-dir-target-in-relations")

---过滤单元顶部高度
---@param unit_data_list table 单元数据列表，每个单元包含 z 和 h 属性
---@param room_height number 房间高度
---@return table 返回包含 passed 和 failed 两个数组的表，分别表示通过和未通过的单元
function _M.filter_unit_Top(unit_data_list, room_height)
    local passed = {}
    local failed = {}

    if not unit_data_list or #unit_data_list == 0 then
        return {passed = {}, failed = {}}
    end

    for _, unit_data in ipairs(unit_data_list) do
        local z = unit_data.z
        local h = unit_data.h

        if z + h > room_height then
            table.insert(passed, unit_data)
        else
            table.insert(failed, unit_data)
        end
    end

    return {passed = passed, failed = failed}
end

---过滤单元顶部覆盖关系
---@param unit_data_list table 单元数据列表，每个单元包含 id 属性
---@param relations table 单元之间的关系数据
---@return table 返回包含 passed 和 failed 两个数组的表，分别表示通过和未通过的单元
function _M.filter_unit_Top_cover(unit_data_list, relations)
    local passed = {}
    local failed = {}

    if not unit_data_list or #unit_data_list == 0 then
        return {passed = {}, failed = {}}
    end

    for _, unit_data in ipairs(unit_data_list) do
        local id = unit_data.id

        local top_cover_ids = check_dir_target.check_dir_target_in_relations("up", id, relations)
        -- 如果存在上方向关系
        if top_cover_ids and #top_cover_ids > 0 then
            local has_name_equal_top_cover = false
            for _, top_cover_id in ipairs(top_cover_ids) do
                local top_cover_unit = unit_data:search("//Part[@id='" .. top_cover_id .. "']")
                if top_cover_unit and top_cover_unit:get_attribute("name") == "顶线" then
                    has_name_equal_top_cover = true
                    break
                end
            end
            if has_name_equal_top_cover then
                table.insert(passed, unit_data)
            else
                table.insert(failed, unit_data)
            end
        else
            table.insert(failed, unit_data)
        end
    end

    return {passed = passed, failed = failed}
end

return _M