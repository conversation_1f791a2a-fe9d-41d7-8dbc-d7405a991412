-- 计算生产参数 - 模块：平板三边抽屉，厂商：三边抽芯，参数：CCBZ

local _M = {}

function _M.dowork(root, target, context)
    -- 定义CXHD和S_RAIL的对应关系
    local rail_code_map = {
        [12] = {  -- CXHD = 12
            -- S_RAIL = 11-16
            [11] = "S12GC3:",
            [12] = "S12GC3:",
            [13] = "S12GC3:",
            [14] = "S12GC3:",
            [15] = "S12GC3:",
            [16] = "S12GC3:",
            -- S_RAIL = 72-76
            [72] = "CS12GCZ:",
            [73] = "CS12GCZ:",
            [74] = "CS12GCZ:",
            [75] = "CS12GCZ:",
            [76] = "CS12GCZ:",
            -- S_RAIL = 82-86
            [82] = "CS12XQL1:",
            [83] = "CS12XQL2:",
            [84] = "CS12XQL3:",
            [85] = "CS12XQL4:",
            [86] = "CS12XQL5:",
            -- S_RAIL = 93-96
            [93] = "CS12GCT:",
            [94] = "CS12GCT:",
            [95] = "CS12GCT:",
            [96] = "CS12GCT:",
            -- S_RAIL = 722-726
            [722] = "CS12BLZ:",
            [723] = "CS12BLZ:",
            [724] = "CS12BLZ:",
            [725] = "CS12BLZ:",
            [726] = "CS12BLZ:",
            -- S_RAIL = 773-776
            [773] = "CS12BLT:",
            [774] = "CS12BLT:",
            [775] = "CS12BLT:",
            [776] = "CS12BLT:"
        },
        [18] = {  -- CXHD = 18
            -- S_RAIL = 11-16
            [11] = "S18GC3:",
            [12] = "S18GC3:",
            [13] = "S18GC3:",
            [14] = "S18GC3:",
            [15] = "S18GC3:",
            [16] = "S18GC3:",
            -- S_RAIL = 72-76
            [72] = "CS18GCZ:",
            [73] = "CS18GCZ:",
            [74] = "CS18GCZ:",
            [75] = "CS18GCZ:",
            [76] = "CS18GCZ:",
            -- S_RAIL = 82-86
            [82] = "CS18XQL1:",
            [83] = "CS18XQL2:",
            [84] = "CS18XQL3:",
            [85] = "CS18XQL4:",
            [86] = "CS18XQL5:",
            -- S_RAIL = 93-96
            [93] = "CS18GCT:",
            [94] = "CS18GCT:",
            [95] = "CS18GCT:",
            [96] = "CS18GCT:",
            -- S_RAIL = 722-726
            [722] = "CS18BLZ:",
            [723] = "CS18BLZ:",
            [724] = "CS18BLZ:",
            [725] = "CS18BLZ:",
            [726] = "CS18BLZ:",
            -- S_RAIL = 773-776
            [773] = "CS18BLT:",
            [774] = "CS18BLT:",
            [775] = "CS18BLT:",
            [776] = "CS18BLT:"
        }
    }

    -- 获取参数值并转换为数字
    local cxhd = tonumber(target.CXHD)
    local s_rail = tonumber(target.S_RAIL)
    
    -- 参数检查
    if not cxhd then
        return nil, "获取不到CXHD参数值或参数值不是有效的数字"
    end
    if not s_rail then
        return nil, "获取不到S_RAIL参数值或参数值不是有效的数字"
    end
    
    -- 检查CXHD是否有效
    local cxhd_map = rail_code_map[cxhd]
    if not cxhd_map then
        return nil, "CXHD参数值不在有效范围内（应为12或18）"
    end
    
    -- 获取对应的返回值
    local result = cxhd_map[s_rail]
    if not result then
        return nil, "S_RAIL参数值不在有效范围内"
    end
    
    return result
end

return _M