=encoding utf-8


=head1 Name


howto_build_on_win32 - Building nginx on the Win32 platform with Visual C


=head1 Prerequisites



To build nginx on the Microsoft Win32 E<copy> platform you need:


=over




=item *

Microsoft Visual C compiler. Microsoft Visual Studio E<copy>
8, 10, 17 are known to work.



=item *

L<MSYS|https://sourceforge.net/projects/mingw/files/MSYS/> or
L<MSYS2|https://www.msys2.org>.



=item *

Perl, if you want to build OpenSSL E<copy> and nginx with SSL support.
For example L<ActivePerl|http://www.activestate.com/activeperl>
or L<Strawberry Perl|http://strawberryperl.com>.



=item *

L<Git|https://cli.github.com/> client.



=item *

L<PCRE|http://www.pcre.org>, L<zlib|http://zlib.net>
and L<OpenSSL|http://www.openssl.org> libraries sources.


=back







=head1 Build steps



Ensure that paths to Perl, Git and MSYS bin directories are added to
PATH environment variable before you start build. To set Visual C environment
run vcvarsall.bat script from Visual C directory.






To build nginx:

=over




=item *

Start MSYS bash.



=item *

Check out nginx sources from the GitHub repository:

    
    git clone https://github.com/nginx/nginx.git





=item *

Create a build and lib directories, and unpack zlib, PCRE and OpenSSL libraries
sources into lib directory:

    
    mkdir objs
    mkdir objs/lib
    cd objs/lib
    tar -xzf ../../pcre2-10.39.tar.gz
    tar -xzf ../../zlib-1.3.1.tar.gz
    tar -xzf ../../openssl-3.0.14.tar.gz





=item *

Run configure script:

    
    auto/configure \
        --with-cc=cl \
        --with-debug \
        --prefix= \
        --conf-path=conf/nginx.conf \
        --pid-path=logs/nginx.pid \
        --http-log-path=logs/access.log \
        --error-log-path=logs/error.log \
        --sbin-path=nginx.exe \
        --http-client-body-temp-path=temp/client_body_temp \
        --http-proxy-temp-path=temp/proxy_temp \
        --http-fastcgi-temp-path=temp/fastcgi_temp \
        --http-scgi-temp-path=temp/scgi_temp \
        --http-uwsgi-temp-path=temp/uwsgi_temp \
        --with-cc-opt=-DFD_SETSIZE=1024 \
        --with-pcre=objs/lib/pcre2-10.39 \
        --with-zlib=objs/lib/zlib-1.3.1 \
        --with-openssl=objs/lib/openssl-3.0.14 \
        --with-openssl-opt=no-asm \
        --with-http_ssl_module





=item *

Run make:

    
    nmake






=back







=head1 See also




=over




=item *

L<windows>



=back






