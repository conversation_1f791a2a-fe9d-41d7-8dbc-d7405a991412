-- 全局缓存

local GlobalCache = {
    cache = {}
}

-- 初始化函数
function GlobalCache:new()
    local obj = {}
    self.__index = self
    setmetatable(obj, self)
    obj.cache = {}
    return obj
end

-- 添加缓存
function GlobalCache:add_cache(key, value)
    self.cache[key] = value
end

-- 获取缓存
function GlobalCache:get_cache(key)
    return self.cache[key]
end

-- 重置缓存
function GlobalCache:clear_cache()
    self.cache = {}
end

-- 删除指定缓存
function GlobalCache:remove_cache(key)
    self.cache[key] = nil
end

-- 检查缓存是否存在
function GlobalCache:has_cache(key)
    return self.cache[key] ~= nil
end

-- 获取所有缓存键
function GlobalCache:get_all_keys()
    local keys = {}
    for key, _ in pairs(self.cache) do
        table.insert(keys, key)
    end
    return keys
end

return GlobalCache