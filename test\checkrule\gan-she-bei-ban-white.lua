-- 干涉检查 异常检测
-- 1、几何引擎查找所有有干涉的对象
-- 2、同单元下 与5mm背板干涉的对象 需要过滤

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local geometry_engine = require("lib.oop-rule-engine")
local xmlua = require("xmlua")
local check_rule_result = require("lib.check-rule-result")

-- 定义规则执行入口
local M = {}

-- 定义规则执行入口
function M.dowork(root, target, context)
	local rule_result = check_rule_result.new("素材干涉检查", check_rule_result.LEVEL.INFO)

	if not root then
		return rule_result:pass("xml is nil")
	end

	-- 存储结果
	local result = {}
	local logs = {}

	-- 1. 几何引擎查找所有有干涉的对象
	local engine_object = geometry_engine.current()
	local overlap_objects = engine_object:get_objects_overlap({}, false)

	-- 白名单过滤 去掉左侧板和背板之间的碰撞
	for _, overlap_object in ipairs(overlap_objects) do
		local overlap_main_object_id = overlap_object.object
		local overlap_main_object_name = overlap_object.objectName
		local overlap_sub_object_id = overlap_object.subObject
		local overlap_sub_object_name = overlap_object.subObjectName
		
		local main_part_xml = root:search("//Part[@id='" .. overlap_main_object_id .. "']")
		local sub_part_xml = root:search("//Part[@id='" .. overlap_sub_object_id .. "']")

		if #main_part_xml ~= 1 or #sub_part_xml ~= 1 then
			goto continue
		end

		local bjbq_main = xml_search.get_value_by_child_node(main_part_xml, "Parameter", "name", "BJBQ")
		local bjbq_sub = xml_search.get_value_by_child_node(sub_part_xml, "Parameter", "name", "BJBQ")

		if bjbq_main == "1" and bjbq_sub == "6" or
			bjbq_main == "2" and bjbq_sub == "6" or
			bjbq_main == "6" and bjbq_sub == "1" or
			bjbq_main == "6" and bjbq_sub == "2" then
			goto continue
		end
		
		table.insert(result, {
			prompt = string.format("素材 %s 与 %s 存在干涉", overlap_main_object_name, overlap_sub_object_name),
			related_ids = {overlap_main_object_id, overlap_sub_object_id}
		})
		::continue::
	end

	table.insert(logs, string.format("干涉检查完成，存在 %d 个干涉对象", #result))

	return rule_result:error(result, logs)
end

return M