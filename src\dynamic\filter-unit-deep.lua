local _M = {}

---过滤单元深度
---@param unit_data_list table 单元数据列表，每个单元包含 left_d 和 right_d 属性
---@return table 返回包含 passed 和 failed 两个数组的表，分别表示通过和未通过的单元
function _M.filter_unit_Deep(unit_data_list, over_value)
    local passed = {}
    local failed = {}

    if not unit_data_list or #unit_data_list == 0 then
        return {passed = {}, failed = {}}
    end

    for _, unit_data in ipairs(unit_data_list) do
        local left_d = tonumber(unit_data.left_d)
        local right_d = tonumber(unit_data.right_d)

        if left_d > over_value or right_d > over_value then
            table.insert(passed, unit_data)
        else
            table.insert(failed, unit_data)
        end
    end

    return {passed = passed, failed = failed}
end

return _M 