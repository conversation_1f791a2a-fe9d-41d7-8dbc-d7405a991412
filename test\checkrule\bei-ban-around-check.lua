local _M = {}
-- 检查单元下没有侧板（顶底）的时候 背板左右（上下）是否有是否有素材 若无 则报错
-- 1、背板（Parameter里面会有一个name=BJBQ的参数6指的是背板）
-- 2、判断柜子是否有侧板（Part节点找）、如果没有侧板，背板往左右外扩1mm，如果没有其他板件，就报错
-- 3、判断柜子是否有顶底板（Part节点找）、如果没有侧板背板往上下外扩1mm，如果没有其他板件，就报错

-- 加载所需模块	
local xml_search = require("lib.xml-search-in-node")
local geometry = require("lib.oop-rule-engine")
local check_rule_result = require("lib.check-rule-result")

-- 定义规则执行入口
function _M.dowork(root, target, context)
	local rule_result = check_rule_result.new("单侧柜检查", check_rule_result.LEVEL.ERROR)

	-- 存储结果
	local result = {}
	local logs = {}

	-- 1. 获取所有单元节点
	local engine_object = geometry.current()

	local part_xml = root:search("/Root/Parts")
	local units_xml = xml_search.get_part_by_mkbq(part_xml, 1)
    
	if not units_xml or #units_xml == 0 then
		return rule_result:pass("没有找到单元节点")
	end

	-- 2. 过滤所有单元 标记所有单元的 是否有左右 顶底 背板
	local all_units_mark = {}
	for _, unit_xml in ipairs(units_xml) do
		local unit_id = unit_xml:get_attribute("id")
		local unit_name = unit_xml:get_attribute("name")
		local left_board = unit_xml:search(".//Parameter[@name='BJBQ' and @value='1']")
		local right_board = unit_xml:search(".//Parameter[@name='BJBQ' and @value='2']")
		local up_board = unit_xml:search(".//Parameter[@name='BJBQ' and @value='3']")
		local down_board = unit_xml:search(".//Parameter[@name='BJBQ' and @value='4']")
		local back_board = xml_search.get_part_by_bjbq(unit_xml, 6)

		local has_left_or_right_board = true
		local has_up_or_down_board = true

		if (not left_board or #left_board == 0) or (not right_board or #right_board == 0) then
			has_left_or_right_board = false
		end

		if (not up_board or #up_board == 0) or (not down_board or #down_board == 0) then
			has_up_or_down_board = false
		end

		if back_board and #back_board > 0 then
			local back_board_id = back_board[1]:get_attribute("id")
			local back_board_name = back_board[1]:get_attribute("name")

			table.insert(all_units_mark, {
				unitId = unit_id,
				unitName = unit_name,
				backBoardId = back_board_id,
				backBoardName = back_board_name,
				hasLeftOrRightBoard = has_left_or_right_board,
				hasUpOrDownBoard = has_up_or_down_board,
			})
		end
	end

	-- 3、根据是否有左右/上下 板 判断背板指定方向是否有其他板件
	if not all_units_mark or #all_units_mark == 0 then
		return rule_result:pass("没有找到任何有背板的单元")
	end

	table.insert(logs, string.format("开始检查 %d 个单元的背板四周", #all_units_mark))
	for _, unit_mark in ipairs(all_units_mark) do
		local vertical_check_result = true
		local horizontal_check_result = true
		-- 如果没有对应的板件 则检查 指定方向上是否有数据
		if not unit_mark.hasUpOrDownBoard then
			local vertical_check = engine_object:get_objects_distance("id", "==", unit_mark.backBoardId, {"up", "down"}, "<=", 1, "prodCatId", "==", "713")
			if not vertical_check or #vertical_check < 2 then
				vertical_check_result = false
			end
		end

		if not unit_mark.hasLeftOrRightBoard then
			local horizontal_check = engine_object:get_objects_distance("id", "==", unit_mark.backBoardId, {"left", "right"}, "<=", 1, "prodCatId", "==", "713")
			if not horizontal_check or #horizontal_check < 2 then
				horizontal_check_result = false
			end
		end
		
		if not vertical_check_result or not horizontal_check_result then
			local message = string.format("单元 %s 的背板 %s 必须与侧板相连", unit_mark.unitName, unit_mark.backBoardName)

			table.insert(result, {
				prompt = message,
				related_ids = {unit_mark.unitId, unit_mark.backBoardId}
			})

			table.insert(logs, string.format("单元 %s (ID: %s) 的背板 %s (ID: %s) 检查结果：上下方向 %s，左右方向 %s", 
				unit_mark.unitName, unit_mark.unitId, unit_mark.backBoardName, unit_mark.backBoardId,
				vertical_check_result and "正常" or "异常", horizontal_check_result and "正常" or "异常"))
		end
	end

	table.insert(logs, string.format("检查完成，发现 %d 个背板在某个方向上缺少板件", #result))

	return rule_result:error(result, logs)
end

return _M
