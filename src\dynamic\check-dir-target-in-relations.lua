local _M = {}

---检查指定方向和目标对象在关系列表中的存在性
---@param dir string 关系方向
---@param target string 目标对象id
---@param relations table 关系列表，每个关系包含 direction、object 和 subObject 字段
---@return table|nil 如果找到匹配的关系，返回关系列表；否则返回 nil
function _M.check_dir_target_in_relations(dir, target, relations)
    if not relations or #relations == 0 then
        return nil
    end

    local relation_list = {}
    for _, relation in ipairs(relations) do
        if relation.direction == dir and relation.object == target then
            table.insert(relation_list, relation.subObject)
        end
    end
    
    return relation_list
end

---收集指定方向和目标对象在关系列表中的存在性
---@param side_boards_ids table 目标对象id列表
---@param relation_list table 几何关系列表
---@param direction string 方向
---@return table 收集的关系列表
function _M.collect_connected_side_boards(side_boards_ids, relation_list, direction)
	local connected_boards = {}

	if side_boards_ids and #side_boards_ids > 0 then
		for _, board_id in ipairs(side_boards_ids) do
			local down_relation = _M.check_dir_target_in_relations(direction, board_id, relation_list)

			if down_relation and #down_relation > 0 then
				table.insert(connected_boards, {
					board_a_id = board_id,
					board_b_ids = down_relation,
				})
			end
		end
	end
	return connected_boards
end
return _M