=encoding utf-8


=head1 Name

C<resty.core.time> - utility functions for time operations.


=head1 Status

This Lua module is currently considered production ready.


=head1 Synopsis


    location = /t {
        content_by_lua_block {
            local time = require "resty.core.time"
            ngx.say(time.monotonic_time())
            ngx.say(time.monotonic_msec())
        }
    }

The value get by C<"resty.core.time".monotonic_time> should equal to the value from /proc/uptime.




=head1 Description

This module provides utility functions for the time operations.




=head1 Methods


=head2 monotonic_msec

B<syntax:> I<monotonic_msec()>

Returns the elapsed time in milliseconds from the machine boot for the current time stamp from the Nginx cached time (no syscall involved unlike <PERSON><PERSON>'s date library).


    local cur_msec = require "resty.core.time".monotonic_msec
    ngx.say(cur_msec())
    

This api was first introduced in lua-resty-core v0.1.25.




=head2 monotonic_time

B<syntax:> I<monotonic_time()>

Returns a floating-point number for the elapsed time in seconds (including milliseconds as the decimal part) from the machine boot for the current time stamp from the Nginx cached time (no syscall involved unlike <PERSON><PERSON>'s date library).


    local cur_time = require "resty.core.time".monotonic_time
    ngx.say(cur_time())
    

This api was first introduced in lua-resty-core v0.1.25.




=head1 Community




=head2 English Mailing List

The L<openresty-en|https://groups.google.com/group/openresty-en> mailing list
is for English speakers.




=head2 Chinese Mailing List

The L<openresty|https://groups.google.com/group/openresty> mailing list is for
Chinese speakers.




=head1 Bugs and Patches

Please report bugs or submit patches by


=over


=item 1.

creating a ticket on the L<GitHub Issue Tracker|https://github.com/openresty/lua-resty-core/issues>,

=item 2.

or posting to the L<OpenResty community>.


=back




=head1 Copyright and License

This module is licensed under the BSD license.

Copyright (C) 2018, by OpenResty Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:


=over


=item *

Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.


=back


=over


=item *

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

the L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

=item *

the ngx_lua module: https://github.com/openresty/lua-nginx-module

=item *

OpenResty: https://openresty.org


=back


