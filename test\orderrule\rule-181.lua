-- 计算生产参数 - 模块：靠墙立柜1格【左右】，厂商：左侧板 【立柜】，参数：SXLJ，连接件区分

local _M = {}

local Engine = require("lib.oop-rule-engine")
local xml_search_in_node = require("lib.xml-search-in-node")

-- 判断 subObject 是否在 object 的上下两端
local function is_up_or_down(object, subObject, direction)
     local obj_abs_z = tonumber(object.absZ)
     -- 竖版高度
     local obj_h = tonumber(object.H)

     local sub_obj_abs_z = tonumber(subObject.absZ)
     -- 横板厚度
     local sub_obj_h = tonumber(subObject.H)

     if direction == "up" then
          return sub_obj_abs_z > obj_abs_z + obj_h - sub_obj_h - 1 and sub_obj_abs_z < obj_abs_z + obj_h + 1
     end
     return sub_obj_abs_z > obj_abs_z - 1 and sub_obj_abs_z < obj_abs_z + sub_obj_h + 1
end

-- 根据方向从 relations_data 中获取对象
local function get_relations_object(relations_data, direction)
     local result = {}
     -- 如果是上下方向，返回角度为90度或270度且厚度H最厚的对象
     if direction == "up" or direction == "down" then
          local thickest_object = nil
          local max_thickness = 0

          for _, data in ipairs(relations_data) do
               if data.direction == direction and (data.angle == 90 or data.angle == 270) then
                    local thickness = tonumber(data.subObject.H) or 0
                    if thickness > max_thickness then
                         max_thickness = thickness
                         thickest_object = data
                    end
               end
          end

          table.insert(result, thickest_object)

          return result
     else
          for _, data in ipairs(relations_data) do
               if data.direction ~= "up" and data.direction ~= "down" then
                    table.insert(result, data)
               end
          end
          return result
     end
end

-- 不同级的情况下，处理侧板左右的连接
local function get_side_object(side_object, direction)
     for _, data in ipairs(side_object) do
          if is_up_or_down(data.object, data.subObject, direction) then
               local sub_object_d = tonumber(data.subObject.D) or 0
               if sub_object_d > 70 and (data.angle == 90 or data.angle == 270) then
                    return false
               end
          end
     end
     return true
end

function _M.dowork(root, target, context)
     local engine_object = Engine.current()
     if not engine_object then
          return nil, "获取不到几何引擎对象"
     end

     -- 找出上下搭左右关系(上下固层)
     local directions = { "up", "down", "left", "right" }
     local bj = { "3", "4" }
     local relations = engine_object:get_objects_distance_value_array("id", "==", { target.id }, directions, "<=", 1, "BJBQ", "==", bj)
     if not relations or #relations == 0 then
          return 0
     end

     local parent_id = target.parentId
     -- 定义一个表，用于存储关系（objectId,subObjectId,direction,is_same_level,angle)
     local relations_data = {}
     for _, relation in ipairs(relations) do
          if (target.id == relation.object) then
               local sub_object = xml_search_in_node.get_childs_node_by_kvs(root, "Part", "id", relation.subObject)
               if sub_object and #sub_object > 0 then
                    local is_same_level = (parent_id == sub_object[1].parentId)
                    local angle = 0
                    table.insert(relations_data, {
                         object = target,
                         subObject = sub_object[1],
                         direction = relation.direction,
                         is_same_level = is_same_level,
                         angle = angle
                    })
               end
          end
     end

     -- 根据关系表，计算角度
     local angleObjects = {}
     for _, data in ipairs(relations_data) do
          table.insert(angleObjects, {
               id1 = data.object.id,
               id2 = data.subObject.id
          })
     end
     local objects_angle = engine_object:getObjectsAngle(angleObjects)
     for _, data in ipairs(objects_angle) do
          -- 遍历角度结果，更新到relations_data中
          for i, relation in ipairs(relations_data) do
               if relation.object.id == data.id1 and relation.subObject.id == data.id2 then
                    relation.angle = data.angle
                    break
               end
          end
     end

     -- 获取关联的左右侧板
     local side_object = get_relations_object(relations_data, "left&right")

     -- 竖板上部
     local is_up_connected = false
     local up_object = get_relations_object(relations_data, "up")
     if #up_object > 0 then
          if up_object[1].is_same_level then
               is_up_connected = true
          else
               is_up_connected = get_side_object(side_object, "up")
          end
     end

     -- 竖版下部
     local is_down_connected = false
     local down_object = get_relations_object(relations_data, "down")
     if #down_object > 0 then
          if down_object[1].is_same_level then
               is_down_connected = true
          else
               is_down_connected = get_side_object(side_object, "down")
          end
     end

     -- 根据上下连接情况返回不同的值
     if is_up_connected and is_down_connected then
          return 24 -- 上下都有连接
     elseif is_up_connected and not is_down_connected then
          return 2  -- 上有连接，下没有连接
     elseif not is_up_connected and is_down_connected then
          return 4  -- 上没有连接，下有连接
     else
          return 0  -- 上下都没有连接
     end
end

return _M
