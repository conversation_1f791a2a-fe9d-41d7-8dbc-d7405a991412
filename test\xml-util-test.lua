--package.path = package.path .. ';windows/lua_scripts/?.lua;'

local util = require('lib/xml-util')

local doc, perr = util.parse('<root><user id="1" name="张三"><school>学校</school></user></root>')
if perr then
    print('解析失败', perr)
    return
end

local user_doc, serr = util.search(doc, '/root/user')
if serr then
    print('搜索失败', serr)
    return
end

print(util.text(user_doc[1]))

print(util.to_xml(user_doc))

print(util.attribute(user_doc[1], 'name'))

print(util.attribute(user_doc[1], 'a404'))

local text, err = util.text_safe(user_doc[1])
if not err then
    print(text)
end

local attr, err = util.attribute_safe(user_doc[1], 'a404')
if not err then
    print(attr)
end
