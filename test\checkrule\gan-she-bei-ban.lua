-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local geometry_engine = require('lib.oop-rule-engine')
local check_rule_result = require("lib.check-rule-result")

-- 定义规则执行入口
local M = {}

-- 定义规则执行入口
function M.dowork(root, target, context)
	local rule_result = check_rule_result.new("素材干涉检查", check_rule_result.LEVEL.INFO)

	if not root then
		return rule_result:pass("xml is nil")
	end

	-- 存储结果
	local result = {}
	local logs = {}

	-- 几何引擎查找所有有干涉的对象
	local engine_object = geometry_engine.current()
	local overlap_objects = engine_object:get_objects_overlap({}, false)

	-- 用于去重的表
	local processed_pairs = {}

	for _, overlap_object in ipairs(overlap_objects) do
		local overlap_main_object_id = overlap_object.object
		local overlap_main_object_name = overlap_object.objectName
		local overlap_sub_object_id = overlap_object.subObject
		local overlap_sub_object_name = overlap_object.subObjectName

		local overlap_main_object_result_name = overlap_main_object_name
		local overlap_sub_object_result_name = overlap_sub_object_name
		local overlap_main_object_result_id = overlap_main_object_id
		local overlap_sub_object_result_id = overlap_sub_object_id

		local overlap_main_object_xmls = root:search("//Part[@id='" .. overlap_main_object_id .. "']")
		local overlap_sub_object_xmls = root:search("//Part[@id='" .. overlap_sub_object_id .. "']")

		if not overlap_main_object_xmls or not overlap_sub_object_xmls or #overlap_main_object_xmls == 0 or #overlap_sub_object_xmls == 0 then
			goto continue
		end

		local overlap_main_object_xml = overlap_main_object_xmls[1]
		local overlap_sub_object_xml = overlap_sub_object_xmls[1]

		local main_mkbq = xml_search.get_value_by_child_node(overlap_main_object_xml, "Parameter", "name", "MKBQ")
		while not main_mkbq do
			local parnet_overlap_main_object_xml = overlap_main_object_xml:parent()
			if not parnet_overlap_main_object_xml then
				break
			end

			main_mkbq = xml_search.get_value_by_child_node(parnet_overlap_main_object_xml, "Parameter", "name", "MKBQ")

			if main_mkbq then
				overlap_main_object_xml = parnet_overlap_main_object_xml
				break
			end

			overlap_main_object_xml = parnet_overlap_main_object_xml
		end

		overlap_main_object_result_name = overlap_main_object_xml:get_attribute("name")
		overlap_main_object_result_id = overlap_main_object_xml:get_attribute("id")

		local sub_mkbq = xml_search.get_value_by_child_node(overlap_sub_object_xml, "Parameter", "name", "MKBQ")
		while not sub_mkbq do
			local parnet_overlap_sub_object_xml = overlap_sub_object_xml:parent()
			if not parnet_overlap_sub_object_xml then
				break
			end

			sub_mkbq = xml_search.get_value_by_child_node(parnet_overlap_sub_object_xml, "Parameter", "name", "MKBQ")

			if sub_mkbq then
				overlap_sub_object_xml = parnet_overlap_sub_object_xml
				break
			end

			overlap_sub_object_xml = parnet_overlap_sub_object_xml
		end

		overlap_sub_object_result_name = overlap_sub_object_xml:get_attribute("name")
		overlap_sub_object_result_id = overlap_sub_object_xml:get_attribute("id")

		if overlap_main_object_result_id == overlap_sub_object_result_id then
			goto continue
		end

		-- 创建去重key，确保较小的ID在前
		local key1 = overlap_main_object_result_id .. "_" .. overlap_sub_object_result_id
		local key2 = overlap_sub_object_result_id .. "_" .. overlap_main_object_result_id
		
		-- 检查是否已经处理过这对对象
		if processed_pairs[key1] or processed_pairs[key2] then
			goto continue
		end
		
		-- 标记这对对象已处理
		processed_pairs[key1] = true
		processed_pairs[key2] = true
		
		table.insert(result, {
			prompt = string.format("素材 %s 与 %s 存在干涉", overlap_main_object_result_name, overlap_sub_object_result_name),
			related_ids = {overlap_main_object_result_id, overlap_sub_object_result_id}
		})

		::continue::
	end

	table.insert(logs, string.format("干涉检查完成，存在 %d 个干涉对象", #result))

	return rule_result:error(result, logs)
end

return M