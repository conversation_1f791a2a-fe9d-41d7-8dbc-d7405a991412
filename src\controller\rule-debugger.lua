local json = require("cjson")
local xmlua = require("xmlua")
local string_util = require("lib.string-util")
local expr_processor = require("lib.expr-processor")
local rule_processor = require("lib.rule-processor")
local attribute_appender = require("lib.attribute-appender")
local Engine = require("lib.oop-rule-engine")

local function create_rule(script, need_geometry)
    -- 检查是否是模块形式（包含_M.dowork函数）
    local is_module = script:match("function%s+_M%.dowork%s*%(")

    -- 生成时间戳作为版本号
    local version_code = tostring(os.time())

    if is_module then
        -- 模块形式构造规则对象
        return {
            id = "debug_rule",
            ruleType = "module",
            luaScript = script,
            versionCode = version_code,
            needGeometry = need_geometry or 0
        }
    else
        -- 表达式形式构造规则对象
        return {
            id = "debug_rule",
            ruleType = "expr",
            luaScript = script,
            versionCode = version_code,
            needGeometry = need_geometry or 0
        }
    end
end

-- 处理请求的主函数
local function process(request)
    -- 验证必要参数
    if not request then
        return {
            success = false,
            message = "请求参数格式错误"
        }
    end

    if string_util.is_empty(request.xmlContent) then
        return {
            success = false,
            message = "XML内容不能为空"
        }
    end

    if string_util.is_empty(request.luaScript) then
        return {
            success = false,
            message = "Lua脚本不能为空"
        }
    end

    -- 解析XML
    local document, err = xmlua.XML.parse(request.xmlContent)
    if not document then
        return {
            success = false,
            message = "XML解析失败: " .. tostring(err)
        }
    end

    -- 查找所有带有id的Part节点并附加属性
    local part_nodes = document:search("//Part[@id]")
    for _, node in ipairs(part_nodes) do
        attribute_appender.append_attributes(node)
    end

    -- 查找目标节点
    local target = document
    if string_util.is_not_empty(request.xpath) then
        local success, nodes = pcall(function() return document:search(request.xpath) end)
        if not success then
            return {
                success = false,
                message = "XPath查询失败: " .. tostring(nodes)
            }
        end
        if nodes and #nodes > 0 then
            target = nodes[1]
        end
    end

    -- 构造规则对象
    local rule = create_rule(request.luaScript, request.needGeometry)


     -- 初始化几何引擎
    local engine = Engine.new(request.xmlContent)
    Engine.set(engine)

    -- 根据规则类型执行不同的处理器
    local value, exec_err
    if rule.ruleType == "expr" then
        -- 执行表达式规则
        value, exec_err = expr_processor.execute_rule(rule, document:root(), target, request.context or {})
    else
        -- 执行标准规则
        value, exec_err = rule_processor.execute_rule(rule, document:root(), target, request.context or {})
    end

    -- 释放引擎
    engine:scene_clear()

    if exec_err then
        return {
            success = false,
            message = "执行规则失败: " .. exec_err
        }
    end

    -- 返回成功结果
    return {
        success = true,
        message = "执行成功",
        result = value
    }
end

-- 主函数，处理HTTP请求
local function main()
    -- 设置响应头
    ngx.header.content_type = "application/json; charset=utf-8"

    -- 只允许POST请求
    if ngx.req.get_method() ~= "POST" then
        ngx.status = 405
        ngx.say(json.encode({
            success = false,
            message = "只支持POST请求"
        }))
        return
    end

    -- 读取请求体
    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    if not data then
        ngx.status = 400
        ngx.say(json.encode({
            success = false,
            message = "请求体不能为空"
        }))
        return
    end

    -- 解析JSON请求体
    local request
    local success, err = pcall(function() request = json.decode(data) end)
    if not success or not request then
        ngx.status = 400
        ngx.say(json.encode({
            success = false,
            message = "解析请求体失败: " .. (err or "JSON格式错误")
        }))
        return
    end

    -- 处理请求
    local response = process(request)

    -- 将结果转换为单行字符串
    if response.success and response.result then
        if type(response.result) == "table" then
            response.result = json.encode(response.result)
        else
            response.result = tostring(response.result)
        end
    end

    -- 返回响应
    ngx.status = 200
    ngx.say(json.encode(response))
end

-- 执行主函数
main()
