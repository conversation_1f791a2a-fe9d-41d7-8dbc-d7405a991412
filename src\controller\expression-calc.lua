local json = require("cjson")
local expr_processor = require("lib.expr-processor")
local rule_data = require("lib.rule-data")
local string_util = require("lib.string-util")

-- 表达式运算接口处理函数
local function process(request)
    -- 解析请求参数
    local data_table = request
    if data_table == nil then
        return {
            success = false,
            message = "请求参数格式错误"
        }
    end

    -- 验证必要参数
    if data_table.expressions == nil or #data_table.expressions == 0 then
        return {
            success = false,
            message = "缺少必要参数: expressions"
        }
    end

    -- 处理每个表达式
    local results = {}
    local errors = {}
    local context = data_table.context or {}

    for _, expr in ipairs(data_table.expressions) do
        -- 检查必要字段
        if not expr.key then
            table.insert(errors, {
                key = "",
                ruleId = expr.ruleId or "",
                success = false,
                errorMsg = "表达式缺少必要字段: key"
            })
            goto continue
        end

        -- 获取target参数
        local target = expr.target or {}

        -- 构建规则对象
        local rule
        if string_util.is_not_empty(expr.ruleId) then
            -- 从规则库获取规则
            local rule_obj, rule_err = rule_data.get_rule_by_id_with_cache(expr.ruleId)
            if not rule_obj then
                table.insert(errors, {
                    key = expr.key,
                    ruleId = expr.ruleId or "",
                    success = false,
                    errorMsg = "获取规则失败: " .. (rule_err or "未知错误")
                })
                goto continue
            end
            rule = rule_obj
        elseif string_util.is_not_empty(expr.expression) then
            -- 使用直接提供的表达式
            rule = {
                id = expr.key,
                versionCode = "1.0",
                luaScript = expr.expression
            }
        else
            table.insert(errors, {
                key = expr.key,
                ruleId = expr.ruleId or "",
                success = false,
                errorMsg = "表达式或规则ID必须提供其一"
            })
            goto continue
        end

        -- 执行表达式计算
        local value, err = expr_processor.execute_rule(rule, nil, target, context)
        if err then
            table.insert(errors, {
                key = expr.key,
                ruleId = expr.ruleId or "",
                success = false,
                errorMsg = err
            })
        else
            table.insert(results, {
                key = expr.key,
                ruleId = expr.ruleId or "",
                success = true,
                value = value
            })
        end

        ::continue::
    end

    -- 返回处理结果
    local status = "执行成功"
    if #errors > 0 then
        if #results == 0 then
            status = "执行失败"
        else
            status = "部分执行成功"
        end
    end

    -- 确保errors是数组
    local final_errors = errors
    if #errors == 0 then
        final_errors = json.empty_array
    end

    return {
        success = #errors == 0,
        message = string.format("%s - 成功: %d, 失败: %d",
            status,
            #results,
            #errors
        ),
        results = #results > 0 and results or nil,
        errors = final_errors
    }
end

-- 主函数，处理HTTP请求
local function main()
    -- 设置响应头
    ngx.header.content_type = "application/json; charset=utf-8"

    -- 只允许POST请求
    if ngx.req.get_method() ~= "POST" then
        ngx.status = 405
        ngx.say(json.encode({
            success = false,
            message = "只支持POST请求"
        }))
        return
    end

    -- 读取请求体
    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    if data == nil then
        ngx.status = 400
        ngx.say(json.encode({
            success = false,
            message = "请求体不能为空"
        }))
        return
    end

    -- 解析JSON请求体
    local request
    local success, err = pcall(function() request = json.decode(data) end)
    if not success or not request then
        ngx.status = 400
        ngx.say(json.encode({
            success = false,
            message = "解析请求体失败: " .. (err or "JSON格式错误")
        }))
        return
    end

    -- 处理请求
    local response = process(request)

    -- 返回响应
    ngx.status = 200
    ngx.say(json.encode(response))
end

-- 执行主函数
main()
