-- 门铰边的侧板是否齐平

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local geometry_engine = require('lib.oop-rule-engine')
local check_rule_result = require("lib.check-rule-result")

-- 定义规则执行入口
local M = {}

-- 获取所有abs_x和abs_y在25mm以内的柜子
function M.get_door_near(root, abs_x, abs_y)

	-- 获取所有的门
	local cabinets_xml = root:search("//Part[@prodCatId='719']")
	if not cabinets_xml or #cabinets_xml == 0 then
		return result, "没有找到任何 prodCatId=719 的Part节点"
	end

	local near_cabinets = {}

	for _, cabinet_xml in ipairs(cabinets_xml) do
		local cabinet_space_xml = cabinet_xml:search(".//Space")
		local cabinet_abs_x = cabinet_space_xml[1]:get_attribute("absX")
		local cabinet_abs_y = cabinet_space_xml[1]:get_attribute("absY")

		if math.abs(cabinet_abs_x - abs_x) < 25 and math.abs(cabinet_abs_y - abs_y) < 25 then
			table.insert(near_cabinets, cabinet_xml)
		end
	end

	return near_cabinets
end

function M.get_board_by_bjbq(node, bjbq)
	-- 获取所有的板件
	local parts_xml = node:search(".//Part[@prodCatId='713']")
	if not parts_xml or #parts_xml == 0 then
		return result, "没有找到任何 prodCatId=713 的Part节点"
	end

	-- 获取侧板
	local side_boards_xml = {}
	for _, part_xml in ipairs(parts_xml) do
		local BJBQ = xml_search.get_value_by_child_node(part_xml, "Parameter", "name", "BJBQ")
		if BJBQ == bjbq then
			table.insert(side_boards_xml, part_xml)
		end
	end


	if #side_boards_xml == 0 then
		return nil
	end


	return side_boards_xml
end

-- 定义规则执行入口
function M.dowork(root, target, context)

	local rule_result = check_rule_result.new("门铰边齐平检查", check_rule_result.LEVEL.ERROR)

	if not root then
		return rule_result:pass("xml is nil")
	end

	-- 存储结果
	local result = {}
	local logs = {}

	-- 几何引擎查找所有有干涉的对象
	local engine_object = geometry_engine.current()

	-- 获取所有的门
	local doors_xml = root:search("//Part[@prodCatId='498']")
	if not doors_xml or #doors_xml == 0 then
		return rule_result:pass("没有找到任何 prodCatId=498 的Part节点")
	end

	-- ngx.log(ngx.INFO, "------------------- doors_xml: ", #doors_xml)

	for _, door_xml in ipairs(doors_xml) do

		local door_name = door_xml:get_attribute("name")
		-- 获取门下面的门铰组

		local mengjiaozu_xml = door_xml:search(".//Part[@prodCatId='2059']")
		if not mengjiaozu_xml or #mengjiaozu_xml == 0 then
			goto continue
		end

		local mengjiaozu_uuid = mengjiaozu_xml[1]:get_attribute("id")

		local door_hinge_face = engine_object:get_door_hinge_face_by_pos(mengjiaozu_uuid)

		local door_space_xml = door_xml:search(".//Space")
		local door_box_z = door_space_xml[1]:get_attribute("boxSizeZ")
		local cabinet_xml = door_xml:parent()
		local cabinet_space_xml = cabinet_xml:search(".//Space")
		local cabinet_box_z = cabinet_space_xml[1]:get_attribute("boxSizeZ")

		local search_cabinets = {}
		

		if tonumber(door_box_z) > tonumber(cabinet_box_z) then
			local door_abs_x = cabinet_space_xml[1]:get_attribute("absX")
			local door_abs_y = cabinet_space_xml[1]:get_attribute("absY")
			local near_cabinets = M.get_door_near(root, door_abs_x, door_abs_y)
			if #near_cabinets > 0 then
				for _, near_cabinet in ipairs(near_cabinets) do
					table.insert(search_cabinets, near_cabinet)
				end
			end
		else 
			table.insert(search_cabinets, cabinet_xml)
		end

		if #search_cabinets < 2 then
			goto continue
		end

		table.insert(logs, string.format("门 %s 铰边 %s", door_name, door_hinge_face.coverType))

		-- ngx.log(ngx.INFO, "------------------- search_cabinets: ", #search_cabinets)
		-- ngx.log(ngx.INFO, "------------------- door_hinge_face: ", door_hinge_face.coverType)

		-- lCoverType 和 rCoverType 是相对于柜子的 左右侧板的初始位置是世界的左右

		if door_hinge_face.coverType == "lCoverType" then

			local left_board_list = {}
			for _, search_cabinet in ipairs(search_cabinets) do
				local left_boards = M.get_board_by_bjbq(search_cabinet, "1")
				if left_boards and #left_boards > 0 then
					for _, left_board in ipairs(left_boards) do
						table.insert(left_board_list, left_board)
					end
				end
			end

			for _, left_board in ipairs(left_board_list) do
				local left_board_space_xml = left_board:search(".//Space")
				local left_board_abs_x = left_board_space_xml[1]:get_attribute("absX")
				local left_board_abs_y = left_board_space_xml[1]:get_attribute("absY")
				local left_board_name = left_board:get_attribute("name")
				local left_board_id = left_board:get_attribute("id")
				
				-- 检查是否与其他板的absx和absy差值超过0.5
				for _, other_board in ipairs(left_board_list) do
					if left_board ~= other_board then
						local other_board_space_xml = other_board:search(".//Space")
						local other_board_name = other_board:get_attribute("name")
						local other_board_id = other_board:get_attribute("id")
						local other_board_abs_x = other_board_space_xml[1]:get_attribute("absX")
						local other_board_abs_y = other_board_space_xml[1]:get_attribute("absY")
						
						if math.abs(left_board_abs_x - other_board_abs_x) > 0.5 or 
						   math.abs(left_board_abs_y - other_board_abs_y) > 0.5 then
							table.insert(result, {
								door_name = door_name,
								prompt = string.format("%s 的门铰所在侧板不在同一个直线上，会导致门铰无法安装，请调整", door_name),
								related_ids = {left_board_id, other_board_id}
							})
							goto continue
						end
					end
				end
			end
			
		
		elseif door_hinge_face.coverType == "rCoverType" then
		
			local right_board_list = {}
			for _, search_cabinet in ipairs(search_cabinets) do
				local right_boards = M.get_board_by_bjbq(search_cabinet, "2")
				if right_boards and #right_boards > 0 then
					for _, right_board in ipairs(right_boards) do
						table.insert(right_board_list, right_board)
					end
				end
			end

			-- ngx.log(ngx.INFO, "------------------- right_board_list: ", #right_board_list)

			for _, right_board in ipairs(right_board_list) do
				local right_board_name = right_board:get_attribute("name")
				local right_board_id = right_board:get_attribute("id")
				local right_board_space_xml = right_board:search(".//Space")
				local right_board_abs_x = right_board_space_xml[1]:get_attribute("absX")
				local right_board_abs_y = right_board_space_xml[1]:get_attribute("absY")
				
				-- 检查是否与其他板的absx和absy差值超过0.5
				for _, other_board in ipairs(right_board_list) do
					if right_board ~= other_board then
						local other_board_space_xml = other_board:search(".//Space")
						local other_board_abs_x = other_board_space_xml[1]:get_attribute("absX")
						local other_board_abs_y = other_board_space_xml[1]:get_attribute("absY")
						local other_board_name = other_board:get_attribute("name")
						local other_board_id = other_board:get_attribute("id")

						-- ngx.log(ngx.INFO, "------------------- right_board_name: ", right_board_name)
						-- ngx.log(ngx.INFO, "------------------- right_board_id: ", right_board_id)
						
						-- ngx.log(ngx.INFO, "------------------- other_board_abs_x: ", other_board_abs_x)
						-- ngx.log(ngx.INFO, "------------------- other_board_abs_y: ", other_board_abs_y)

						-- ngx.log(ngx.INFO, "------------------- right_board_abs_x: ", right_board_abs_x)
						-- ngx.log(ngx.INFO, "------------------- right_board_abs_y: ", right_board_abs_y)

						if math.abs(right_board_abs_x - other_board_abs_x) > 0.5 or 
						   math.abs(right_board_abs_y - other_board_abs_y) > 0.5 then
							table.insert(result, {
								door_name = door_name,
								prompt = string.format("%s 的门铰所在侧板不在同一个直线上，会导致门铰无法安装，请调整", door_name),
								related_ids = {right_board_id, other_board_id}
							})
							goto continue
						end
					end
				end
			end
		else
			goto continue
		end

		::continue::
	end

	table.insert(logs, "门铰边齐平检查 检查完成 共有" .. #result .. "个对象问题")
    return rule_result:error(result, logs)
end

return M