--[[
根据传入的lua脚本字符串，生成一个lua文件并加载到运行时

参数说明:
- scriptName: 脚本名称
- scriptContent: lua脚本字符串内容
- outputPath: 输出文件路径,默认放置于dynamic目录中
- fileName: 文件名称,如果存在同名文件会覆盖替换

返回值:
- success: 布尔值,表示是否成功
- error: 字符串,错误信息(仅当success为false时存在)

使用示例:
local generator = require("lib/luaGenerator")
local success, err = generator.generateLuaFile("test", scriptStr)
if not success then
    print("生成失败:", err)
end
]]

local M = {}

-- 获取当前脚本所在目录的函数
local function get_script_path()
    local info = debug.getinfo(1, "S")
    local script_path = info.source:sub(2)  -- 移除开头的 '@'
    return script_path:match("(.*[/\\])") or "./"
end

-- 获取项目根目录的函数
local function get_root_path()
    local current_path = get_script_path()
    -- 从 src/lib/ 回退到项目根目录
    return current_path:match("(.*[/\\])src[/\\]lib[/\\]") or "../"
end

---生成 Lua 文件
---@param scriptContent string Lua脚本内容
---@param outputPath string 输出文件路径
---@return boolean success 是否成功
---@return string|nil error 错误信息
function M.generate_lua_file(scriptName, scriptContent)
    -- 参数校验
    if type(scriptContent) ~= "string" then
        return false, "脚本内容必须是字符串类型"
    end
    
    if type(scriptName) ~= "string" then
        return false, "输出路径必须是字符串类型"
    end

     -- 构建相对于项目根目录的路径
     local root_path = get_root_path()
     local outputPath = root_path .. "windows/lua_scripts/dynamic/" .. scriptName .. ".lua"
     -- 如果当前环境是 linux 则 将文件复制到 /usr/local/bin/lua_scripts/dynamic/ 目录下
     -- todo linux环境测试
     if package.config:sub(1, 1) == "/" then
         outputPath = "/usr/local/openresty/nginx/lua_scripts/dynamic/" .. scriptName .. ".lua"
     end

    -- 尝试加载脚本内容以验证语法
    local loadSuccess, loadError = load(scriptContent)
    if not loadSuccess then
        return false, "Lua脚本语法错误: " .. tostring(loadError)
    end

    -- 创建并写入文件的辅助函数
    local function writeFile(filePath, content)
        -- 确保目录存在
        local dir_path = filePath:match("(.*[/\\])")
        if dir_path then
            local mkdir_command
            if package.config:sub(1,1) == "/" then
                -- Linux 系统
                mkdir_command = "mkdir -p '" .. dir_path .. "'"
            else
                -- Windows 系统
                dir_path = dir_path:gsub("/", "\\")
                mkdir_command = 'if not exist "' .. dir_path .. '" mkdir "' .. dir_path .. '"'
            end
            os.execute(mkdir_command)
        end

        -- 直接以写入模式打开文件(如果文件存在会覆盖)
        local file, openError = io.open(filePath, "w")
        if not file then
            return false, string.format("无法打开文件: %s, 错误详情: %s", filePath, tostring(openError))
        end

        -- 写入新的脚本内容
        local writeSuccess, writeError = pcall(function()
            file:write(content)
            file:close() 
        end)

        if not writeSuccess then
            return false, string.format("写入文件失败: %s", tostring(writeError))
        end

        return true, "成功"
    end

    -- 写入文件(如果存在会覆盖)
    local success, errMsg = writeFile(outputPath, scriptContent)
    if not success then
        return false, "写入源文件失败" .. errMsg
    end

    return true, nil
end

return M