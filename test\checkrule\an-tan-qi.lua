-- 门板按弹器 异常检测
-- 门板宽度小于250mm，且ATQ不等于0，则认为有错误数据

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local check_rule_result = require("lib.check-rule-result")

local M = {}

-- 定义规则执行入口
function M.dowork(root, target, context)
	local rule_result = check_rule_result.new("按弹器检查", check_rule_result.LEVEL.INFO)
	
	-- 存储结果
	local result = {}
    
    -- 1. 获取所有单元节点
	local xml_parts_p = root:search("/Root/Parts")
    local doors_xml = xml_search.get_door_list(xml_parts_p)

	if not doors_xml or #doors_xml == 0 then
		return rule_result:pass("门板按弹器检测：没有找到任何 MKBQ=3 的门板")
	end

	for _, door_xml in ipairs(doors_xml) do
		local id = door_xml:get_attribute("id")
		local name = door_xml:get_attribute("name")
		local atq = xml_search.get_value_in_parameter(door_xml, "ATQ")
		local w = tonumber(door_xml.W)

		-- 门板宽度小于250mm，且ATQ不等于0，则认为有错误数据
		if atq and w and tonumber(w) < 250 and tonumber(atq) ~= 0 then
			table.insert(result, {
				prompt = string.format("%s 的宽度 %d ≤250mm 不可使用按弹器", name, w),
				related_ids = {id}
			})
		end
	end

    return rule_result:error(result, { "已收集" .. #doors_xml .. "个门板数据  其中ATQ异常的数据有" .. #result .. "个" })
end

return M