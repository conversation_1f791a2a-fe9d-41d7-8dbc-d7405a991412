-- 检测规则通用的返回数据结构
-- 调用 pass or error 方法 返回json格式结果
-- 外部使用方式
-- local check_rule_result = require("lib.check-rule-result")
-- local result = check_rule_result.new("prompt", check_rule_result.LEVEL.INFO)
-- result:pass(logs)
-- result:error(result, logs)


local _M = {}

-- 定义枚举值
_M.LEVEL = {
    ERROR = "error",
    WARNING = "warning",
    INFO = "info",
}

-- 初始化规则结果对象
function _M.new(prompt, level)
    local self = {
        level = level or _M.LEVEL.INFO,
        prompt = prompt or "",
        result = {},
        logs = {},
        success = true
    }
    setmetatable(self, { __index = _M })
    return self
end

-- 检查规则结果完全通过  result一般是nil 返回结果
-- 这里有2种情况 一种是 没有可检查数据 一种是 可检查数据都通过
function _M:pass(logs)
    self.success = true
    if logs then
        self.logs = logs
    end
    return self:to_json()
end

-- 检查规则结果有异常  result一定要有内容 
function _M:error(result_data, logs)
    self.success = false

    if result_data then
        -- 如果result_data是table 并且 长度为0 则认为没有异常
        if type(result_data) == "table" and #result_data == 0 then
            self.success = true
        end
        self.result = result_data
    end
    if logs then
        self.logs = logs
    end
    return self:to_json()
end

-- 获取JSON格式的结果
function _M:to_json()
    return {
        success = self.success,
        level = self.level,
        prompt = self.prompt,
        result = self.result,
        logs = self.logs
    }
end

function _M.check_rule_result(rule_result)
    return rule_result
end

return _M
