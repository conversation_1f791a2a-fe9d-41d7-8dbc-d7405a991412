=encoding utf-8

=head1 NAME

ngx_stream_keyval_module - Module ngx_stream_keyval_module




=head1



The C<ngx_stream_keyval_module> module (1.13.7) creates variables
with values taken from key-value pairs managed by the
L<API|ngx_http_api_module>
or a variable that can also be set with
L<njs|https://github.com/nginx/njs-examples/>.






B<NOTE>

This module is available as part of our
commercial subscription.





=head1 Example Configuration




    
    http {
    
        server {
            ...
            location /api {
                api write=on;
            }
        }
    }
    
    stream {
    
        keyval_zone zone=one:32k state=/var/lib/nginx/state/one.keyval;
        keyval      $ssl_server_name $name zone=one;
    
        server {
            listen              12345 ssl;
            proxy_pass          $name;
            ssl_certificate     /usr/local/nginx/conf/cert.pem;
            ssl_certificate_key /usr/local/nginx/conf/cert.key;
        }
    }






=head1 Directives

=head2 keyval


B<syntax:> keyval I<
    I<C<key>>
    I<C<$variable>>
    C<zone>=I<C<name>>>



B<context:> I<stream>





Creates a new I<C<$variable>> whose value
is looked up by the I<C<key>> in the key-value database.
Matching rules are defined by the
C<type> parameter of the
C<keyval_zone> directive.
The database is stored in a shared memory zone
specified by the C<zone> parameter.







=head2 keyval_zone


B<syntax:> keyval_zone I<
    C<zone>=I<C<name>>:I<C<size>>
    [C<state>=I<C<file>>]
    [C<timeout>=I<C<time>>]
    [C<type>=C<string>E<verbar>C<ip>E<verbar>C<prefix>]
    [C<sync>]>



B<context:> I<stream>





Sets the I<C<name>> and I<C<size>> of the shared memory zone
that keeps the key-value database.
Key-value pairs are managed by the
L<API|ngx_http_api_module>.





The optional C<state> parameter specifies a I<C<file>>
that keeps the current state of the key-value database in the JSON format
and makes it persistent across nginx restarts.
Changing the file content directly should be avoided.





Examples:

    
    keyval_zone zone=one:32k state=/var/lib/nginx/state/one.keyval; # path for Linux
    keyval_zone zone=one:32k state=/var/db/nginx/state/one.keyval;  # path for FreeBSD







The optional C<timeout> parameter (1.15.0) sets
the time after which key-value pairs are removed from the zone.





The optional C<type> parameter (1.17.1) activates
an extra index optimized for matching the key of a certain type
and defines matching rules when evaluating
a keyval C<$variable>.

B<NOTE>

The index is stored in the same shared memory zone
and thus requires additional storage.



=over



=item C<type=string>




default, no index is enabled;
variable lookup is performed using exact match
of the record key and a search key



=item C<type=ip>




the search key is the textual representation of IPv4 or IPv6 address
or CIDR range;
to match a record key, the search key must belong to a subnet
specified by a record key or exactly match an IP address



=item C<type=prefix>




variable lookup is performed using prefix match
of a record key and a search key (1.17.5);
to match a record key, the record key must be a prefix of the search key




=back







The optional C<sync> parameter (1.15.0) enables
L<synchronization|ngx_stream_zone_sync_module>
of the shared memory zone.
The synchronization requires the
C<timeout> parameter to be set.

B<NOTE>

If the synchronization is enabled, removal of key-value pairs (no matter
L<one|ngx_http_api_module>
or
L<all|ngx_http_api_module>)
will be performed only on a target cluster node.
The same key-value pairs on other cluster nodes
will be removed upon C<timeout>.








