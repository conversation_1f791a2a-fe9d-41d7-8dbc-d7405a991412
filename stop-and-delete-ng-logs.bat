cd windows
nginx -s stop

if exist lua_scripts (
    rd /s /q lua_scripts
)

del logs\access.log
del logs\error.log

if exist lualib\resty (
    cd ..
    for /r commons\lualib\ %%i in (*) do (
        if exist windows\lualib\resty\%%~nxi (
            del /q windows\lualib\resty\%%~nxi
        )
    )
    cd windows
)

if exist lualib\xmlua (
    cd ..
    for /r commons\lualib\ %%i in (*) do (
        if exist windows\lualib\%%~nxi (
            del /q windows\lualib\%%~nxi
        )
    )
    rd /s /q windows\lualib\xmlua
    rd /s /q windows\lualib\luacs
    cd windows
)

cd ..
