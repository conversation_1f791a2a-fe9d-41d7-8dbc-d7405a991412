=encoding utf-8



=head1 Name

B<ngx_set_misc> - Various set_xxx directives added to nginx's rewrite module (md5/sha1, sql/json quoting, and many more)

I<This module is not distributed with the Nginx source.> See L<the installation instructions>.


=head1 Version

This document describes ngx_set_misc L<v0.32|https://github.com/openresty/set-misc-nginx-module/tags> released on 19 April 2018.


=head1 Synopsis


     location /foo {
         set $a $arg_a;
         set_if_empty $a 56;
    
         # GET /foo?a=32 will yield $a == 32
         # while GET /foo and GET /foo?a= will
         # yeild $a == 56 here.
     }
    
     location /bar {
         set $foo "hello\n\n'\"\\";
         set_quote_sql_str $foo $foo; # for mysql
    
         # OR in-place editing:
         #   set_quote_sql_str $foo;
    
         # now $foo is: 'hello\n\n\'\"\\'
     }
    
     location /bar {
         set $foo "hello\n\n'\"\\";
         set_quote_pgsql_str $foo;  # for PostgreSQL
    
         # now $foo is: E'hello\n\n\'\"\\'
     }
    
     location /json {
         set $foo "hello\n\n'\"\\";
         set_quote_json_str $foo $foo;
    
         # OR in-place editing:
         #   set_quote_json_str $foo;
    
         # now $foo is: "hello\n\n'\"\\"
     }
    
     location /baz {
         set $foo "hello%20world";
         set_unescape_uri $foo $foo;
    
         # OR in-place editing:
         #   set_unescape_uri $foo;
    
         # now $foo is: hello world
     }
    
     upstream_list universe moon sun earth;
     upstream moon { ... }
     upstream sun { ... }
     upstream earth { ... }
     location /foo {
         set_hashed_upstream $backend universe $arg_id;
         drizzle_pass $backend; # used with ngx_drizzle
     }
    
     location /base32 {
         set $a 'abcde';
         set_encode_base32 $a;
         set_decode_base32 $b $a;
    
         # now $a == 'c5h66p35' and
         # $b == 'abcde'
     }
    
     location /base64 {
         set $a 'abcde';
         set_encode_base64 $a;
         set_decode_base64 $b $a;
    
         # now $a == 'YWJjZGU=' and
         # $b == 'abcde'
     }
    
     location /hex {
         set $a 'abcde';
         set_encode_hex $a;
         set_decode_hex $b $a;
    
         # now $a == '6162636465' and
         # $b == 'abcde'
     }
    
     # GET /sha1 yields the output
     #   aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d
     location /sha1 {
         set_sha1 $a hello;
         echo $a;
     }
    
     # ditto
     location /sha1 {
         set $a hello;
         set_sha1 $a;
         echo $a;
     }
    
     # GET /today yields the date of today in local time using format 'yyyy-mm-dd'
     location /today {
         set_local_today $today;
         echo $today;
     }
    
     # GET /signature yields the hmac-sha-1 signature
     # given a secret and a string to sign
     # this example yields the base64 encoded singature which is
     # "HkADYytcoQQzqbjQX33k/ZBB/DQ="
     location /signature {
         set $secret_key 'secret-key';
         set $string_to_sign "some-string-to-sign";
         set_hmac_sha1 $signature $secret_key $string_to_sign;
         set_encode_base64 $signature $signature;
         echo $signature;
     }
    
     location = /rand {
         set $from 3;
         set $to 15;
         set_random $rand $from $to;
    
         # or write directly
         #   set_random $rand 3 15;
    
         echo $rand;  # will print a random integer in the range [3, 15]
     }


=head1 Description

This module extends the standard HttpRewriteModule's directive set to provide more functionalities like URI escaping and unescaping, JSON quoting, Hexadecimal/MD5/SHA1/Base32/Base64 digest encoding and decoding, random number generator, and more!

Every directive provided by this module can be mixed freely with other L<ngx_http_rewrite_module|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html>'s directives, like L<if|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html#if> and L<set|http://nginx.org/en/docs/http/ngx_http_rewrite_module.html#set>. (Thanks to the L<Nginx Devel Kit|https://github.com/simpl/ngx_devel_kit>!)




=head1 Directives




=head2 set_if_empty

B<syntax:> I<set_if_empty $dst E<lt>srcE<gt>>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

Assign the value of the argument C<< <src> >> if and only if variable C<$dst> is empty (i.e., not found or has an empty string value).

In the following example,


     set $a 32;
     set_if_empty $a 56;

the variable C<$dst> will take the value 32 at last. But in the sample


     set $a '';
     set $value "hello, world"
     set_if_empty $a $value;

C<$a> will take the value C<"hello, world"> at last.




=head2 set_quote_sql_str

B<syntax:> I<set_quote_sql_str $dst E<lt>srcE<gt>>

B<syntax:> I<set_quote_sql_str $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

When taking two arguments, this directive will quote the value of the second argument C<< <src> >> by MySQL's string value quoting rule and assign the result into the first argument, variable C<$dst>. For example,


     location /test {
         set $value "hello\n\r'\"\\";
         set_quote_sql_str $quoted $value;
    
         echo $quoted;
     }

Then request C<GET /test> will yield the following output


     'hello\n\r\'\"\\'

Please note that we're using L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>'s L<echo directive|http://github.com/openresty/echo-nginx-module#echo> here to output values of nginx variables directly.

When taking a single argument, this directive will do in-place modification of the argument variable. For example,


     location /test {
         set $value "hello\n\r'\"\\";
         set_quote_sql_str $value;
    
         echo $value;
     }

then request C<GET /test> will give exactly the same output as the previous example.

This directive is usually used to prevent SQL injection.

This directive can be invoked by L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>'s L<ndk.set_var.DIRECTIVE|http://github.com/openresty/lua-nginx-module#ndkset_vardirective> interface and L<array-var-nginx-module|http://github.com/openresty/array-var-nginx-module>'s L<array_map_op|http://github.com/openresty/array-var-nginx-module#array_map_op> directive.




=head2 set_quote_pgsql_str

B<syntax:> I<set_quote_pgsql_str $dst E<lt>srcE<gt>>

B<syntax:> I<set_quote_pgsql_str $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

Very much like L<set_quote_sql_str>, but with PostgreSQL quoting rules for SQL string literals.




=head2 set_quote_json_str

B<syntax:> I<set_quote_json_str $dst E<lt>srcE<gt>>

B<syntax:> I<set_quote_json_str $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

When taking two arguments, this directive will quote the value of the second argument C<< <src> >> by JSON string value quoting rule and assign the result into the first argument, variable C<$dst>. For example,


     location /test {
         set $value "hello\n\r'\"\\";
         set_quote_json_str $quoted $value;
    
         echo $quoted;
     }

Then request C<GET /test> will yield the following output


     "hello\n\r'\"\\"

Please note that we're using L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>'s L<echo directive|http://github.com/openresty/echo-nginx-module#echo> here to output values of nginx variables directly.

When taking a single argument, this directive will do in-place modification of the argument variable. For example,


     location /test {
         set $value "hello\n\r'\"\\";
         set_quote_json_str $value;
    
         echo $value;
     }

then request C<GET /test> will give exactly the same output as the previous example.

This directive can be invoked by L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>'s L<ndk.set_var.DIRECTIVE|http://github.com/openresty/lua-nginx-module#ndkset_vardirective> interface and L<array-var-nginx-module|http://github.com/openresty/array-var-nginx-module>'s L<array_map_op|http://github.com/openresty/array-var-nginx-module#array_map_op> directive.




=head2 set_unescape_uri

B<syntax:> I<set_unescape_uri $dst E<lt>srcE<gt>>

B<syntax:> I<set_unescape_uri $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

When taking two arguments, this directive will unescape the value of the second argument C<< <src> >> as a URI component and assign the result into the first argument, variable C<$dst>. For example,


     location /test {
         set_unescape_uri $key $arg_key;
         echo $key;
     }

Then request C<GET /test?key=hello+world%21> will yield the following output


    hello world!

The nginx standard L<$arg_PARAMETER|http://nginx.org/en/docs/http/ngx_http_core_module.html#var_arg_> variable holds the raw (escaped) value of the URI parameter. So we need the C<set_unescape_uri> directive to unescape it first.

Please note that we're using L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>'s L<echo directive|http://github.com/openresty/echo-nginx-module#echo> here to output values of nginx variables directly.

When taking a single argument, this directive will do in-place modification of the argument variable. For example,


     location /test {
         set $key $arg_key;
         set_unescape_uri $key;
    
         echo $key;
     }

then request C<GET /test?key=hello+world%21> will give exactly the same output as the previous example.

This directive can be invoked by L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>'s L<ndk.set_var.DIRECTIVE|http://github.com/openresty/lua-nginx-module#ndkset_vardirective> interface and L<array-var-nginx-module|http://github.com/openresty/array-var-nginx-module>'s L<array_map_op|http://github.com/openresty/array-var-nginx-module#array_map_op> directive.




=head2 set_escape_uri

B<syntax:> I<set_escape_uri $dst E<lt>srcE<gt>>

B<syntax:> I<set_escape_uri $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

Very much like the L<set_unescape_uri> directive, but does the conversion the other way around, i.e., URL component escaping.




=head2 set_hashed_upstream

B<syntax:> I<set_hashed_upstream $dst E<lt>upstream_list_nameE<gt> E<lt>srcE<gt>>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

Hashes the string argument C<< <src> >> into one of the upstream name included in the upstream list named C<< <upstream_list_name> >>. The hash function being used is simple modulo.

Here's an example,


     upstream moon { ... }
     upstream sun { ... }
     upstream earth { ... }
    
     upstream_list universe moon sun earth;
    
     location /test {
         set_unescape_uri $key $arg_key;
         set $list_name universe;
         set_hashed_upstream $backend $list_name $key;
    
         echo $backend;
     }

Then C<GET /test?key=blah> will output either "moon", "sun", or "earth", depending on the actual value of the C<key> query argument.

This directive is usually used to compute an nginx variable to be passed to L<memc-nginx-module|http://github.com/openresty/memc-nginx-module>'s L<memc_pass|http://github.com/openresty/memc-nginx-module#memc_pass> directive, L<redis2-nginx-module|http://github.com/openresty/redis2-nginx-module>'s [[HttpRedis2Module#redis2_pass]] directive, and L<ngx_http_proxy_module|http://nginx.org/en/docs/http/ngx_http_proxy_module.html>'s L<proxy_pass|http://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_pass> directive, among others.




=head2 set_encode_base32

B<syntax:> I<set_encode_base32 $dst E<lt>srcE<gt>>

B<syntax:> I<set_encode_base32 $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

When taking two arguments, this directive will encode the value of the second argument C<< <src> >> to its base32(hex) digest and assign the result into the first argument, variable C<$dst>. For example,


     location /test {
         set $raw "abcde";
         set_encode_base32 $digest $raw;
    
         echo $digest;
     }

Then request C<GET /test> will yield the following output



Please note that we're using L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>'s L<echo directive|http://github.com/openresty/echo-nginx-module#echo> here to output values of nginx variables directly.

RFC forces the C<[A-Z2-7]> RFC-3548 compliant encoding, but we are using the "base32hex" encoding (C<[0-9a-v]>) by default. The L<set_base32_alphabet> directive (first introduced in C<v0.28>) allows you to change the alphabet used for encoding/decoding so RFC-3548 compliant encoding is still possible by custom configurations.

By default, the C<=> character is used to pad the left-over bytes due to alignment. But the padding behavior can be completely disabled by setting L<set_base32_padding> C<off>.

When taking a single argument, this directive will do in-place modification of the argument variable. For example,


     location /test {
         set $value "abcde";
         set_encode_base32 $value;
    
         echo $value;
     }

then request C<GET /test> will give exactly the same output as the previous example.

This directive can be invoked by L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>'s L<ndk.set_var.DIRECTIVE|http://github.com/openresty/lua-nginx-module#ndkset_vardirective> interface and L<array-var-nginx-module|http://github.com/openresty/array-var-nginx-module>'s L<array_map_op|http://github.com/openresty/array-var-nginx-module#array_map_op> directive.




=head2 set_base32_padding

B<syntax:> I<set_base32_padding on|off>

B<default:> I<on>

B<context:> I<http, server, server if, location, location if>

B<phase:> I<no>

This directive can control whether to pad left-over bytes with the "=" character when encoding a base32 digest by the
L<set_encode_base32> directive.

This directive was first introduced in C<v0.28>. If you use earlier versions of this module, then you should use L<set_misc_base32_padding> instead.




=head2 set_misc_base32_padding

B<syntax:> I<set_misc_base32_padding on|off>

B<default:> I<on>

B<context:> I<http, server, server if, location, location if>

B<phase:> I<no>

This directive has been deprecated since C<v0.28>. Use L<set_base32_padding> instead if you are using C<v0.28+>.




=head2 set_base32_alphabet

B<syntax:> I<set_base32_alphabet E<lt>alphabetE<gt>>

B<default:> I<"0123456789abcdefghijklmnopqrstuv">

B<context:> I<http, server, server if, location, location if>

B<phase:> I<no>

This directive controls the alphabet used for encoding/decoding a base32 digest. It accepts a string containing the desired alphabet like "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567" for standard alphabet.

Extended (base32hex) alphabet is used by default.

This directive was first introduced in C<v0.28>.




=head2 set_decode_base32

B<syntax:> I<set_decode_base32 $dst E<lt>srcE<gt>>

B<syntax:> I<set_decode_base32 $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

Similar to the L<set_encode_base32> directive, but does exactly the opposite operation, .i.e, decoding a base32(hex) digest into its original form.




=head2 set_encode_base64

B<syntax:> I<set_encode_base64 $dst E<lt>srcE<gt>>

B<syntax:> I<set_encode_base64 $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

When taking two arguments, this directive will encode the value of the second argument C<< <src> >> to its base64 digest and assign the result into the first argument, variable C<$dst>. For example,


     location /test {
         set $raw "abcde";
         set_encode_base64 $digest $raw;
    
         echo $digest;
     }

Then request C<GET /test> will yield the following output


    YWJjZGU=

Please note that we're using L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>'s L<echo directive|http://github.com/openresty/echo-nginx-module#echo> here to output values of nginx variables directly.

When taking a single argument, this directive will do in-place modification of the argument variable. For example,


     location /test {
         set $value "abcde";
         set_encode_base64 $value;
    
         echo $value;
     }

then request C<GET /test> will give exactly the same output as the previous example.

This directive can be invoked by L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>'s L<ndk.set_var.DIRECTIVE|http://github.com/openresty/lua-nginx-module#ndkset_vardirective> interface and L<array-var-nginx-module|http://github.com/openresty/array-var-nginx-module>'s L<array_map_op|http://github.com/openresty/array-var-nginx-module#array_map_op> directive.




=head2 set_encode_base64url

B<syntax:> I<set_encode_base64url $dst E<lt>srcE<gt>>

B<syntax:> I<set_encode_base64url $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

When taking two arguments, this directive will encode the value of the second argument C<< <src> >> to its base64 url safe digest and assign the result into the first argument, variable C<$dst>. For example,


     location /test {
         set $raw "abcde";
         set_encode_base64url $digest $raw;
    
         echo $digest;
     }

Then request C<GET /test> will yield the following output


    YWJjZGU=

Please note that we're using L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>'s L<echo directive|http://github.com/openresty/echo-nginx-module#echo> here to output values of nginx variables directly.

When taking a single argument, this directive will do in-place modification of the argument variable. For example,


     location /test {
         set $value "abcde";
         set_encode_base64url $value;
    
         echo $value;
     }

then request C<GET /test> will give exactly the same output as the previous example.

This directive can be invoked by L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>'s L<ndk.set_var.DIRECTIVE|http://github.com/openresty/lua-nginx-module#ndkset_vardirective> interface and L<array-var-nginx-module|http://github.com/openresty/array-var-nginx-module>'s L<array_map_op|http://github.com/openresty/array-var-nginx-module#array_map_op> directive.




=head2 set_decode_base64

B<syntax:> I<set_decode_base64 $dst E<lt>srcE<gt>>

B<syntax:> I<set_decode_base64 $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

Similar to the L<set_encode_base64> directive, but does exactly the opposite operation, .i.e, decoding a base64 digest into its original form.




=head2 set_decode_base64url

B<syntax:> I<set_decode_base64url $dst E<lt>srcE<gt>>

B<syntax:> I<set_decode_base64url $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

Similar to the L<set_encode_base64url> directive, but does exactly the the opposite operation, .i.e, decoding a base64 url safe digest into its original form.




=head2 set_encode_hex

B<syntax:> I<set_encode_hex $dst E<lt>srcE<gt>>

B<syntax:> I<set_encode_hex $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

When taking two arguments, this directive will encode the value of the second argument C<< <src> >> to its hexadecimal digest and assign the result into the first argument, variable C<$dst>. For example,


     location /test {
         set $raw "章亦春";
         set_encode_hex $digest $raw;
    
         echo $digest;
     }

Then request C<GET /test> will yield the following output



Please note that we're using L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>'s L<echo directive|http://github.com/openresty/echo-nginx-module#echo> here to output values of nginx variables directly.

When taking a single argument, this directive will do in-place modification of the argument variable. For example,


     location /test {
         set $value "章亦春";
         set_encode_hex $value;
    
         echo $value;
     }

then request C<GET /test> will give exactly the same output as the previous example.

This directive can be invoked by L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>'s L<ndk.set_var.DIRECTIVE|http://github.com/openresty/lua-nginx-module#ndkset_vardirective> interface and L<array-var-nginx-module|http://github.com/openresty/array-var-nginx-module>'s L<array_map_op|http://github.com/openresty/array-var-nginx-module#array_map_op> directive.




=head2 set_decode_hex

B<syntax:> I<set_decode_hex $dst E<lt>srcE<gt>>

B<syntax:> I<set_decode_hex $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

Similar to the L<set_encode_hex> directive, but does exactly the opposite operation, .i.e, decoding a hexadecimal digest into its original form.




=head2 set_sha1

B<syntax:> I<set_sha1 $dst E<lt>srcE<gt>>

B<syntax:> I<set_sha1 $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

When taking two arguments, this directive will encode the value of the second argument C<< <src> >> to its L<SHA-1|http://en.wikipedia.org/wiki/SHA-1> digest and assign the result into the first argument, variable C<$dst>. The hexadecimal form of the C<SHA-1> digest will be generated automatically, use L<set_decode_hex> to decode the result if you want the binary form of the C<SHA-1> digest.

For example,


     location /test {
         set $raw "hello";
         set_sha1 $digest $raw;
    
         echo $digest;
     }

Then request C<GET /test> will yield the following output



Please note that we're using L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>'s L<echo directive|http://github.com/openresty/echo-nginx-module#echo> here to output values of nginx variables directly.

When taking a single argument, this directive will do in-place modification of the argument variable. For example,


     location /test {
         set $value "hello";
         set_sha1 $value;
    
         echo $value;
     }

then request C<GET /test> will give exactly the same output as the previous example.

This directive can be invoked by L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>'s L<ndk.set_var.DIRECTIVE|http://github.com/openresty/lua-nginx-module#ndkset_vardirective> interface and L<array-var-nginx-module|http://github.com/openresty/array-var-nginx-module>'s L<array_map_op|http://github.com/openresty/array-var-nginx-module#array_map_op> directive.




=head2 set_md5

B<syntax:> I<set_md5 $dst E<lt>srcE<gt>>

B<syntax:> I<set_md5 $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

B<category:> I<ndk_set_var_value>

When taking two arguments, this directive will encode the value of the second argument C<< <src> >> to its L<MD5|http://en.wikipedia.org/wiki/MD5> digest and assign the result into the first argument, variable C<$dst>. The hexadecimal form of the C<MD5> digest will be generated automatically, use L<set_decode_hex> to decode the result if you want the binary form of the C<MD5> digest.

For example,


     location /test {
         set $raw "hello";
         set_md5 $digest $raw;
    
         echo $digest;
     }

Then request C<GET /test> will yield the following output

    5d41402abc4b2a76b9719d911017c592

Please note that we're using L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>'s L<echo directive|http://github.com/openresty/echo-nginx-module#echo> here to output values of nginx variables directly.

When taking a single argument, this directive will do in-place modification of the argument variable. For example,


     location /test {
         set $value "hello";
         set_md5 $value;
    
         echo $value;
     }

then request C<GET /test> will give exactly the same output as the previous example.

This directive can be invoked by L<lua-nginx-module|http://github.com/openresty/lua-nginx-module>'s L<ndk.set_var.DIRECTIVE|http://github.com/openresty/lua-nginx-module#ndkset_vardirective> interface and L<array-var-nginx-module|http://github.com/openresty/array-var-nginx-module>'s L<array_map_op|http://github.com/openresty/array-var-nginx-module#array_map_op> directive.




=head2 set_hmac_sha1

B<syntax:> I<set_hmac_sha1 $dst E<lt>secret_keyE<gt> E<lt>srcE<gt>>

B<syntax:> I<set_hmac_sha1 $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

Computes the L<HMAC-SHA1|http://en.wikipedia.org/wiki/HMAC> digest of the argument C<< <src> >> and assigns the result into the argument variable C<$dst> with the secret key C<< <secret_key> >>.

The raw binary form of the C<HMAC-SHA1> digest will be generated, use L<set_encode_base64>, for example, to encode the result to a textual representation if desired.

For example,


     location /test {
         set $secret 'thisisverysecretstuff';
         set $string_to_sign 'some string we want to sign';
         set_hmac_sha1 $signature $secret $string_to_sign;
         set_encode_base64 $signature $signature;
         echo $signature;
     }

Then request C<GET /test> will yield the following output


    R/pvxzHC4NLtj7S+kXFg/NePTmk=

Please note that we're using L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>'s L<echo directive|http://github.com/openresty/echo-nginx-module#echo> here to output values of nginx variables directly.

This directive requires the OpenSSL library enabled in your Nginx build (usually by passing the C<--with-http_ssl_module> option to the C<./configure> script).




=head2 set_hmac_sha256

B<syntax:> I<set_hmac_sha256 $dst E<lt>secret_keyE<gt> E<lt>srcE<gt>>

B<syntax:> I<set_hmac_sha256 $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

Computes the L<HMAC-SHA256|http://en.wikipedia.org/wiki/HMAC> digest of the argument C<< <src> >> and assigns the result into the argument variable C<$dst> with the secret key C<< <secret_key> >>.

The raw binary form of the C<HMAC-SHA256> digest will be generated, use L<set_encode_base64>, for example, to encode the result to a textual representation if desired.

For example,


     location /test {
         set $secret 'thisisverysecretstuff';
         set $string_to_sign 'some string we want to sign';
         set_hmac_sha256 $signature $secret $string_to_sign;
         set_encode_base64 $signature $signature;
         echo $signature;
     }

Then request C<GET /test> will yield the following output


    4pU3GRQrKKIoeLb9CqYsavHE2l6Hx+KMmRmesU+Cfrs=

Please note that we're using L<echo-nginx-module|http://github.com/openresty/echo-nginx-module>'s L<echo directive|http://github.com/openresty/echo-nginx-module#echo> here to output values of nginx variables directly.

This directive requires the OpenSSL library enabled in your Nginx build (usually by passing the C<--with-http_ssl_module> option to the C<./configure> script).




=head2 set_random

B<syntax:> I<set_random $res E<lt>fromE<gt> E<lt>toE<gt>>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

Generates a (pseudo) random number (in textual form) within the range C<< [<$from>, <$to>] >> (inclusive).

Only non-negative numbers are allowed for the C<< <from> >> and C<< <to> >> arguments.

When C<< <from> >> is greater than C<< <to> >>, their values will be exchanged accordingly.

For instance,


     location /test {
         set $from 5;
         set $to 7;
         set_random $res $from $to;
    
         echo $res;
     }

then request C<GET /test> will output a number between 5 and 7 (i.e., among 5, 6, 7).

For now, there's no way to configure a custom random generator seed.

Behind the scene, it makes use of the standard C function C<rand()>.

This directive was first introduced in the C<v0.22rc1> release.

See also L<set_secure_random_alphanum> and L<set_secure_random_lcalpha>.




=head2 set_secure_random_alphanum

B<syntax:> I<set_secure_random_alphanum $res E<lt>lengthE<gt>>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

Generates a cryptographically-strong random string C<< <length> >> characters long with the alphabet C<[a-zA-Z0-9]>.

C<< <length> >> may be between 1 and 64, inclusive.

For instance,


     location /test {
         set_secure_random_alphanum $res 32;
    
         echo $res;
     }

then request C<GET /test> will output a string like C<ivVVRP2DGaAqDmdf3Rv4ZDJ7k0gOfASz>.

This functionality depends on the presence of the C</dev/urandom> device, available on most UNIX-like systems.

See also L<set_secure_random_lcalpha> and L<set_random>.

This directive was first introduced in the C<v0.22rc8> release.




=head2 set_secure_random_lcalpha

B<syntax:> I<set_secure_random_lcalpha $res E<lt>lengthE<gt>>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

Generates a cryptographically-strong random string C<< <length> >> characters long with the alphabet C<[a-z]>.

C<< <length> >> may be between 1 and 64, inclusive.

For instance,


     location /test {
         set_secure_random_lcalpha $res 32;
    
         echo $res;
     }

then request C<GET /test> will output a string like C<kcuxcddktffsippuekhshdaclaquiusj>.

This functionality depends on the presence of the C</dev/urandom> device, available on most UNIX-like systems.

This directive was first introduced in the C<v0.22rc8> release.

See also L<set_secure_random_alphanum> and L<set_random>.




=head2 set_rotate

B<syntax:> I<set_rotate $value E<lt>fromE<gt> E<lt>toE<gt>>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

Increments C<$value> but keeps it in range from C<< <from> >> to C<< <to> >>. 
If C<$value> is greater than C<< <to> >> or less than C<< <from> >> is will be 
set to C<< <from> >> value.

The current value after running this directive will always be saved on a per-location basis. And the this saved value will be used for incrementation when the C<$value> is not initialized or has a bad value.

Only non-negative numbers are allowed for the C<< <from> >> and C<< <to> >> arguments.

When C<< <from> >> is greater than C<< <to> >>, their values will be exchanged accordingly.

For instance,


     location /rotate {
         default_type text/plain;
         set $counter $cookie_counter;
         set_rotate $counter 1 5;
         echo $counter;
         add_header Set-Cookie counter=$counter;
     }

then request C<GET /rotate> will output next number between 1 and 5 (i.e., 1, 2, 3, 4, 5) on each
refresh of the page. This directive may be userful for banner rotation purposes.

Another example is to use server-side value persistence to do simple round-robin:


     location /rotate {
         default_type text/plain;
         set_rotate $counter 0 3;
         echo $counter;
     }

And accessing C</rotate> will also output integer sequence 0, 1, 2, 3, 0, 1, 2, 3, and so on.

This directive was first introduced in the C<v0.22rc7> release.




=head2 set_local_today

B<syntax:> I<set_local_today $dst>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

Set today's date ("yyyy-mm-dd") in localtime to the argument variable C<$dst>.

Here's an example,


     location /today {
         set_local_today $today;
         echo $today;
     }

then request C<GET /today> will output something like


    2011-08-16

and year, the actual date you get here will vary every day ;)

Behind the scene, this directive utilizes the C<ngx_time> API in the Nginx core, so usually no syscall is involved due to the time caching mechanism in the Nginx core.




=head2 set_formatted_gmt_time

B<syntax:> I<set_formatted_gmt_time $res E<lt>time-formatE<gt>>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

Set a formatted GMT time to variable C<$res> (as the first argument) using the format string in the second argument.

All the conversion specification notations in the standard C function C<strftime> are supported, like C<%Y> (for 4-digit years) and C<%M> (for minutes in decimal). See E<lt>http://linux.die.net/man/3/strftimeE<gt> for a complete list of conversion specification symbols.

Below is an example:


     location = /t {
         set_formatted_gmt_time $timestr "%a %b %e %H:%M:%S %Y GMT";
         echo $timestr;
     }

Accessing C</t> yields the output


    Fri Dec 13 15:34:37 2013 GMT

This directive was first added in the C<0.23> release.

See also L<set_formatted_local_time>.




=head2 set_formatted_local_time

B<syntax:> I<set_formatted_local_time $res E<lt>time-formatE<gt>>

B<default:> I<no>

B<context:> I<location, location if>

B<phase:> I<rewrite>

Set a formatted local time to variable C<$res> (as the first argument) using the format string in the second argument.

All the conversion specification notations in the standard C function C<strftime> are supported, like C<%Y> (for 4-digit years) and C<%M> (for minutes in decimal). See E<lt>http://linux.die.net/man/3/strftimeE<gt> for a complete list of conversion specification symbols.

Below is an example:


     location = /t {
         set_formatted_local_time $timestr "%a %b %e %H:%M:%S %Y %Z";
         echo $timestr;
     }

Accessing C</t> yields the output


    Fri Dec 13 15:42:15 2013 PST

This directive was first added in the C<0.23> release.

See also L<set_formatted_gmt_time>.




=head1 Caveats

Do not use L<$arg_PARAMETER|http://nginx.org/en/docs/http/ngx_http_core_module.html#var_arg_>, L<$cookie_COOKIE|http://nginx.org/en/docs/http/ngx_http_core_module.html#var_cookie_>, L<$http_HEADER|http://nginx.org/en/docs/http/ngx_http_core_module.html#var_http_> or other special variables defined in the Nginx core module as the target variable in this module's directives. For instance,


     set_if_empty $arg_user 'foo';  # DO NOT USE THIS!

may lead to segmentation faults.




=head1 Installation

This module is included and enabled by default in the L<OpenResty bundle|http://openresty.org>. If you want to install this module manually with your own Nginx source tarball, then follow the steps below:

Grab the nginx source code from L<nginx.org|http://nginx.org/>, for example,
the version 1.13.6 (see L<nginx compatibility>), and then build the source with this module:


     wget 'http://nginx.org/download/nginx-1.13.6.tar.gz'
     tar -xzvf nginx-1.13.6.tar.gz
     cd nginx-1.13.6/
    
     # Here we assume you would install you nginx under /opt/nginx/.
     ./configure --prefix=/opt/nginx \
         --with-http_ssl_module \
         --add-module=/path/to/ngx_devel_kit \
         --add-module=/path/to/set-misc-nginx-module
    
     make -j2
     make install

Download the latest version of the release tarball of this module from L<set-misc-nginx-module file list|http://github.com/openresty/set-misc-nginx-module/tags>, and the latest tarball for L<ngx_devel_kit|https://github.com/simplresty/ngx_devel_kit> from its L<file list|https://github.com/simplresty/ngx_devel_kit/tags>.




=head2 Building as a dynamic module

Starting from NGINX 1.9.11, you can also compile this module as a dynamic module, by using the C<--add-dynamic-module=PATH> option instead of C<--add-module=PATH> on the
C<./configure> command line above. And then you can explicitly load the module in your C<nginx.conf> via the L<load_module|http://nginx.org/en/docs/ngx_core_module.html#load_module>
directive, for example,


    load_module /path/to/modules/ndk_http_module.so;  # assuming NDK is built as a dynamic module too
    load_module /path/to/modules/ngx_http_set_misc_module.so;

Also, this module is included and enabled by default in the L<OpenResty bundle|http://openresty.org/>.




=head1 Compatibility

The following versions of Nginx should work with this module:


=over


=item *

B<1.13.x>                      (last tested: 1.13.6)

=item *

B<1.12.x>

=item *

B<1.11.x>                      (last tested: 1.11.2)

=item *

B<1.10.x>

=item *

B<1.9.x>                       (last tested: 1.9.15)

=item *

B<1.8.x>

=item *

B<1.7.x>                       (last tested: 1.7.10)

=item *

B<1.6.x>

=item *

B<1.5.x>                       (last tested: 1.5.8)

=item *

B<1.4.x>                       (last tested: 1.4.4)

=item *

B<1.2.x>                       (last tested: 1.2.9)

=item *

B<1.1.x>                       (last tested: 1.1.5)

=item *

B<1.0.x>                       (last tested: 1.0.15)

=item *

B<0.9.x>                       (last tested: 0.9.4)

=item *

B<0.8.x>                       (last tested: 0.8.54)

=item *

B<0.7.x E<gt>= 0.7.46>             (last tested: 0.7.68)


=back

If you find that any particular version of Nginx above 0.7.46 does not work with this module, please consider L<reporting a bug>.




=head1 Report Bugs

Although a lot of effort has been put into testing and code tuning, there must be some serious bugs lurking somewhere in this module. So whenever you are bitten by any quirks, please don't hesitate to


=over


=item 1.

send a bug report or even patches to the L<openresty-en mailing list|https://groups.google.com/group/openresty-en>,

=item 2.

or create a ticket on the L<issue tracking interface|http://github.com/openresty/set-misc-nginx-module/issues> provided by GitHub.


=back




=head1 Source Repository

Available on github at L<openrestyE<sol>set-misc-nginx-module|http://github.com/openresty/set-misc-nginx-module>.




=head1 Changes

The change logs for every release of this module can be obtained from the OpenResty bundle's change logs:

E<lt>http://openresty.org/#ChangesE<gt>




=head1 Test Suite

This module comes with a Perl-driven test suite. The L<test cases|http://github.com/openresty/set-misc-nginx-module/tree/master/t/> are
L<declarative|http://github.com/openresty/set-misc-nginx-module/blob/master/t/escape-uri.t> too. Thanks to the L<Test::Nginx|http://search.cpan.org/perldoc?Test::Nginx> module in the Perl world.

To run it on your side:


     $ PATH=/path/to/your/nginx-with-set-misc-module:$PATH prove -r t

You need to terminate any Nginx processes before running the test suite if you have changed the Nginx server binary.

Because a single nginx server (by default, C<localhost:1984>) is used across all the test scripts (C<.t> files), it's meaningless to run the test suite in parallel by specifying C<-jN> when invoking the C<prove> utility.




=head1 Getting involved

You'll be very welcomed to submit patches to the L<author> or just ask for a commit bit to the L<source repository> on GitHub.




=head1 Author

Yichun Zhang (agentzh) I<E<lt><EMAIL><gt>>, OpenResty Inc.

This wiki page is also maintained by the author himself, and everybody is encouraged to improve this page as well.




=head1 Copyright & License

Copyright (C) 2009-2018, Yichun Zhang (章亦春) E<lt><EMAIL><gt>, OpenResty Inc.

This module is licensed under the terms of the BSD license.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:


=over


=item *

Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.

=item *

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED
TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

L<Nginx Devel Kit|https://github.com/simpl/ngx_devel_kit>

=item *

L<The OpenResty bundle|http://openresty.org>


=back



