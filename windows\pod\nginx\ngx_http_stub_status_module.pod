=encoding utf-8

=head1 NAME

ngx_http_stub_status_module - Module ngx_http_stub_status_module




=head1



The C<ngx_http_stub_status_module> module provides
access to basic status information.





This module is not built by default, it should be enabled with the
C<--with-http_stub_status_module>
configuration parameter.




=head1 Example Configuration




    
    location = /basic_status {
        stub_status;
    }


This configuration creates a simple web page
with basic status data which may look like as follows:

    
    Active connections: 291
    server accepts handled requests
     16630948 16630948 31070465
    Reading: 6 Writing: 179 Waiting: 106






=head1 Directives

=head2 stub_status




B<context:> I<server>


B<context:> I<location>





The basic status information will be accessible from the surrounding location.






B<NOTE>

In versions prior to 1.7.5,
the directive syntax required an arbitrary argument, for example,
“C<stub_status on>”.








=head1 Data



The following status information is provided:

=over



=item C<Active connections>




The current number of active client connections
including C<Waiting> connections.



=item C<accepts>




The total number of accepted client connections.



=item C<handled>




The total number of handled connections.
Generally, the parameter value is the same as C<accepts>
unless some resource limits have been reached
(for example, the
L<ngx_core_module> limit).



=item C<requests>




The total number of client requests.



=item C<Reading>




The current number of connections where nginx is reading the request header.



=item C<Writing>




The current number of connections
where nginx is writing the response back to the client.



=item C<Waiting>




The current number of idle client connections waiting for a request.




=back






=head1 Embedded Variables



The C<ngx_http_stub_status_module> module
supports the following embedded variables (1.3.14):

=over



=item C<$connections_active>




same as the C<Active connections> value;



=item C<$connections_reading>




same as the C<Reading> value;



=item C<$connections_writing>




same as the C<Writing> value;



=item C<$connections_waiting>




same as the C<Waiting> value.




=back






