-- 获取 xml下层所有Part节点中 满足kvs_array 的节点
-- 当match_type == "attribute" 时，查找的是当前part的属性
-- 当match_type ~= "attribute" 时，则识别成子节点 节点名 查找该节点名的所有节点 匹配满足的kvs的节点
-- 如果有多个  会全部返回

-- XML part节点搜索函数模块
local core = require("lib.xml-part-core")

local M = {}

-- 函数：通过key-value数组在XML中搜索匹配项
function M.get_xmllist_bykvs_in_part(xml, match_type, kvs_array)
    -- 参数校验
    if not xml or type(kvs_array) ~= "table" or #kvs_array == 0 then
        return nil, "get_xmllist_bykvs_in_part: invalid parameters"
    end

    -- 获取指定xml节点下所有层级的Part节点（包括子节点和孙节点等）
    local parts = xml:search(".//Part")
    if not parts or #parts == 0 then
        return nil, "get_xmllist_bykvs_in_part: no part nodes found"
    end

    -- 存储匹配的节点
    local matched_nodes = {}

    -- 遍历每个part节点
    for _, part in ipairs(parts) do
        if match_type == "attribute" then
            -- 如果是属性匹配，直接检查part节点
            if core.check_node_matches(part, match_type, kvs_array) then
                table.insert(matched_nodes, part)
            end
        else
            -- 如果是子节点匹配，先获取子节点再检查
            local child_nodes = part:search("./" .. match_type)
            if child_nodes and #child_nodes > 0 then
                for _, child in ipairs(child_nodes) do
                    if core.check_node_matches(child, "attribute", kvs_array) then
                        -- 存放子节点
                        table.insert(matched_nodes, child)
                    end
                end
            end
        end
    end

    -- 返回匹配的节点数组
    return matched_nodes, nil
end

function M.get_child_match_kvs(xml_nodes, kvs_array)
    local findTarget = false
    for _, child in ipairs(xml_nodes) do
        local failed_match = true
        for index, value in ipairs(kvs_array) do
            local child_value = tostring(child:get_attribute(value.key))
            local tar_value = tostring(value.value)
            if child_value ~= tar_value then
                failed_match = false
                break
            end
            -- 任意一个child 全部属性的 kv 都命中 说明 这个就是我们要找的节点 因为返回的是父级Part 因此可以直接break
            if failed_match then
                findTarget = true
                break
            end
        end         
    end
    return findTarget
end

-- 函数：筛选 满足所有 key-value数组 返回满足这个节点的 父级Part节点
function M.get_part_xmllist_bykvs_in_part(xml, match_type, kvs_array)
    -- 参数校验
    if not xml or type(kvs_array) ~= "table" or #kvs_array == 0 then
        return nil, "get_xmllist_bykvs_in_part: invalid parameters"
    end

    -- 获取指定xml节点下所有层级的Part节点（包括子节点和孙节点等）
    local parts = xml:search(".//Part")
    if not parts or #parts == 0 then
        return nil, "get_xmllist_bykvs_in_part: no part nodes found"
    end

    -- 存储匹配的节点
    local matched_nodes = {}

    -- 遍历每个part节点
    for _, part in ipairs(parts) do
        if match_type == "attribute" then
            -- 如果是属性匹配，直接检查part节点
            if M.get_child_match_kvs({part}, kvs_array) then
                table.insert(matched_nodes, part)
            end
        else
            -- 如果是子节点匹配，先获取子节点再检查
            
            local param_str = ""
            for _, kvs in ipairs(kvs_array) do
                if _ == 1 then
                    param_str = param_str .. string.format("@%s='%s'", kvs.key, kvs.value)
                else
                    param_str = param_str .. " and " .. string.format("@%s='%s'", kvs.key, kvs.value)
                end
            end
            local xPath = string.format("./%s[%s]", match_type, param_str)
            local child_nodes = part:search(xPath)
            
            if child_nodes and #child_nodes > 0 then
                table.insert(matched_nodes, part)
            end
        end
    end

    -- 返回匹配的节点数组
    return matched_nodes, nil
end

return M 