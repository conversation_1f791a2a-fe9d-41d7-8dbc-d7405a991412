local _M = {}

-- 加载所需模块
local check_rule_result = require("lib.check-rule-result")

-- 定义规则执行入口
function _M.dowork(root, target, context)
    local rule_result = check_rule_result.new("顶封板超尺寸检查", check_rule_result.LEVEL.ERROR)

    -- 存储结果
    local result = {}
    local logs = {}

    -- 1. 获取所有单元节点

    local ding_feng_ban_xmls = root:search("//Part[@productId='3FO3O2K41A28']")

    if not ding_feng_ban_xmls or #ding_feng_ban_xmls == 0 then
        return rule_result:pass("未找到任何顶封板")
    end

    for _, ding_feng_ban in ipairs(ding_feng_ban_xmls) do
        -- local h_str = ding_feng_ban:get_attribute("H")
        -- local textureName = ding_feng_ban:get_attribute("textureName")
        -- local ding_feng_ban_h_num = tonumber(h_str)
        -- if ding_feng_ban_h_num and ding_feng_ban_h_num == 18 then
        local mian_ban_xmls = ding_feng_ban:search(".//Part[@productId='3FO3NTWLKTGL']")
        for _, mian_ban in ipairs(mian_ban_xmls) do
            local w_str = mian_ban:get_attribute("W")
            local d_str = mian_ban:get_attribute("D")
            local w_num = tonumber(w_str)
            local d_num = tonumber(d_str)
            if w_num and d_num and d_num == 18 and w_num > 2780 then
                local prompt = string.format("当前 %s 的W是( %d ),面板的W不能超过（2780）；请设置分段；",
                    ding_feng_ban:get_attribute("name"), w_num)
                table.insert(result, {
                    prompt = prompt,
                    related_ids = { ding_feng_ban.id, mian_ban.id }
                })
            end
        end
        local jia_gu_ban_xmls = ding_feng_ban:search(".//Part[@productId='3FO3NTWU5574']")
        for _, jia_gu_ban in ipairs(jia_gu_ban_xmls) do
            local w_str = jia_gu_ban:get_attribute("W")
            local h_str = jia_gu_ban:get_attribute("H")
            local w_num = tonumber(w_str)
            local h_num = tonumber(h_str)
            if w_num and h_num and h_num == 18 and w_num > 2780 then
                local prompt = string.format("当前 %s 的W是( %d ),加固板的W不能超过（2780）；请设置分段；",
                    ding_feng_ban:get_attribute("name"), w_num)
                table.insert(result, {
                    prompt = prompt,
                    related_ids = { ding_feng_ban.id, jia_gu_ban.id }
                })
            end
        end
        -- end
    end

    return rule_result:error(result, logs)
end

return _M
