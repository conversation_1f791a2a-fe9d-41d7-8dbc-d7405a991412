local json = require("cjson")
local redis_client = require("lib.redisclient")
local string_util = require("lib.string-util")
local ngx = require("ngx")
local rule_processor = require("lib.rule-processor")
local expr_processor = require("lib.expr-processor")
local rule_data = require("lib.rule-data")

-- 处理Redis数据请求
local function process(request)
    -- 验证请求参数
    if not request or type(request) ~= "table" then
        return {
            success = false,
            message = "请求参数格式错误"
        }
    end

    -- 验证必要参数
    if #request == 0 then
        return {
            success = false,
            message = "请求参数不能为空"
        }
    end

    -- 处理每个请求项
    local success_results = {}
    local failed_results = {}

    for _, item in ipairs(request) do
        local result = {
            bizType = item.bizType or "",
            redisKey = item.redisKey or "",
            redisValue = "",
            needDecompress = item.needDecompress or false
        }

        -- 检查必要字段
        if string_util.is_empty(item.bizType) or string_util.is_empty(item.redisKey) then
            result.errorMsg = "参数缺少必要字段"
            table.insert(failed_results, result)
        else
            -- 如果是lrucache业务类型，返回缓存统计信息
            if item.bizType == "lrucache" then
                -- 添加错误处理
                local success, cache_stats = pcall(function()
                    return {
                        ruleProcessor = rule_processor.get_cache_stats(),
                        exprProcessor = expr_processor.get_cache_stats(),
                        ruleData = rule_data.get_cache_stats()
                    }
                end)
                
                if success then
                    result.redisValue = json.encode(cache_stats)
                    -- 清除所有缓存
                    local clear_results = {
                        ruleProcessor = rule_processor.clear_cache(),
                        exprProcessor = expr_processor.clear_cache(),
                        ruleData = rule_data.clear_all_rule_cache()
                    }
                    -- 添加清除结果到返回值
                    result.clearResults = clear_results
                else
                    result.errorMsg = "获取缓存统计信息失败: " .. tostring(cache_stats)
                    table.insert(failed_results, result)
                    goto continue
                end
                table.insert(success_results, result)
            else
                -- 获取Redis值
                local value, err
                if item.needDecompress then
                    value, err = redis_client.zstd_get(item.redisKey)
                else
                    value, err = redis_client.get(item.redisKey)
                end

                if err then
                    result.errorMsg = "获取Redis数据失败: " .. err
                    table.insert(failed_results, result)
                else
                    result.redisValue = value or ""
                    table.insert(success_results, result)
                end
            end
        end
        ::continue::
    end

    -- 返回响应
    return {
        success = #failed_results == 0,
        message = #failed_results > 0 and "部分请求处理失败" or "执行成功",
        successResults = success_results,
        failedResults = failed_results
    }
end

-- 主函数，处理HTTP请求
local function main()
    -- 设置响应头
    ngx.header.content_type = "application/json; charset=utf-8"

    -- 只允许POST请求
    if ngx.req.get_method() ~= "POST" then
        ngx.status = 405
        ngx.say(json.encode({
            success = false,
            message = "只支持POST请求"
        }))
        return
    end

    -- 读取请求体
    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    if data == nil then
        ngx.status = 400
        ngx.say(json.encode({
            success = false,
            message = "请求体不能为空"
        }))
        return
    end

    -- 解析JSON请求体
    local request
    local success, err = pcall(function() request = json.decode(data) end)
    if not success or not request then
        ngx.status = 400
        ngx.say(json.encode({
            success = false,
            message = "解析请求体失败: " .. (err or "JSON格式错误")
        }))
        return
    end

    -- 处理请求
    local response = process(request)
    -- 返回响应
    ngx.status = 200
    ngx.say(json.encode(response))
end

-- 执行主函数
main() 