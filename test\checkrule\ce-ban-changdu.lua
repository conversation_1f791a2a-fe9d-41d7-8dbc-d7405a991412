-- 检测侧板的长宽是否超出最大尺寸限制

-- 加载所需模块
local xml_search = require("lib.xml-search-in-node")
local check_rule_result = require("lib.check-rule-result")

local M = {}

-- 定义材料尺寸限制
local material_size_limits = {
    ["ML1053AG_可可胡桃"] = {
        [5] = {a_max = 2440, b_max = 1220},
        [9] = {a_max = 2440, b_max = 1220},
        [18] = {a_max = 2800, b_max = 1220},
        [25] = {a_max = 2800, b_max = 1220}
    },
    ["ML1054AG_秋韵黄橡"] = {
        [5] = {a_max = 2440, b_max = 1220},
        [9] = {a_max = 2440, b_max = 1220},
        [18] = {a_max = 2800, b_max = 1220},
        [25] = {a_max = 2800, b_max = 1220}
    },
    ["ML966JA_臻白"] = {
        [5] = {a_max = 2440, b_max = 1220},
        [18] = {a_max = 2440, b_max = 1220},
		[25] = {a_max = 2800, b_max = 1220}
    }
}

local function get_material_size_limits(material_name, thickness)
    return material_size_limits[material_name][thickness]
end

-- 定义规则执行入口
function M.dowork(root, target, context)
	local rule_result = check_rule_result.new("侧板超尺寸检查", check_rule_result.LEVEL.ERROR)

	if not root then
		return rule_result:pass("xml is nil")
	end

	-- 获取所有的板件
	local parts_xml = root:search("//Part[@prodCatId='713']")
	if not parts_xml or #parts_xml == 0 then
		return rule_result:pass("没有找到任何 prodCatId=713 的Part节点")
	end

	-- 获取侧板
	local side_boards_xml = {}
	for _, part_xml in ipairs(parts_xml) do
		local bjbq = xml_search.get_value_by_child_node(part_xml, "Parameter", "name", "BJBQ")
		if bjbq == "1" or bjbq == "2" then
			table.insert(side_boards_xml, part_xml)
		end
	end

	if #side_boards_xml == 0 then
		return rule_result:pass("没有找到任何侧板")
	end

	local err_side_board_list = {}
	local logs = {}
	for _, side_board_xml in ipairs(side_boards_xml) do
		local id = side_board_xml:get_attribute("id")
		local name = side_board_xml:get_attribute("name")
		local texture_name = side_board_xml:get_attribute("textureName")
		local w = tonumber(side_board_xml:get_attribute("W"))
		local h = tonumber(side_board_xml:get_attribute("H"))
		local d = tonumber(side_board_xml:get_attribute("D"))
		local czfx = xml_search.get_value_by_child_node(side_board_xml, "Parameter", "name", "textureAngleDegree")

		-- 获取材料尺寸限制
		local limit_size = get_material_size_limits(texture_name, w)

		if limit_size and czfx then
			local h_max = 0
			local w_max = 0
			if tonumber(czfx) == 0 then
				h_max = limit_size.a_max
				w_max = limit_size.b_max
			elseif tonumber(czfx) == 90 then
				h_max = limit_size.b_max
				w_max = limit_size.a_max
			end

			local h_over_size = h > h_max
			local w_over_size = w > w_max
			if h_over_size then
				table.insert(err_side_board_list, {
					prompt = string.format("侧板 %s 的当前高度 %s 超过最大限制高度 %d ，请设置上飘分段", name, h, h_max),
					related_ids = {id}
				})
			end

			if w_over_size then
				table.insert(err_side_board_list, {
					prompt = string.format("侧板 %s 的当前宽度 %s 超过最大限制宽度 %d ，请设置上飘分段", name, w, w_max),
					related_ids = {id}
				})
			end
		end
	end

	table.insert(logs, string.format("已收集 %d 个侧板数据，其中异常的数据有 %d 个", #side_boards_xml, #err_side_board_list))
    return rule_result:error(err_side_board_list, logs)
end

return M