local xmlua = require("xmlua")

local function main()

    local xpath = ngx.req.get_headers()['xpath']

    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    if data == nil then
        ngx.say('{"code":1,"msg":"值不能为空."}')
        return
    end

    local doc = xmlua.XML.parse(data)
    local element = doc:search(xpath)
    if element == nil then
        ngx.header["Content-Type"] = "application/json;charset=UTF-8"
        ngx.say('{"code":1,"msg":"xpath not found."}')
        return
    else
        --ngx.say('{"code":0,"xpath":"' .. element:to_xml() .. '"} ')
        ngx.header["Content-Type"] = "text/xml;charset=UTF-8"
        ngx.say(element:to_xml())
    end

end

main()
