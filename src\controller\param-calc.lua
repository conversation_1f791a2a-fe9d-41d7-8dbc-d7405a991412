local json = require("cjson")
local xmlua = require("xmlua")
local xml_data = require("lib.xml-data")
local rule_data = require("lib.rule-data")
local expr_processor = require("lib.expr-processor")
local rule_processor = require("lib.rule-processor")
local string_util = require("lib.string-util")
local attribute_appender = require("lib.attribute-appender")
local ngx = require("ngx")
local Engine = require("lib.oop-rule-engine")

-- 查找所有带有id的Part节点并建立映射
local function find_part_nodes(document)
    if not document then
        return nil, "XML文档无效"
    end

    -- 查找所有带有id的Part节点
    local nodes = document:search("//Part[@id]")
    if not nodes then
        return nil, "XPath查询失败"
    end

    -- 建立节点映射表
    local node_map = {}
    for _, node in ipairs(nodes) do
        local id = node:get_attribute("id")
        node_map[id] = node

        -- 将Part节点子节点属性附加到Part节点
        attribute_appender.append_attributes(node)
    end

    return node_map
end

-- 根据execOrder字段对参数进行排序
local function sort_params_by_exec_order(params)
    if not params or #params == 0 then
        return
    end

    table.sort(params, function(a, b)
        -- 如果execOrder不存在或相等，保持原有顺序
        if not a.execOrder and not b.execOrder then
            return false
        elseif not a.execOrder then
            return false
        elseif not b.execOrder then
            return true
        else
            return a.execOrder < b.execOrder
        end
    end)
end

-- 清理所有缓存
local function clear_all_caches()
    local success, err = pcall(function()
        rule_processor.clear_cache()
        expr_processor.clear_cache()
        rule_data.clear_all_rule_cache()
    end)
    
    if not success then
        ngx.log(ngx.ERR, "清理缓存失败: ", err)
        return
    end
    
    ngx.log(ngx.INFO, "缓存清理成功")
end

-- 初始化几何引擎
local function init_geometry_engine(xml_content)
    local engine = Engine.new(xml_content)
    Engine.set(engine)
end

-- 释放几何引擎资源
local function release_geometry_engine()
    local success, err = pcall(function()
        local engine = Engine.current()
        if engine then
            engine:scene_clear()
            return true
        else
            ngx.log(ngx.WARN, "没有找到活动的几何引擎实例")
            return false
        end
    end)

    if not success then
        ngx.log(ngx.ERR, "释放几何引擎资源失败: ", err)
        return false
    end

    return true
end

-- 生产参数运算接口处理函数
local function process(request)
    -- 解析请求参数
    local data_table = request
    if data_table == nil then
        return {
            success = false,
            message = "请求参数格式错误"
        }
    end

    -- 验证必要参数
    if not data_table.params or #data_table.params == 0 then
        return {
            success = false,
            message = "缺少必要参数: params",
            errors = json.empty_array,
            results = nil
        }
    end

    -- 验证 XML 内容或缓存键
    if (string_util.is_empty(data_table.xmlContent) and string_util.is_empty(data_table.xmlCacheKey)) then
        return {
            success = false,
            message = "必须提供 xmlContent 或 xmlCacheKey",
            errors = json.empty_array,
            results = nil
        }
    end

    -- 清理缓存（如果需要）
    if data_table.clearCache == true then
        clear_all_caches()
    end

    -- 获取XML内容
    local xml_content
    if string_util.is_not_empty(data_table.xmlContent) then
        xml_content = data_table.xmlContent
    else
        local content, err = xml_data.get_xml_from_cache(data_table.xmlCacheKey)
        if err then
            return {
                success = false,
                message = "获取XML内容失败: " .. err .. "[" .. tostring(data_table.xmlCacheKey) .. "]",
                errors = json.empty_array,
                results = nil
            }
        end
        xml_content = content
    end

    -- 使用xmlua解析XML
    local document, err = xmlua.XML.parse(xml_content)
    if not document then
        return {
            success = false,
            message = "XML解析失败: " .. err .. "[" .. tostring(data_table.xmlCacheKey) .. "]",
            errors = json.empty_array,
            results = nil
        }
    end

    -- 一次性查找所有需要的节点
    local node_map, find_err = find_part_nodes(document)
    if not node_map then
        return {
            success = false,
            message = "查找节点失败: " .. (find_err or "未知错误") .. "[" .. tostring(data_table.xmlCacheKey) .. "]",
            errors = json.empty_array,
            results = nil
        }
    end

    -- 初始化几何引擎
    init_geometry_engine(xml_content)

    -- 按照execOrder字段对参数进行排序
    sort_params_by_exec_order(data_table.params)

    -- 按顺序处理参数，执行规则
    local results = {}
    local errors = {}

    for _, param in ipairs(data_table.params) do
        -- 检查必要字段
        if string_util.is_empty(param.id) or string_util.is_empty(param.productId) or string_util.is_empty(param.vendorCode) or string_util.is_empty(param.paramCode) then
            table.insert(errors, {
                id = param.id or "",
                productId = param.productId or "",
                vendorCode = param.vendorCode or "",
                paramCode = param.paramCode or "",
                ruleId = param.ruleId or "",
                errorMsg = "参数缺少必要字段",
                execOrder = param.execOrder
            })
            goto continue
        end

        -- 获取节点
        local node = node_map[param.id]
        if not node then
            table.insert(errors, {
                id = param.id,
                productId = param.productId,
                vendorCode = param.vendorCode,
                paramCode = param.paramCode,
                ruleId = param.ruleId or "",
                errorMsg = "未找到ID为 " .. param.id .. " 的节点数据",
                execOrder = param.execOrder
            })
            goto continue
        end

        -- 获取规则对象
        local rule, rule_err
        if string_util.is_not_empty(param.ruleId) then
            -- 如果提供了规则ID，从规则库获取规则
            rule, rule_err = rule_data.get_rule_by_id_with_cache(param.ruleId)
        elseif param.rule then
            -- 如果规则ID为空但提供了rule对象，直接使用该对象
            rule = param.rule
        else
            -- 两者都未提供
            rule_err = "未提供规则ID或规则对象"
        end

        if not rule then
            table.insert(errors, {
                id = param.id,
                productId = param.productId,
                vendorCode = param.vendorCode,
                paramCode = param.paramCode,
                ruleId = param.ruleId or "",
                errorMsg = "获取规则失败: " .. (rule_err or "未知错误"),
                execOrder = param.execOrder
            })
            goto continue
        end

        -- 根据规则类型执行不同的处理器
        local value, exec_err
        if rule.ruleType == "expr" then
            -- 执行表达式规则
            value, exec_err = expr_processor.execute_rule(rule, document:root(), node, param or {})
        else
            -- 执行标准规则
            value, exec_err = rule_processor.execute_rule(rule, document:root(), node, param or {})
        end

        if exec_err then
            table.insert(errors, {
                id = param.id,
                productId = param.productId,
                vendorCode = param.vendorCode,
                paramCode = param.paramCode,
                ruleId = param.ruleId or "",
                execOrder = param.execOrder,
                errorMsg = "执行规则失败: " .. exec_err
            })
            goto continue
        end

        -- 将计算结果添加到节点对象中
        node[param.paramCode] = tostring(value)

        -- 收集成功的结果
        table.insert(results, {
            id = param.id,
            productId = param.productId,
            vendorCode = param.vendorCode,
            paramCode = param.paramCode,
            ruleId = param.ruleId or "",
            execOrder = param.execOrder,
            value = value
        })

        ::continue::
    end

    -- 返回处理结果，包含成功和失败的信息
    local status = #errors == 0 and "执行成功" or (#results == 0 and "执行失败" or "部分执行成功")
    local final_errors = errors
    if #errors == 0 then
        final_errors = json.empty_array
    end

    return {
        success = #errors == 0,
        message = string.format("%s[%s] - 成功: %d, 失败: %d",
            status,
            tostring(data_table.xmlCacheKey),
            #results,
            #errors
        ),
        results = #results > 0 and results or nil,
        errors = final_errors
    }
end

-- 日志记录函数
local function log_request(data)
    if type(data) == "string" then
        ngx.log(ngx.INFO, "生产参数请求入参: ", data)
    else
        ngx.log(ngx.INFO, "生产参数请求入参: ", json.encode(data))
    end
end

-- 主函数，处理HTTP请求
local function main()
    -- 设置响应头
    ngx.header.content_type = "application/json; charset=utf-8"

    -- 只允许POST请求
    if ngx.req.get_method() ~= "POST" then
        ngx.status = 405
        ngx.say(json.encode({
            success = false,
            message = "只支持POST请求"
        }))
        return
    end

    -- 读取请求体
    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    if not data then
        ngx.status = 400
        ngx.say(json.encode({
            success = false,
            message = "请求体不能为空"
        }))
        return
    end

    -- 记录请求日志
    -- log_request(data)

    -- 解析JSON请求体
    local request, err = json.decode(data)
    if not request then
        ngx.status = 400
        ngx.say(json.encode({
            success = false,
            message = "解析请求体失败: " .. (err or "JSON格式错误")
        }))
        return
    end

    -- 处理请求
    local response = process(request)

    -- 清理几何引擎资源
    release_geometry_engine()

    -- 返回响应
    ngx.status = 200
    ngx.say(json.encode(response))
end

-- 执行主函数
main()
