=encoding utf-8


=head1 Name


openresty/luajit2 - OpenResty's maintained branch of LuaJIT.


=head1 Description


This is the official OpenResty branch of LuaJIT. It is not to be considered a
fork, since we still regularly synchronize changes from the upstream LuaJIT
project (https://github.com/LuaJIT/LuaJIT).


=head1 OpenResty extensions


Additionally to synchronizing upstream changes, we introduce our own changes
which haven't been merged yet (or never will be). This document describes those
changes that are specific to this branch.


=head2 New Lua APIs



=head3 table.isempty


B<syntax:> I<res = isempty(tab)>

Returns C<true> when the given Lua table contains neither non-nil array elements
nor non-nil key-value pairs, or C<false> otherwise.

This API can be JIT compiled.

Usage:


    local isempty = require "table.isempty"
    
    print(isempty({}))  -- true
    print(isempty({nil, dog = nil}))  -- true
    print(isempty({"a", "b"}))  -- false
    print(isempty({nil, 3}))  -- false
    print(isempty({cat = 3}))  -- false




=head3 table.isarray


B<syntax:> I<res = isarray(tab)>

Returns C<true> when the given Lua table is a pure array-like Lua table, or
C<false> otherwise.

Empty Lua tables are treated as arrays.

This API can be JIT compiled.

Usage:


    local isarray = require "table.isarray"
    
    print(isarray{"a", true, 3.14})  -- true
    print(isarray{dog = 3})  -- false
    print(isarray{})  -- true




=head3 table.nkeys


B<syntax:> I<n = nkeys(tab)>

Returns the total number of elements in a given Lua table (i.e. from both the
array and hash parts combined).

This API can be JIT compiled.

Usage:


    local nkeys = require "table.nkeys"
    
    print(nkeys({}))  -- 0
    print(nkeys({ "a", nil, "b" }))  -- 2
    print(nkeys({ dog = 3, cat = 4, bird = nil }))  -- 2
    print(nkeys({ "a", dog = 3, cat = 4 }))  -- 3




=head3 table.clone


B<syntax:> I<t = clone(tab)>

Returns a shallow copy of the given Lua table.

This API can be JIT compiled.

Usage:


    local clone = require "table.clone"
    
    local x = {x=12, y={5, 6, 7}}
    local y = clone(x)
    ... use y ...

B<Note:> We observe 7% over-all speedup in the edgelang-fan compiler's
compiling speed whose Lua is generated by the fanlang compiler.

B<Note bis:> Deep cloning is planned to be supported by adding C<true> as a
second argument.




=head3 jit.prngstate


B<syntax:> I<state = jit.prngstate(state?)>

Returns (and optionally sets) the current PRNG state (an array of 8 Lua
numbers with 32-bit integer values) currently used by the JIT compiler.

When the C<state> argument is non-nil, it is expected to be an array of up to 8
unsigned Lua numbers, each with value less than 2\I<\>32-1. This will set the
current PRNG state and return the state that was overridden.

B<Note:> For backward compatibility, C<state> argument can also be an unsigned
Lua number less than 2\I<\>32-1.

B<Note:> When the C<state> argument is an array and less than 8 numbers, or the
C<state> is a number, the remaining positions are filled with zeros.

Usage:


    local state = jit.prngstate()
    local oldstate = jit.prngstate{ a, b, c, ... }
    
    jit.prngstate(32) -- {32, 0, 0, 0, 0, 0, 0, 0}
    jit.prngstate{432, 23, 50} -- {432, 23, 50, 0, 0, 0, 0, 0}

B<Note:> This API has no effect if LuaJIT is compiled with
C<-DLUAJIT_DISABLE_JIT>, and will return a table with all C<0>.




=head3 thread.exdata


B<syntax:> I<exdata = th_exdata(data?)>

This API allows for embedding user data into a thread (C<lua_State>).

The retrieved C<exdata> value on the Lua land is represented as a cdata object
of the ctype C<void*>.

As of this version, retrieving the C<exdata> (i.e. C<th_exdata()> without any
argument) can be JIT compiled.

Usage:


    local th_exdata = require "thread.exdata"
    
    th_exdata(0xdeadbeefLL)  -- set the exdata of the current Lua thread
    local exdata = th_exdata()  -- fetch the exdata of the current Lua thread

Also available are the following public C API functions for manipulating
C<exdata> on the C land:


    void lua_setexdata(lua_State *L, void *exdata);
    void *lua_getexdata(lua_State *L);

The C<exdata> pointer is initialized to C<NULL> when the main thread is created.
Any child Lua thread will inherit its parent's C<exdata>, but still can override
it.

B<Note:> This API will not be available if LuaJIT is compiled with
C<-DLUAJIT_DISABLE_FFI>.

B<Note bis:> This API is used internally by the OpenResty core, and it is
strongly discouraged to use it yourself in the context of OpenResty.




=head3 thread.exdata2


B<syntax:> I<exdata = th_exdata2(data?)>

Similar to C<thread.exdata> but for a 2nd separate user data as a pointer value.




=head2 New C API



=head3 lua_setexdata



    void lua_setexdata(lua_State *L, void *exdata);

Sets extra user data as a pointer value to the current Lua state or thread.




=head3 lua_getexdata



    void *lua_getexdata(lua_State *L);

Gets extra user data as a pointer value to the current Lua state or thread.




=head3 lua_setexdata2



    void lua_setexdata2(lua_State *L, void *exdata2);

Similar to C<lua_setexdata> but for a 2nd user data (pointer) value.




=head3 lua_getexdata2



    void *lua_getexdata2(lua_State *L);

Similar to C<lua_getexdata> but for a 2nd user data (pointer) value.




=head3 lua_resetthread



    void lua_resetthread(lua_State *L, lua_State *th);

Resets the state of C<th> to the initial state of a newly created Lua thread
object as returned by C<lua_newthread()>. This is mainly for Lua thread
recycling. Lua threads in arbitrary states (like yielded or errored) can be
reset properly.

The current implementation does not shrink the already allocated Lua stack
though. It only clears it.




=head2 New macros


The macros described in this section have been added to this branch.




=head3 C<OPENRESTY_LUAJIT>


In the C<luajit.h> header file, a new macro C<OPENRESTY_LUAJIT> was defined to
help distinguishing this OpenResty-specific branch of LuaJIT.


=head3 C<HAVE_LUA_RESETTHREAD>


This macro is set when the C<lua_resetthread> C API is present.




=head2 Optimizations



=head3 Updated JIT default parameters


We use more appressive default JIT compiler options to help large OpenResty
Lua applications.

The following C<jit.opt> options are used by default:


    maxtrace=8000
    maxrecord=16000
    minstitch=3
    maxmcode=40960  -- in KB




=head3 String hashing


This optimization only applies to Intel CPUs supporting the SSE 4.2 instruction
sets. For such CPUs, and when this branch is compiled with C<-msse4.2>, the
string hashing function used for strings interning will be based on an
optimized crc32 implementation (see C<lj_str_new()>).

This optimization still provides constant-time hashing complexity (C<O(n)>), but
makes hash collision attacks harder for strings up to 127 bytes of size.




=head2 Updated bytecode options



=head3 New C<-bL> option


The bytecode option C<L> was added to display Lua sources line numbers.

For example, C<luajit -bL -e 'print(1)'> now produces bytecode dumps like below:


    -- BYTECODE -- "print(1)":0-1
    0001     [1]    GGET     0   0      ; "print"
    0002     [1]    KSHORT   1   1
    0003     [1]    CALL     0   1   2
    0004     [1]    RET0     0   1

The C<[N]> column corresponds to the Lua source line number. For example, C<[1]>
means "the first source line".




=head3 Updated C<-bl> option


The bytecode option C<l> was updated to display the constant tables of each Lua
prototype.

For example, C<luajit -bl a.lua'> now produces bytecode dumps like below:


    -- BYTECODE -- a.lua:0-48
    KGC    0    "print"
    KGC    1    "hi"
    KGC    2    table
    KGC    3    a.lua:17
    KN    1    1000000
    KN    2    1.390671161567e-309
    ...




=head2 Miscellaneous



=over


=item *

Increased the maximum number of allowed upvalues from 60 to 120.

=item *

Various important bugfixes in the JIT compiler and Lua VM which have
not been merged in upstream LuaJIT.

=item *

Removed the GCC 4 requirement for x86 on older systems such as Solaris i386.

=item *

In the C<Makefile> file, make sure we always install the symlink for "luajit"
even for alpha or beta versions.

=item *

Applied a patch to fix DragonFlyBSD compatibility. Note: this is not an
officially supported target.

=item *

feature: jit.dump: output Lua source location after every BC.

=item *

feature: added internal memory-buffer-based trace entry/exit/start-recording
event logging, mainly for debugging bugs in the JIT compiler. it requires
C<-DLUA_USE_TRACE_LOGS> when building LuaJIT.

=item *

feature: save C<< g->jit_base >> to C<< g->saved_jit_base >> before C<lj_err_throw>
clears C<< g->jit_base >> which makes it impossible to get Lua backtrace in such
states.


=back




=head1 Copyright & License


LuaJIT is a Just-In-Time (JIT) compiler for the Lua programming language.

Project Homepage: http://luajit.org/

LuaJIT is Copyright (C) 2005-2019 Mike Pall.

Additional patches for OpenResty are copyrighted by Yichun Zhang and OpenResty
Inc.:

Copyright (C) 2017-2019 Yichun Zhang. All rights reserved.

Copyright (C) 2017-2019 OpenResty Inc. All rights reserved.

LuaJIT is free software, released under the MIT license.
See full Copyright Notice in the COPYRIGHT file or in luajit.h.

Documentation for the official LuaJIT is available in HTML format.
Please point your favorite browser to:

    doc/luajit.html


