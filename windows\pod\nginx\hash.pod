=encoding utf-8


=head1 Name


hash - Setting up hashes


=head1



To quickly process static sets of data such as server names,
L<ngx_http_map_module> directive’s values,
MIME types, names of request header strings,
nginx uses hash tables.
During the start and each re-configuration nginx selects the
minimum possible sizes of hash tables such that the bucket size
that stores keys with identical hash values does not exceed the
configured parameter (hash bucket size).
The size of a table is expressed in buckets.
The adjustment is continued until the table size exceeds the
hash max size parameter.
Most hashes have the corresponding directives that allow changing
these parameters, for example, for the server names hash they are
L<ngx_http_core_module>
and L<ngx_http_core_module>.





The hash bucket size parameter is aligned to the size that is a
multiple of the processor’s cache line size.  This speeds up
key search in a hash on modern processors by reducing the number
of memory accesses.
If hash bucket size is equal to one processor’s cache line size
then the number of memory accesses during the key search will be
two in the worst caseE<mdash>first to compute the bucket address,
and second during the key search inside the bucket.
Therefore, if nginx emits the message requesting to increase
either hash max size or hash bucket size then the first parameter
should first be increased.




