# 技术方案文档：数据转换 - 定制转换第三方方案数据为自身平台方案数据

## 1. 引言与背景

### 1.1. 项目目标
（简述 NH-AI 系统对接多个第三方设计前端，需要统一数据格式的背景和目标。）

### 1.2. 当前挑战
NH-AI 系统需要能够对接多个外部的第三方设计平台。由于每个第三方设计平台提供的方案数据在内容格式和结构上都各不相同，NH-AI 系统必须具备将这些多样化的数据转换成自身标准统一的方案数据格式的能力。

### 1.3. 文档目的
（说明本文档旨在阐述将第三方方案数据转换为 NH-AI 自身平台方案数据的技术方案。）

### 1.4. 通用数据补全需求
第三方设计平台的方案数据中包含的产品(定制和非定制)、户型等数据均为第三方平台自身的数据。通用数据补全作为数据转换的后续数据补充节点，它需实现对第三方平台的产品与NH-AI平台产品的关联及通用业务属性的补全(增添)。

## 2. 总体设计方案

### 2.1. 核心技术选型：定制化数据转换方案

本项目针对“定制转换第三方方案数据为自身平台方案数据”的核心需求，选定以下关键技术和设计模式：

1.  **统一内部标准数据模型 (Canonical Data Model)**：定义一套 NH-AI 平台内部统一、标准化的方案数据结构。所有外部数据源的数据，无论其原始格式和结构如何，最终都将被转换为此标准模型。这确保了平台内部数据的一致性和处理的便捷性。

2.  **适配器模式 (Adapter Pattern)**：为每一个对接的第三方设计平台开发专属的数据适配器。
    *   **职责**：负责从特定第三方平台获取原始方案数据（可能通过 API、文件接口等方式），并将其解析为 Lua Table 等内存中的中间表示形式。适配器将封装与特定数据源通信和初步解析的复杂性。
    *   **支持格式**：能够灵活支持 JSON, XML (利用项目已有的 `xmlua` 工具), CSV, 或其他专有数据格式。

3.  **转换器模式 (Transformer Pattern)**：同样为每个第三方平台开发专属的数据转换器。
    *   **职责**：接收由对应适配器解析后的中间数据（Lua Table），并依据预定义的映射规则，将其精确转换为 NH-AI 的内部标准数据模型。
    *   **转换逻辑**：
        *   对于简单的字段映射，可采用直接编程方式实现。
        *   对于复杂的转换规则、条件判断、数据校验和默认值填充等，强烈建议利用本项目核心的**规则引擎 (Rule Engine)**。通过将转换规则外部化、配置化，可以极大提高系统的灵活性、可维护性和业务响应速度。

4.  **模块化与可扩展性**：适配器和转换器均设计为独立的、可插拔的模块。当需要对接新的第三方平台时，只需开发新的适配器和转换器模块，对现有核心系统的影响降至最低。

该技术选型旨在构建一个健壮、灵活且易于扩展的数据转换通道，有效应对多源异构数据的挑战。

### 2.2. 架构图
（预留位置，后续可添加系统架构图，展示数据流和各模块关系。）
```mermaid
graph LR
    subgraph 第三方A
        direction LR
        A_RawData[(原始数据 A)]
    end
    subgraph 第三方B
        direction LR
        B_RawData[(原始数据 B)]
    end
    subgraph 数据转换
        direction TB
        AdapterA[适配器 A]
        TransformerA[转换器 A]
        AdapterB[适配器 B]
        TransformerB[转换器 B]
        CanonicalModel[NH-AI 标准数据模型]
        Orchestrator[调度业务服务]
    end
    subgraph 业务服务
        direction LR
        CoreApp[业务服务]
    end

    A_RawData --> AdapterA
    AdapterA -- 解析后数据 --> TransformerA
    TransformerA -- 转换后标准数据 --> CanonicalModel

    B_RawData --> AdapterB
    AdapterB -- 解析后数据 --> TransformerB
    TransformerB -- 转换后标准数据 --> CanonicalModel

    CanonicalModel --> Orchestrator
    Orchestrator --> CoreApp

    style 第三方A fill:#f9f,stroke:#333,stroke-width:2px
    style 第三方B fill:#f9f,stroke:#333,stroke-width:2px
    style 数据转换 fill:#ccf,stroke:#333,stroke-width:2px
    style 业务服务 fill:#cfc,stroke:#333,stroke-width:2px
```

## 3. 模块详细设计

### 3.1. NH-AI 内部标准数据模型 (Canonical Data Model)
    - 详细定义 NH-AI 平台统一的方案数据结构。
    - 字段列表、数据类型、约束条件等。
    - （可参考 `src/integration/canonical_model/` 目录下的定义）

### 3.2. 适配器层 (Adapter Layer)
    - 职责：负责从各个第三方系统获取原始数据，并将其解析为统一的中间 Lua Table 结构。
    - 为每个第三方系统实现一个独立的适配器模块。
    - 支持的数据格式：JSON, XML, CSV, 其他专有格式等。
    - （可参考 `src/integration/adapters/` 和 `src/integration/examples/adapter_example.lua`）

### 3.3. 转换器层 (Transformer Layer)
    - 职责：将适配器解析后的中间 Lua Table 数据，根据预定义的映射规则，转换为 NH-AI 内部标准数据模型。
    - 为每个第三方系统实现一个独立的转换器模块。
    - 转换逻辑可以简单直接映射，也可以利用规则引擎（Rule Engine）实现复杂逻辑。
    - （可参考 `src/integration/transformers/` 和 `src/integration/examples/transformer_example.lua`）

### 3.4. 编排与调度服务 (Orchestration Service)
    - 职责：管理整个数据接入和转换的流程。
    - 动态加载和调用对应的适配器和转换器。
    - 记录日志、处理异常。

## 4. 数据处理流程
（详细描述从接收第三方数据到最终入库或供核心应用使用的完整流程。）

## 5. 错误处理与日志记录
    - 定义常见的错误类型及其处理机制。
    - 日志级别和内容规范。

## 6. 部署与维护
    - 新增第三方接入的流程。
    - 配置管理。

## 7. 未来展望与优化点
    - 性能优化。
    - 规则动态更新。
    - 可视化监控。

## 8. 附录
    - 相关术语解释。
    - 参考文献。
