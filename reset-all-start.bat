cd windows
taskkill /F /IM nginx.exe

if exist lua_scripts (
    rd /s /q lua_scripts
)

del logs\access.log
del logs\error.log

if exist lualib\resty (
    cd ..
    for /r commons\lualib\ %%i in (*) do (
        if exist windows\lualib\resty\%%~nxi (
            del /q windows\lualib\resty\%%~nxi
        )
    )
    cd windows
)

if exist lualib\xmlua (
    cd ..
    for /r commons\lualib\ %%i in (*) do (
        if exist windows\lualib\%%~nxi (
            del /q windows\lualib\%%~nxi
        )
    )
    rd /s /q windows\lualib\xmlua
    rd /s /q windows\lualib\luacs
    cd windows
)

cd ..

xcopy /E /Y "%~dp0commons\resty\*" "%~dp0windows\lualib\resty\"
xcopy /E /Y "%~dp0commons\lualib\*" "%~dp0windows\lualib\"
if not exist "%~dp0windows\lua_scripts" mkdir "%~dp0windows\lua_scripts"
xcopy /E /Y "%~dp0src\*" "%~dp0windows\lua_scripts\"
xcopy /E /Y "%~dp0src\controller\*" "%~dp0windows\lua_scripts\"
cd windows

mkdir logs

start nginx.exe
cd ..

