'94', 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==0 and tonumber(target.CG)==0 and tonumber(target.JSLX)==0'
'95', 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==0 and tonumber(target.CG)==0 and tonumber(target.JSLX)>0'
'96', 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==0 and tonumber(target.CG)==1 and tonumber(target.JSLX)==0'
'97', 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==0 and tonumber(target.CG)==1 and tonumber(target.JSLX)>0'
'98', 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==90 and tonumber(target.CG)==0 and tonumber(target.JSLX)==0'
'99', 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==90 and tonumber(target.CG)==0 and tonumber(target.JSLX)>0'
'100', 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==90 and tonumber(target.CG)==1 and tonumber(target.JSLX)==0'
'101', 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==90 and tonumber(target.CG)==1 and tonumber(target.JSLX)>0'
'105', 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==0'
'106', 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==0'
'107', 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0'
'108', 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0'
'109', 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==0'
'110', 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==0'
'111', 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0'
'112', 'tonumber(target.PT)==18 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)>0'
'113', 'tonumber(target.PT)==25 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)>0'
'114', 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==0'
'115', 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==0'
'116', 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0'
'117', 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0'
'118', 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==0'
'119', 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==0'
'120', 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0'
'121', 'tonumber(target.PT)==18 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)>0'
'122', 'tonumber(target.PT)==25 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)>0'
'123', 'tonumber(target.PT)==18 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==0'
'124', 'tonumber(target.PT)==25 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==0'
'125', 'tonumber(target.PT)==18 and tonumber(target.CutOut)==23'
'126', 'tonumber(target.PT)==18 and tonumber(target.CutOut)==24'
'127', 'tonumber(target.PT)==18 and tonumber(target.CutOut)==234'
'128', 'tonumber(target.PT)==25 and tonumber(target.CutOut)==23'
'129', 'tonumber(target.PT)==25 and tonumber(target.CutOut)==24'
'130', 'tonumber(target.PT)==25 and tonumber(target.CutOut)==234'
'131', 'tonumber(target.PT)==18 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==1'
'132', 'tonumber(target.PT)==25 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==1'
'133', 'tonumber(target.PT)==18 and tonumber(target.CutOut)==0'
'134', 'tonumber(target.PT)==18 and tonumber(target.CutOut)>0'
'135', 'tonumber(target.PT)==25 and tonumber(target.CutOut)==0'
'136', 'tonumber(target.PT)==25 and tonumber(target.CutOut)>0'
'137', 'tonumber(target.PT)==35 and tonumber(target.CutOut)==0'
'138', 'tonumber(target.PT)==35 and tonumber(target.CutOut)>0'
'139', 'tonumber(target.PT)==18 and tonumber(target.CutOut)==0'
'140', 'tonumber(target.PT)==18 and tonumber(target.CutOut)>0'
'141', 'tonumber(target.PT)==25 and tonumber(target.CutOut)==0'
'142', 'tonumber(target.PT)==25 and tonumber(target.CutOut)>0'
'143', 'tonumber(target.PT)==18 and tonumber(target.Height)>0 and tonumber(target.Height)<70'
'144', 'tonumber(target.PT)==25'
'145', 'tonumber(target.PT)==18 and tonumber(target.Height)>=70'
'153', 'tonumber(target.JSLX)==0 and tonumber(target.JGLX)==1'
'154', 'tonumber(target.JSLX)>0 and tonumber(target.JGLX)==1'
'155', 'tonumber(target.CXHD)==18'
'156', 'tonumber(target.CXHD)==12'
'157', 'tonumber(target.CTGZJ)==1'
'158', 'tonumber(target.CTGZJ)==2'
'159', 'tonumber(target.CTGZJ)>1'
'161', 'tonumber(target.Width)>=144 and tonumber(target.Width)<=494'
'162', 'tonumber(target.Width)>494 and tonumber(target.Width)<=894'
'163', 'tonumber(target.Width)>894 and tonumber(target.Width)<=1186'
'166', 'tonumber(target.PT)==18'
'167', 'tonumber(target.PT)==25'
'168', 'tonumber(target.PT)==18 and tonumber(target.Height)==18 and tonumber(target.TMDGC)==0'
'169', 'tonumber(target.PT)==18 and tonumber(target.Height)>18 and tonumber(target.Height)<=28 and tonumber(target.TMDGC)==0'
'170', 'tonumber(target.PT)==18 and tonumber(target.Height)>28 and tonumber(target.Height)<=41 and tonumber(target.TMDGC)==0'
'171', 'tonumber(target.TMDGC)==1'

-- SQL更新语句
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==0 and tonumber(target.CG)==0 and tonumber(target.JSLX)==0' WHERE id = 94;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==0 and tonumber(target.CG)==0 and tonumber(target.JSLX)>0' WHERE id = 95;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==0 and tonumber(target.CG)==1 and tonumber(target.JSLX)==0' WHERE id = 96;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==0 and tonumber(target.CG)==1 and tonumber(target.JSLX)>0' WHERE id = 97;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==90 and tonumber(target.CG)==0 and tonumber(target.JSLX)==0' WHERE id = 98;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==90 and tonumber(target.CG)==0 and tonumber(target.JSLX)>0' WHERE id = 99;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==90 and tonumber(target.CG)==1 and tonumber(target.JSLX)==0' WHERE id = 100;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.MBKS)==1 and tonumber(target.textureAngleDegree)==90 and tonumber(target.CG)==1 and tonumber(target.JSLX)>0' WHERE id = 101;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==0' WHERE id = 105;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==0' WHERE id = 106;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0' WHERE id = 107;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0' WHERE id = 108;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==0' WHERE id = 109;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==0' WHERE id = 110;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0' WHERE id = 111;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)>0' WHERE id = 112;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)>0' WHERE id = 113;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==0' WHERE id = 114;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==0' WHERE id = 115;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0' WHERE id = 116;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0' WHERE id = 117;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)==0 and tonumber(target.JGLX)==0' WHERE id = 118;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==0' WHERE id = 119;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.DT_Pos)>0 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)==0' WHERE id = 120;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)>0' WHERE id = 121;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.JGLX)==1 and tonumber(target.CBSP)>0' WHERE id = 122;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==0' WHERE id = 123;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==0' WHERE id = 124;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==23' WHERE id = 125;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==24' WHERE id = 126;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==234' WHERE id = 127;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==23' WHERE id = 128;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==24' WHERE id = 129;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==234' WHERE id = 130;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==1' WHERE id = 131;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==0 and tonumber(target.JGLX)==1' WHERE id = 132;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==0' WHERE id = 133;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)>0' WHERE id = 134;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==0' WHERE id = 135;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)>0' WHERE id = 136;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==35 and tonumber(target.CutOut)==0' WHERE id = 137;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==35 and tonumber(target.CutOut)>0' WHERE id = 138;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)==0' WHERE id = 139;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.CutOut)>0' WHERE id = 140;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)==0' WHERE id = 141;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25 and tonumber(target.CutOut)>0' WHERE id = 142;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.Height)>0 and tonumber(target.Height)<70' WHERE id = 143;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25' WHERE id = 144;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.Height)>=70' WHERE id = 145;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.JSLX)==0 and tonumber(target.JGLX)==1' WHERE id = 153;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.JSLX)>0 and tonumber(target.JGLX)==1' WHERE id = 154;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.CXHD)==18' WHERE id = 155;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.CXHD)==12' WHERE id = 156;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.CTGZJ)==1' WHERE id = 157;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.CTGZJ)==2' WHERE id = 158;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.CTGZJ)>1' WHERE id = 159;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.Width)>=144 and tonumber(target.Width)<=494' WHERE id = 161;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.Width)>494 and tonumber(target.Width)<=894' WHERE id = 162;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.Width)>894 and tonumber(target.Width)<=1186' WHERE id = 163;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18' WHERE id = 166;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==25' WHERE id = 167;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.Height)==18 and tonumber(target.TMDGC)==0' WHERE id = 168;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.Height)>18 and tonumber(target.Height)<=28 and tonumber(target.TMDGC)==0' WHERE id = 169;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.PT)==18 and tonumber(target.Height)>28 and tonumber(target.Height)<=41 and tonumber(target.TMDGC)==0' WHERE id = 170;
UPDATE nhai_rule_version SET lua_script = 'tonumber(target.TMDGC)==1' WHERE id = 171;