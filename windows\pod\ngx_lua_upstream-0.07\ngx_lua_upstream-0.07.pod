=encoding utf-8


=head1 Name

ngx_http_lua_upstream - Nginx C module to expose Lua API to ngx_lua for Nginx upstreams


=head1 Status

This module is production ready.


=head1 Synopsis


    http {
        upstream foo.com {
            server 127.0.0.1 fail_timeout=53 weight=4 max_fails=100;
            server agentzh.org:81;
        }
    
        upstream bar {
            server *********;
        }
    
        server {
            listen 8080;
    
            # sample output for the following /upstream interface:
            # upstream foo.com:
            #     addr = 127.0.0.1:80, weight = 4, fail_timeout = 53, max_fails = 100
            #     addr = 106.184.1.99:81, weight = 1, fail_timeout = 10, max_fails = 1
            # upstream bar:
            #     addr = *********:80, weight = 1, fail_timeout = 10, max_fails = 1
    
            location = /upstreams {
                default_type text/plain;
                content_by_lua_block {
                    local concat = table.concat
                    local upstream = require "ngx.upstream"
                    local get_servers = upstream.get_servers
                    local get_upstreams = upstream.get_upstreams
    
                    local us = get_upstreams()
                    for _, u in ipairs(us) do
                        ngx.say("upstream ", u, ":")
                        local srvs, err = get_servers(u)
                        if not srvs then
                            ngx.say("failed to get servers in upstream ", u)
                        else
                            for _, srv in ipairs(srvs) do
                                local first = true
                                for k, v in pairs(srv) do
                                    if first then
                                        first = false
                                        ngx.print("    ")
                                    else
                                        ngx.print(", ")
                                    end
                                    if type(v) == "table" then
                                        ngx.print(k, " = {", concat(v, ", "), "}")
                                    else
                                        ngx.print(k, " = ", v)
                                    end
                                end
                                ngx.print("\n")
                            end
                        end
                    end
                }
            }
        }
    }


=head1 Functions




=head2 get_upstreams

C<syntax: names = upstream.get_upstreams()>

Get a list of the names for all the named upstream groups (i.e., explicit C<upstream {}> blocks).

Note that implicit upstream groups created by C<proxy_pass> and etc are excluded.




=head2 get_servers

C<syntax: servers = upstream.get_servers(upstream_name)>

Get configurations for all the servers in the specified upstream group. Please one server may take multiple addresses when its server name can be resolved to multiple addresses.

The return value is an array-like Lua table. Each table entry is a hash-like Lua table that takes the following keys:


=over


=item *

addr

socket address(es). can be either a Lua string or an array-like Lua table of Lua strings.

=item *

backup

=item *

fail_timeout

=item *

max_fails

=item *

name

=item *

weight


=back




=head2 get_primary_peers

C<syntax: peers = upstream.get_primary_peers(upstream_name)>

Get configurations for all the primary (non-backup) peers in the specified upstream group.

The return value is an array-like Lua table for all the primary peers. Each table entry is a (nested) hash-like Lua table that takes the following keys:


=over


=item *

current_weight

=item *

effective_weight

=item *

fail_timeout

=item *

fails

=item *

id

Identifier (ID) for the peer. This ID can be used to reference a peer in a group in the peer modifying API.

=item *

max_fails

=item *

name

Socket address for the current peer

=item *

weight

=item *

accessed

Timestamp for the last access (in seconds since the Epoch)

=item *

checked

Timestamp for the last check (in seconds since the Epoch)

=item *

down

Holds true if the peer has been marked as "down", otherwise this key is not present

=item *

conns

Number of active connections to the peer (this requires NGINX 1.9.0 or above).


=back




=head2 get_backup_peers

C<syntax: peers = upstream.get_backup_peers(upstream_name)>

Get configurations for all the backup peers in the specified upstream group.

The return value has the same structure as L<get_primary_peers> function.




=head2 set_peer_down

C<syntax: ok, err = upstream.set_peer_down(upstream_name, is_backup, peer_id, down_value)>

Set the "down" (boolean) attribute of the specified peer.

To uniquely specify a peer, you need to specify the upstream name, whether or not it is a backup peer, and the peer id (starting from 0).

Note that this method only changes the peer settings in the current Nginx worker
process. You need to synchronize the changes across all the Nginx workers yourself if you
want a server-wide change (for example, by means of L<ngx_lua|https://github.com/openresty/lua-nginx-module#ngxshareddict>'s L<ngx.shared.DICT|https://github.com/openresty/lua-nginx-module#ngxshareddict>).

Below is an example. Consider we have a "bar" upstream block in C<nginx.conf>:


    upstream bar {
        server *********;
        server 127.0.0.3 backup;
        server 127.0.0.4 fail_timeout=23 weight=7 max_fails=200 backup;
    }

then


    upstream.set_peer_down("bar", false, 0, true)

will turn down the primary peer corresponding to C<server *********>.

Similarly,


    upstream.set_peer_down("bar", true, 1, true)

will turn down the backup peer corresponding to C<server 127.0.0.4 ...>.

You can turn on a peer again by providing a C<false> value as the 4th argument.




=head2 current_upstream_name

C<syntax: name = upstream.current_upstream_name()>

Returns the name of the proxied upstream for the current request.
If there is no upstream for this request (no C<proxy_pass> call), or this
function is called in a phase prior to the content phase, then the return value
will be C<nil>. If a port is explicitly included in the upstream definition or
C<proxy_pass> directive, it will be included in the return value of this function.

Example:


    -- upstream my_upstream { ... }
    -- proxy_pass http://my_upstream;
    upstream.current_upstream_name() --> my_upstream
    
    -- proxy_pass http://example.com:1234;
    upstream.current_upstream_name() --> example.com:1234

Note that implicit upstreams created by C<proxy_pass> are included, contrary to
the output of C<upstream.get_upstreams()>.




=head1 TODO


=over


=item *

Add API to add or remove servers to existing upstream groups.


=back




=head1 Compatibility

The following versions of Nginx should work with this module:


=over


=item *

B<1.11.x> (last tested: 1.11.2)

=item *

B<1.10.x>

=item *

B<1.9.x>  (last tested: 1.9.15)

=item *

B<1.8.x>

=item *

B<1.7.x>  (last tested: 1.7.10)

=item *

B<1.6.x>

=item *

B<1.5.x>  (last tested: 1.5.12)


=back




=head1 Installation

This module is bundled and enabled by default in the L<OpenResty|http://openresty.org> bundle. And you are recommended to use OpenResty.


=over


=item 1.

Grab the nginx source code from L<nginx.org|http://nginx.org/>, for example,
the version 1.11.2 (see L<nginx compatibility>),

=item 2.

then grab the source code of the L<ngx_lua|https://github.com/openresty/lua-nginx-module#installation> as well as its dependencies like L<LuaJIT|http://luajit.org/download.html>.

=item 3.

and finally build the source with this module:


=back


    wget 'http://nginx.org/download/nginx-1.11.2.tar.gz'
    tar -xzvf nginx-1.11.2.tar.gz
    cd nginx-1.11.2/
    
    # assuming your luajit is installed to /opt/luajit:
    export LUAJIT_LIB=/opt/luajit/lib
    
    # assuming you are using LuaJIT v2.1:
    export LUAJIT_INC=/opt/luajit/include/luajit-2.1
    
    # Here we assume you would install you nginx under /opt/nginx/.
    ./configure --prefix=/opt/nginx \
        --with-ld-opt="-Wl,-rpath,$LUAJIT_LIB" \
        --add-module=/path/to/lua-nginx-module \
        --add-module=/path/to/lua-upstream-nginx-module
    
    make -j2
    make install

Starting from NGINX 1.9.11, you can also compile this module as a dynamic module, by using the C<--add-dynamic-module=PATH> option instead of C<--add-module=PATH> on the
C<./configure> command line above. And then you can explicitly load the module in your C<nginx.conf> via the L<load_module|http://nginx.org/en/docs/ngx_core_module.html#load_module>
directive, for example,


    load_module /path/to/modules/ngx_http_lua_upstream_module.so;




=head1 Author

Yichun "agentzh" Zhang (章亦春) E<lt><EMAIL><gt>, OpenResty Inc.




=head1 Copyright and License

This module is licensed under the BSD license.

Copyright (C) 2014-2017, by Yichun "agentzh" Zhang, OpenResty Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:


=over


=item *

Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.


=back


=over


=item *

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

the ngx_lua module: http://github.com/openresty/lua-nginx-module#readme

=item *

the L<lua-resty-upstream-healthcheck|https://github.com/openresty/lua-resty-upstream-healthcheck> library which makes use of the Lua API provided by this module.


=back



