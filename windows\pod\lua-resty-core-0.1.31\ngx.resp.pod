=encoding utf-8


=head1 Name

ngx.resp - Lua API for HTTP response handling.


=head1 Status

This Lua module is production ready.


=head1 Synopsis


    local ngx_resp = require "ngx.resp"
    
    -- add_header
    ngx_resp.add_header("Foo", "bar")
    ngx_resp.add_header("Foo", "baz")
    --> there will be two new headers in HTTP response:
    --> Foo: bar and Foo: baz
    
    
    ngx_resp.set(531, "user defined error")
    --> the response line will be: 531 user defiend error




=head1 Description

This Lua module provides Lua API which could be used to handle HTTP response.




=head1 Methods

All the methods of this module are static (or module-level). That is, you do
not need an object (or instance) to call these methods.




=head2 add_header

B<syntax:> I<ngx_resp.add_header(header_name, header_value)>

This function adds specified header with corresponding value to the response of
current request. The C<header_value> could be either a string or a table.

The C<ngx.resp.add_header> works mostly like:

=over


=item *

L<ngx.header.HEADER|https://github.com/openresty/lua-nginx-module#ngxheaderheader>

=item *

Nginx's L<add_header|http://nginx.org/en/docs/http/ngx_http_headers_module.html#add_header> directive.


=back

However, unlike C<ngx.header.HEADER>, this method appends new header to the old
one instead of overriding it.

Unlike C<add_header> directive, this method will override the builtin header
instead of appending it.




=head2 set_status

B<syntax:> I<ngx_resp.status(status, reason?)>

Unlike C<ngx.status> which only sets the status, this function sets the response
status with an optional reason. The C<reason> should be a string.




=head1 Community




=head2 English Mailing List

The L<openresty-en|https://groups.google.com/group/openresty-en> mailing list
is for English speakers.




=head2 Chinese Mailing List

The L<openresty|https://groups.google.com/group/openresty> mailing list is for
Chinese speakers.




=head1 Bugs and Patches

Please report bugs or submit patches by


=over


=item 1.

creating a ticket on the L<GitHub Issue Tracker|https://github.com/openresty/lua-resty-core/issues>,

=item 2.

or posting to the L<OpenResty community>.


=back




=head1 Copyright and License

This module is licensed under the BSD license.

Copyright (C) 2018, by Yichun "agentzh" Zhang, OpenResty Inc.

All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:


=over


=item *

Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.


=back


=over


=item *

Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.


=back

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




=head1 See Also


=over


=item *

the L<lua-resty-core|https://github.com/openresty/lua-resty-core> library.

=item *

the ngx_lua module: https://github.com/openresty/lua-nginx-module

=item *

OpenResty: https://openresty.org


=back



