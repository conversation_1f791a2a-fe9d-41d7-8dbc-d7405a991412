### XML 初始化接口（橱柜）
POST {{host}}/InitScript
Content-Type: application/json

{
  "code": 0,
  "msg": "操作成功",
  "success": true,
  "data": "eyJ0b3RhbCI6MiwiY3VycmVudCI6MSwic2l6ZSI6MTAwLCJyZWNvcmRzIjpbeyJpZCI6IjY5NzQ2MDM2NDM3OTU3NDMxOSIsInNjcmlwdE5hbWUiOiLmnaHku7blhbPogZQiLCJzY3JpcHRDb250ZW50Ijoi5rWL6K+VIiwiZGVzY3JpcHQiOiLmnaHku7blhbPogZQiLCJ3YXJuaW5nTGV2ZWwiOiIwIiwiZW5hYmxlIjoxLCJvcmdhbklkIjoiQzAwMDAyMzg0IiwiY3JlYXRvciI6IjEwMDAzMTIyOTkiLCJtb2RpZmllciI6IjEwMDAzMTIyOTkiLCJjcmVhdG9yTmFtZSI6IuS6p+eglOa1i+ivlTAwOCIsIm1vZGlmaWVyTmFtZSI6IuS6p+eglOa1i+ivlTAwOCIsImNyZWF0ZVRpbWUiOiIyMDI1LTA0LTA4IDE0OjU4OjI5IiwiY2F0ZWdvcnkiOiJjdXBib2FyZCIsImNhdGVnb3J5TmFtZSI6IuapseafnCIsImF1dG9TcGxpdCI6MCwic3RvcmVTcGxpdCI6MCwiZmFjdG9yeUF1ZGl0IjowLCJpc0Rlc2lnbiI6MX0seyJpZCI6IjY5NzQ2MDI4NjEwNTQ3MzA1OSIsInNjcmlwdE5hbWUiOiLlr7nosaHlhbPns7vliKTmlq0iLCJzY3JpcHRDb250ZW50Ijoi5rWL6K+VIiwiZGVzY3JpcHQiOiLlr7nosaHlhbPns7vliKTmlq0iLCJ3YXJuaW5nTGV2ZWwiOiIwIiwiZW5hYmxlIjoxLCJvcmdhbklkIjoiQzAwMDAyMzg0IiwiY3JlYXRvciI6IjEwMDAzMTIyOTkiLCJtb2RpZmllciI6IjEwMDAzMTIyOTkiLCJjcmVhdG9yTmFtZSI6IuS6p+eglOa1i+ivlTAwOCIsIm1vZGlmaWVyTmFtZSI6IuS6p+eglOa1i+ivlTAwOCIsImNyZWF0ZVRpbWUiOiIyMDI1LTA0LTA4IDE0OjU4OjEwIiwiY2F0ZWdvcnkiOiJjdXBib2FyZCIsImNhdGVnb3J5TmFtZSI6IuapseafnCIsImF1dG9TcGxpdCI6MCwic3RvcmVTcGxpdCI6MCwiZmFjdG9yeUF1ZGl0IjowLCJpc0Rlc2lnbiI6MX1dfQ==",
  "category": "cupboard",
  "originid": "C00002384"
}

### XML 初始化接口（衣柜）
POST https://rulecheck.3vjia.com/InitScript
Content-Type: application/json

{
  "code": 0,
  "msg": "操作成功",
  "success": true,
  "data": "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",
  "category": "wardrobe",
  "originid": "C00002384"
}

### XML 处理接口
POST {{host}}/ProcessXMLScript
Content-Type: application/json

{}

### XML 测试接口
POST {{host}}/TestScript
Content-Type: application/json

{}

### 测试 XML 解析和 xpath 功能
# curl -H 'xpath: /root/user' -H 'Content-Type: text/xml' -d '<?xml version="1.0" encoding="UTF-8"?><root><user id="1" name="张三"><school>学校</school></user></root>' 'http://localhost:8889/TestXmlua' && echo
GET http://localhost:8889/TestXmlua
Content-Type: text/xml
xpath: /root/user

<root>
    <user id="1" name="张三">
        <school>学校</school>
    </user>
</root>

### 测试 Redis 集群读取操作：get
# curl -H 'Content-Type: application/json' -d '{"method": "get", "key": "lua:rc:user"}' 'http://localhost:8889/TestRedisClientGet' && echo
POST {{host}}/TestRedisClientGet
Content-Type: application/json

{
  "method": "get",
  "key": "lua:rc:user"
}

### 测试 Redis 集群读取操作：mget
# 注意：这里返回的 json 中并没有 lua:rc:404 这个键，因为这个键不存在，cjson 序列化时会自动跳过值为 nil 的键
# curl -H 'Content-Type: application/json' -d '{"method": "mget", "keys": ["lua:rc:str", "lua:rc:404", "lua:rc:user:1", "lua:rc:user:2", "lua:rc:user:33"]}' 'http://localhost:8889/TestRedisClientGet' && echo
POST {{host}}/TestRedisClientGet
Content-Type: application/json

{
  "method": "mget",
  "keys": [
    "lua:rc:str",
    "lua:rc:404",
    "lua:rc:user:1",
    "lua:rc:user:2",
    "lua:rc:user:33"
  ]
}

### 测试 Redis 集群读取操作：zstd_get
# curl -H 'Content-Type: application/json' -d '{"method": "zstd_get", "key": "lua:rc:zstd"}' 'http://localhost:8889/TestRedisClientGet' && echo
POST {{host}}/TestRedisClientGet
Content-Type: application/json

{
  "method": "zstd_get",
  "key": "lua:rc:zstd"
}

### 测试 Redis 集群读取操作：zstd_mget
# 注意：这里返回的 json 中并没有 lua:rc:404 这个键，因为这个键不存在，cjson 序列化时会自动跳过值为 nil 的键
# curl -H 'Content-Type: application/json' -d '{"method": "zstd_mget", "keys": ["lua:rc:zstd", "lua:rc:zstd-str", "lua:rc:404"]}' 'http://localhost:8889/TestRedisClientGet' && echo
POST {{host}}/TestRedisClientGet
Content-Type: application/json

{
  "method": "zstd_mget",
  "keys": [
    "lua:rc:zstd",
    "lua:rc:zstd-str",
    "lua:rc:404"
  ]
}

### 测试 Redis 集群写入操作：set
# curl -H 'Content-Type: application/json' -d '{"method": "set", "single": {"key": "lua:rc:user", "value": "{\"id\": 999, \"name\": \"张三feng\"}"}}' 'http://localhost:8889/TestRedisClientSet' && echo
POST {{host}}/TestRedisClientSet
Content-Type: application/json

{
  "method": "set",
  "single": {
    "key": "lua:rc:user",
    "value": "{\"id\": 999, \"name\": \"张三feng\"}"
  }
}

### 测试 Redis 集群写入操作：mset
# curl -H 'Content-Type: application/json' -d '{"method": "mset", "multiple": [{"key": "lua:rc:user:1998", "value": "{\"id\": 1998, \"name\": \"无名898\"}", "expire_seconds": 60}, {"key": "lua:rc:user:2", "value": "{\"id\": 2, \"name\": \"张四feng\"}"}]}' 'http://localhost:8889/TestRedisClientSet' && echo
POST {{host}}/TestRedisClientSet
Content-Type: application/json

{
  "method": "mset",
  "multiple": [
    {
      "key": "lua:rc:user:1998",
      "value": "{\"id\": 1998, \"name\": \"无名898\"}",
      "expire_seconds": 60
    },
    {
      "key": "lua:rc:user:2",
      "value": "{\"id\": 2, \"name\": \"张四feng\"}"
    }
  ]
}

### 测试 Redis 集群写入操作：zstd_set
# curl -H 'Content-Type: application/json' -d '{"method": "zstd_set", "single": {"key": "lua:rc:zstd-str", "value": "这是一段中文ABC123", "expire_seconds": 10}}' 'http://localhost:8889/TestRedisClientSet' && echo
POST {{host}}/TestRedisClientSet
Content-Type: application/json

{
  "method": "zstd_set",
  "single": {
    "key": "lua:rc:zstd-str",
    "value": "这是一段中文ABC123",
    "expire_seconds": 10
  }
}

### 测试 Redis 集群写入操作：zstd_mset
# curl -H 'Content-Type: application/json' -d '{"method": "zstd_mset", "multiple": [{"key": "lua:rc:zstd", "value": "{\"id\": 688, \"name\": \"压缩688\"}"}, {"key": "lua:rc:zstd-str", "value": "这是一段被压缩的内容1234567890AAA", "expire_seconds": 20}]}' 'http://localhost:8889/TestRedisClientSet' && echo
POST {{host}}/TestRedisClientSet
Content-Type: application/json

{
  "method": "zstd_mset",
  "multiple": [
    {
      "key": "lua:rc:zstd",
      "value": "{\"id\": 688, \"name\": \"压缩688\"}"
    },
    {
      "key": "lua:rc:zstd-str",
      "value": "这是一段被压缩的内容1234567890AAA",
      "expire_seconds": 20
    }
  ]
}

### 测试 Redis 集群检索操作：keys（全节点检索，可以没有 hashtag）
# curl -H 'Content-Type: application/json' -d '{"method": "keys", "pattern": "lua:rc:user:*"}' 'http://localhost:8889/TestRedisClientMisc' && echo
POST {{host}}/TestRedisClientMisc
Content-Type: application/json

{
  "method": "keys",
  "pattern": "lua:rc:user:*"
}

### 测试 Redis 集群删除操作：del
# curl -H 'Content-Type: application/json' -d '{"method": "del", "keys": ["re:4444", "re:5555", "re:2222"]}' 'http://localhost:8889/TestRedisClientMisc' && echo
POST {{host}}/TestRedisClientMisc
Content-Type: application/json

{
  "method": "del",
  "keys": [
    "re:4444",
    "re:5555",
    "re:2222"
  ]
}

### 测试规则引擎胶水代码逻辑
# curl -H 'Content-Type: application/json' -d '{"file": "D:/Code/suofeiya/rule-engine/test/xmlStr.xml"}' 'http://localhost:8889/TestRuleEngineGlue' && echo
# curl -H 'Content-Type: application/json' -d '{"file": "/usr/local/xml.xml' 'http://localhost:8889/TestRuleEngineGlue' && echo
POST {{host}}/TestRuleEngineGlue
Content-Type: application/json

{
  "file": "D:/Code/suofeiya/rule-engine/test/xmlStr.xml"
}

### 健康检查
# curl 'http://localhost:8889/actuator/health' && echo
GET {{host}}/actuator/health

### 规则脚本
# curl -H 'Content-Type: application/json' -d '{"xmlCacheKey": "product:123456:xml"}' 'http://localhost:8889/api/v1/rule/param/calc' && echo
POST {{host}}/api/v1/rule/param/calc
Content-Type: application/json
Trace-Id: traceid1111

{
  "xmlCacheKey": ""
}
