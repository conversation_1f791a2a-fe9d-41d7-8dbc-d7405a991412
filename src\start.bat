cd windows
nginx -s stop
cd ..

xcopy /E /Y commons\resty\ windows\lualib\resty\
xcopy /E /Y commons\lualib\ windows\lualib\
if not exist windows\lua_scripts mkdir windows\lua_scripts
xcopy /E /Y src\ windows\lua_scripts\
xcopy /E /Y src\controller\ windows\lua_scripts\
cd windows

@REM if not defined REDIS_HOST set REDIS_HOST=127.0.0.1
@REM if not defined REDIS_PORT set REDIS_PORT=6379

mkdir logs
start nginx.exe
cd ..
