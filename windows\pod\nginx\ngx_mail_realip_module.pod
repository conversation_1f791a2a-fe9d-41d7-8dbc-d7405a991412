=encoding utf-8

=head1 NAME

ngx_mail_realip_module - Module ngx_mail_realip_module




=head1



The C<ngx_mail_realip_module> module is used
to change the client address and port
to the ones sent in the PROXY protocol header (1.19.8).
The PROXY protocol must be previously enabled by setting the
L<ngx_mail_core_module> parameter
in the C<listen> directive.




=head1 Example Configuration




    
    listen 110 proxy_protocol;
    
    set_real_ip_from  ***********/24;
    set_real_ip_from  ***********;
    set_real_ip_from  2001:0db8::/32;






=head1 Directives

=head2 set_real_ip_from


B<syntax:> set_real_ip_from I<
    I<C<address>> E<verbar>
    I<C<CIDR>> E<verbar>
    C<unix:>>



B<context:> I<mail>


B<context:> I<server>





Defines trusted addresses that are known to send correct
replacement addresses.
If the special value C<unix:> is specified,
all UNIX-domain sockets will be trusted.







