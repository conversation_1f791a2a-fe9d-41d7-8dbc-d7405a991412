-- 获取xml中 子节点 的 属性值
-- 专门用于找 某一个 name value 匹配的子节点 并返回这个子节点的value
-- 如果匹配到多个，则返回第一个

local core = require("lib.xml-part-core")
local _M = {}

-- 函数：通过key-value数组在XML中搜索匹配项
function _M.get_value_by_child_node(xml, match_type, name, value)
    -- 参数校验
    if not xml or not match_type or not name or not value then
        return nil, "get_value_by_child_node: invalid parameters"
    end

    local matched_nodes, err = core.get_childs_node_by_kvs(xml, match_type, name, value)
    if not matched_nodes then
        return nil, err
    end

    if not matched_nodes or #matched_nodes == 0 then
        return nil, "没有匹配的数据"
    end

    local rst_value = matched_nodes[1]:get_attribute("value")
    return rst_value, nil
end

-- 函数：通过key-value数组在XML中搜索匹配项
function _M.get_childs_node_by_kvs(xml, match_type, name, value)
    -- 参数校验
    if not xml or not match_type or not name or not value then
        return nil, "get_childs_node_by_kvs: invalid parameters"
    end

    local matched_nodes, err = core.get_childs_node_by_kvs(xml, match_type, name, value)
    if not matched_nodes then
        return nil, err
    end
    
    return matched_nodes
end

-- 函数：在XML中搜索匹配项
function _M.find_value_in_node(xml, match_type, key)
    -- 参数校验
    if not xml or not match_type or not key then
        return nil, "find_value_in_node: invalid parameters"
    end

    local match_value, err = core.find_value_in_node(xml, match_type, key)
    if not match_value then
        return nil, err
    end

    return match_value
end

-- 传一个部件xml进来 返回对应 parameter 的 value
function _M.get_value_in_parameter(xml, name)
	local parameter_xml = xml:search("./Parameters/Parameter[@name='" .. name .. "']")
	if #parameter_xml == 0 then
		return nil
	end
	return parameter_xml[1]:get_attribute("value")
end

-- 传一个xml进来 返回xml内所有门列表
-- 需注意 xml下一级就要有Part节点 这样外部可以控制要找带真门的 还是不带真门的
function _M.get_door_list(xml)
	local door_list = {}
	local part_list_xml = xml:search(".//Part")
	if not part_list_xml or #part_list_xml == 0 then
		return nil
	end

	for _, part_xml in ipairs(part_list_xml) do
		local mkbq = _M.get_value_in_parameter(part_xml, "MKBQ")
		if mkbq == "3" then
			table.insert(door_list, part_xml)
		end
	end
	return door_list
end

-- 传一个xml进来 返回xml内所有门列表
-- 需注意 xml下一级就要有Part节点 这样外部可以控制要找带真门的 还是不带真门的
function _M.get_part_by_mkbq(xml, mkv)
	local part_list = {}
	local part_list_xml = xml:search(".//Part")
	if not part_list_xml or #part_list_xml == 0 then
		return nil
	end

	for _, part_xml in ipairs(part_list_xml) do
		local mkbq = _M.get_value_in_parameter(part_xml, "MKBQ")
		if mkbq == tostring(mkv) then
			table.insert(part_list, part_xml)
		end
	end
	return part_list
end

-- 传一个xml进来 返回xml内所有门列表
-- 需注意 xml下一级就要有Part节点 这样外部可以控制要找带真门的 还是不带真门的
function _M.get_part_by_bjbq(xml, bjv)
	local part_list = {}
	local part_list_xml = xml:search(".//Part")
	if not part_list_xml or #part_list_xml == 0 then
		return nil
	end

	for _, part_xml in ipairs(part_list_xml) do
		local mkbq = _M.get_value_in_parameter(part_xml, "BJBQ")
		if mkbq == tostring(bjv) then
			table.insert(part_list, part_xml)
		end
	end
	return part_list
end

return _M