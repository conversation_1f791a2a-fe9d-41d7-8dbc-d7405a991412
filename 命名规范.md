# 命名规范

命名尽量语义化，比如 `name`、`age`、`width`、`height`、`depth`等。尽量别用`a`、`b`、`c`这种（除了循环变量）

## 变量 Variables

snake_case (小写蛇形命名法)，允许前导下划线 _ 表示私有或内部变量。

比如：`file`、`filename`、`filepath`、`cabinet_id`、`cabinetid`。

## 常量 Constants

UPPER_SNAKE_CASE (大写蛇形命名法)。

一般用在默认值上，比如`DEFAULT_POOL_SIZE`、`DEFAULT_CONNECT_TIMEOUT_IN_MILLISE`。

## 函数 Functions

snake_case (小写蛇形命名法)，允许前导下划线 _ 表示私有或内部函数。

如果是`C++`打包成动态库然后通过`metatable`动态调用，那么允许使用 camelCase (小驼峰命名法)。

比如`make_box`（手动封装）、`makeBox`（动态库原始方法）、`sceneInit`（动态库原始方法）

## 函数参数 Function Arguments

snake_case (小写蛇形命名法)，允许前导下划线 _ 表示忽略的参数。

遵循变量的命名规范。

## 表字段 Table Fields

snake_case (小写蛇形命名法) 或者 camelCase (小驼峰命名法)，允许前导下划线 _。

相当于对象的字段，比如`{name='名称'}`或者`{userAge=18}`这样定义表。

## 模块 Modules

lower-hyphen (小写连字符命名法) 或者 snake_case (小写蛇形命名法)。

比如`src/lib`目录下的`redisclient.lua`、`rule-engine.lua`等模块或者库。

## 循环变量 Loop Variables

snake_case (小写蛇形命名法)，允许前导下划线 _ 表示忽略的参数。

遵循变量的命名规范。

## 接口 API 文件

CamelCase (小驼峰命名法) 或者 lower-hyphen (小写连字符命名法) 。

比如`src/controller`目录下的`TestRedisClientSet.lua`、`test-delete-redis-script.lua`。

## 库封装规范

如果封装的库每次在使用的时候都需要实例化，那么应该以对象形式进行封装。在引入库之后通过`Xxx:new()`进行实例化，并使用`inst:xxx()`格式进行方法调用。比如原生的`zstd`类库。

如果封装的库每次在使用的时候不需要实例化，相当于工具包，那么应该以静态方法形式进行封装。在引入库之后通过`xxx.xxx()`直接进行方法调用，无需实例化。比如封装的`redisclient`、`rule-engine`。
